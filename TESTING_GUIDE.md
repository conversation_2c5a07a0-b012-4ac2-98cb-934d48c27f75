# 🧪 Guide de Test Manuel - EquiNova Material-UI Theme System v2.0

## 🚀 Démarrage Rapide

### 1. <PERSON><PERSON><PERSON><PERSON> le Backend

```bash
# Dans le répertoire racine
cd backend
docker-compose up -d

# Vérifier que les services sont actifs
docker-compose ps
```

### 2. <PERSON><PERSON><PERSON><PERSON> le Frontend

```bash
# Dans le répertoire frontend
cd frontend
npm install  # Si nécessaire
npm run dev

# Ou avec Docker
docker-compose -f docker-compose.dev.yml up frontend
```

### 3. Accéder à l'Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **Documentation API**: http://localhost:8000/docs
- **Adminer (DB)**: http://localhost:8080
- **Mailcatcher**: http://localhost:1080
- **Traefik Dashboard**: http://localhost:8090

### 4. Vérifier que Tout Fonctionne

```bash
# Vérifier l'état des services
docker-compose ps

# Tester l'API backend
curl http://localhost:8000/health

# Vérifier les logs si nécessaire
docker-compose logs backend
docker-compose logs frontend
```

## 🎯 Points de Test Prioritaires

### 🎨 Système de Thème (Priorité 1)

#### Pages à Tester :
1. **`/theme-demo`** - Démonstration de base
2. **`/theme-studio`** - Studio de thème avancé
3. **`/theme-system-validation`** - Validation système

#### Tests Critiques :
- [ ] Basculement Light/Dark mode (bouton en haut à droite)
- [ ] Changement de variantes de thème (Default, Corporate, Modern, Classic)
- [ ] Ouverture du configurateur de thème (icône Settings)
- [ ] Persistance des préférences (rafraîchir la page)
- [ ] Export/Import de thèmes
- [ ] Personnalisation de marque (couleurs, logos, polices)

### 📊 Gestion des Cas Légaux (Priorité 2)

#### Pages à Tester :
1. **`/material-legal-cases-table`** - Table DataGrid Material-UI
2. **`/legal-cases`** - Version Chakra UI (pour comparaison)

#### Tests Critiques :
- [ ] Chargement de la table DataGrid
- [ ] Pagination et tri des colonnes
- [ ] Recherche avec debounce
- [ ] Filtres par statut et priorité
- [ ] Sélection de lignes (simple et multiple)
- [ ] Ajout de nouveau cas (bouton + FAB)
- [ ] Édition de cas existant
- [ ] Opérations en lot (sélection multiple)
- [ ] Indicateurs de statut et barres de progression

### ♿ Accessibilité (Priorité 3)

#### Tests à Effectuer :
- [ ] Navigation au clavier (Tab, Enter, Escape, flèches)
- [ ] Lecteur d'écran (activer le lecteur d'écran du navigateur)
- [ ] Contraste des couleurs (vérifier en mode sombre/clair)
- [ ] Focus visible sur tous les éléments interactifs
- [ ] ARIA labels et descriptions

### 📱 Responsive Design (Priorité 4)

#### Breakpoints à Tester :
- [ ] Mobile (320px - 768px)
- [ ] Tablet (768px - 1024px)
- [ ] Desktop (1024px+)
- [ ] Large Desktop (1440px+)

## 🐛 Signalement de Bugs

### Template de Bug Report

```markdown
**Bug**: [Titre descriptif]

**Sévérité**: Critique/Haute/Moyenne/Basse

**Étapes pour reproduire**:
1. 
2. 
3. 

**Comportement attendu**: 

**Comportement actuel**: 

**Environnement**:
- OS: 
- Navigateur: 
- Résolution d'écran: 

**Screenshots**: [Si applicable]
```

## 🔧 Commandes Utiles

### Debugging Frontend
```bash
# Logs détaillés
npm run dev -- --debug

# Build de production pour tester les performances
npm run build
npm run preview
```

### Debugging Backend
```bash
# Logs des containers
docker-compose logs -f

# Redémarrer un service spécifique
docker-compose restart backend
```

### Nettoyage
```bash
# Nettoyer le cache npm
npm cache clean --force

# Redémarrer complètement Docker
docker-compose down
docker-compose up -d
```

## 📊 Métriques à Surveiller

### Performance
- **Temps de chargement initial** : < 3s
- **Temps de basculement de thème** : < 200ms
- **Temps de rendu DataGrid** : < 1s pour 100 éléments

### Accessibilité
- **Score Lighthouse** : > 90
- **Contraste des couleurs** : Ratio > 4.5:1
- **Navigation clavier** : 100% fonctionnelle

### UX
- **Fluidité des animations** : 60fps
- **Feedback visuel** : Immédiat sur toutes les actions
- **Messages d'erreur** : Clairs et utiles

## 🎯 Checklist de Test Rapide (15 min)

### ✅ Test Express
1. [ ] Démarrer frontend/backend
2. [ ] Se connecter à l'application
3. [ ] Changer le thème (light/dark)
4. [ ] Ouvrir `/material-legal-cases-table`
5. [ ] Ajouter un nouveau cas légal
6. [ ] Éditer un cas existant
7. [ ] Tester la recherche
8. [ ] Ouvrir `/theme-studio`
9. [ ] Personnaliser les couleurs
10. [ ] Exporter les préférences
11. [ ] Tester sur mobile (DevTools)
12. [ ] Vérifier la navigation clavier

## 📝 Rapport de Test

### À Remplir Après Tests

**Date**: ___________
**Testeur**: ___________
**Durée**: ___________

**Résultats**:
- **Fonctionnalité**: ⭐⭐⭐⭐⭐ (1-5)
- **Performance**: ⭐⭐⭐⭐⭐ (1-5)
- **Accessibilité**: ⭐⭐⭐⭐⭐ (1-5)
- **UX**: ⭐⭐⭐⭐⭐ (1-5)
- **Mobile**: ⭐⭐⭐⭐⭐ (1-5)

**Bugs trouvés**: _____ (nombre)
**Améliorations suggérées**: _____ (nombre)

**Prêt pour la production**: ✅ / ❌

---

## 🆘 Support

Si vous rencontrez des problèmes :

1. **Vérifier les logs** : `docker-compose logs`
2. **Redémarrer les services** : `docker-compose restart`
3. **Nettoyer et redémarrer** : `docker-compose down && docker-compose up -d`
4. **Vérifier les ports** : `netstat -tulpn | grep :5173`

**Issue GitHub** : #103 - Manual Testing and Bug Fixes

---

**Bonne chance pour les tests ! 🚀**
