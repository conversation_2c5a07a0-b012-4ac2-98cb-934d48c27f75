#!/bin/bash

# EquiNova Production Deployment Script
# Issue #107: Deploy Docker + Traefik + PostgreSQL infrastructure

set -e

echo "🚀 Starting EquiNova Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_error ".env.production file not found. Please create it first."
    exit 1
fi

# Load environment variables
print_status "Loading environment variables..."
export $(cat .env.production | grep -v '^#' | xargs)

# Validate required environment variables
required_vars=("POSTGRES_PASSWORD" "SECRET_KEY" "FIRST_SUPERUSER_PASSWORD")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Required environment variable $var is not set"
        exit 1
    fi
done

# Check if passwords are still default
if [[ "$POSTGRES_PASSWORD" == "CHANGE_THIS_SECURE_PASSWORD_123!" ]]; then
    print_error "Please change the default POSTGRES_PASSWORD in .env.production"
    exit 1
fi

if [[ "$SECRET_KEY" == "CHANGE_THIS_SECRET_KEY_64_CHARACTERS_LONG_RANDOM_STRING_HERE" ]]; then
    print_error "Please change the default SECRET_KEY in .env.production"
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p ./backend/uploads
mkdir -p ./backend/logs
mkdir -p ./backups
mkdir -p ./traefik/logs

# Set proper permissions
print_status "Setting proper permissions..."
chmod 600 .env.production
chmod 600 traefik/acme.json
chmod 755 ./backend/uploads
chmod 755 ./backups

# Stop existing containers if running
print_status "Stopping existing containers..."
docker-compose -f docker-compose.production.yml down --remove-orphans || true

# Pull latest images
print_status "Pulling latest images..."
docker-compose -f docker-compose.production.yml pull

# Build custom images
print_status "Building custom images..."
docker-compose -f docker-compose.production.yml build --no-cache

# Start services
print_status "Starting services..."
docker-compose -f docker-compose.production.yml up -d

# Wait for services to be healthy
print_status "Waiting for services to be healthy..."
sleep 30

# Check service status
print_status "Checking service status..."
docker-compose -f docker-compose.production.yml ps

# Test database connection
print_status "Testing database connection..."
if docker-compose -f docker-compose.production.yml exec -T db pg_isready -U $POSTGRES_USER -d $POSTGRES_DB; then
    print_success "Database is ready"
else
    print_error "Database is not ready"
    exit 1
fi

# Run database migrations
print_status "Running database migrations..."
docker-compose -f docker-compose.production.yml exec -T backend alembic upgrade head

# Create first superuser
print_status "Creating first superuser..."
docker-compose -f docker-compose.production.yml exec -T backend python -c "
from app.core.config import settings
from app.crud import create_user
from app.models import UserCreate
from app.core.db import SessionLocal

db = SessionLocal()
try:
    user = create_user(
        session=db,
        user_create=UserCreate(
            email='${FIRST_SUPERUSER}',
            password='${FIRST_SUPERUSER_PASSWORD}',
            is_superuser=True
        )
    )
    print(f'Superuser created: {user.email}')
except Exception as e:
    print(f'Superuser might already exist: {e}')
finally:
    db.close()
"

# Display deployment information
print_success "🎉 Deployment completed successfully!"
echo ""
echo "📋 Deployment Information:"
echo "=========================="
echo "🌐 Production URL: https://tunleg.com"
echo "🔧 Development URL: https://dev.tunleg.com"
echo "🧪 QA URL: https://qa.tunleg.com"
echo "🗄️  Database Admin: https://adminer.tunleg.com"
echo "📊 Traefik Dashboard: https://traefik.tunleg.com"
echo ""
echo "👤 Admin Credentials:"
echo "Email: $FIRST_SUPERUSER"
echo "Password: [Check .env.production file]"
echo ""
echo "🔍 Useful Commands:"
echo "==================="
echo "View logs: docker-compose -f docker-compose.production.yml logs -f"
echo "Stop services: docker-compose -f docker-compose.production.yml down"
echo "Restart services: docker-compose -f docker-compose.production.yml restart"
echo "Check status: docker-compose -f docker-compose.production.yml ps"
echo ""
print_warning "Remember to:"
print_warning "1. Configure DNS records to point to this server"
print_warning "2. Set up automated backups"
print_warning "3. Configure monitoring"
print_warning "4. Review security settings"
