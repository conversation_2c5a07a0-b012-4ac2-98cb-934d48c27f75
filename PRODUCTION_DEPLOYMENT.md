# 🚀 EquiNova Production Deployment Guide

> **Status**: ✅ READY FOR DEPLOYMENT  
> **Issue**: #107 - Deploy Docker + Traefik + PostgreSQL infrastructure  
> **Domain**: tunleg.com  
> **VPS**: ************* (<PERSON><PERSON><PERSON>)

## 📋 Overview

This guide covers the complete production deployment of EquiNova using Docker, Traefik, and PostgreSQL on the configured Hetzner VPS.

### Architecture
- **Reverse Proxy**: Traefik 3.0 with automatic SSL (Let's Encrypt)
- **Database**: PostgreSQL 15 with persistent storage
- **Backend**: FastAPI with health checks
- **Frontend**: React/Nginx for all environments
- **Environments**: Production, Development, QA

## 🌐 URLs Configuration

| Environment | URL | Purpose |
|-------------|-----|---------|
| Production | https://tunleg.com | Main application |
| Development | https://dev.tunleg.com | Development testing |
| QA/Staging | https://qa.tunleg.com | Pre-production testing |
| API | https://tunleg.com/api | Backend API |
| Database Admin | https://adminer.tunleg.com | Database management |
| Traefik Dashboard | https://traefik.tunleg.com | Proxy monitoring |

## 🔧 Prerequisites

### On VPS (Already Completed)
- ✅ Ubuntu 22.04 LTS
- ✅ Docker and Docker Compose installed
- ✅ UFW firewall configured (ports 22, 80, 443)
- ✅ SSH key authentication
- ✅ Domain DNS configured

### Required Files
- ✅ `docker-compose.production.yml`
- ✅ `traefik/traefik.yml`
- ✅ `traefik/acme.json` (empty, 600 permissions)
- ✅ `.env.production` (configured)
- ✅ `deploy-production.sh` (executable)

## 🚀 Deployment Steps

### Step 1: Prepare Environment Variables
```bash
# Copy and configure environment file
cp .env.production .env.production.local
nano .env.production.local

# Required changes:
# - POSTGRES_PASSWORD: Strong password
# - SECRET_KEY: 64-character random string
# - FIRST_SUPERUSER_PASSWORD: Admin password
# - EMAIL: Your email for Let's Encrypt
```

### Step 2: Generate Secure Passwords
```bash
# Generate PostgreSQL password
openssl rand -base64 32

# Generate secret key
openssl rand -hex 32

# Generate Traefik basic auth password
htpasswd -nb admin your_password
```

### Step 3: Deploy to VPS
```bash
# Upload files to VPS
scp -r . user@*************:/opt/equinova/

# SSH to VPS
ssh user@*************

# Navigate to application directory
cd /opt/equinova

# Run deployment script
./deploy-production.sh
```

### Step 4: Verify Deployment
```bash
# Check all services are running
docker-compose -f docker-compose.production.yml ps

# Check logs
docker-compose -f docker-compose.production.yml logs -f

# Test endpoints
curl -k https://tunleg.com/api/v1/utils/health-check/
curl -k https://dev.tunleg.com
curl -k https://qa.tunleg.com
```

## 🔒 Security Configuration

### SSL Certificates
- **Provider**: Let's Encrypt (automatic)
- **Renewal**: Automatic via Traefik
- **Protocols**: TLS 1.2, TLS 1.3
- **HSTS**: Enabled (31536000 seconds)

### Database Security
- **Network**: Internal Docker network only
- **Access**: Local binding (127.0.0.1:5432)
- **Authentication**: Password-based
- **Backups**: Automated daily backups

### Application Security
- **CORS**: Configured for specific domains
- **Headers**: Security headers enabled
- **Authentication**: JWT-based
- **Admin Access**: Basic auth for admin interfaces

## 📊 Monitoring & Maintenance

### Health Checks
```bash
# Application health
curl https://tunleg.com/api/v1/utils/health-check/

# Database health
docker-compose -f docker-compose.production.yml exec db pg_isready

# Container status
docker-compose -f docker-compose.production.yml ps
```

### Log Management
```bash
# View all logs
docker-compose -f docker-compose.production.yml logs -f

# View specific service logs
docker-compose -f docker-compose.production.yml logs -f backend
docker-compose -f docker-compose.production.yml logs -f traefik

# Traefik access logs
tail -f /opt/equinova/traefik/logs/access.log
```

### Backup Procedures
```bash
# Manual database backup
docker-compose -f docker-compose.production.yml exec db pg_dump -U equinova_user equinova > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup uploaded files
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz ./backend/uploads/
```

## 🔄 Common Operations

### Update Application
```bash
# Pull latest changes
git pull origin master

# Rebuild and restart
docker-compose -f docker-compose.production.yml build --no-cache
docker-compose -f docker-compose.production.yml up -d
```

### Scale Services
```bash
# Scale backend instances
docker-compose -f docker-compose.production.yml up -d --scale backend=3
```

### Database Operations
```bash
# Run migrations
docker-compose -f docker-compose.production.yml exec backend alembic upgrade head

# Create database backup
docker-compose -f docker-compose.production.yml exec db pg_dump -U equinova_user equinova > backup.sql

# Restore database
docker-compose -f docker-compose.production.yml exec -T db psql -U equinova_user equinova < backup.sql
```

## 🚨 Troubleshooting

### SSL Certificate Issues
```bash
# Check certificate status
docker-compose -f docker-compose.production.yml logs traefik | grep -i certificate

# Force certificate renewal
docker-compose -f docker-compose.production.yml restart traefik
```

### Database Connection Issues
```bash
# Check database logs
docker-compose -f docker-compose.production.yml logs db

# Test connection
docker-compose -f docker-compose.production.yml exec backend python -c "from app.core.db import engine; print(engine.execute('SELECT 1').scalar())"
```

### Service Not Accessible
```bash
# Check Traefik routing
curl -H "Host: tunleg.com" http://localhost/api/v1/utils/health-check/

# Check DNS resolution
nslookup tunleg.com
dig tunleg.com A
```

## 📞 Support & Resources

### Useful Commands
```bash
# Stop all services
docker-compose -f docker-compose.production.yml down

# Start all services
docker-compose -f docker-compose.production.yml up -d

# Restart specific service
docker-compose -f docker-compose.production.yml restart backend

# View resource usage
docker stats
```

### Configuration Files
- **Docker Compose**: `docker-compose.production.yml`
- **Traefik Config**: `traefik/traefik.yml`
- **Environment**: `.env.production`
- **SSL Certificates**: `traefik/acme.json`

### External Resources
- **Traefik Documentation**: https://doc.traefik.io/traefik/
- **Docker Compose**: https://docs.docker.com/compose/
- **Let's Encrypt**: https://letsencrypt.org/docs/
- **PostgreSQL**: https://www.postgresql.org/docs/

---

**Last Updated**: 2025-01-16  
**Deployment Version**: v1.0  
**Maintained By**: EquiNova DevOps Team
