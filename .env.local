# EquiNova LOCAL Development Environment
# Simple configuration for daily development work

# =============================================================================
# ENVIRONMENT
# =============================================================================
ENVIRONMENT=local

# =============================================================================
# DATABASE CONFIGURATION (LOCAL)
# =============================================================================
POSTGRES_DB=equinova_local
POSTGRES_USER=equinova_user
POSTGRES_PASSWORD=local_dev_password_123

# =============================================================================
# APPLICATION SECURITY (LOCAL)
# =============================================================================
SECRET_KEY=local-development-secret-key-not-for-production-use-only

# =============================================================================
# ADMIN USER (LOCAL)
# =============================================================================
FIRST_SUPERUSER=admin@localhost
FIRST_SUPERUSER_PASSWORD=admin123

# =============================================================================
# EMAIL CONFIGURATION (LOCAL - Optional)
# =============================================================================
SMTP_HOST=
SMTP_PORT=587
SMTP_TLS=true
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=noreply@localhost
EMAILS_FROM_NAME=EquiNova Local

# =============================================================================
# FRONTEND CONFIGURATION (LOCAL)
# =============================================================================
FRONTEND_HOST=http://localhost:3000
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# DEVELOPMENT FLAGS (LOCAL)
# =============================================================================
DEBUG=true
LOG_LEVEL=DEBUG
