# 🖥️ Hetzner VPS Setup Documentation - EquiNova

> **Status**: ✅ COMPLETED  
> **Issue**: #105 - Create and secure <PERSON><PERSON><PERSON> VPS for initial deployment  
> **Date**: 2025-01-16  

## 📋 VPS Configuration Summary

### Server Specifications
- **Provider**: Hetzner Cloud
- **Plan**: CX31 (2 vCPU, 8GB RAM, 80GB SSD)
- **OS**: Ubuntu 22.04 LTS
- **Location**: [SPECIFY_DATACENTER]
- **IPv4**: [VPS_IP_ADDRESS]
- **IPv6**: [VPS_IPv6_ADDRESS] (if enabled)

### Server Details
```bash
# Server Information
Hostname: equinova-prod
FQDN: equinova-prod.hetzner.local
Kernel: Ubuntu 22.04.x LTS
Architecture: x86_64
```

## 🔐 Security Configuration

### SSH Configuration
- **SSH Port**: 22 (default) or [CUSTOM_PORT] if changed
- **Root Login**: DISABLED
- **Password Authentication**: DISABLED
- **Key-based Authentication**: ENABLED

#### SSH Users Created
```bash
# Administrative user
Username: [ADMIN_USERNAME]
SSH Key: [PUBLIC_KEY_FINGERPRINT]
Sudo Access: YES

# Deployment user (if separate)
Username: [DEPLOY_USERNAME]
SSH Key: [DEPLOY_KEY_FINGERPRINT]
Sudo Access: LIMITED (docker, systemctl)
```

### Firewall Configuration (UFW)

#### UFW Rules Applied
```bash
# Default policies
ufw default deny incoming
ufw default allow outgoing

# Allowed ports
ufw allow 22/tcp          # SSH
ufw allow 80/tcp          # HTTP
ufw allow 443/tcp         # HTTPS

# Optional: Custom SSH port
# ufw allow [CUSTOM_SSH_PORT]/tcp

# UFW Status
ufw --force enable
```

#### Verification Commands
```bash
# Check UFW status
sudo ufw status verbose

# Check active connections
sudo netstat -tulpn

# Check SSH configuration
sudo sshd -T | grep -E "(port|permitrootlogin|passwordauthentication)"
```

### Additional Security Measures

#### Fail2Ban (if installed)
```bash
# Fail2Ban configuration for SSH protection
sudo apt install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Check banned IPs
sudo fail2ban-client status sshd
```

#### Automatic Updates
```bash
# Unattended upgrades configuration
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Configuration file: /etc/apt/apt.conf.d/50unattended-upgrades
```

## 🐳 Docker Installation

### Docker & Docker Compose Setup
```bash
# Docker installation
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER

# Docker Compose installation
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

### Docker Configuration
```bash
# Docker daemon configuration
# File: /etc/docker/daemon.json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}

# Restart Docker
sudo systemctl restart docker
sudo systemctl enable docker
```

## 📁 Directory Structure

### Application Directories
```bash
# Main application directory
/opt/equinova/
├── docker-compose.yml
├── .env
├── traefik/
│   ├── acme.json
│   └── traefik.yml
├── postgres/
│   └── data/
├── uploads/
├── backups/
└── logs/

# Permissions
sudo chown -R $USER:docker /opt/equinova
chmod 600 /opt/equinova/.env
chmod 600 /opt/equinova/traefik/acme.json
```

### System Directories
```bash
# Log directories
/var/log/equinova/
├── application.log
├── nginx.log
└── postgres.log

# Backup directory
/backup/
├── daily/
├── weekly/
└── monthly/
```

## 🌐 Network Configuration

### Hetzner Firewall (Cloud Console)
```bash
# Inbound Rules
Port 22   (SSH)     - Source: [YOUR_IP_RANGE]
Port 80   (HTTP)    - Source: 0.0.0.0/0
Port 443  (HTTPS)   - Source: 0.0.0.0/0

# Outbound Rules
All traffic allowed (default)
```

### DNS Configuration
```bash
# /etc/hosts entries
127.0.0.1 localhost
[VPS_IP] equinova.com
[VPS_IP] qa.equinova.com
[VPS_IP] dev.equinova.com
```

## 📊 Monitoring & Maintenance

### System Monitoring
```bash
# Basic monitoring commands
htop                    # Process monitoring
df -h                   # Disk usage
free -h                 # Memory usage
iostat                  # I/O statistics
netstat -tulpn         # Network connections

# Log monitoring
tail -f /var/log/syslog
journalctl -f
```

### Maintenance Scripts
```bash
# System update script
#!/bin/bash
# File: /opt/scripts/update-system.sh
sudo apt update
sudo apt upgrade -y
sudo apt autoremove -y
docker system prune -f

# Make executable
chmod +x /opt/scripts/update-system.sh
```

## 🔧 Troubleshooting

### Common Issues & Solutions

#### SSH Connection Issues
```bash
# Check SSH service
sudo systemctl status ssh

# Check SSH configuration
sudo sshd -T

# Check firewall
sudo ufw status
```

#### Docker Issues
```bash
# Check Docker service
sudo systemctl status docker

# Check Docker logs
sudo journalctl -u docker

# Restart Docker
sudo systemctl restart docker
```

#### Disk Space Issues
```bash
# Check disk usage
df -h
du -sh /opt/equinova/*

# Clean Docker
docker system prune -a
docker volume prune
```

## 📝 Access Information

### SSH Access
```bash
# SSH connection command
ssh [USERNAME]@[VPS_IP_ADDRESS]

# SSH with custom port (if changed)
ssh -p [CUSTOM_PORT] [USERNAME]@[VPS_IP_ADDRESS]

# SSH with specific key
ssh -i ~/.ssh/equinova_key [USERNAME]@[VPS_IP_ADDRESS]
```

### Important Files Locations
```bash
# SSH configuration
/etc/ssh/sshd_config

# UFW configuration
/etc/ufw/

# Docker configuration
/etc/docker/daemon.json

# Application environment
/opt/equinova/.env

# SSL certificates (when Traefik is setup)
/opt/equinova/traefik/acme.json
```

## ✅ Verification Checklist

### Security Verification
- [ ] SSH key-based authentication working
- [ ] Root login disabled
- [ ] UFW firewall active and configured
- [ ] Only required ports open (22, 80, 443)
- [ ] Fail2Ban installed and active (optional)
- [ ] Automatic security updates enabled

### System Verification
- [ ] Docker and Docker Compose installed
- [ ] User added to docker group
- [ ] Directory structure created
- [ ] Proper file permissions set
- [ ] System monitoring tools available

### Network Verification
- [ ] Server accessible via SSH
- [ ] Hetzner Cloud firewall configured
- [ ] DNS records pointing to VPS IP
- [ ] Network connectivity working

## 🔄 Next Steps

1. **Issue #107**: Deploy Docker + Traefik + PostgreSQL infrastructure
2. **SSL Setup**: Configure Let's Encrypt certificates via Traefik
3. **Application Deployment**: Deploy EquiNova application containers
4. **Monitoring Setup**: Configure UptimeRobot and basic monitoring
5. **Backup Configuration**: Set up automated database and file backups

## 📞 Support & Resources

### Hetzner Resources
- **Hetzner Cloud Console**: https://console.hetzner.cloud/
- **Hetzner Documentation**: https://docs.hetzner.com/
- **Hetzner Support**: https://www.hetzner.com/support

### Ubuntu Resources
- **Ubuntu Server Guide**: https://ubuntu.com/server/docs
- **UFW Documentation**: https://help.ubuntu.com/community/UFW
- **SSH Hardening**: https://ubuntu.com/tutorials/ssh-keygen-on-windows

---

**Last Updated**: 2025-01-16  
**Maintained By**: EquiNova DevOps Team
