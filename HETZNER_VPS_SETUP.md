
# VPS Configuration for Tunleg.com

## 1. General Information

- **Provider**: Hetzner Cloud
- **Instance**: CX32 (4 vCPU, 8GB RAM, 80GB SSD)
- **Region**: Helsinki (eu-central)
- **Public IP**: *************
- **Operating System**: Ubuntu 22.04 LTS

---

## 2. SSH Access

- **Primary User**: `mohamed` (non-root)
- **Authentication Method**: SSH key (public key added on <PERSON><PERSON><PERSON>)
- **SSH Port**: 22
- **Root Access**: Disabled via SSH

### User creation and SSH setup

```bash
adduser mohamed
usermod -aG sudo mohamed
mkdir -p /home/<USER>/.ssh
cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
chown -R mohamed:mohamed /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

### SSH hardening in `/etc/ssh/sshd_config`

```conf
PermitRootLogin no
PasswordAuthentication no
ChallengeResponseAuthentication no
UsePAM yes
```

---

## 3. Firewall Configuration

### UFW (Ubuntu Firewall)

```bash
sudo ufw allow OpenSSH
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw enable
```

### Hetzner Cloud Firewall

- Applied firewall: `tunleg-firewall`
- Allowed inbound rules:
  - SSH (TCP port 22)
  - HTTP (TCP port 80)
  - HTTPS (TCP port 443)
  - ICMP (Ping)

---

## 4. Notes and Recommendations

- Never connect to SSH using the root account.
- Keep the private SSH key secure.
- Document any important changes in this file.
- Next steps: configure DNS, install Nginx, set up SSL certificates.

---

## 5. Useful Resources

- [Hetzner Cloud Documentation](https://docs.hetzner.cloud/)
- [Ubuntu SSH Hardening Guide](https://www.digitalocean.com/community/tutorials/ssh-hardening-on-ubuntu-20-04)
- [UFW Documentation](https://help.ubuntu.com/community/UFW)
