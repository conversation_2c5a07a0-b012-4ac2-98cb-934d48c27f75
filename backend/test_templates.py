#!/usr/bin/env python3
"""
Test script for Case Templates and Automation functionality
"""

import asyncio
import json
from datetime import datetime, timedelta

from app.models import (
    CaseTemplate, 
    DocumentTemplate, 
    WorkflowTemplate,
    CaseTemplateCreate,
    DocumentTemplateCreate,
    WorkflowTemplateCreate,
    CaseType
)

# Sample case template data
SAMPLE_CASE_TEMPLATES = [
    {
        "name": "Civil Litigation Template",
        "description": "Standard template for civil litigation cases with discovery, motions, and trial phases",
        "case_type": CaseType.CIVIL,
        "template_data": {
            "milestones": [
                {
                    "title": "Initial Case Assessment",
                    "description": "Review case facts and determine legal strategy",
                    "type": "assessment",
                    "order": 1,
                    "required": True,
                    "estimated_hours": 4
                },
                {
                    "title": "Discovery Phase",
                    "description": "Conduct discovery including depositions and document requests",
                    "type": "discovery",
                    "order": 2,
                    "required": True,
                    "estimated_hours": 20
                },
                {
                    "title": "Motion Practice",
                    "description": "File and respond to pre-trial motions",
                    "type": "motion",
                    "order": 3,
                    "required": False,
                    "estimated_hours": 8
                },
                {
                    "title": "Trial Preparation",
                    "description": "Prepare for trial including witness prep and exhibits",
                    "type": "trial_prep",
                    "order": 4,
                    "required": True,
                    "estimated_hours": 15
                }
            ],
            "deadlines": [
                {
                    "title": "Answer Filing Deadline",
                    "description": "Deadline to file answer to complaint",
                    "type": "filing",
                    "critical": True,
                    "reminder_days": 7
                },
                {
                    "title": "Discovery Cutoff",
                    "description": "Last day to complete discovery",
                    "type": "discovery",
                    "critical": True,
                    "reminder_days": 14
                },
                {
                    "title": "Motion Filing Deadline",
                    "description": "Deadline to file dispositive motions",
                    "type": "filing",
                    "critical": False,
                    "reminder_days": 7
                }
            ]
        },
        "is_active": True,
        "is_public": True,
        "tags": ["litigation", "civil", "standard", "discovery"]
    },
    {
        "name": "Contract Dispute Template",
        "description": "Template for contract dispute cases with breach analysis and damages calculation",
        "case_type": CaseType.CORPORATE,
        "template_data": {
            "milestones": [
                {
                    "title": "Contract Analysis",
                    "description": "Analyze contract terms and identify breach",
                    "type": "analysis",
                    "order": 1,
                    "required": True,
                    "estimated_hours": 6
                },
                {
                    "title": "Damages Calculation",
                    "description": "Calculate damages and lost profits",
                    "type": "analysis",
                    "order": 2,
                    "required": True,
                    "estimated_hours": 4
                },
                {
                    "title": "Settlement Negotiation",
                    "description": "Attempt to resolve through negotiation",
                    "type": "settlement",
                    "order": 3,
                    "required": False,
                    "estimated_hours": 8
                }
            ],
            "deadlines": [
                {
                    "title": "Demand Letter",
                    "description": "Send demand letter to opposing party",
                    "type": "communication",
                    "critical": False,
                    "reminder_days": 3
                }
            ]
        },
        "is_active": True,
        "is_public": True,
        "tags": ["contract", "dispute", "commercial", "breach"]
    }
]

# Sample document templates
SAMPLE_DOCUMENT_TEMPLATES = [
    {
        "name": "Demand Letter Template",
        "description": "Standard demand letter for contract disputes",
        "case_type": CaseType.CORPORATE,
        "document_type": "letter",
        "template_content": """{{date}}

{{opposing_party_name}}
{{opposing_party_address}}

Re: {{case_title}} - Demand for Payment

Dear {{opposing_party_contact}},

This letter serves as formal notice that {{client_name}} demands immediate payment of {{amount_owed}} pursuant to the contract dated {{contract_date}}.

The breach occurred when {{breach_description}}. As a result, {{client_name}} has suffered damages in the amount of {{damages_amount}}.

You have {{payment_deadline_days}} days from the date of this letter to cure this breach by paying the full amount owed. Failure to do so will result in legal action being commenced against you.

Please contact the undersigned immediately to arrange payment.

Sincerely,

{{lawyer_name}}
{{lawyer_title}}
{{law_firm_name}}
{{lawyer_contact_info}}""",
        "placeholders": [
            "date", "opposing_party_name", "opposing_party_address", "opposing_party_contact",
            "case_title", "client_name", "amount_owed", "contract_date", "breach_description",
            "damages_amount", "payment_deadline_days", "lawyer_name", "lawyer_title",
            "law_firm_name", "lawyer_contact_info"
        ],
        "is_active": True,
        "is_public": True,
        "tags": ["demand", "letter", "contract", "payment"]
    },
    {
        "name": "Motion to Dismiss Template",
        "description": "Template for motion to dismiss under Rule 12(b)(6)",
        "case_type": CaseType.CIVIL,
        "document_type": "motion",
        "template_content": """IN THE {{court_name}}

{{plaintiff_name}},
                    Plaintiff,
v.                                          Case No. {{case_number}}

{{defendant_name}},
                    Defendant.

MOTION TO DISMISS PURSUANT TO RULE 12(b)(6)

TO THE HONORABLE COURT:

NOW COMES {{defendant_name}}, by and through undersigned counsel, and respectfully moves this Court to dismiss {{plaintiff_name}}'s Complaint pursuant to Rule 12(b)(6) of the Rules of Civil Procedure for failure to state a claim upon which relief can be granted.

GROUNDS FOR MOTION

{{motion_grounds}}

WHEREFORE, {{defendant_name}} respectfully requests that this Court grant this Motion to Dismiss and dismiss {{plaintiff_name}}'s Complaint with prejudice.

Respectfully submitted,

{{lawyer_signature}}
{{lawyer_name}}, Esq.
{{bar_number}}
{{law_firm_name}}
{{lawyer_address}}
{{lawyer_phone}}
{{lawyer_email}}

Attorney for {{defendant_name}}""",
        "placeholders": [
            "court_name", "plaintiff_name", "case_number", "defendant_name",
            "motion_grounds", "lawyer_signature", "lawyer_name", "bar_number",
            "law_firm_name", "lawyer_address", "lawyer_phone", "lawyer_email"
        ],
        "is_active": True,
        "is_public": True,
        "tags": ["motion", "dismiss", "civil", "pleading"]
    }
]

# Sample workflow templates
SAMPLE_WORKFLOW_TEMPLATES = [
    {
        "name": "New Case Intake Workflow",
        "description": "Standard workflow for processing new case intake",
        "case_type": CaseType.CIVIL,
        "workflow_steps": [
            {
                "name": "Initial Client Consultation",
                "description": "Meet with client to discuss case details",
                "type": "meeting",
                "order": 1,
                "required": True,
                "estimated_hours": 2
            },
            {
                "name": "Conflict Check",
                "description": "Perform conflict of interest check",
                "type": "review",
                "order": 2,
                "required": True,
                "estimated_hours": 0.5
            },
            {
                "name": "Retainer Agreement",
                "description": "Prepare and execute retainer agreement",
                "type": "task",
                "order": 3,
                "required": True,
                "estimated_hours": 1
            },
            {
                "name": "Case File Setup",
                "description": "Create case file and initial documentation",
                "type": "task",
                "order": 4,
                "required": True,
                "estimated_hours": 1
            }
        ],
        "automation_rules": [
            {
                "name": "Welcome Email",
                "trigger": "case_created",
                "condition": "status == 'open'",
                "action": "send_email",
                "enabled": True
            },
            {
                "name": "Retainer Reminder",
                "trigger": "time_elapsed",
                "condition": "days_since_created > 3 && retainer_signed == false",
                "action": "send_notification",
                "enabled": True
            }
        ],
        "is_active": True,
        "is_public": True,
        "tags": ["intake", "new_case", "workflow", "automation"]
    }
]

def print_template_summary():
    """Print a summary of the sample templates"""
    print("🎯 Case Templates and Automation - Sample Data Summary")
    print("=" * 60)
    
    print(f"\n📋 Case Templates ({len(SAMPLE_CASE_TEMPLATES)}):")
    for template in SAMPLE_CASE_TEMPLATES:
        milestones = len(template["template_data"]["milestones"])
        deadlines = len(template["template_data"]["deadlines"])
        print(f"  • {template['name']}")
        print(f"    Type: {template['case_type']}")
        print(f"    Milestones: {milestones}, Deadlines: {deadlines}")
        print(f"    Tags: {', '.join(template['tags'])}")
    
    print(f"\n📄 Document Templates ({len(SAMPLE_DOCUMENT_TEMPLATES)}):")
    for template in SAMPLE_DOCUMENT_TEMPLATES:
        placeholders = len(template["placeholders"])
        print(f"  • {template['name']}")
        print(f"    Type: {template['document_type']} for {template['case_type']}")
        print(f"    Placeholders: {placeholders}")
        print(f"    Tags: {', '.join(template['tags'])}")
    
    print(f"\n⚙️ Workflow Templates ({len(SAMPLE_WORKFLOW_TEMPLATES)}):")
    for template in SAMPLE_WORKFLOW_TEMPLATES:
        steps = len(template["workflow_steps"])
        rules = len(template["automation_rules"])
        print(f"  • {template['name']}")
        print(f"    Type: {template['case_type']}")
        print(f"    Steps: {steps}, Automation Rules: {rules}")
        print(f"    Tags: {', '.join(template['tags'])}")
    
    print("\n✅ Sample data ready for testing!")
    print("💡 Use this data to test the templates API endpoints")

if __name__ == "__main__":
    print_template_summary()
