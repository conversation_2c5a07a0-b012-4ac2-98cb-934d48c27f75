from datetime import datetime, timedelta, timezone
from typing import Any

import jwt
from passlib.context import CryptContext

from app.core.config import settings
from app.models import UserRole

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# def create_access_token(subject: str | Any, expires_delta: timedelta) -> str:
#     expire = datetime.now(timezone.utc) + expires_delta
#     to_encode = {"exp": expire, "sub": str(subject)}
#     encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
#     return encoded_jwt


def create_access_token(
    subject: str | Any,
    expires_delta: timedelta,
    role: UserRole | None = None,
    additional_claims: dict[str, Any] | None = None,
) -> str:
    """
    Create a JWT token with optional role and additional claims.

    Args:
        subject: The subject (usually user email) to include in the token
        expires_delta: Time duration until token expires
        role: Optional UserRole to include in the token
        additional_claims: Any additional claims to include

    Returns:
        Encoded JWT token
    """
    expire = datetime.now(timezone.utc) + expires_delta
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        **({"role": role.value} if role else {}),
        **(additional_claims or {}),
    }
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against a hashed password.

    Args:
        plain_password: Password in plain text
        hashed_password: Hashed password to compare against

    Returns:
        bool: True if passwords match, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Generate a hashed version of the password.

    Args:
        password: Password in plain text

    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)


def validate_token(token: str) -> dict[str, Any]:
    """
    Validate and decode a JWT token.

    Args:
        token: JWT token to validate

    Returns:
        dict: Decoded token payload

    Raises:
        jwt.PyJWTError: If token is invalid
    """
    decoded_token: dict[str, Any] = jwt.decode(
        token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
    )
    return decoded_token
