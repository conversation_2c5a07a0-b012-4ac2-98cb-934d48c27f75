"""
Enhanced Notification Service

This module provides advanced services for creating and managing notifications
with multi-channel delivery, templates, and user preferences.
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from sqlmodel import Session, select, and_

from app.models import (
    Notification,
    NotificationCreate,
    NotificationType,
    NotificationPriority,
    NotificationChannel,
    NotificationPreferences,
    NotificationTemplate,
    NotificationRule,
    NotificationDelivery,
    NotificationDeliveryStatus,
    User,
    UserRole,
    LegalCase,
    CaseDocument,
    ClientMessage,
    CaseActivity,
    ActivityType,
)


class NotificationService:
    """Enhanced service for managing notifications with multi-channel delivery"""

    @staticmethod
    def create_notification(
        session: Session,
        title: str,
        message: str,
        notification_type: NotificationType,
        user_id: uuid.UUID,
        case_id: Optional[uuid.UUID] = None,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        notification_metadata: Optional[dict] = None,
        expires_at: Optional[datetime] = None,
        auto_send: bool = True,
        channels: Optional[List[NotificationChannel]] = None,
        template_id: Optional[uuid.UUID] = None
    ) -> Notification:
        """Create a new notification with optional multi-channel delivery"""

        notification = Notification(
            title=title,
            message=message,
            notification_type=notification_type,
            priority=priority,
            user_id=user_id,
            case_id=case_id,
            notification_metadata=notification_metadata or {},
            expires_at=expires_at
        )

        session.add(notification)
        session.commit()
        session.refresh(notification)

        # Auto-send through configured channels
        if auto_send:
            NotificationService.send_notification(
                session=session,
                notification=notification,
                channels=channels,
                template_id=template_id
            )

        return notification

    @staticmethod
    def send_notification(
        session: Session,
        notification: Notification,
        channels: Optional[List[NotificationChannel]] = None,
        template_id: Optional[uuid.UUID] = None
    ) -> List[NotificationDelivery]:
        """Send notification through specified channels"""

        deliveries = []

        # Get user preferences if no channels specified
        if not channels:
            channels = NotificationService._get_user_preferred_channels(
                session, notification.user_id, notification.notification_type
            )

        # Send through each channel
        for channel in channels:
            try:
                delivery = None

                if channel == NotificationChannel.EMAIL:
                    delivery = NotificationService._send_email_notification(
                        session, notification, template_id
                    )
                elif channel == NotificationChannel.IN_APP:
                    delivery = NotificationService._send_in_app_notification(
                        session, notification
                    )
                # Add other channels (SMS, Push, etc.) here

                if delivery:
                    deliveries.append(delivery)

            except Exception as e:
                # Log error and continue with other channels
                print(f"Failed to send notification via {channel}: {e}")

        return deliveries

    @staticmethod
    def _get_user_preferred_channels(
        session: Session,
        user_id: uuid.UUID,
        notification_type: NotificationType
    ) -> List[NotificationChannel]:
        """Get user's preferred channels for notification type"""

        preferences = session.exec(
            select(NotificationPreferences).where(
                and_(
                    NotificationPreferences.user_id == user_id,
                    NotificationPreferences.notification_type == notification_type
                )
            )
        ).first()

        if preferences and preferences.is_enabled:
            return preferences.channels

        # Default channels based on notification priority
        user = session.get(User, user_id)
        if not user:
            return [NotificationChannel.IN_APP]

        # Default channel preferences
        if user.role == UserRole.CLIENT:
            return [NotificationChannel.IN_APP, NotificationChannel.EMAIL]
        else:
            return [NotificationChannel.IN_APP]

    @staticmethod
    def _send_email_notification(
        session: Session,
        notification: Notification,
        template_id: Optional[uuid.UUID] = None
    ) -> Optional[NotificationDelivery]:
        """Send email notification"""

        try:
            from app.services.email_notification_service import EmailNotificationService

            email_service = EmailNotificationService(session)
            return email_service.send_notification_email(
                notification=notification,
                template_id=template_id
            )
        except Exception as e:
            print(f"Email notification failed: {e}")
            return None

    @staticmethod
    def _send_in_app_notification(
        session: Session,
        notification: Notification
    ) -> NotificationDelivery:
        """Send in-app notification (just create delivery record)"""

        user = session.get(User, notification.user_id)

        delivery = NotificationDelivery(
            notification_id=notification.id,
            channel=NotificationChannel.IN_APP,
            status=NotificationDeliveryStatus.DELIVERED,
            recipient_address=user.email if user else "unknown",
            sent_at=datetime.utcnow(),
            delivered_at=datetime.utcnow()
        )

        session.add(delivery)
        session.commit()
        session.refresh(delivery)

        return delivery
    
    @staticmethod
    def create_case_update_notification(
        session: Session,
        case: LegalCase,
        update_message: str,
        priority: NotificationPriority = NotificationPriority.MEDIUM
    ) -> List[Notification]:
        """Create notifications for case updates to relevant clients"""
        
        notifications = []
        
        # Find clients associated with this case
        # For now, we'll use a simple approach based on client_name
        # In a more sophisticated system, you'd have a proper client-case relationship
        if case.client_name:
            # Find users with matching names (clients)
            clients = session.exec(
                select(User).where(
                    and_(
                        User.role == UserRole.CLIENT,
                        User.full_name.ilike(f"%{case.client_name}%")
                    )
                )
            ).all()
            
            for client in clients:
                notification = NotificationService.create_notification(
                    session=session,
                    title=f"Case Update: {case.title}",
                    message=update_message,
                    notification_type=NotificationType.CASE_UPDATE,
                    user_id=client.id,
                    case_id=case.id,
                    priority=priority,
                    notification_metadata={
                        "case_title": case.title,
                        "case_type": case.case_type,
                        "case_status": case.status
                    }
                )
                notifications.append(notification)
        
        return notifications
    
    @staticmethod
    def create_document_shared_notification(
        session: Session,
        document: CaseDocument,
        case: LegalCase
    ) -> List[Notification]:
        """Create notifications when a document is shared with clients"""
        
        notifications = []
        
        if case.client_name:
            # Find clients associated with this case
            clients = session.exec(
                select(User).where(
                    and_(
                        User.role == UserRole.CLIENT,
                        User.full_name.ilike(f"%{case.client_name}%")
                    )
                )
            ).all()
            
            for client in clients:
                notification = NotificationService.create_notification(
                    session=session,
                    title=f"New Document Shared: {case.title}",
                    message=f"A new document '{document.original_filename}' has been shared with you for case: {case.title}",
                    notification_type=NotificationType.DOCUMENT_SHARED,
                    user_id=client.id,
                    case_id=case.id,
                    priority=NotificationPriority.MEDIUM,
                    notification_metadata={
                        "document_id": str(document.id),
                        "document_name": document.original_filename,
                        "document_category": document.category,
                        "case_title": case.title
                    }
                )
                notifications.append(notification)
        
        return notifications
    
    @staticmethod
    def create_status_change_notification(
        session: Session,
        case: LegalCase,
        old_status: str,
        new_status: str,
        changed_by: User
    ) -> List[Notification]:
        """Create notifications for case status changes"""
        
        notifications = []
        
        if case.client_name:
            # Find clients associated with this case
            clients = session.exec(
                select(User).where(
                    and_(
                        User.role == UserRole.CLIENT,
                        User.full_name.ilike(f"%{case.client_name}%")
                    )
                )
            ).all()
            
            status_message = f"Case status changed from '{old_status.replace('_', ' ').title()}' to '{new_status.replace('_', ' ').title()}'"
            
            for client in clients:
                notification = NotificationService.create_notification(
                    session=session,
                    title=f"Status Update: {case.title}",
                    message=status_message,
                    notification_type=NotificationType.STATUS_CHANGE,
                    user_id=client.id,
                    case_id=case.id,
                    priority=NotificationPriority.HIGH,
                    notification_metadata={
                        "old_status": old_status,
                        "new_status": new_status,
                        "changed_by": changed_by.full_name,
                        "case_title": case.title
                    }
                )
                notifications.append(notification)
        
        return notifications
    
    @staticmethod
    def create_system_announcement(
        session: Session,
        title: str,
        message: str,
        target_roles: List[UserRole] = None,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        expires_at: Optional[datetime] = None
    ) -> List[Notification]:
        """Create system-wide announcements for specific user roles"""
        
        if target_roles is None:
            target_roles = [UserRole.CLIENT]  # Default to clients only
        
        notifications = []
        
        # Get all users with target roles
        users = session.exec(
            select(User).where(
                and_(
                    User.role.in_(target_roles),
                    User.is_active == True
                )
            )
        ).all()
        
        for user in users:
            notification = NotificationService.create_notification(
                session=session,
                title=title,
                message=message,
                notification_type=NotificationType.SYSTEM_ANNOUNCEMENT,
                user_id=user.id,
                priority=priority,
                notification_metadata={
                    "target_roles": [role.value for role in target_roles],
                    "announcement": True
                },
                expires_at=expires_at
            )
            notifications.append(notification)
        
        return notifications
