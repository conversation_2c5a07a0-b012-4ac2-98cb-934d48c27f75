"""
Email Notification Service

This module provides advanced email notification services with templates,
delivery tracking, and user preferences management.
"""

import uuid
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from jinja2 import Template, Environment, BaseLoader

from sqlmodel import Session, select, and_

from app.models import (
    User,
    UserRole,
    Notification,
    NotificationTemplate,
    NotificationPreferences,
    NotificationDelivery,
    NotificationType,
    NotificationChannel,
    NotificationDeliveryStatus,
    NotificationPriority,
    LegalCase,
)
from app.utils import send_email
from app.core.config import settings


class EmailNotificationService:
    """Advanced email notification service with templates and tracking"""
    
    def __init__(self, session: Session):
        self.session = session
        self.jinja_env = Environment(loader=BaseLoader())
    
    def send_notification_email(
        self,
        notification: Notification,
        template_id: Optional[uuid.UUID] = None,
        custom_variables: Optional[Dict[str, Any]] = None
    ) -> NotificationDelivery:
        """Send an email notification using templates"""
        
        # Get user and check preferences
        user = self.session.get(User, notification.user_id)
        if not user or not user.email:
            raise ValueError("User not found or no email address")
        
        # Check if user wants email notifications for this type
        if not self._should_send_email(user, notification):
            delivery = NotificationDelivery(
                notification_id=notification.id,
                channel=NotificationChannel.EMAIL,
                status=NotificationDeliveryStatus.FAILED,
                recipient_address=user.email,
                error_message="User preferences disabled email for this notification type"
            )
            self.session.add(delivery)
            self.session.commit()
            return delivery
        
        # Get or create template
        template = self._get_template(notification.notification_type, template_id)
        
        # Prepare variables for template
        variables = self._prepare_template_variables(notification, user, custom_variables)
        
        # Render email content
        subject = self._render_template(template.subject_template, variables)
        body = self._render_template(template.body_template, variables)
        
        # Create delivery record
        delivery = NotificationDelivery(
            notification_id=notification.id,
            channel=NotificationChannel.EMAIL,
            status=NotificationDeliveryStatus.PENDING,
            recipient_address=user.email,
            delivery_metadata={
                "template_id": str(template.id),
                "variables": variables,
                "subject": subject
            }
        )
        self.session.add(delivery)
        self.session.commit()
        self.session.refresh(delivery)
        
        try:
            # Send email
            email_data = {
                "subject": subject,
                "email_to": user.email,
                "body": body,
            }
            
            # Add tracking parameters
            tracking_params = {
                "delivery_id": str(delivery.id),
                "notification_id": str(notification.id),
                "user_id": str(user.id)
            }
            
            # Add tracking links to body
            body_with_tracking = self._add_tracking_links(body, tracking_params)
            email_data["body"] = body_with_tracking
            
            # Send the email
            send_email(**email_data)
            
            # Update delivery status
            delivery.status = NotificationDeliveryStatus.SENT
            delivery.sent_at = datetime.utcnow()
            delivery.external_id = f"equinova-{delivery.id}"  # In real app, use provider ID
            
        except Exception as e:
            # Update delivery status on failure
            delivery.status = NotificationDeliveryStatus.FAILED
            delivery.failed_at = datetime.utcnow()
            delivery.error_message = str(e)
        
        self.session.add(delivery)
        self.session.commit()
        
        return delivery
    
    def _should_send_email(self, user: User, notification: Notification) -> bool:
        """Check if user preferences allow email for this notification type"""
        
        # Get user preferences for this notification type
        preferences = self.session.exec(
            select(NotificationPreferences).where(
                and_(
                    NotificationPreferences.user_id == user.id,
                    NotificationPreferences.notification_type == notification.notification_type
                )
            )
        ).first()
        
        if not preferences:
            # No preferences set, use defaults based on notification priority
            return notification.priority in [NotificationPriority.HIGH, NotificationPriority.URGENT]
        
        # Check if email channel is enabled
        if not preferences.is_enabled or NotificationChannel.EMAIL not in preferences.channels:
            return False
        
        # Check priority threshold
        priority_order = {
            NotificationPriority.LOW: 1,
            NotificationPriority.MEDIUM: 2,
            NotificationPriority.HIGH: 3,
            NotificationPriority.URGENT: 4
        }
        
        if priority_order.get(notification.priority, 0) < priority_order.get(preferences.priority_threshold, 0):
            return False
        
        # Check quiet hours
        if preferences.quiet_hours_start and preferences.quiet_hours_end:
            current_time = datetime.utcnow().time()
            start_time = datetime.strptime(preferences.quiet_hours_start, "%H:%M").time()
            end_time = datetime.strptime(preferences.quiet_hours_end, "%H:%M").time()
            
            if start_time <= current_time <= end_time:
                return False
        
        # Check frequency (for non-urgent notifications)
        if notification.priority != NotificationPriority.URGENT and preferences.frequency == "never":
            return False
        
        return True
    
    def _get_template(
        self,
        notification_type: NotificationType,
        template_id: Optional[uuid.UUID] = None
    ) -> NotificationTemplate:
        """Get notification template by ID or type"""
        
        if template_id:
            template = self.session.get(NotificationTemplate, template_id)
            if template and template.is_active:
                return template
        
        # Get default template for notification type
        template = self.session.exec(
            select(NotificationTemplate).where(
                and_(
                    NotificationTemplate.notification_type == notification_type,
                    NotificationTemplate.channel == NotificationChannel.EMAIL,
                    NotificationTemplate.is_active == True,
                    NotificationTemplate.is_system == True
                )
            )
        ).first()
        
        if template:
            return template
        
        # Create default template if none exists
        return self._create_default_template(notification_type)
    
    def _create_default_template(self, notification_type: NotificationType) -> NotificationTemplate:
        """Create a default email template for notification type"""
        
        templates = {
            NotificationType.CASE_UPDATE: {
                "subject": "Case Update: {{ case_title }}",
                "body": """
                <h2>Case Update Notification</h2>
                <p>Dear {{ user_name }},</p>
                <p>There has been an update to your case: <strong>{{ case_title }}</strong></p>
                <p><strong>Update:</strong> {{ notification_message }}</p>
                <p>Case Status: {{ case_status }}</p>
                <p>You can view more details in your client portal.</p>
                <p>Best regards,<br>{{ law_firm_name }}</p>
                """
            },
            NotificationType.DOCUMENT_SHARED: {
                "subject": "New Document Shared: {{ document_name }}",
                "body": """
                <h2>Document Shared</h2>
                <p>Dear {{ user_name }},</p>
                <p>A new document has been shared with you for case: <strong>{{ case_title }}</strong></p>
                <p><strong>Document:</strong> {{ document_name }}</p>
                <p>You can access this document in your client portal.</p>
                <p>Best regards,<br>{{ law_firm_name }}</p>
                """
            },
            NotificationType.STATUS_CHANGE: {
                "subject": "Case Status Update: {{ case_title }}",
                "body": """
                <h2>Case Status Change</h2>
                <p>Dear {{ user_name }},</p>
                <p>The status of your case <strong>{{ case_title }}</strong> has been updated.</p>
                <p><strong>Previous Status:</strong> {{ old_status }}</p>
                <p><strong>New Status:</strong> {{ new_status }}</p>
                <p>{{ notification_message }}</p>
                <p>Best regards,<br>{{ law_firm_name }}</p>
                """
            },
            NotificationType.DEADLINE_APPROACHING: {
                "subject": "Deadline Reminder: {{ case_title }}",
                "body": """
                <h2>Deadline Reminder</h2>
                <p>Dear {{ user_name }},</p>
                <p>This is a reminder about an upcoming deadline for your case: <strong>{{ case_title }}</strong></p>
                <p><strong>Deadline:</strong> {{ deadline_date }}</p>
                <p><strong>Description:</strong> {{ notification_message }}</p>
                <p>Please contact us if you have any questions.</p>
                <p>Best regards,<br>{{ law_firm_name }}</p>
                """
            }
        }
        
        template_data = templates.get(notification_type, {
            "subject": "Notification: {{ notification_title }}",
            "body": """
            <h2>{{ notification_title }}</h2>
            <p>Dear {{ user_name }},</p>
            <p>{{ notification_message }}</p>
            <p>Best regards,<br>{{ law_firm_name }}</p>
            """
        })
        
        # Create system admin user for template creation
        admin_user = self.session.exec(
            select(User).where(User.role == UserRole.ADMIN)
        ).first()
        
        if not admin_user:
            raise ValueError("No admin user found to create system templates")
        
        template = NotificationTemplate(
            name=f"Default {notification_type.value.replace('_', ' ').title()} Email",
            description=f"System default email template for {notification_type.value}",
            notification_type=notification_type,
            channel=NotificationChannel.EMAIL,
            subject_template=template_data["subject"],
            body_template=template_data["body"],
            variables=["user_name", "notification_title", "notification_message", "law_firm_name"],
            is_active=True,
            is_system=True,
            created_by=admin_user.id
        )
        
        self.session.add(template)
        self.session.commit()
        self.session.refresh(template)
        
        return template
    
    def _prepare_template_variables(
        self,
        notification: Notification,
        user: User,
        custom_variables: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Prepare variables for template rendering"""
        
        variables = {
            "user_name": user.full_name or user.email,
            "user_email": user.email,
            "notification_title": notification.title,
            "notification_message": notification.message,
            "notification_priority": notification.priority.value,
            "law_firm_name": getattr(settings, "PROJECT_NAME", "EquiNova Legal"),
            "current_date": datetime.utcnow().strftime("%B %d, %Y"),
            "current_time": datetime.utcnow().strftime("%I:%M %p"),
        }
        
        # Add case-specific variables if notification is case-related
        if notification.case_id:
            case = self.session.get(LegalCase, notification.case_id)
            if case:
                variables.update({
                    "case_title": case.title,
                    "case_status": case.status,
                    "case_type": case.case_type,
                    "client_name": case.client_name,
                })
        
        # Add notification metadata
        if notification.notification_metadata:
            variables.update(notification.notification_metadata)
        
        # Add custom variables
        if custom_variables:
            variables.update(custom_variables)
        
        return variables
    
    def _render_template(self, template_string: str, variables: Dict[str, Any]) -> str:
        """Render Jinja2 template with variables"""
        
        try:
            template = self.jinja_env.from_string(template_string)
            return template.render(**variables)
        except Exception as e:
            # Fallback to simple string replacement
            result = template_string
            for key, value in variables.items():
                result = result.replace(f"{{{{ {key} }}}}", str(value))
            return result
    
    def _add_tracking_links(self, body: str, tracking_params: Dict[str, str]) -> str:
        """Add tracking parameters to links in email body"""
        
        # Simple implementation - in production, use proper email tracking service
        tracking_pixel = f"""
        <img src="{settings.SERVER_HOST}/api/v1/notifications/track/open?{self._build_query_string(tracking_params)}" 
             width="1" height="1" style="display:none;" />
        """
        
        return body + tracking_pixel
    
    def _build_query_string(self, params: Dict[str, str]) -> str:
        """Build query string from parameters"""
        return "&".join([f"{k}={v}" for k, v in params.items()])
    
    def track_email_open(self, delivery_id: uuid.UUID) -> bool:
        """Track email open event"""
        
        delivery = self.session.get(NotificationDelivery, delivery_id)
        if delivery and not delivery.opened_at:
            delivery.opened_at = datetime.utcnow()
            if delivery.status == NotificationDeliveryStatus.SENT:
                delivery.status = NotificationDeliveryStatus.OPENED
            self.session.add(delivery)
            self.session.commit()
            return True
        
        return False
    
    def track_email_click(self, delivery_id: uuid.UUID, link_url: str) -> bool:
        """Track email click event"""
        
        delivery = self.session.get(NotificationDelivery, delivery_id)
        if delivery:
            delivery.clicked_at = datetime.utcnow()
            if delivery.status in [NotificationDeliveryStatus.SENT, NotificationDeliveryStatus.OPENED]:
                delivery.status = NotificationDeliveryStatus.CLICKED
            
            # Update metadata with click info
            if not delivery.delivery_metadata:
                delivery.delivery_metadata = {}
            
            if "clicks" not in delivery.delivery_metadata:
                delivery.delivery_metadata["clicks"] = []
            
            delivery.delivery_metadata["clicks"].append({
                "url": link_url,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            self.session.add(delivery)
            self.session.commit()
            return True
        
        return False
