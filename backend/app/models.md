```mermaid
erDiagram
    User {
        uuid id PK
        string email
        boolean is_active
        boolean is_superuser
        string full_name
        string role
        string bar_association_id
        string specialization
        string hashed_password
    }
    
    Item {
        uuid id PK
        string title
        string description
        uuid owner_id FK
    }
    
    LegalCase {
        uuid id PK
        string title
        string summary
        string client_name
        uuid lawyer_id FK
    }

    User ||--o{ Item : "has"
    User ||--o{ LegalCase : "handles"
    Item }o--|| User : "belongs to"
    LegalCase }o--|| User : "assigned to"
```