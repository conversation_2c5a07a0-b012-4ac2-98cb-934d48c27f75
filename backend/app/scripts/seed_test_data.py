#!/usr/bin/env python3
"""
Seed test data for EquiNova application.
Creates test users for different roles and sample legal cases.
"""

import logging
from datetime import date, timedelta
from sqlmodel import Session, select

from app.core.db import engine
from app.core.security import get_password_hash
from app.models import User, UserRole, LegalCase, CaseType, Item
import uuid

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test user credentials - SAVE THESE FOR TESTING!
TEST_USERS = {
    "admin": {
        "email": "<EMAIL>",
        "password": "AdminTest123!",
        "full_name": "System Administrator",
        "role": UserRole.ADMIN,
        "is_superuser": True,
    },
    "lawyer1": {
        "email": "<EMAIL>",
        "password": "LawyerTest123!",
        "full_name": "<PERSON>",
        "role": UserRole.LAWYER,
        "bar_association_id": "BAR-2024-001",
        "specialization": "Corporate Law",
    },
    "lawyer2": {
        "email": "<EMAIL>",
        "password": "LawyerTest123!",
        "full_name": "<PERSON>",
        "role": UserRole.LAWYER,
        "bar_association_id": "BAR-2024-002",
        "specialization": "Family Law",
    },
    "lawyer3": {
        "email": "<EMAIL>",
        "password": "LawyerTest123!",
        "full_name": "Sophie Laurent",
        "role": UserRole.LAWYER,
        "bar_association_id": "BAR-2024-003",
        "specialization": "Criminal Law",
    },
    "assistant1": {
        "email": "<EMAIL>",
        "password": "AssistantTest123!",
        "full_name": "Claire Moreau",
        "role": UserRole.ASSISTANT,
    },
    "assistant2": {
        "email": "<EMAIL>",
        "password": "AssistantTest123!",
        "full_name": "Pierre Leroy",
        "role": UserRole.ASSISTANT,
    },
    "client1": {
        "email": "<EMAIL>",
        "password": "ClientTest123!",
        "full_name": "Alice Johnson",
        "role": UserRole.CLIENT,
    },
    "client2": {
        "email": "<EMAIL>",
        "password": "ClientTest123!",
        "full_name": "Robert Smith",
        "role": UserRole.CLIENT,
    },
    "client3": {
        "email": "<EMAIL>",
        "password": "ClientTest123!",
        "full_name": "Emma Wilson",
        "role": UserRole.CLIENT,
    },
    "client4": {
        "email": "<EMAIL>",
        "password": "ClientTest123!",
        "full_name": "David Brown",
        "role": UserRole.CLIENT,
    },
}

def create_test_users(session: Session) -> dict[str, User]:
    """Create test users for all roles."""
    created_users = {}

    logger.info("Creating test users...")

    for user_key, user_data in TEST_USERS.items():
        # Check if user already exists
        existing_user = session.exec(
            select(User).where(User.email == user_data["email"])
        ).first()

        if existing_user:
            logger.info(f"User {user_data['email']} already exists, skipping...")
            created_users[user_key] = existing_user
            continue

        # Create new user
        user = User(
            id=uuid.uuid4(),
            email=user_data["email"],
            hashed_password=get_password_hash(user_data["password"]),
            full_name=user_data["full_name"],
            role=user_data["role"],
            is_superuser=user_data.get("is_superuser", False),
            bar_association_id=user_data.get("bar_association_id"),
            specialization=user_data.get("specialization"),
        )

        session.add(user)
        created_users[user_key] = user
        logger.info(f"Created user: {user_data['full_name']} ({user_data['email']})")

    session.commit()

    # Assign assistants and clients to lawyers
    logger.info("Assigning users to lawyers...")

    # Assign assistant1 and client1, client2 to lawyer1 (Marie Dubois)
    created_users["assistant1"].assigned_lawyer_id = created_users["lawyer1"].id
    created_users["client1"].assigned_lawyer_id = created_users["lawyer1"].id
    created_users["client2"].assigned_lawyer_id = created_users["lawyer1"].id

    # Assign assistant2 and client3, client4 to lawyer2 (Jean Martin)
    created_users["assistant2"].assigned_lawyer_id = created_users["lawyer2"].id
    created_users["client3"].assigned_lawyer_id = created_users["lawyer2"].id
    created_users["client4"].assigned_lawyer_id = created_users["lawyer2"].id

    session.commit()
    logger.info("User assignments completed")

    return created_users


def create_sample_legal_cases(session: Session, users: dict[str, User]) -> None:
    """Create sample legal cases for testing."""
    logger.info("Creating sample legal cases...")

    sample_cases = [
        {
            "title": "Corporate Merger - TechCorp & InnovateInc",
            "client_name": "TechCorp Solutions",
            "case_type": CaseType.CORPORATE,
            "lawyer": users["lawyer1"],
            "opening_date": date.today() - timedelta(days=30),
            "description": "Complex merger between two technology companies involving due diligence, regulatory compliance, and contract negotiations.",
        },
        {
            "title": "Employment Discrimination Case",
            "client_name": "Alice Johnson",
            "case_type": CaseType.EMPLOYMENT,
            "lawyer": users["lawyer1"],
            "opening_date": date.today() - timedelta(days=15),
            "description": "Workplace discrimination case involving wrongful termination and hostile work environment claims.",
        },
        {
            "title": "Divorce and Child Custody",
            "client_name": "Emma Wilson",
            "case_type": CaseType.FAMILY,
            "lawyer": users["lawyer2"],
            "opening_date": date.today() - timedelta(days=45),
            "description": "Contested divorce proceedings with child custody arrangements and asset division.",
        },
        {
            "title": "Personal Injury - Car Accident",
            "client_name": "Robert Smith",
            "case_type": CaseType.PERSONAL_INJURY,
            "lawyer": users["lawyer2"],
            "opening_date": date.today() - timedelta(days=20),
            "description": "Motor vehicle accident resulting in serious injuries, seeking compensation for medical expenses and lost wages.",
        },
        {
            "title": "Criminal Defense - Fraud Allegations",
            "client_name": "David Brown",
            "case_type": CaseType.CRIMINAL,
            "lawyer": users["lawyer3"],
            "opening_date": date.today() - timedelta(days=10),
            "description": "White-collar crime defense involving allegations of financial fraud and embezzlement.",
        },
        {
            "title": "Real Estate Transaction",
            "client_name": "Green Valley Properties",
            "case_type": CaseType.REAL_ESTATE,
            "lawyer": users["lawyer1"],
            "opening_date": date.today() - timedelta(days=5),
            "description": "Commercial real estate acquisition involving environmental assessments and zoning compliance.",
        },
        {
            "title": "Immigration Visa Application",
            "client_name": "International Family Services",
            "case_type": CaseType.IMMIGRATION,
            "lawyer": users["lawyer2"],
            "opening_date": date.today() - timedelta(days=60),
            "description": "Family reunification visa application with complex documentation requirements.",
        },
        {
            "title": "Intellectual Property Dispute",
            "client_name": "Innovation Labs Inc",
            "case_type": CaseType.INTELLECTUAL_PROPERTY,
            "lawyer": users["lawyer3"],
            "opening_date": date.today() - timedelta(days=25),
            "description": "Patent infringement case involving software algorithms and trade secret protection.",
        },
    ]

    for case_data in sample_cases:
        # Check if case already exists
        existing_case = session.exec(
            select(LegalCase).where(LegalCase.title == case_data["title"])
        ).first()

        if existing_case:
            logger.info(f"Case '{case_data['title']}' already exists, skipping...")
            continue

        legal_case = LegalCase(
            id=uuid.uuid4(),
            title=case_data["title"],
            client_name=case_data["client_name"],
            case_type=case_data["case_type"],
            lawyer_id=case_data["lawyer"].id,
            opening_date=case_data["opening_date"],
            description=case_data["description"],
        )

        session.add(legal_case)
        logger.info(f"Created case: {case_data['title']}")

    session.commit()
    logger.info("Sample legal cases created successfully")


def create_sample_items(session: Session, users: dict[str, User]) -> None:
    """Create sample items for testing."""
    logger.info("Creating sample items...")

    sample_items = [
        {
            "title": "Contract Template - Service Agreement",
            "description": "Standard service agreement template for corporate clients",
            "owner": users["lawyer1"],
        },
        {
            "title": "Legal Research - Employment Law Updates",
            "description": "Recent changes in employment law regulations and case precedents",
            "owner": users["assistant1"],
        },
        {
            "title": "Client Meeting Notes - TechCorp Merger",
            "description": "Detailed notes from client meeting regarding merger proceedings",
            "owner": users["assistant1"],
        },
        {
            "title": "Court Filing - Motion to Dismiss",
            "description": "Motion to dismiss filed in the discrimination case",
            "owner": users["lawyer1"],
        },
        {
            "title": "Family Law Precedent Research",
            "description": "Research on recent family law precedents for custody cases",
            "owner": users["assistant2"],
        },
        {
            "title": "Settlement Agreement Draft",
            "description": "Draft settlement agreement for personal injury case",
            "owner": users["lawyer2"],
        },
    ]

    for item_data in sample_items:
        # Check if item already exists
        existing_item = session.exec(
            select(Item).where(Item.title == item_data["title"])
        ).first()

        if existing_item:
            logger.info(f"Item '{item_data['title']}' already exists, skipping...")
            continue

        item = Item(
            id=uuid.uuid4(),
            title=item_data["title"],
            description=item_data["description"],
            owner_id=item_data["owner"].id,
        )

        session.add(item)
        logger.info(f"Created item: {item_data['title']}")

    session.commit()
    logger.info("Sample items created successfully")


def print_test_credentials():
    """Print test user credentials for easy reference."""
    print("\n" + "="*80)
    print("🔐 TEST USER CREDENTIALS FOR EQUINOVA")
    print("="*80)
    print("Save these credentials for testing different roles:")
    print()

    for user_key, user_data in TEST_USERS.items():
        role_emoji = {
            UserRole.ADMIN: "👑",
            UserRole.LAWYER: "⚖️",
            UserRole.ASSISTANT: "📋",
            UserRole.CLIENT: "👤"
        }

        print(f"{role_emoji.get(user_data['role'], '👤')} {user_data['role'].upper()}: {user_data['full_name']}")
        print(f"   Email: {user_data['email']}")
        print(f"   Password: {user_data['password']}")
        if user_data.get('specialization'):
            print(f"   Specialization: {user_data['specialization']}")
        print()

    print("="*80)
    print("🚀 You can now test the application with different user roles!")
    print("="*80)


def main():
    """Main function to seed all test data."""
    logger.info("Starting test data seeding...")

    with Session(engine) as session:
        # Create test users
        users = create_test_users(session)

        # Create sample legal cases
        create_sample_legal_cases(session, users)

        # Create sample items
        create_sample_items(session, users)

    # Print credentials for testing
    print_test_credentials()

    logger.info("Test data seeding completed successfully!")


if __name__ == "__main__":
    main()
