import uuid
from typing import Any

from sqlmodel import Session, select

from app.core.security import get_password_hash, verify_password
from app.models import (
    Item,
    ItemCreate,
    LegalCase,
    LegalCaseCreate,
    LegalCaseUpdate,
    User,
    UserCreate,
    UserRole,
    UserUpdate,
)


def create_user(*, session: Session, user_create: UserCreate) -> User:
    db_obj = User.model_validate(
        user_create,
        update={
            "hashed_password": get_password_hash(user_create.password),
            "role": user_create.role,
        },
    )
    session.add(db_obj)
    session.commit()
    session.refresh(db_obj)
    return db_obj


def update_user(*, session: Session, db_user: User, user_in: UserUpdate) -> Any:
    user_data = user_in.model_dump(exclude_unset=True)
    extra_data = {}
    if "password" in user_data:
        password = user_data["password"]
        hashed_password = get_password_hash(password)
        extra_data["hashed_password"] = hashed_password
    db_user.sqlmodel_update(user_data, update=extra_data)
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    return db_user


def get_user_by_email(*, session: Session, email: str) -> User | None:
    statement = select(User).where(User.email == email)
    session_user = session.exec(statement).first()
    return session_user


def authenticate(*, session: Session, email: str, password: str) -> User | None:
    db_user = get_user_by_email(session=session, email=email)
    if not db_user:
        return None
    if not verify_password(password, db_user.hashed_password):
        return None
    return db_user


def create_item(*, session: Session, item_in: ItemCreate, owner_id: uuid.UUID) -> Item:
    db_item = Item.model_validate(item_in, update={"owner_id": owner_id})
    session.add(db_item)
    session.commit()
    session.refresh(db_item)
    return db_item


# CRUD operations for LegalCase
class CRUDLegalCase:
    @staticmethod
    def create(*, session: Session, obj_in: LegalCaseCreate) -> LegalCase:
        db_obj = LegalCase.model_validate(obj_in)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj

    @staticmethod
    def get(*, session: Session, id: uuid.UUID) -> LegalCase | None:
        statement = select(LegalCase).where(LegalCase.id == id)
        return session.exec(statement).first()

    @staticmethod
    def update(
        *, session: Session, id: uuid.UUID, obj_in: LegalCaseUpdate
    ) -> LegalCase:
        db_obj = CRUDLegalCase.get(session=session, id=id)
        if not db_obj:
            raise ValueError("LegalCase not found")
        update_data = obj_in.model_dump(exclude_unset=True)
        db_obj.sqlmodel_update(update_data)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj

    @staticmethod
    def remove(*, session: Session, id: uuid.UUID) -> LegalCase:
        db_obj = CRUDLegalCase.get(session=session, id=id)
        if not db_obj:
            raise ValueError("LegalCase not found")
        session.delete(db_obj)
        session.commit()
        return db_obj


crud_legal_case = CRUDLegalCase()


# User assignment CRUD operations
def assign_user_to_lawyer(
    *, session: Session, user_id: uuid.UUID, lawyer_id: uuid.UUID
) -> User:
    """Assign a client or assistant to a lawyer"""
    # Get the user to be assigned
    user = session.get(User, user_id)
    if not user:
        raise ValueError("User not found")

    # Verify the assigned lawyer exists and is actually a lawyer
    lawyer = session.get(User, lawyer_id)
    if not lawyer:
        raise ValueError("Lawyer not found")
    if lawyer.role != UserRole.LAWYER:
        raise ValueError("Assigned user must be a lawyer")

    # Verify the user being assigned is a client or assistant
    if user.role not in [UserRole.CLIENT, UserRole.ASSISTANT]:
        raise ValueError("Only clients and assistants can be assigned to lawyers")

    # Update the assignment
    user.assigned_lawyer_id = lawyer_id
    session.add(user)
    session.commit()
    session.refresh(user)
    return user


def unassign_user_from_lawyer(*, session: Session, user_id: uuid.UUID) -> User:
    """Remove assignment of a client or assistant from their lawyer"""
    user = session.get(User, user_id)
    if not user:
        raise ValueError("User not found")

    user.assigned_lawyer_id = None
    session.add(user)
    session.commit()
    session.refresh(user)
    return user


def get_lawyer_clients(*, session: Session, lawyer_id: uuid.UUID) -> list[User]:
    """Get all clients assigned to a specific lawyer"""
    statement = select(User).where(
        User.assigned_lawyer_id == lawyer_id,
        User.role == UserRole.CLIENT
    )
    return list(session.exec(statement).all())


def get_lawyer_assistants(*, session: Session, lawyer_id: uuid.UUID) -> list[User]:
    """Get all assistants assigned to a specific lawyer"""
    statement = select(User).where(
        User.assigned_lawyer_id == lawyer_id,
        User.role == UserRole.ASSISTANT
    )
    return list(session.exec(statement).all())


def get_lawyer_assigned_users(*, session: Session, lawyer_id: uuid.UUID) -> list[User]:
    """Get all users (clients and assistants) assigned to a specific lawyer"""
    statement = select(User).where(User.assigned_lawyer_id == lawyer_id)
    return list(session.exec(statement).all())


def get_unassigned_users(*, session: Session) -> list[User]:
    """Get all clients and assistants that are not assigned to any lawyer"""
    statement = select(User).where(
        User.assigned_lawyer_id.is_(None),
        User.role.in_([UserRole.CLIENT, UserRole.ASSISTANT])
    )
    return list(session.exec(statement).all())
