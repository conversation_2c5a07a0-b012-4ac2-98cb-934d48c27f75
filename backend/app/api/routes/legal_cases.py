from datetime import date
from typing import Any, List, Annotated
from uuid import UUID

from pydantic import BaseModel

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import asc, desc
from sqlmodel import Session, func, or_, select

from app.api.deps import Lawyer<PERSON>coped<PERSON>ser, SessionDep, LegalAccessUser
from app.core.db import get_session
from app.crud import crud_legal_case
from app.models import (
    ActivityType,
    CaseActivity,
    CaseActivityPublic,
    CaseActivitiesPublic,
    CasePriority,
    CaseStatus,
    CaseStatusHistory,
    CaseStatusHistoryPublic,
    CaseStatusHistoriesPublic,
    CaseType,
    LegalCase,
    LegalCaseCreate,
    LegalCaseDetail,
    LegalCasePublic,
    LegalCasesPublic,
    LegalCaseUpdate,
    LegalCaseWithLawyer,
    StatusChangeRequest,
    User,
    UserPublic,
    UserRole,
)

router = APIRouter()


# Bulk update models
class BulkStatusUpdateRequest(BaseModel):
    """Request model for bulk status updates"""
    case_ids: List[str]
    new_status: str
    notes: str | None = None


class BulkUpdateResult(BaseModel):
    """Result model for bulk operations"""
    success_count: int
    failed_count: int
    failed_cases: List[dict]
    message: str


@router.post(
    "/",
    response_model=LegalCasePublic,
)
def create_legal_case(
    legal_case: LegalCaseCreate,
    current_user: LegalAccessUser,  # noqa: ARG001
    session: SessionDep
) -> LegalCasePublic:
    created = crud_legal_case.create(session=session, obj_in=legal_case)
    return LegalCasePublic.model_validate(created)


@router.get(
    "/",
    response_model=LegalCasesPublic,
)
def read_legal_cases(
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    search: str | None = Query(None, description="Search in title and client name"),
    case_type: CaseType | None = Query(None, description="Filter by case type"),
    status: str | None = Query(None, description="Filter by case status"),
    priority: str | None = Query(None, description="Filter by case priority"),
    lawyer_id: UUID | None = Query(None, description="Filter by assigned lawyer"),
    sort_by: str | None = Query(
        "opening_date",
        description="Sort by field (title, client_name, opening_date, case_type, status, priority)",
    ),
    sort_order: str | None = Query("desc", description="Sort order (asc, desc)"),
    date_from: date | None = Query(None, description="Filter cases from this date"),
    date_to: date | None = Query(None, description="Filter cases to this date"),
) -> LegalCasesPublic:
    """
    Retrieve legal cases with pagination, search, filtering, and sorting.
    Role-based access control:
    - Admins can see all cases
    - Lawyers can only see cases assigned to them
    - Clients and assistants can only see cases where they are the client (via assigned lawyer)
    """

    # Build the base query with lawyer information
    query = select(LegalCase, User).outerjoin(User, LegalCase.lawyer_id == User.id)  # type: ignore
    count_query = select(func.count()).select_from(LegalCase)

    # Apply role-based filtering
    if current_user.role == UserRole.LAWYER:
        # Lawyers can only see their own cases
        query = query.where(LegalCase.lawyer_id == current_user.id)
        count_query = count_query.where(LegalCase.lawyer_id == current_user.id)
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        # Clients and assistants can only see cases from their assigned lawyer
        if current_user.assigned_lawyer_id:
            query = query.where(LegalCase.lawyer_id == current_user.assigned_lawyer_id)
            count_query = count_query.where(LegalCase.lawyer_id == current_user.assigned_lawyer_id)
        else:
            # If not assigned to any lawyer, they can't see any cases
            query = query.where(False)  # This will return no results
            count_query = count_query.where(False)
    # Admins can see all cases (no additional filtering needed)

    # Apply search filter (case-insensitive)
    if search:
        search_filter = or_(
            LegalCase.title.ilike(f"%{search}%"),  # type: ignore
            LegalCase.client_name.ilike(f"%{search}%")  # type: ignore
        )
        query = query.where(search_filter)
        count_query = count_query.where(search_filter)

    # Apply case type filter
    if case_type:
        query = query.where(LegalCase.case_type == case_type)
        count_query = count_query.where(LegalCase.case_type == case_type)

    # Apply status filter
    if status:
        query = query.where(LegalCase.status == status)
        count_query = count_query.where(LegalCase.status == status)

    # Apply priority filter
    if priority:
        query = query.where(LegalCase.priority == priority)
        count_query = count_query.where(LegalCase.priority == priority)

    # Apply lawyer filter
    if lawyer_id:
        query = query.where(LegalCase.lawyer_id == lawyer_id)
        count_query = count_query.where(LegalCase.lawyer_id == lawyer_id)

    # Apply date range filters
    if date_from:
        query = query.where(LegalCase.opening_date >= date_from)
        count_query = count_query.where(LegalCase.opening_date >= date_from)

    if date_to:
        query = query.where(LegalCase.opening_date <= date_to)
        count_query = count_query.where(LegalCase.opening_date <= date_to)

    # Apply sorting
    if sort_by and hasattr(LegalCase, sort_by):
        sort_column = getattr(LegalCase, sort_by)
        if sort_order == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
    else:
        # Default sort by opening_date desc
        query = query.order_by(desc(LegalCase.opening_date))  # type: ignore

    # Apply pagination
    query = query.offset(skip).limit(limit)

    # Execute queries
    results = session.exec(query).all()
    count = session.exec(count_query).one()

    # Build the response with lawyer information
    legal_cases_with_lawyers = []
    for legal_case, lawyer in results:
        case_data = LegalCaseWithLawyer.model_validate(legal_case)
        case_data.lawyer = UserPublic.model_validate(lawyer) if lawyer else None
        legal_cases_with_lawyers.append(case_data)

    return LegalCasesPublic(data=legal_cases_with_lawyers, count=count)


@router.get(
    "/lawyers",
    response_model=list[UserPublic],
)
def get_available_lawyers(
    current_user: LegalAccessUser,  # noqa: ARG001
    session: SessionDep
) -> list[UserPublic]:
    """
    Get all users with LAWYER role for assignment to legal cases.
    """
    lawyers = session.exec(
        select(User).where(User.role == UserRole.LAWYER, User.is_active)
    ).all()
    return [UserPublic.model_validate(lawyer) for lawyer in lawyers]


@router.get(
    "/{legal_case_id}",
    response_model=LegalCaseDetail,
)
def read_legal_case(
    legal_case_id: UUID,
    current_user: LawyerScopedUser,
    session: SessionDep
) -> LegalCaseDetail:
    # Get case with lawyer information
    query = select(LegalCase, User).outerjoin(User, LegalCase.lawyer_id == User.id).where(LegalCase.id == legal_case_id)
    result = session.exec(query).first()

    if not result:
        raise HTTPException(status_code=404, detail="Legal case not found")

    legal_case, lawyer = result

    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or legal_case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    # Admins can access all cases

    # Get recent activities for this case
    activities_query = select(CaseActivity, User).outerjoin(
        User, CaseActivity.user_id == User.id
    ).where(CaseActivity.case_id == legal_case_id).order_by(desc(CaseActivity.created_at)).limit(10)

    activities_result = session.exec(activities_query).all()
    recent_activities = []

    for activity, activity_user in activities_result:
        activity_public = CaseActivityPublic.model_validate(activity)
        if activity_user:
            activity_public.user = UserPublic.model_validate(activity_user)
        recent_activities.append(activity_public)

    # Get status history for this case (last 10 records)
    status_history_query = select(CaseStatusHistory, User).outerjoin(
        User, CaseStatusHistory.changed_by == User.id
    ).where(CaseStatusHistory.case_id == legal_case_id).order_by(desc(CaseStatusHistory.changed_at)).limit(10)

    status_history_result = session.exec(status_history_query).all()
    status_history = []

    for history, history_user in status_history_result:
        history_public = CaseStatusHistoryPublic.model_validate(history)
        if history_user:
            history_public.changed_by_user = UserPublic.model_validate(history_user)
        status_history.append(history_public)

    # Build the detailed response
    case_detail = LegalCaseDetail.model_validate(legal_case)
    if lawyer:
        case_detail.lawyer = UserPublic.model_validate(lawyer)
    case_detail.recent_activities = recent_activities
    case_detail.status_history = status_history

    return case_detail


@router.put(
    "/{legal_case_id}",
    response_model=LegalCasePublic,
)
def update_legal_case(
    legal_case_id: UUID,
    legal_case_in: LegalCaseUpdate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> LegalCasePublic:
    legal_case = session.get(LegalCase, legal_case_id)
    if not legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control - only lawyers and admins can update cases
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        raise HTTPException(status_code=403, detail="Clients and assistants cannot update cases")
    # Admins can update all cases

    try:
        updated_case = crud_legal_case.update(
            session=session, id=legal_case_id, obj_in=legal_case_in
        )
    except ValueError:
        raise HTTPException(status_code=404, detail="Legal case not found")

    return LegalCasePublic.model_validate(updated_case)


@router.patch(
    "/{legal_case_id}",
    response_model=LegalCasePublic,
)
def partial_update_legal_case(
    legal_case_id: UUID,
    legal_case_in: LegalCaseUpdate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> LegalCasePublic:
    legal_case = session.get(LegalCase, legal_case_id)
    if not legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control - only lawyers and admins can update cases
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        raise HTTPException(status_code=403, detail="Clients and assistants cannot update cases")
    # Admins can update all cases

    updated = crud_legal_case.update(
        session=session, id=legal_case_id, obj_in=legal_case_in
    )
    return LegalCasePublic.model_validate(updated)


@router.delete(
    "/{legal_case_id}",
    response_model=dict,
)
def delete_legal_case(
    legal_case_id: UUID,
    current_user: LawyerScopedUser,
    session: SessionDep
) -> dict[str, Any]:
    db_legal_case = crud_legal_case.get(session=session, id=legal_case_id)
    if not db_legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control - only lawyers and admins can delete cases
    if current_user.role == UserRole.LAWYER and db_legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        raise HTTPException(status_code=403, detail="Clients and assistants cannot delete cases")
    # Admins can delete all cases

    crud_legal_case.remove(session=session, id=legal_case_id)
    return {"message": "Legal case deleted successfully"}


# BULK OPERATIONS - Must be defined BEFORE /{legal_case_id}/status to avoid route conflicts
@router.patch(
    "/bulk/status",
    response_model=BulkUpdateResult,
)
def bulk_update_case_status(
    bulk_request: BulkStatusUpdateRequest,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> BulkUpdateResult:
    """
    Update status for multiple cases at once.
    Only lawyers and admins can perform bulk status updates.
    Each case is validated individually for access control and status transitions.
    """



    # Only lawyers and admins can perform bulk updates
    if current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        raise HTTPException(status_code=403, detail="Clients and assistants cannot perform bulk status updates")

    if not bulk_request.case_ids:
        raise HTTPException(status_code=400, detail="No case IDs provided")

    if len(bulk_request.case_ids) > 50:  # Limit bulk operations
        raise HTTPException(status_code=400, detail="Cannot update more than 50 cases at once")

    # Validate that new_status is a valid status
    valid_statuses = ["open", "in_progress", "under_review", "closed", "archived"]
    if bulk_request.new_status not in valid_statuses:
        raise HTTPException(status_code=400, detail=f"Invalid status '{bulk_request.new_status}'. Valid statuses: {valid_statuses}")

    # Convert string IDs to UUIDs and validate them
    case_uuids = []
    for case_id_str in bulk_request.case_ids:
        try:
            case_uuid = UUID(case_id_str)
            case_uuids.append(case_uuid)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid UUID format: {case_id_str}")

    success_count = 0
    failed_count = 0
    failed_cases = []

    for case_id in case_uuids:
        try:
            # Get the case
            legal_case = session.get(LegalCase, case_id)
            if not legal_case:
                failed_cases.append({
                    "case_id": str(case_id),
                    "error": "Case not found"
                })
                failed_count += 1
                continue

            # Apply role-based access control
            if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
                failed_cases.append({
                    "case_id": str(case_id),
                    "error": "Access denied - not your case"
                })
                failed_count += 1
                continue

            # Store old status for validation and history
            old_status = legal_case.status
            new_status = bulk_request.new_status

            # Skip if status is the same
            if old_status == new_status:
                failed_cases.append({
                    "case_id": str(case_id),
                    "error": f"Case is already in {new_status} status"
                })
                failed_count += 1
                continue

            # Validate status transition
            if not CaseStatus.is_valid_transition(old_status, new_status, current_user.role.value):
                valid_transitions = CaseStatus.get_valid_transitions(old_status, current_user.role.value)
                failed_cases.append({
                    "case_id": str(case_id),
                    "error": f"Invalid transition from {old_status} to {new_status}. Valid: {valid_transitions}"
                })
                failed_count += 1
                continue

            # Update the status
            legal_case.status = new_status
            session.add(legal_case)

            # Create status history record
            status_history = CaseStatusHistory(
                case_id=case_id,
                old_status=old_status,
                new_status=new_status,
                changed_by=current_user.id,
                notes=bulk_request.notes
            )
            session.add(status_history)

            # Log the activity
            activity = CaseActivity(
                case_id=case_id,
                activity_type=ActivityType.STATUS_CHANGED,
                description=f"Status changed from {old_status} to {new_status} (bulk update)" +
                           (f" - {bulk_request.notes}" if bulk_request.notes else ""),
                user_id=current_user.id,
                activity_metadata={
                    "old_status": old_status,
                    "new_status": new_status,
                    "notes": bulk_request.notes,
                    "bulk_update": True
                }
            )
            session.add(activity)

            success_count += 1

        except Exception as e:
            failed_cases.append({
                "case_id": str(case_id),
                "error": f"Unexpected error: {str(e)}"
            })
            failed_count += 1

    # Commit all successful changes
    if success_count > 0:
        session.commit()

    # Build response message
    if failed_count == 0:
        message = f"Successfully updated {success_count} cases"
    elif success_count == 0:
        message = f"Failed to update all {failed_count} cases"
    else:
        message = f"Updated {success_count} cases, {failed_count} failed"

    return BulkUpdateResult(
        success_count=success_count,
        failed_count=failed_count,
        failed_cases=failed_cases,
        message=message
    )


@router.patch(
    "/{legal_case_id}/status",
    response_model=LegalCasePublic,
)
def update_case_status(
    legal_case_id: UUID,
    status_request: StatusChangeRequest,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> LegalCasePublic:
    """Update case status with enhanced tracking and history"""
    legal_case = session.get(LegalCase, legal_case_id)
    if not legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control - only lawyers and admins can update status
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        raise HTTPException(status_code=403, detail="Clients and assistants cannot update case status")
    # Admins can update all cases

    # Store old status for history tracking
    old_status = legal_case.status

    # Don't update if status is the same
    if old_status == status_request.new_status.value:
        raise HTTPException(status_code=400, detail="Case is already in this status")

    # Validate status transition
    if not CaseStatus.is_valid_transition(old_status, status_request.new_status.value, current_user.role.value):
        valid_transitions = CaseStatus.get_valid_transitions(old_status, current_user.role.value)
        raise HTTPException(
            status_code=400,
            detail=f"Invalid status transition from {old_status} to {status_request.new_status.value}. Valid transitions: {valid_transitions}"
        )

    # Update the status
    legal_case.status = status_request.new_status.value
    session.add(legal_case)

    # Create status history record
    status_history = CaseStatusHistory(
        case_id=legal_case_id,
        old_status=old_status,
        new_status=status_request.new_status.value,
        changed_by=current_user.id,
        notes=status_request.notes
    )
    session.add(status_history)

    # Log the activity
    activity = CaseActivity(
        case_id=legal_case_id,
        activity_type=ActivityType.STATUS_CHANGED,
        description=f"Status changed from {old_status} to {status_request.new_status.value}" +
                   (f" - {status_request.notes}" if status_request.notes else ""),
        user_id=current_user.id,
        activity_metadata={
            "old_status": old_status,
            "new_status": status_request.new_status.value,
            "notes": status_request.notes
        }
    )
    session.add(activity)

    session.commit()
    session.refresh(legal_case)

    return LegalCasePublic.model_validate(legal_case)


@router.get(
    "/{legal_case_id}/activities",
    response_model=CaseActivitiesPublic,
)
def read_case_activities(
    legal_case_id: UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of records to return"),
    activity_type: str | None = Query(None, description="Filter by activity type"),
    search: str | None = Query(None, description="Search in activity descriptions"),
    start_date: date | None = Query(None, description="Filter activities from this date"),
    end_date: date | None = Query(None, description="Filter activities until this date"),
) -> CaseActivitiesPublic:
    """Get activities for a specific case"""
    # Verify case exists and user has access
    legal_case = session.get(LegalCase, legal_case_id)
    if not legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or legal_case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    # Admins can access all cases

    # Build base query with filters
    base_conditions = [CaseActivity.case_id == legal_case_id]

    # Apply filters
    if activity_type:
        base_conditions.append(CaseActivity.activity_type == activity_type)
    if search:
        base_conditions.append(CaseActivity.description.contains(search))
    if start_date:
        base_conditions.append(CaseActivity.created_at >= start_date)
    if end_date:
        base_conditions.append(CaseActivity.created_at <= end_date)

    # Get activities with user information
    query = select(CaseActivity, User).outerjoin(
        User, CaseActivity.user_id == User.id
    ).where(*base_conditions).order_by(desc(CaseActivity.created_at)).offset(skip).limit(limit)

    count_query = select(func.count()).select_from(CaseActivity).where(*base_conditions)

    activities_result = session.exec(query).all()
    count = session.exec(count_query).one()

    activities = []
    for activity, activity_user in activities_result:
        activity_public = CaseActivityPublic.model_validate(activity)
        if activity_user:
            activity_public.user = UserPublic.model_validate(activity_user)
        activities.append(activity_public)

    return CaseActivitiesPublic(data=activities, count=count)


@router.get(
    "/{legal_case_id}/status-history",
    response_model=CaseStatusHistoriesPublic,
)
def read_case_status_history(
    legal_case_id: UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of records to return"),
) -> CaseStatusHistoriesPublic:
    """Get status change history for a specific case"""
    # Verify case exists and user has access
    legal_case = session.get(LegalCase, legal_case_id)
    if not legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or legal_case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    # Admins can access all cases

    # Get status history with user information
    query = select(CaseStatusHistory, User).outerjoin(
        User, CaseStatusHistory.changed_by == User.id
    ).where(CaseStatusHistory.case_id == legal_case_id).order_by(desc(CaseStatusHistory.changed_at)).offset(skip).limit(limit)

    count_query = select(func.count()).select_from(CaseStatusHistory).where(CaseStatusHistory.case_id == legal_case_id)

    history_result = session.exec(query).all()
    count = session.exec(count_query).one()

    history_records = []
    for history, history_user in history_result:
        history_public = CaseStatusHistoryPublic.model_validate(history)
        if history_user:
            history_public.changed_by_user = UserPublic.model_validate(history_user)
        history_records.append(history_public)

    return CaseStatusHistoriesPublic(data=history_records, count=count)


@router.patch(
    "/{legal_case_id}/priority",
    response_model=LegalCasePublic,
)
def update_case_priority(
    legal_case_id: UUID,
    priority: CasePriority,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> LegalCasePublic:
    """Update case priority and log the activity"""
    legal_case = session.get(LegalCase, legal_case_id)
    if not legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control - only lawyers and admins can update priority
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        raise HTTPException(status_code=403, detail="Clients and assistants cannot update case priority")
    # Admins can update all cases

    # Store old priority for activity logging
    old_priority = legal_case.priority

    # Don't update if priority is the same
    if old_priority == priority.value:
        raise HTTPException(status_code=400, detail="Case already has this priority")

    # Update the priority
    legal_case.priority = priority.value
    session.add(legal_case)

    # Log the activity
    activity = CaseActivity(
        case_id=legal_case_id,
        activity_type=ActivityType.CASE_UPDATED,
        description=f"Priority changed from {old_priority} to {priority.value}",
        user_id=current_user.id,
        activity_metadata={"old_priority": old_priority, "new_priority": priority.value}
    )
    session.add(activity)

    session.commit()
    session.refresh(legal_case)

    return LegalCasePublic.model_validate(legal_case)


@router.get(
    "/status-options",
    response_model=list[str],
)
def get_status_options(
    current_user: LegalAccessUser,  # noqa: ARG001
) -> list[str]:
    """Get all available case status options"""
    return [status.value for status in CaseStatus]


@router.get(
    "/priority-options",
    response_model=list[str],
)
def get_priority_options(
    current_user: LegalAccessUser,  # noqa: ARG001
) -> list[str]:
    """Get all available case priority options"""
    return [priority.value for priority in CasePriority]


@router.get(
    "/{legal_case_id}/valid-status-transitions",
    response_model=list[str],
)
def get_valid_status_transitions(
    legal_case_id: UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> list[str]:
    """Get valid status transitions for a specific case based on current status and user role"""
    legal_case = session.get(LegalCase, legal_case_id)
    if not legal_case:
        raise HTTPException(status_code=404, detail="Legal case not found")

    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and legal_case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or legal_case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    # Admins can access all cases

    return CaseStatus.get_valid_transitions(legal_case.status, current_user.role.value)



