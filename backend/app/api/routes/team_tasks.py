"""
Team Task Management API Routes

This module provides API endpoints for team task management,
including task creation, assignment, and progress tracking.
"""

import uuid
from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, or_, and_, desc

from app.api.deps import (
    CurrentUser,
    SessionDep,
    get_current_user,
)
from app.models import (
    # User and auth models
    User,
    UserPublic,
    
    # Case models
    LegalCase,
    
    # Team collaboration models
    CaseTeamMember,
    TeamMemberRole,
    
    # Task models
    TeamTask,
    TeamTaskCreate,
    TeamTaskUpdate,
    TeamTaskPublic,
    TeamTasksPublic,
    TaskStatus,
    TaskPriority,
    
    # Activity models
    CaseActivity,
    ActivityType,
)

router = APIRouter()


# Team Task Management
@router.get("/cases/{case_id}/tasks", response_model=TeamTasksPublic)
def get_case_tasks(
    case_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: TaskStatus | None = Query(None),
    priority: TaskPriority | None = Query(None),
    assigned_to: uuid.UUID | None = Query(None),
    search: str | None = Query(None),
) -> Any:
    """Get tasks for a specific case"""
    
    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check if user has access to this case
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            _is_team_member(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Build query
    query = select(TeamTask).where(TeamTask.case_id == case_id)
    
    # Apply filters
    if status:
        query = query.where(TeamTask.status == status)
    
    if priority:
        query = query.where(TeamTask.priority == priority)
    
    if assigned_to:
        query = query.where(TeamTask.assigned_to == assigned_to)
    
    if search:
        search_filter = f"%{search}%"
        query = query.where(
            or_(
                TeamTask.title.ilike(search_filter),
                TeamTask.description.ilike(search_filter)
            )
        )
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()
    
    # Apply pagination and ordering
    query = query.order_by(
        TeamTask.priority.desc(),  # High priority first
        TeamTask.due_date.asc(),   # Earliest due date first
        TeamTask.created_at.desc() # Newest first
    ).offset(skip).limit(limit)
    
    tasks = session.exec(query).all()
    
    # Convert to public format with user info
    task_data = []
    for task in tasks:
        creator = session.get(User, task.created_by)
        assignee = session.get(User, task.assigned_to) if task.assigned_to else None
        
        task_public = TeamTaskPublic.model_validate(task)
        task_public.creator = creator
        task_public.assignee = assignee
        task_public.case = case
        
        # Calculate if overdue
        if task.due_date and task.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
            task_public.is_overdue = datetime.utcnow() > task.due_date
        
        # Calculate progress percentage (simple implementation)
        if task.status == TaskStatus.COMPLETED:
            task_public.progress_percentage = 100.0
        elif task.status == TaskStatus.IN_PROGRESS:
            task_public.progress_percentage = 50.0
        elif task.status == TaskStatus.UNDER_REVIEW:
            task_public.progress_percentage = 80.0
        else:
            task_public.progress_percentage = 0.0
        
        task_data.append(task_public)
    
    return TeamTasksPublic(data=task_data, count=total_count)


@router.post("/cases/{case_id}/tasks", response_model=TeamTaskPublic)
def create_task(
    case_id: uuid.UUID,
    task_in: TeamTaskCreate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Create a new task for a case"""
    
    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check if user has access to this case
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            _is_team_member(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Verify assignee exists and is a team member (if specified)
    if task_in.assigned_to:
        assignee = session.get(User, task_in.assigned_to)
        if not assignee:
            raise HTTPException(status_code=404, detail="Assignee not found")
        
        # Check if assignee is a team member or case lawyer
        if not (assignee.id == case.lawyer_id or 
                _is_team_member(session, case_id, assignee.id)):
            raise HTTPException(status_code=400, detail="Assignee must be a team member")
    
    # Create task
    task = TeamTask.model_validate(
        task_in,
        update={
            "case_id": case_id,
            "created_by": current_user.id
        }
    )
    
    session.add(task)
    session.commit()
    session.refresh(task)
    
    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.MILESTONE_CREATED,  # Using milestone as closest match
        description=f"Created task: {task.title}",
        user_id=current_user.id,
        activity_metadata={
            "task_id": str(task.id),
            "task_title": task.title,
            "assigned_to": str(task.assigned_to) if task.assigned_to else None
        }
    )
    session.add(activity)
    session.commit()
    
    # Load related data for response
    creator = session.get(User, task.created_by)
    assignee = session.get(User, task.assigned_to) if task.assigned_to else None
    
    task_public = TeamTaskPublic.model_validate(task)
    task_public.creator = creator
    task_public.assignee = assignee
    task_public.case = case
    task_public.is_overdue = False
    task_public.progress_percentage = 0.0
    
    return task_public


@router.get("/cases/{case_id}/tasks/{task_id}", response_model=TeamTaskPublic)
def get_task(
    case_id: uuid.UUID,
    task_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Get a specific task"""
    
    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check if user has access to this case
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            _is_team_member(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get task
    task = session.get(TeamTask, task_id)
    if not task or task.case_id != case_id:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # Load related data
    creator = session.get(User, task.created_by)
    assignee = session.get(User, task.assigned_to) if task.assigned_to else None
    
    task_public = TeamTaskPublic.model_validate(task)
    task_public.creator = creator
    task_public.assignee = assignee
    task_public.case = case
    
    # Calculate if overdue
    if task.due_date and task.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
        task_public.is_overdue = datetime.utcnow() > task.due_date
    
    # Calculate progress percentage
    if task.status == TaskStatus.COMPLETED:
        task_public.progress_percentage = 100.0
    elif task.status == TaskStatus.IN_PROGRESS:
        task_public.progress_percentage = 50.0
    elif task.status == TaskStatus.UNDER_REVIEW:
        task_public.progress_percentage = 80.0
    else:
        task_public.progress_percentage = 0.0
    
    return task_public


@router.patch("/cases/{case_id}/tasks/{task_id}", response_model=TeamTaskPublic)
def update_task(
    case_id: uuid.UUID,
    task_id: uuid.UUID,
    task_update: TeamTaskUpdate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Update a task"""
    
    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Get task
    task = session.get(TeamTask, task_id)
    if not task or task.case_id != case_id:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # Check permissions (creator, assignee, case lawyer, or admin can update)
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            task.created_by == current_user.id or
            task.assigned_to == current_user.id or
            _is_team_member(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    # Verify new assignee exists and is a team member (if specified)
    if task_update.assigned_to:
        assignee = session.get(User, task_update.assigned_to)
        if not assignee:
            raise HTTPException(status_code=404, detail="Assignee not found")
        
        if not (assignee.id == case.lawyer_id or 
                _is_team_member(session, case_id, assignee.id)):
            raise HTTPException(status_code=400, detail="Assignee must be a team member")
    
    # Track status change for completion date
    old_status = task.status
    
    # Update fields
    update_data = task_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(task, field, value)
    
    # Set completion date if status changed to completed
    if old_status != TaskStatus.COMPLETED and task.status == TaskStatus.COMPLETED:
        task.completed_at = datetime.utcnow()
    elif task.status != TaskStatus.COMPLETED:
        task.completed_at = None
    
    task.updated_at = datetime.utcnow()
    
    session.add(task)
    session.commit()
    session.refresh(task)
    
    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.MILESTONE_UPDATED,
        description=f"Updated task: {task.title}",
        user_id=current_user.id,
        activity_metadata={
            "task_id": str(task.id),
            "task_title": task.title,
            "updated_fields": list(update_data.keys()),
            "old_status": old_status,
            "new_status": task.status
        }
    )
    session.add(activity)
    session.commit()
    
    # Load related data for response
    creator = session.get(User, task.created_by)
    assignee = session.get(User, task.assigned_to) if task.assigned_to else None
    
    task_public = TeamTaskPublic.model_validate(task)
    task_public.creator = creator
    task_public.assignee = assignee
    task_public.case = case
    
    # Calculate if overdue
    if task.due_date and task.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
        task_public.is_overdue = datetime.utcnow() > task.due_date
    
    # Calculate progress percentage
    if task.status == TaskStatus.COMPLETED:
        task_public.progress_percentage = 100.0
    elif task.status == TaskStatus.IN_PROGRESS:
        task_public.progress_percentage = 50.0
    elif task.status == TaskStatus.UNDER_REVIEW:
        task_public.progress_percentage = 80.0
    else:
        task_public.progress_percentage = 0.0
    
    return task_public


@router.delete("/cases/{case_id}/tasks/{task_id}")
def delete_task(
    case_id: uuid.UUID,
    task_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Delete a task"""
    
    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Get task
    task = session.get(TeamTask, task_id)
    if not task or task.case_id != case_id:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # Check permissions (creator, case lawyer, or admin can delete)
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            task.created_by == current_user.id):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    # Log activity before deletion
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.MILESTONE_DELETED,
        description=f"Deleted task: {task.title}",
        user_id=current_user.id,
        activity_metadata={
            "task_id": str(task.id),
            "task_title": task.title
        }
    )
    session.add(activity)
    
    # Delete task
    session.delete(task)
    session.commit()
    
    return {"message": "Task deleted successfully"}


# Helper function
def _is_team_member(session: Session, case_id: uuid.UUID, user_id: uuid.UUID) -> bool:
    """Check if user is a team member of the case"""
    team_member = session.exec(
        select(CaseTeamMember).where(
            and_(
                CaseTeamMember.case_id == case_id,
                CaseTeamMember.user_id == user_id,
                CaseTeamMember.is_active == True
            )
        )
    ).first()
    return team_member is not None
