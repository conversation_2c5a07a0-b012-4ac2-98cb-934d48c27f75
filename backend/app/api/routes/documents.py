import uuid
from pathlib import Path
from typing import Any, Annotated

from fastapi import APIRouter, File, HTTPException, UploadFile

from app.api.deps import CurrentUser, SessionDep
from app.models import Message

router = APIRouter(prefix="/documents", tags=["documents"])

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

ALLOWED_CONTENT_TYPES = [
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
]

MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB


@router.post("/upload/")
async def upload_document(
    current_user: CurrentUser, file: UploadFile = File(...)
) -> Any:
    """
    Upload a PDF or DOCX document.
    """
    # Validate file type
    if file.content_type not in ALLOWED_CONTENT_TYPES:
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only PDF and DOCX files are allowed.",
        )

    # Read file content to check size
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="File size exceeds 50MB limit.")

    # Generate unique filename
    file_extension = Path(file.filename).suffix if file.filename else ""
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename

    try:
        # Save file to disk
        with open(file_path, "wb") as f:
            f.write(content)

        return {
            "message": "File uploaded successfully",
            "filename": file.filename,
            "file_id": unique_filename,
            "content_type": file.content_type,
            "size": len(content),
            "uploaded_by": current_user.email,
        }

    except Exception:
        # Clean up file if it was created
        if file_path.exists():
            file_path.unlink()
        raise HTTPException(status_code=500, detail="Failed to save file")


@router.get("/health/")
async def documents_health_check() -> Message:
    """
    Health check for documents service.
    """
    return Message(message="Documents service is healthy")
