import uuid
from pathlib import Path
from typing import Any, Annotated
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Query
from fastapi.responses import FileResponse
from sqlmodel import Session, select, func, desc, asc

from app.api.deps import LawyerScopedUser, SessionDep
from app.core.db import get_session
from app.models import (
    CaseDocument,
    CaseDocumentCreate,
    CaseDocumentUpdate,
    CaseDocumentPublic,
    CaseDocumentsPublic,
    CaseFolder,
    LegalCase,
    User,
    UserRole,
    DocumentCategory,
    ActivityType,
    CaseActivity,
)

router = APIRouter()

# File storage configuration
UPLOAD_DIR = Path("uploads/case_documents")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

ALLOWED_CONTENT_TYPES = [
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "image/jpeg",
    "image/png",
    "image/gif",
    "text/plain",
]

MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB


def get_folder_path(session: Session, folder_id: uuid.UUID | None) -> list[str]:
    """Get the full path of a folder as a list of folder names"""
    if not folder_id:
        return []

    path = []
    current_folder_id = folder_id

    while current_folder_id:
        folder = session.get(CaseFolder, current_folder_id)
        if not folder:
            break
        path.insert(0, folder.name)
        current_folder_id = folder.parent_folder_id

    return path


def check_document_access(document: CaseDocument, user: LawyerScopedUser) -> bool:
    """Check if user has access to view a document based on permissions"""
    if user.role == UserRole.ADMIN:
        return True
    
    # Uploader can always access their documents
    if document.uploaded_by == user.id:
        return True
    
    # Check confidentiality and client sharing
    if document.is_confidential and user.role == UserRole.CLIENT:
        return False
    
    if user.role == UserRole.CLIENT and not document.is_shared_with_client:
        return False
    
    # Lawyers and assistants can access non-confidential documents
    if user.role in [UserRole.LAWYER, UserRole.ASSISTANT]:
        return True
    
    return False


def check_document_edit_access(document: CaseDocument, user: LawyerScopedUser) -> bool:
    """Check if user can edit/delete a document"""
    if user.role == UserRole.ADMIN:
        return True
    
    # Uploader can edit their documents
    if document.uploaded_by == user.id:
        return True
    
    # Lawyers can edit documents in their cases
    if user.role == UserRole.LAWYER:
        return True
    
    return False


@router.get("/{case_id}/documents")
def get_case_documents(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    folder_id: uuid.UUID | None = Query(None),
    category: DocumentCategory | None = Query(None),
    search: str | None = Query(None),
    sort_by: str = Query("uploaded_at", regex="^(uploaded_at|filename|file_size|category)$"),
    sort_order: str = Query("desc", regex="^(asc|desc)$"),
) -> CaseDocumentsPublic:
    """Get documents for a specific case with filtering and pagination"""
    
    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Apply role-based access control for case
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Build documents query
    query = select(CaseDocument, User).join(User, CaseDocument.uploaded_by == User.id).where(
        CaseDocument.case_id == case_id
    )
    
    # Apply document access filtering based on user role
    if current_user.role == UserRole.CLIENT:
        # Clients can only see documents shared with them and non-confidential
        query = query.where(
            (CaseDocument.is_shared_with_client == True) &
            (CaseDocument.is_confidential == False)
        )
    elif current_user.role == UserRole.ASSISTANT:
        # Assistants can see non-confidential documents and their own uploads
        query = query.where(
            (CaseDocument.is_confidential == False) |
            (CaseDocument.uploaded_by == current_user.id)
        )
    # Lawyers and admins can see all documents (no additional filtering)
    
    # Apply additional filters
    if folder_id is not None:
        query = query.where(CaseDocument.folder_id == folder_id)
    if category:
        query = query.where(CaseDocument.category == category.value)
    if search:
        query = query.where(
            (CaseDocument.original_filename.contains(search)) |
            (CaseDocument.description.contains(search))
        )
    
    # Apply sorting
    sort_column = getattr(CaseDocument, sort_by)
    if sort_order == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    # Get total count
    count_query = select(func.count()).select_from(
        query.subquery()
    )
    total_count = session.exec(count_query).one()
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    results = session.exec(query).all()
    
    # Build response
    documents = []
    for document, uploader in results:
        if check_document_access(document, current_user):
            document_public = CaseDocumentPublic(
                **document.model_dump(),
                uploader=uploader,
                folder_path=get_folder_path(session, document.folder_id),
                can_edit=check_document_edit_access(document, current_user),
                can_delete=check_document_edit_access(document, current_user),
                can_download=check_document_access(document, current_user),
                download_url=f"/api/v1/legal-cases/{case_id}/documents/{document.id}/download",
            )
            documents.append(document_public)
    
    return CaseDocumentsPublic(data=documents, count=total_count)


@router.post("/{case_id}/documents/upload")
async def upload_case_document(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
    file: UploadFile = File(...),
    folder_id: uuid.UUID | None = Query(None),
    category: DocumentCategory = Query(DocumentCategory.OTHER),
    description: str | None = Query(None),
    tags: str | None = Query(None),  # Comma-separated tags
    is_confidential: bool = Query(False),
    is_shared_with_client: bool = Query(False),
) -> CaseDocumentPublic:
    """Upload a document to a specific case"""
    
    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Validate folder if specified
    if folder_id:
        from app.models import CaseFolder
        folder = session.get(CaseFolder, folder_id)
        if not folder or folder.case_id != case_id:
            raise HTTPException(status_code=404, detail="Folder not found")
    
    # Validate file type
    if file.content_type not in ALLOWED_CONTENT_TYPES:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid file type. Allowed types: {', '.join(ALLOWED_CONTENT_TYPES)}",
        )
    
    # Read file content to check size
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="File size exceeds 50MB limit.")
    
    # Generate unique filename
    file_extension = Path(file.filename).suffix if file.filename else ""
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    case_dir = UPLOAD_DIR / str(case_id)
    case_dir.mkdir(exist_ok=True)
    file_path = case_dir / unique_filename
    
    try:
        # Save file to disk
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Parse tags
        parsed_tags = None
        if tags:
            parsed_tags = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        # Create document record
        document = CaseDocument(
            case_id=case_id,
            folder_id=folder_id,
            filename=unique_filename,
            original_filename=file.filename or "unknown",
            file_path=str(file_path),
            content_type=file.content_type or "application/octet-stream",
            file_size=len(content),
            category=category.value,
            description=description,
            tags=parsed_tags,
            is_confidential=is_confidential,
            is_shared_with_client=is_shared_with_client,
            uploaded_by=current_user.id,
        )
        
        session.add(document)
        session.commit()
        session.refresh(document)
        
        # Create activity log
        activity = CaseActivity(
            case_id=case_id,
            activity_type=ActivityType.DOCUMENT_UPLOADED.value,
            description=f"Uploaded document: {file.filename}",
            user_id=current_user.id,
            activity_metadata={
                "document_id": str(document.id),
                "filename": file.filename,
                "category": category.value,
                "file_size": len(content),
            }
        )
        session.add(activity)
        session.commit()
        
        # Get uploader info for response
        uploader = session.get(User, current_user.id)
        
        return CaseDocumentPublic(
            **document.model_dump(),
            uploader=uploader,
            can_edit=True,
            can_delete=True,
            can_download=True,
            download_url=f"/api/v1/legal-cases/{case_id}/documents/{document.id}/download",
        )
        
    except Exception as e:
        # Clean up file if it was created
        if file_path.exists():
            file_path.unlink()
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")


@router.put("/{case_id}/documents/{document_id}")
def update_case_document(
    case_id: uuid.UUID,
    document_id: uuid.UUID,
    document_data: CaseDocumentUpdate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> CaseDocumentPublic:
    """Update document metadata"""
    
    # Get document
    document = session.get(CaseDocument, document_id)
    if not document or document.case_id != case_id:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check edit permissions
    if not check_document_edit_access(document, current_user):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Validate folder if changing
    if document_data.folder_id is not None:
        if document_data.folder_id:
            from app.models import CaseFolder
            folder = session.get(CaseFolder, document_data.folder_id)
            if not folder or folder.case_id != case_id:
                raise HTTPException(status_code=404, detail="Folder not found")

    # Update document fields
    if document_data.folder_id is not None:
        document.folder_id = document_data.folder_id
    if document_data.category is not None:
        document.category = document_data.category.value
    if document_data.description is not None:
        document.description = document_data.description
    if document_data.tags is not None:
        document.tags = document_data.tags
    if document_data.is_confidential is not None:
        document.is_confidential = document_data.is_confidential
    if document_data.is_shared_with_client is not None:
        document.is_shared_with_client = document_data.is_shared_with_client
    
    document.updated_at = datetime.utcnow()
    
    session.add(document)
    session.commit()
    session.refresh(document)
    
    # Get uploader info
    uploader = session.get(User, document.uploaded_by)
    
    return CaseDocumentPublic(
        **document.model_dump(),
        uploader=uploader,
        can_edit=check_document_edit_access(document, current_user),
        can_delete=check_document_edit_access(document, current_user),
        can_download=check_document_access(document, current_user),
        download_url=f"/api/v1/legal-cases/{case_id}/documents/{document.id}/download",
    )


@router.get("/{case_id}/documents/{document_id}/download")
def download_case_document(
    case_id: uuid.UUID,
    document_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> FileResponse:
    """Download a case document"""

    # Get document
    document = session.get(CaseDocument, document_id)
    if not document or document.case_id != case_id:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check download permissions
    if not check_document_access(document, current_user):
        raise HTTPException(status_code=403, detail="Access denied")

    # Check if file exists
    file_path = Path(document.file_path)
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found on disk")

    # Log download activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.DOCUMENT_DOWNLOADED.value,
        description=f"Downloaded document: {document.original_filename}",
        user_id=current_user.id,
        activity_metadata={
            "document_id": str(document.id),
            "filename": document.original_filename,
        }
    )
    session.add(activity)
    session.commit()

    return FileResponse(
        path=file_path,
        filename=document.original_filename,
        media_type=document.content_type,
    )


@router.delete("/{case_id}/documents/{document_id}")
def delete_case_document(
    case_id: uuid.UUID,
    document_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> dict[str, Any]:
    """Delete a case document"""

    # Get document
    document = session.get(CaseDocument, document_id)
    if not document or document.case_id != case_id:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check delete permissions
    if not check_document_edit_access(document, current_user):
        raise HTTPException(status_code=403, detail="Access denied")

    # Delete file from disk
    file_path = Path(document.file_path)
    if file_path.exists():
        try:
            file_path.unlink()
        except Exception:
            # Log error but continue with database deletion
            pass

    # Delete from database
    session.delete(document)
    session.commit()

    # Log deletion activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.DOCUMENT_DELETED.value,
        description=f"Deleted document: {document.original_filename}",
        user_id=current_user.id,
        activity_metadata={
            "document_id": str(document.id),
            "filename": document.original_filename,
        }
    )
    session.add(activity)
    session.commit()

    return {"message": "Document deleted successfully"}


@router.get("/{case_id}/documents/categories")
def get_document_categories() -> dict[str, Any]:
    """Get available document categories"""
    categories = {}
    for category in DocumentCategory:
        categories[category.value] = {
            "label": category.value.replace("_", " ").title(),
            "value": category.value,
        }

    return {"categories": categories}


@router.get("/{case_id}/documents/stats")
def get_case_document_stats(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> dict[str, Any]:
    """Get document statistics for a case"""

    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Get document statistics
    base_query = select(CaseDocument).where(CaseDocument.case_id == case_id)

    # Apply access filtering
    if current_user.role == UserRole.CLIENT:
        base_query = base_query.where(
            (CaseDocument.is_shared_with_client == True) &
            (CaseDocument.is_confidential == False)
        )
    elif current_user.role == UserRole.ASSISTANT:
        base_query = base_query.where(
            (CaseDocument.is_confidential == False) |
            (CaseDocument.uploaded_by == current_user.id)
        )

    documents = session.exec(base_query).all()

    # Calculate statistics
    total_count = len(documents)
    total_size = sum(doc.file_size for doc in documents)

    # Group by category
    category_stats = {}
    for doc in documents:
        category = doc.category
        if category not in category_stats:
            category_stats[category] = {"count": 0, "size": 0}
        category_stats[category]["count"] += 1
        category_stats[category]["size"] += doc.file_size

    # Recent uploads (last 7 days)
    from datetime import timedelta
    recent_cutoff = datetime.utcnow() - timedelta(days=7)
    recent_count = len([doc for doc in documents if doc.uploaded_at >= recent_cutoff])

    return {
        "total_documents": total_count,
        "total_size_bytes": total_size,
        "total_size_mb": round(total_size / (1024 * 1024), 2),
        "recent_uploads": recent_count,
        "categories": category_stats,
    }
