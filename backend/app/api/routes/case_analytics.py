import uuid
from datetime import datetime, timedelta, date
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, and_, or_, desc, asc

from app.api.deps import LawyerScopedUser
from app.core.db import get_session
from app.models import (
    LegalCase,
    User,
    UserPublic,
    CaseActivity,
    CaseDocument,
    CaseMilestone,
    CaseDeadline,
    CaseNote,
    DocumentCaseLink,
    ActivityType,
    UserRole,
    CaseStatus,
    CasePriority,
)

router = APIRouter()


@router.get("/{case_id}/analytics")
def get_case_analytics(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    period_days: int = Query(30, ge=1, le=365, description="Analysis period in days"),
    include_comparisons: bool = Query(True, description="Include comparative analytics"),
) -> Dict[str, Any]:
    """Get comprehensive analytics for a specific case"""
    
    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check access permissions
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Calculate analysis period
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=period_days)
    
    # === CASE OVERVIEW ===
    case_overview = get_case_overview(session, case, start_date, end_date)
    
    # === ACTIVITY ANALYTICS ===
    activity_analytics = get_activity_analytics(session, case_id, start_date, end_date)
    
    # === DOCUMENT ANALYTICS ===
    document_analytics = get_document_analytics(session, case_id, start_date, end_date)
    
    # === PROGRESS ANALYTICS ===
    progress_analytics = get_progress_analytics(session, case_id, start_date, end_date)
    
    # === COLLABORATION ANALYTICS ===
    collaboration_analytics = get_collaboration_analytics(session, case_id, start_date, end_date)
    
    # === PERFORMANCE METRICS ===
    performance_metrics = get_performance_metrics(session, case, start_date, end_date)
    
    # === TIME ANALYTICS ===
    time_analytics = get_time_analytics(session, case, start_date, end_date)
    
    # === COMPARATIVE ANALYTICS ===
    comparative_analytics = {}
    if include_comparisons:
        comparative_analytics = get_comparative_analytics(session, case, current_user)
    
    # === HEALTH INDICATORS ===
    health_indicators = calculate_case_health(session, case, case_overview, progress_analytics, activity_analytics)
    
    return {
        "case_id": case_id,
        "analysis_period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "days": period_days
        },
        "case_overview": case_overview,
        "activity_analytics": activity_analytics,
        "document_analytics": document_analytics,
        "progress_analytics": progress_analytics,
        "collaboration_analytics": collaboration_analytics,
        "performance_metrics": performance_metrics,
        "time_analytics": time_analytics,
        "health_indicators": health_indicators,
        "comparative_analytics": comparative_analytics,
        "generated_at": datetime.utcnow().isoformat(),
        "generated_by": {
            "user_id": current_user.id,
            "user_name": current_user.full_name,
            "user_role": current_user.role
        }
    }


def get_case_overview(session: Session, case: LegalCase, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """Get basic case overview and metadata"""
    
    # Calculate case age
    case_age_days = (datetime.utcnow().date() - case.opening_date).days
    
    # Get lawyer information
    lawyer = session.get(User, case.lawyer_id)
    
    # Get total counts
    total_activities = session.exec(
        select(func.count()).select_from(CaseActivity).where(CaseActivity.case_id == case.id)
    ).one()
    
    total_documents = session.exec(
        select(func.count()).select_from(CaseDocument).where(CaseDocument.case_id == case.id)
    ).one()
    
    total_notes = session.exec(
        select(func.count()).select_from(CaseNote).where(CaseNote.case_id == case.id)
    ).one()
    
    # Get recent activity count
    recent_activities = session.exec(
        select(func.count()).select_from(CaseActivity).where(
            and_(
                CaseActivity.case_id == case.id,
                CaseActivity.created_at >= start_date,
                CaseActivity.created_at <= end_date
            )
        )
    ).one()
    
    return {
        "case_id": str(case.id),
        "title": case.title,
        "client_name": case.client_name,
        "case_type": case.case_type,
        "status": case.status,
        "priority": case.priority,
        "opening_date": case.opening_date.isoformat(),
        "case_age_days": case_age_days,
        "lawyer": {
            "id": str(lawyer.id),
            "name": lawyer.full_name,
            "email": lawyer.email
        } if lawyer else None,
        "totals": {
            "activities": total_activities,
            "documents": total_documents,
            "notes": total_notes,
            "recent_activities": recent_activities
        }
    }


def get_activity_analytics(session: Session, case_id: uuid.UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """Get detailed activity analytics"""
    
    # Activity by type
    activity_by_type = session.exec(
        select(CaseActivity.activity_type, func.count()).where(
            and_(
                CaseActivity.case_id == case_id,
                CaseActivity.created_at >= start_date,
                CaseActivity.created_at <= end_date
            )
        ).group_by(CaseActivity.activity_type)
    ).all()
    
    activity_types = {activity_type: count for activity_type, count in activity_by_type}
    
    # Activity by user
    activity_by_user = session.exec(
        select(User.full_name, User.id, func.count()).join(
            CaseActivity, User.id == CaseActivity.user_id
        ).where(
            and_(
                CaseActivity.case_id == case_id,
                CaseActivity.created_at >= start_date,
                CaseActivity.created_at <= end_date
            )
        ).group_by(User.id, User.full_name)
    ).all()
    
    user_activities = [
        {"user_name": name, "user_id": str(user_id), "activity_count": count}
        for name, user_id, count in activity_by_user
    ]
    
    # Activity timeline (daily)
    activity_timeline = session.exec(
        select(
            func.date(CaseActivity.created_at).label('date'),
            func.count().label('count')
        ).where(
            and_(
                CaseActivity.case_id == case_id,
                CaseActivity.created_at >= start_date,
                CaseActivity.created_at <= end_date
            )
        ).group_by(func.date(CaseActivity.created_at)).order_by(asc('date'))
    ).all()
    
    timeline_data = [
        {"date": date.isoformat(), "activity_count": count}
        for date, count in activity_timeline
    ]
    
    # Calculate activity velocity (activities per day)
    total_activities = sum(activity_types.values())
    period_days = (end_date - start_date).days or 1
    activity_velocity = round(total_activities / period_days, 2)
    
    return {
        "total_activities": total_activities,
        "activity_velocity": activity_velocity,
        "activity_by_type": activity_types,
        "activity_by_user": user_activities,
        "activity_timeline": timeline_data,
        "most_active_day": max(timeline_data, key=lambda x: x["activity_count"]) if timeline_data else None,
        "average_daily_activities": round(total_activities / max(1, len(timeline_data)), 2)
    }


def get_document_analytics(session: Session, case_id: uuid.UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """Get document usage and analytics"""
    
    # Documents by category
    docs_by_category = session.exec(
        select(CaseDocument.category, func.count()).where(
            and_(
                CaseDocument.case_id == case_id,
                CaseDocument.uploaded_at >= start_date,
                CaseDocument.uploaded_at <= end_date
            )
        ).group_by(CaseDocument.category)
    ).all()
    
    category_distribution = {category: count for category, count in docs_by_category}
    
    # Document size analytics
    size_stats = session.exec(
        select(
            func.count().label('count'),
            func.sum(CaseDocument.file_size).label('total_size'),
            func.avg(CaseDocument.file_size).label('avg_size'),
            func.max(CaseDocument.file_size).label('max_size')
        ).where(
            and_(
                CaseDocument.case_id == case_id,
                CaseDocument.uploaded_at >= start_date,
                CaseDocument.uploaded_at <= end_date
            )
        )
    ).one()
    
    # Document links analytics
    link_stats = session.exec(
        select(func.count()).select_from(DocumentCaseLink).where(
            DocumentCaseLink.document_id.in_(
                select(CaseDocument.id).where(CaseDocument.case_id == case_id)
            )
        )
    ).one()
    
    # Upload timeline
    upload_timeline = session.exec(
        select(
            func.date(CaseDocument.uploaded_at).label('date'),
            func.count().label('count')
        ).where(
            and_(
                CaseDocument.case_id == case_id,
                CaseDocument.uploaded_at >= start_date,
                CaseDocument.uploaded_at <= end_date
            )
        ).group_by(func.date(CaseDocument.uploaded_at)).order_by(asc('date'))
    ).all()
    
    upload_data = [
        {"date": date.isoformat(), "upload_count": count}
        for date, count in upload_timeline
    ]
    
    return {
        "total_documents": size_stats.count or 0,
        "total_size_bytes": size_stats.total_size or 0,
        "total_size_mb": round((size_stats.total_size or 0) / (1024 * 1024), 2),
        "average_size_bytes": round(size_stats.avg_size or 0, 2),
        "largest_file_bytes": size_stats.max_size or 0,
        "category_distribution": category_distribution,
        "total_links": link_stats,
        "upload_timeline": upload_data,
        "upload_velocity": round((size_stats.count or 0) / max(1, (end_date - start_date).days), 2)
    }


def get_progress_analytics(session: Session, case_id: uuid.UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """Get progress and milestone analytics"""
    
    # Milestone statistics
    total_milestones = session.exec(
        select(func.count()).select_from(CaseMilestone).where(CaseMilestone.case_id == case_id)
    ).one()
    
    completed_milestones = session.exec(
        select(func.count()).select_from(CaseMilestone).where(
            and_(
                CaseMilestone.case_id == case_id,
                CaseMilestone.status == "completed"
            )
        )
    ).one()
    
    overdue_milestones = session.exec(
        select(func.count()).select_from(CaseMilestone).where(
            and_(
                CaseMilestone.case_id == case_id,
                CaseMilestone.status.in_(["not_started", "in_progress"]),
                CaseMilestone.target_date < date.today()
            )
        )
    ).one()
    
    # Deadline statistics
    total_deadlines = session.exec(
        select(func.count()).select_from(CaseDeadline).where(CaseDeadline.case_id == case_id)
    ).one()
    
    upcoming_deadlines = session.exec(
        select(func.count()).select_from(CaseDeadline).where(
            and_(
                CaseDeadline.case_id == case_id,
                CaseDeadline.is_completed == False,
                CaseDeadline.deadline_date <= datetime.utcnow() + timedelta(days=30)
            )
        )
    ).one()
    
    # Progress calculation
    progress_percentage = (completed_milestones / max(1, total_milestones)) * 100
    
    return {
        "total_milestones": total_milestones,
        "completed_milestones": completed_milestones,
        "overdue_milestones": overdue_milestones,
        "progress_percentage": round(progress_percentage, 2),
        "total_deadlines": total_deadlines,
        "upcoming_deadlines": upcoming_deadlines,
        "milestone_completion_rate": round((completed_milestones / max(1, total_milestones)) * 100, 2),
        "on_track": overdue_milestones == 0 and upcoming_deadlines <= 3
    }


def get_collaboration_analytics(session: Session, case_id: uuid.UUID, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """Get team collaboration analytics"""

    # Unique users who worked on the case
    active_users = session.exec(
        select(User.id, User.full_name, User.role).distinct().join(
            CaseActivity, User.id == CaseActivity.user_id
        ).where(
            and_(
                CaseActivity.case_id == case_id,
                CaseActivity.created_at >= start_date,
                CaseActivity.created_at <= end_date
            )
        )
    ).all()

    team_members = [
        {"user_id": str(user_id), "name": name, "role": role}
        for user_id, name, role in active_users
    ]

    # Collaboration frequency (interactions between users)
    user_interactions = {}
    for user_id, name, role in active_users:
        user_activities = session.exec(
            select(func.count()).select_from(CaseActivity).where(
                and_(
                    CaseActivity.case_id == case_id,
                    CaseActivity.user_id == user_id,
                    CaseActivity.created_at >= start_date,
                    CaseActivity.created_at <= end_date
                )
            )
        ).one()

        user_interactions[str(user_id)] = {
            "name": name,
            "role": role,
            "activity_count": user_activities
        }

    # Notes collaboration
    notes_by_user = session.exec(
        select(User.full_name, func.count()).join(
            CaseNote, User.id == CaseNote.created_by
        ).where(
            and_(
                CaseNote.case_id == case_id,
                CaseNote.created_at >= start_date,
                CaseNote.created_at <= end_date
            )
        ).group_by(User.id, User.full_name)
    ).all()

    notes_collaboration = {name: count for name, count in notes_by_user}

    return {
        "team_size": len(team_members),
        "team_members": team_members,
        "user_interactions": user_interactions,
        "notes_collaboration": notes_collaboration,
        "collaboration_score": min(100, len(team_members) * 20 + sum(user_interactions[uid]["activity_count"] for uid in user_interactions) / 10)
    }


def get_performance_metrics(session: Session, case: LegalCase, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """Calculate case performance metrics"""

    case_age_days = (datetime.utcnow().date() - case.opening_date).days

    # Activity frequency
    total_activities = session.exec(
        select(func.count()).select_from(CaseActivity).where(CaseActivity.case_id == case.id)
    ).one()

    activity_frequency = total_activities / max(1, case_age_days)

    # Status change frequency
    status_changes = session.exec(
        select(func.count()).select_from(CaseActivity).where(
            and_(
                CaseActivity.case_id == case.id,
                CaseActivity.activity_type == ActivityType.STATUS_CHANGED.value
            )
        )
    ).one()

    # Document productivity
    total_documents = session.exec(
        select(func.count()).select_from(CaseDocument).where(CaseDocument.case_id == case.id)
    ).one()

    document_productivity = total_documents / max(1, case_age_days)

    # Response time (average time between activities)
    recent_activities = session.exec(
        select(CaseActivity.created_at).where(
            CaseActivity.case_id == case.id
        ).order_by(desc(CaseActivity.created_at)).limit(10)
    ).all()

    avg_response_time_hours = 0
    if len(recent_activities) > 1:
        time_diffs = []
        for i in range(len(recent_activities) - 1):
            diff = (recent_activities[i] - recent_activities[i + 1]).total_seconds() / 3600
            time_diffs.append(diff)
        avg_response_time_hours = sum(time_diffs) / len(time_diffs)

    # Performance score calculation
    performance_score = calculate_performance_score(
        activity_frequency, document_productivity, avg_response_time_hours, case.priority
    )

    return {
        "case_age_days": case_age_days,
        "activity_frequency": round(activity_frequency, 3),
        "status_changes": status_changes,
        "document_productivity": round(document_productivity, 3),
        "average_response_time_hours": round(avg_response_time_hours, 2),
        "performance_score": performance_score,
        "efficiency_rating": get_efficiency_rating(performance_score)
    }


def get_time_analytics(session: Session, case: LegalCase, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """Get time tracking and billing analytics"""

    case_age_days = (datetime.utcnow().date() - case.opening_date).days

    # Estimated time based on activities and documents
    total_activities = session.exec(
        select(func.count()).select_from(CaseActivity).where(CaseActivity.case_id == case.id)
    ).one()

    total_documents = session.exec(
        select(func.count()).select_from(CaseDocument).where(CaseDocument.case_id == case.id)
    ).one()

    # Rough time estimation (this would be replaced with actual time tracking)
    estimated_hours = (total_activities * 0.5) + (total_documents * 0.25)  # Rough estimates

    # Time distribution by activity type
    activity_time_distribution = {}
    activity_types = session.exec(
        select(CaseActivity.activity_type, func.count()).where(
            CaseActivity.case_id == case.id
        ).group_by(CaseActivity.activity_type)
    ).all()

    for activity_type, count in activity_types:
        # Estimate time per activity type
        time_per_activity = {
            "case_created": 1.0,
            "document_uploaded": 0.25,
            "note_added": 0.5,
            "status_changed": 0.5,
            "milestone_created": 0.75,
            "deadline_created": 0.5,
        }.get(activity_type, 0.5)

        activity_time_distribution[activity_type] = {
            "count": count,
            "estimated_hours": round(count * time_per_activity, 2)
        }

    return {
        "case_age_days": case_age_days,
        "estimated_total_hours": round(estimated_hours, 2),
        "estimated_daily_hours": round(estimated_hours / max(1, case_age_days), 2),
        "activity_time_distribution": activity_time_distribution,
        "billable_hours_estimate": round(estimated_hours * 0.8, 2),  # 80% billable rate
        "time_efficiency": calculate_time_efficiency(estimated_hours, case_age_days, case.priority)
    }


def get_comparative_analytics(session: Session, case: LegalCase, current_user: LawyerScopedUser) -> Dict[str, Any]:
    """Get comparative analytics against similar cases"""

    # Find similar cases (same type, same lawyer, or same priority)
    similar_cases_query = select(LegalCase).where(
        and_(
            LegalCase.id != case.id,
            or_(
                LegalCase.case_type == case.case_type,
                LegalCase.lawyer_id == case.lawyer_id,
                LegalCase.priority == case.priority
            )
        )
    )

    # Apply role-based filtering
    if current_user.role == UserRole.LAWYER:
        similar_cases_query = similar_cases_query.where(LegalCase.lawyer_id == current_user.id)

    similar_cases = session.exec(similar_cases_query.limit(50)).all()

    if not similar_cases:
        return {"message": "No similar cases found for comparison"}

    # Calculate averages for comparison
    total_similar = len(similar_cases)

    # Average case duration for completed cases
    completed_similar = [c for c in similar_cases if c.status == "closed"]
    avg_duration_days = 0
    if completed_similar:
        durations = [(datetime.utcnow().date() - c.opening_date).days for c in completed_similar]
        avg_duration_days = sum(durations) / len(durations)

    # Average activity count
    activity_counts = []
    for similar_case in similar_cases:
        count = session.exec(
            select(func.count()).select_from(CaseActivity).where(CaseActivity.case_id == similar_case.id)
        ).one()
        activity_counts.append(count)

    avg_activities = sum(activity_counts) / len(activity_counts) if activity_counts else 0

    # Current case metrics for comparison
    current_case_age = (datetime.utcnow().date() - case.opening_date).days
    current_activities = session.exec(
        select(func.count()).select_from(CaseActivity).where(CaseActivity.case_id == case.id)
    ).one()

    return {
        "similar_cases_count": total_similar,
        "comparison_metrics": {
            "average_duration_days": round(avg_duration_days, 1),
            "average_activities": round(avg_activities, 1),
            "current_case_age_days": current_case_age,
            "current_activities": current_activities
        },
        "performance_vs_average": {
            "duration_comparison": "above_average" if current_case_age > avg_duration_days else "below_average",
            "activity_comparison": "above_average" if current_activities > avg_activities else "below_average"
        },
        "percentile_ranking": {
            "duration": calculate_percentile(current_case_age, [c for c in similar_cases if c.status != "closed"]),
            "activity": calculate_percentile(current_activities, activity_counts)
        }
    }


def calculate_case_health(session: Session, case: LegalCase, overview: Dict, progress: Dict, activity: Dict) -> Dict[str, Any]:
    """Calculate overall case health indicators"""

    health_score = 100
    health_factors = []

    # Progress health
    progress_percentage = progress.get("progress_percentage", 0)
    if progress_percentage < 25:
        health_score -= 20
        health_factors.append("Low progress completion")
    elif progress_percentage > 75:
        health_score += 10
        health_factors.append("Good progress")

    # Overdue milestones
    overdue_milestones = progress.get("overdue_milestones", 0)
    if overdue_milestones > 0:
        health_score -= (overdue_milestones * 15)
        health_factors.append(f"{overdue_milestones} overdue milestones")

    # Activity level
    activity_velocity = activity.get("activity_velocity", 0)
    if activity_velocity < 0.5:
        health_score -= 15
        health_factors.append("Low activity level")
    elif activity_velocity > 2:
        health_score += 5
        health_factors.append("High activity level")

    # Case age vs progress
    case_age = overview.get("case_age_days", 0)
    if case_age > 365 and progress_percentage < 50:
        health_score -= 25
        health_factors.append("Long duration with low progress")

    # Priority vs activity
    if case.priority == "urgent" and activity_velocity < 1:
        health_score -= 20
        health_factors.append("Urgent case with low activity")

    health_score = max(0, min(100, health_score))

    # Determine health status
    if health_score >= 80:
        health_status = "excellent"
        health_color = "green"
    elif health_score >= 60:
        health_status = "good"
        health_color = "blue"
    elif health_score >= 40:
        health_status = "fair"
        health_color = "yellow"
    elif health_score >= 20:
        health_status = "poor"
        health_color = "orange"
    else:
        health_status = "critical"
        health_color = "red"

    return {
        "health_score": health_score,
        "health_status": health_status,
        "health_color": health_color,
        "health_factors": health_factors,
        "recommendations": generate_health_recommendations(health_factors, case, progress, activity)
    }


def calculate_performance_score(activity_freq: float, doc_productivity: float, response_time: float, priority: str) -> int:
    """Calculate overall performance score"""

    score = 50  # Base score

    # Activity frequency scoring
    if activity_freq > 1:
        score += 20
    elif activity_freq > 0.5:
        score += 10
    elif activity_freq < 0.1:
        score -= 20

    # Document productivity scoring
    if doc_productivity > 0.5:
        score += 15
    elif doc_productivity > 0.2:
        score += 5
    elif doc_productivity < 0.05:
        score -= 15

    # Response time scoring (lower is better)
    if response_time < 24:  # Less than 24 hours
        score += 15
    elif response_time < 72:  # Less than 3 days
        score += 5
    elif response_time > 168:  # More than a week
        score -= 20

    # Priority adjustment
    if priority == "urgent":
        score += 10  # Higher expectations for urgent cases
    elif priority == "low":
        score -= 5

    return max(0, min(100, score))


def get_efficiency_rating(performance_score: int) -> str:
    """Get efficiency rating based on performance score"""

    if performance_score >= 85:
        return "excellent"
    elif performance_score >= 70:
        return "good"
    elif performance_score >= 55:
        return "average"
    elif performance_score >= 40:
        return "below_average"
    else:
        return "poor"


def calculate_time_efficiency(estimated_hours: float, case_age_days: int, priority: str) -> Dict[str, Any]:
    """Calculate time efficiency metrics"""

    # Expected hours per day based on priority
    expected_daily_hours = {
        "urgent": 2.0,
        "high": 1.5,
        "medium": 1.0,
        "low": 0.5
    }.get(priority, 1.0)

    expected_total_hours = case_age_days * expected_daily_hours
    efficiency_ratio = estimated_hours / max(1, expected_total_hours)

    if efficiency_ratio > 1.2:
        efficiency_status = "over_allocated"
    elif efficiency_ratio > 0.8:
        efficiency_status = "well_allocated"
    elif efficiency_ratio > 0.5:
        efficiency_status = "under_allocated"
    else:
        efficiency_status = "significantly_under_allocated"

    return {
        "efficiency_ratio": round(efficiency_ratio, 2),
        "efficiency_status": efficiency_status,
        "expected_hours": round(expected_total_hours, 2),
        "actual_hours": round(estimated_hours, 2),
        "variance_hours": round(estimated_hours - expected_total_hours, 2)
    }


def calculate_percentile(value: float, comparison_list: List[Any]) -> int:
    """Calculate percentile ranking"""

    if not comparison_list:
        return 50

    comparison_values = [float(v) if isinstance(v, (int, float)) else 0 for v in comparison_list]
    comparison_values.sort()

    position = sum(1 for v in comparison_values if v <= value)
    percentile = (position / len(comparison_values)) * 100

    return min(100, max(0, int(percentile)))


def generate_health_recommendations(health_factors: List[str], case: LegalCase, progress: Dict, activity: Dict) -> List[str]:
    """Generate actionable recommendations based on health factors"""

    recommendations = []

    if "Low progress completion" in health_factors:
        recommendations.append("Consider breaking down remaining work into smaller milestones")
        recommendations.append("Schedule regular progress review meetings")

    if "overdue milestones" in str(health_factors):
        recommendations.append("Prioritize completing overdue milestones")
        recommendations.append("Review and adjust milestone target dates if necessary")

    if "Low activity level" in health_factors:
        recommendations.append("Increase case activity frequency")
        recommendations.append("Schedule regular case review sessions")

    if "Long duration with low progress" in health_factors:
        recommendations.append("Consider case strategy review")
        recommendations.append("Evaluate if additional resources are needed")

    if "Urgent case with low activity" in health_factors:
        recommendations.append("Immediately increase focus on this urgent case")
        recommendations.append("Consider reassigning resources to prioritize this case")

    # General recommendations
    if case.priority == "urgent" and not recommendations:
        recommendations.append("Maintain current pace for this urgent case")

    if progress.get("progress_percentage", 0) > 80:
        recommendations.append("Case is nearing completion - focus on final deliverables")

    if not recommendations:
        recommendations.append("Case appears to be progressing well")
        recommendations.append("Continue current approach and monitor regularly")

    return recommendations
