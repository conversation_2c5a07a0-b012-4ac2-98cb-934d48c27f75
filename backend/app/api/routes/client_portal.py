"""
Client Portal API Routes

This module provides API endpoints specifically designed for client access,
with appropriate security and data filtering for client users.
"""

import uuid
from datetime import datetime, date
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, or_, and_, desc

from app.api.deps import (
    CurrentUser,
    SessionDep,
    get_current_active_superuser,
    get_current_user,
)
from app.core.config import settings
from app.models import (
    # User and auth models
    User,
    UserRole,
    
    # Case models
    LegalCase,
    CaseStatus,
    CaseType,
    
    # Client portal models
    Notification,
    NotificationCreate,
    NotificationUpdate,
    NotificationPublic,
    NotificationsPublic,
    NotificationType,
    NotificationPriority,
    
    ClientMessage,
    ClientMessageCreate,
    ClientMessageUpdate,
    ClientMessagePublic,
    ClientMessagesPublic,
    MessageStatus,
    
    ClientDashboardStats,
    ClientCaseView,
    ClientCasesPublic,
    
    # Document models
    CaseDocument,
    DocumentCategory,
    
    # Activity models
    CaseActivity,
    ActivityType,
)

router = APIRouter()


# Client-specific dependency to ensure only clients can access client portal
def get_current_client_user(current_user: CurrentUser) -> User:
    """Ensure the current user is a client"""
    if current_user.role != UserRole.CLIENT:
        raise HTTPException(
            status_code=403,
            detail="Access denied. Client portal is only available to client users."
        )
    return current_user


# Dependency for client or lawyer access (for shared resources)
def get_current_client_or_lawyer(current_user: CurrentUser) -> User:
    """Allow access to clients and lawyers (for shared resources)"""
    if current_user.role not in [UserRole.CLIENT, UserRole.LAWYER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=403,
            detail="Access denied. Insufficient permissions."
        )
    return current_user


# Client Dashboard
@router.get("/dashboard", response_model=ClientDashboardStats)
def get_client_dashboard(
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
) -> Any:
    """Get client dashboard statistics"""
    
    # Count active cases for this client
    active_cases_query = select(func.count(LegalCase.id)).where(
        and_(
            LegalCase.client_name.ilike(f"%{current_user.full_name}%"),
            LegalCase.status.in_(["open", "in_progress"])
        )
    )
    active_cases = session.exec(active_cases_query).one()
    
    # Count total documents shared with client
    shared_docs_query = select(func.count(CaseDocument.id)).join(
        LegalCase, CaseDocument.case_id == LegalCase.id
    ).where(
        and_(
            LegalCase.client_name.ilike(f"%{current_user.full_name}%"),
            CaseDocument.is_shared_with_client == True
        )
    )
    total_documents = session.exec(shared_docs_query).one()
    
    # Count unread messages
    unread_messages_query = select(func.count(ClientMessage.id)).where(
        and_(
            ClientMessage.recipient_id == current_user.id,
            ClientMessage.status != MessageStatus.READ
        )
    )
    unread_messages = session.exec(unread_messages_query).one()
    
    # Count unread notifications
    unread_notifications_query = select(func.count(Notification.id)).where(
        and_(
            Notification.user_id == current_user.id,
            Notification.is_read == False
        )
    )
    unread_notifications = session.exec(unread_notifications_query).one()
    
    # TODO: Add upcoming appointments and pending deadlines when those models are implemented
    upcoming_appointments = 0
    pending_deadlines = 0
    
    return ClientDashboardStats(
        active_cases=active_cases,
        total_documents=total_documents,
        unread_messages=unread_messages,
        unread_notifications=unread_notifications,
        upcoming_appointments=upcoming_appointments,
        pending_deadlines=pending_deadlines
    )


# Client Cases (Limited View)
@router.get("/cases", response_model=ClientCasesPublic)
def get_client_cases(
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: str | None = Query(None),
    case_type: CaseType | None = Query(None),
) -> Any:
    """Get cases for the current client with limited information"""
    
    # Base query for client's cases
    query = select(LegalCase).where(
        LegalCase.client_name.ilike(f"%{current_user.full_name}%")
    )
    
    # Apply filters
    if status:
        query = query.where(LegalCase.status == status)
    
    if case_type:
        query = query.where(LegalCase.case_type == case_type)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()
    
    # Apply pagination and ordering
    query = query.order_by(desc(LegalCase.opening_date)).offset(skip).limit(limit)
    cases = session.exec(query).all()
    
    # Convert to client view format
    client_cases = []
    for case in cases:
        # Get lawyer info
        lawyer = session.get(User, case.lawyer_id) if case.lawyer_id else None
        
        # Count shared documents
        shared_docs_count = session.exec(
            select(func.count(CaseDocument.id)).where(
                and_(
                    CaseDocument.case_id == case.id,
                    CaseDocument.is_shared_with_client == True
                )
            )
        ).one()
        
        # Count unread messages for this case
        unread_messages_count = session.exec(
            select(func.count(ClientMessage.id)).where(
                and_(
                    ClientMessage.case_id == case.id,
                    ClientMessage.recipient_id == current_user.id,
                    ClientMessage.status != MessageStatus.READ
                )
            )
        ).one()
        
        # Get recent updates (last 3 activities)
        recent_activities = session.exec(
            select(CaseActivity).where(
                CaseActivity.case_id == case.id
            ).order_by(desc(CaseActivity.created_at)).limit(3)
        ).all()
        
        recent_updates = [activity.description for activity in recent_activities]
        
        client_case = ClientCaseView(
            id=case.id,
            title=case.title,
            case_type=case.case_type,
            status=case.status,
            opening_date=case.opening_date,
            description=case.description,
            next_hearing_date=None,  # TODO: Implement when hearing model is added
            lawyer=lawyer,
            recent_updates=recent_updates,
            shared_documents_count=shared_docs_count,
            unread_messages_count=unread_messages_count
        )
        client_cases.append(client_case)
    
    return ClientCasesPublic(data=client_cases, count=total_count)


# Client Case Details (Limited View)
@router.get("/cases/{case_id}", response_model=ClientCaseView)
def get_client_case_detail(
    case_id: uuid.UUID,
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
) -> Any:
    """Get detailed information about a specific case for the client"""
    
    # Get case and verify client access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Verify client has access to this case
    if not case.client_name or current_user.full_name.lower() not in case.client_name.lower():
        raise HTTPException(status_code=403, detail="Access denied to this case")
    
    # Get lawyer info
    lawyer = session.get(User, case.lawyer_id) if case.lawyer_id else None
    
    # Count shared documents
    shared_docs_count = session.exec(
        select(func.count(CaseDocument.id)).where(
            and_(
                CaseDocument.case_id == case.id,
                CaseDocument.is_shared_with_client == True
            )
        )
    ).one()
    
    # Count unread messages for this case
    unread_messages_count = session.exec(
        select(func.count(ClientMessage.id)).where(
            and_(
                ClientMessage.case_id == case.id,
                ClientMessage.recipient_id == current_user.id,
                ClientMessage.status != MessageStatus.READ
            )
        )
    ).one()
    
    # Get recent updates (last 5 activities)
    recent_activities = session.exec(
        select(CaseActivity).where(
            CaseActivity.case_id == case.id
        ).order_by(desc(CaseActivity.created_at)).limit(5)
    ).all()
    
    recent_updates = [activity.description for activity in recent_activities]
    
    return ClientCaseView(
        id=case.id,
        title=case.title,
        case_type=case.case_type,
        status=case.status,
        opening_date=case.opening_date,
        description=case.description,
        next_hearing_date=None,  # TODO: Implement when hearing model is added
        lawyer=lawyer,
        recent_updates=recent_updates,
        shared_documents_count=shared_docs_count,
        unread_messages_count=unread_messages_count
    )


# Client Notifications
@router.get("/notifications", response_model=NotificationsPublic)
def get_client_notifications(
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    is_read: bool | None = Query(None),
    notification_type: NotificationType | None = Query(None),
    priority: NotificationPriority | None = Query(None),
) -> Any:
    """Get notifications for the current client"""

    # Base query for client's notifications
    query = select(Notification).where(Notification.user_id == current_user.id)

    # Apply filters
    if is_read is not None:
        query = query.where(Notification.is_read == is_read)

    if notification_type:
        query = query.where(Notification.notification_type == notification_type)

    if priority:
        query = query.where(Notification.priority == priority)

    # Filter out expired notifications
    query = query.where(
        or_(
            Notification.expires_at.is_(None),
            Notification.expires_at > datetime.utcnow()
        )
    )

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()

    # Get unread count
    unread_query = select(func.count(Notification.id)).where(
        and_(
            Notification.user_id == current_user.id,
            Notification.is_read == False,
            or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > datetime.utcnow()
            )
        )
    )
    unread_count = session.exec(unread_query).one()

    # Apply pagination and ordering
    query = query.order_by(desc(Notification.created_at)).offset(skip).limit(limit)
    notifications = session.exec(query).all()

    # Convert to public format with case info
    notification_data = []
    for notification in notifications:
        case = session.get(LegalCase, notification.case_id) if notification.case_id else None
        notification_public = NotificationPublic.model_validate(notification)
        if case:
            notification_public.case = case
        notification_data.append(notification_public)

    return NotificationsPublic(
        data=notification_data,
        count=total_count,
        unread_count=unread_count
    )


@router.patch("/notifications/{notification_id}", response_model=NotificationPublic)
def mark_notification_read(
    notification_id: uuid.UUID,
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
) -> Any:
    """Mark a notification as read"""

    notification = session.get(Notification, notification_id)
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")

    # Verify ownership
    if notification.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Mark as read
    notification.is_read = True
    notification.read_at = datetime.utcnow()

    session.add(notification)
    session.commit()
    session.refresh(notification)

    # Load case info if available
    case = session.get(LegalCase, notification.case_id) if notification.case_id else None
    notification_public = NotificationPublic.model_validate(notification)
    if case:
        notification_public.case = case

    return notification_public


@router.post("/notifications/mark-all-read")
def mark_all_notifications_read(
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
) -> Any:
    """Mark all notifications as read for the current client"""

    # Update all unread notifications
    notifications = session.exec(
        select(Notification).where(
            and_(
                Notification.user_id == current_user.id,
                Notification.is_read == False
            )
        )
    ).all()

    read_count = 0
    for notification in notifications:
        notification.is_read = True
        notification.read_at = datetime.utcnow()
        session.add(notification)
        read_count += 1

    session.commit()

    return {"message": f"Marked {read_count} notifications as read"}


# Client Messaging
@router.get("/messages", response_model=ClientMessagesPublic)
def get_client_messages(
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    case_id: uuid.UUID | None = Query(None),
    status: MessageStatus | None = Query(None),
    search: str | None = Query(None),
) -> Any:
    """Get messages for the current client"""

    # Base query for client's messages (sent or received)
    query = select(ClientMessage).where(
        or_(
            ClientMessage.sender_id == current_user.id,
            ClientMessage.recipient_id == current_user.id
        )
    )

    # Apply filters
    if case_id:
        query = query.where(ClientMessage.case_id == case_id)

    if status:
        query = query.where(ClientMessage.status == status)

    if search:
        search_filter = f"%{search}%"
        query = query.where(
            or_(
                ClientMessage.subject.ilike(search_filter),
                ClientMessage.content.ilike(search_filter)
            )
        )

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()

    # Get unread count (only received messages)
    unread_query = select(func.count(ClientMessage.id)).where(
        and_(
            ClientMessage.recipient_id == current_user.id,
            ClientMessage.status != MessageStatus.READ
        )
    )
    unread_count = session.exec(unread_query).one()

    # Apply pagination and ordering
    query = query.order_by(desc(ClientMessage.created_at)).offset(skip).limit(limit)
    messages = session.exec(query).all()

    # Convert to public format with user and case info
    message_data = []
    for message in messages:
        sender = session.get(User, message.sender_id)
        recipient = session.get(User, message.recipient_id)
        case = session.get(LegalCase, message.case_id) if message.case_id else None

        # Count replies
        replies_count = session.exec(
            select(func.count(ClientMessage.id)).where(
                ClientMessage.parent_message_id == message.id
            )
        ).one()

        message_public = ClientMessagePublic.model_validate(message)
        message_public.sender = sender
        message_public.recipient = recipient
        message_public.case = case
        message_public.replies_count = replies_count
        message_public.can_reply = True  # Clients can always reply

        message_data.append(message_public)

    return ClientMessagesPublic(
        data=message_data,
        count=total_count,
        unread_count=unread_count
    )


@router.post("/messages", response_model=ClientMessagePublic)
def send_client_message(
    message_in: ClientMessageCreate,
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
) -> Any:
    """Send a message from client to lawyer"""

    # Verify recipient exists and is a lawyer
    recipient = session.get(User, message_in.recipient_id)
    if not recipient:
        raise HTTPException(status_code=404, detail="Recipient not found")

    if recipient.role not in [UserRole.LAWYER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=400,
            detail="Messages can only be sent to lawyers or administrators"
        )

    # If case_id is provided, verify client has access to the case
    if message_in.case_id:
        case = session.get(LegalCase, message_in.case_id)
        if not case:
            raise HTTPException(status_code=404, detail="Case not found")

        # Verify client has access to this case
        if not case.client_name or current_user.full_name.lower() not in case.client_name.lower():
            raise HTTPException(status_code=403, detail="Access denied to this case")

    # Create message
    message = ClientMessage.model_validate(
        message_in,
        update={"sender_id": current_user.id}
    )

    session.add(message)
    session.commit()
    session.refresh(message)

    # Create notification for recipient
    notification = Notification(
        title=f"New message from {current_user.full_name}",
        message=f"Subject: {message.subject}",
        notification_type=NotificationType.MESSAGE_RECEIVED,
        priority=NotificationPriority.MEDIUM,
        user_id=recipient.id,
        case_id=message.case_id,
        notification_metadata={
            "message_id": str(message.id),
            "sender_name": current_user.full_name
        }
    )
    session.add(notification)
    session.commit()

    # Load related data for response
    sender = session.get(User, message.sender_id)
    case = session.get(LegalCase, message.case_id) if message.case_id else None

    message_public = ClientMessagePublic.model_validate(message)
    message_public.sender = sender
    message_public.recipient = recipient
    message_public.case = case
    message_public.replies_count = 0
    message_public.can_reply = True

    return message_public


@router.get("/messages/{message_id}", response_model=ClientMessagePublic)
def get_client_message(
    message_id: uuid.UUID,
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
) -> Any:
    """Get a specific message"""

    message = session.get(ClientMessage, message_id)
    if not message:
        raise HTTPException(status_code=404, detail="Message not found")

    # Verify access (sender or recipient)
    if message.sender_id != current_user.id and message.recipient_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Mark as read if current user is the recipient
    if message.recipient_id == current_user.id and message.status != MessageStatus.READ:
        message.status = MessageStatus.READ
        message.read_at = datetime.utcnow()
        session.add(message)
        session.commit()
        session.refresh(message)

    # Load related data
    sender = session.get(User, message.sender_id)
    recipient = session.get(User, message.recipient_id)
    case = session.get(LegalCase, message.case_id) if message.case_id else None

    # Count replies
    replies_count = session.exec(
        select(func.count(ClientMessage.id)).where(
            ClientMessage.parent_message_id == message.id
        )
    ).one()

    message_public = ClientMessagePublic.model_validate(message)
    message_public.sender = sender
    message_public.recipient = recipient
    message_public.case = case
    message_public.replies_count = replies_count
    message_public.can_reply = True

    return message_public


@router.get("/messages/{message_id}/replies", response_model=ClientMessagesPublic)
def get_message_replies(
    message_id: uuid.UUID,
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
) -> Any:
    """Get replies to a specific message"""

    # Verify access to parent message
    parent_message = session.get(ClientMessage, message_id)
    if not parent_message:
        raise HTTPException(status_code=404, detail="Message not found")

    if parent_message.sender_id != current_user.id and parent_message.recipient_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get replies
    query = select(ClientMessage).where(
        ClientMessage.parent_message_id == message_id
    ).order_by(ClientMessage.created_at)

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()

    # Apply pagination
    query = query.offset(skip).limit(limit)
    replies = session.exec(query).all()

    # Convert to public format
    reply_data = []
    for reply in replies:
        sender = session.get(User, reply.sender_id)
        recipient = session.get(User, reply.recipient_id)
        case = session.get(LegalCase, reply.case_id) if reply.case_id else None

        reply_public = ClientMessagePublic.model_validate(reply)
        reply_public.sender = sender
        reply_public.recipient = recipient
        reply_public.case = case
        reply_public.replies_count = 0  # Replies don't have sub-replies
        reply_public.can_reply = True

        reply_data.append(reply_public)

    return ClientMessagesPublic(
        data=reply_data,
        count=total_count,
        unread_count=0  # Not applicable for replies
    )


# Client Document Access
@router.get("/cases/{case_id}/documents")
def get_client_case_documents(
    case_id: uuid.UUID,
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    category: DocumentCategory | None = Query(None),
    search: str | None = Query(None),
) -> Any:
    """Get documents shared with the client for a specific case"""

    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    # Verify client has access to this case
    if not case.client_name or current_user.full_name.lower() not in case.client_name.lower():
        raise HTTPException(status_code=403, detail="Access denied to this case")

    # Base query for shared documents only
    query = select(CaseDocument).where(
        and_(
            CaseDocument.case_id == case_id,
            CaseDocument.is_shared_with_client == True,
            CaseDocument.is_confidential == False  # Extra safety check
        )
    )

    # Apply filters
    if category:
        query = query.where(CaseDocument.category == category)

    if search:
        search_filter = f"%{search}%"
        query = query.where(
            or_(
                CaseDocument.filename.ilike(search_filter),
                CaseDocument.original_filename.ilike(search_filter),
                CaseDocument.description.ilike(search_filter)
            )
        )

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()

    # Apply pagination and ordering
    query = query.order_by(desc(CaseDocument.uploaded_at)).offset(skip).limit(limit)
    documents = session.exec(query).all()

    # Convert to public format (simplified for clients)
    document_data = []
    for doc in documents:
        uploader = session.get(User, doc.uploaded_by)

        doc_data = {
            "id": doc.id,
            "filename": doc.original_filename,
            "description": doc.description,
            "category": doc.category,
            "file_size": doc.file_size,
            "uploaded_at": doc.uploaded_at,
            "uploader_name": uploader.full_name if uploader else "Unknown",
            "can_download": True,
            "download_url": f"/api/v1/client-portal/documents/{doc.id}/download"
        }
        document_data.append(doc_data)

    return {
        "data": document_data,
        "count": total_count
    }


@router.get("/documents/{document_id}/download")
def download_client_document(
    document_id: uuid.UUID,
    current_user: User = Depends(get_current_client_user),
    session: SessionDep = Depends(),
) -> Any:
    """Download a document shared with the client"""

    # Get document
    document = session.get(CaseDocument, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Verify document is shared with client and not confidential
    if not document.is_shared_with_client or document.is_confidential:
        raise HTTPException(status_code=403, detail="Access denied to this document")

    # Verify client has access to the case
    case = session.get(LegalCase, document.case_id)
    if not case or not case.client_name or current_user.full_name.lower() not in case.client_name.lower():
        raise HTTPException(status_code=403, detail="Access denied to this case")

    # Log download activity
    activity = CaseActivity(
        case_id=document.case_id,
        activity_type=ActivityType.DOCUMENT_DOWNLOADED,
        description=f"Client {current_user.full_name} downloaded document: {document.original_filename}",
        user_id=current_user.id,
        activity_metadata={
            "document_id": str(document.id),
            "document_name": document.original_filename,
            "client_download": True
        }
    )
    session.add(activity)
    session.commit()

    # Return file response (implementation depends on file storage system)
    # For now, return file info - actual file serving would be handled by file storage service
    return {
        "download_url": f"/files/{document.file_path}",
        "filename": document.original_filename,
        "content_type": document.content_type,
        "file_size": document.file_size
    }


# Utility endpoints for client portal
@router.get("/case-types")
def get_case_types_for_client(
    current_user: User = Depends(get_current_client_user),  # noqa: ARG001
) -> Any:
    """Get available case types for filtering"""
    return [{"value": case_type.value, "label": case_type.value.replace("_", " ").title()}
            for case_type in CaseType]


@router.get("/case-statuses")
def get_case_statuses_for_client(
    current_user: User = Depends(get_current_client_user),  # noqa: ARG001
) -> Any:
    """Get available case statuses for filtering"""
    return [{"value": status.value, "label": status.value.replace("_", " ").title()}
            for status in CaseStatus]


@router.get("/document-categories")
def get_document_categories_for_client(
    current_user: User = Depends(get_current_client_user),  # noqa: ARG001
) -> Any:
    """Get available document categories for filtering"""
    return [{"value": category.value, "label": category.value.replace("_", " ").title()}
            for category in DocumentCategory]
