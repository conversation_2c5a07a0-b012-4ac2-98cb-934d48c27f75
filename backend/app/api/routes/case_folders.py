import uuid
from typing import Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, desc, asc

from app.api.deps import LawyerScopedUser
from app.core.db import get_session
from app.models import (
    CaseFolder,
    CaseFolderCreate,
    CaseFolderUpdate,
    CaseFolderPublic,
    CaseFoldersPublic,
    CaseDocument,
    LegalCase,
    User,
    UserRole,
    ActivityType,
    CaseActivity,
)

router = APIRouter()


def check_folder_access(folder: CaseFolder, user: LawyerScopedUser) -> bool:
    """Check if user has access to view a folder based on case permissions"""
    if user.role == UserRole.ADMIN:
        return True
    
    # Creator can always access their folders
    if folder.created_by == user.id:
        return True
    
    # Check case access (same logic as documents)
    # This will be handled at the case level in the routes
    return True


def check_folder_edit_access(folder: CaseFolder, user: LawyerScopedUser) -> bool:
    """Check if user can edit/delete a folder"""
    if user.role == UserRole.ADMIN:
        return True
    
    # Creator can edit their folders
    if folder.created_by == user.id:
        return True
    
    # Lawyers can edit folders in their cases
    if user.role == UserRole.LAWYER:
        return True
    
    # System folders cannot be deleted by non-admins
    if folder.is_system_folder and user.role != UserRole.ADMIN:
        return False
    
    return False


def get_folder_path(session: Session, folder_id: uuid.UUID | None) -> list[str]:
    """Get the full path of a folder as a list of folder names"""
    if not folder_id:
        return []
    
    path = []
    current_folder_id = folder_id
    
    while current_folder_id:
        folder = session.get(CaseFolder, current_folder_id)
        if not folder:
            break
        path.insert(0, folder.name)
        current_folder_id = folder.parent_folder_id
    
    return path


def create_default_folders(session: Session, case_id: uuid.UUID, user_id: uuid.UUID) -> list[CaseFolder]:
    """Create default folder structure for a new case"""
    default_folders = [
        {"name": "Evidence", "color": "#ef4444"},
        {"name": "Contracts", "color": "#3b82f6"},
        {"name": "Correspondence", "color": "#10b981"},
        {"name": "Court Documents", "color": "#8b5cf6"},
        {"name": "Research", "color": "#06b6d4"},
        {"name": "Internal", "color": "#6b7280"},
    ]
    
    folders = []
    for folder_data in default_folders:
        folder = CaseFolder(
            case_id=case_id,
            name=folder_data["name"],
            description=f"Default folder for {folder_data['name'].lower()}",
            color=folder_data["color"],
            is_system_folder=True,
            created_by=user_id,
        )
        session.add(folder)
        folders.append(folder)
    
    session.commit()
    return folders


@router.get("/{case_id}/folders")
def get_case_folders(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    parent_folder_id: uuid.UUID | None = Query(None),
    include_documents: bool = Query(False),
) -> CaseFoldersPublic:
    """Get folders for a specific case"""
    
    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Apply role-based access control for case
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Build folders query
    query = select(CaseFolder, User).join(User, CaseFolder.created_by == User.id).where(
        CaseFolder.case_id == case_id,
        CaseFolder.parent_folder_id == parent_folder_id
    ).order_by(asc(CaseFolder.folder_order), asc(CaseFolder.name))
    
    results = session.exec(query).all()
    
    # Build response
    folders = []
    for folder, creator in results:
        # Count documents in this folder
        doc_count = session.exec(
            select(func.count()).where(CaseDocument.folder_id == folder.id)
        ).one()
        
        # Count subfolders
        subfolder_count = session.exec(
            select(func.count()).where(CaseFolder.parent_folder_id == folder.id)
        ).one()
        
        # Calculate total size of documents in folder
        total_size = session.exec(
            select(func.coalesce(func.sum(CaseDocument.file_size), 0)).where(
                CaseDocument.folder_id == folder.id
            )
        ).one()
        
        folder_public = CaseFolderPublic(
            **folder.model_dump(),
            creator=creator,
            document_count=doc_count,
            subfolder_count=subfolder_count,
            total_size=total_size,
            can_edit=check_folder_edit_access(folder, current_user),
            can_delete=check_folder_edit_access(folder, current_user) and not folder.is_system_folder,
            path=get_folder_path(session, folder.parent_folder_id),
        )
        folders.append(folder_public)
    
    return CaseFoldersPublic(data=folders, count=len(folders))


@router.post("/{case_id}/folders")
def create_case_folder(
    case_id: uuid.UUID,
    folder_data: CaseFolderCreate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseFolderPublic:
    """Create a new folder for a case"""
    
    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Validate parent folder exists if specified
    if folder_data.parent_folder_id:
        parent_folder = session.get(CaseFolder, folder_data.parent_folder_id)
        if not parent_folder or parent_folder.case_id != case_id:
            raise HTTPException(status_code=404, detail="Parent folder not found")
    
    # Check for duplicate folder names in the same parent
    existing_folder = session.exec(
        select(CaseFolder).where(
            CaseFolder.case_id == case_id,
            CaseFolder.parent_folder_id == folder_data.parent_folder_id,
            CaseFolder.name == folder_data.name
        )
    ).first()
    
    if existing_folder:
        raise HTTPException(status_code=400, detail="Folder with this name already exists in this location")
    
    # Create folder
    folder = CaseFolder(
        case_id=case_id,
        name=folder_data.name,
        description=folder_data.description,
        parent_folder_id=folder_data.parent_folder_id,
        color=folder_data.color,
        created_by=current_user.id,
    )
    
    session.add(folder)
    session.commit()
    session.refresh(folder)
    
    # Create activity log
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.CASE_UPDATED.value,  # We can add FOLDER_CREATED later
        description=f"Created folder: {folder_data.name}",
        user_id=current_user.id,
        activity_metadata={
            "folder_id": str(folder.id),
            "folder_name": folder_data.name,
            "parent_folder_id": str(folder_data.parent_folder_id) if folder_data.parent_folder_id else None,
        }
    )
    session.add(activity)
    session.commit()
    
    # Get creator info for response
    creator = session.get(User, current_user.id)
    
    return CaseFolderPublic(
        **folder.model_dump(),
        creator=creator,
        document_count=0,
        subfolder_count=0,
        total_size=0,
        can_edit=True,
        can_delete=True,
        path=get_folder_path(session, folder.parent_folder_id),
    )


@router.put("/{case_id}/folders/{folder_id}")
def update_case_folder(
    case_id: uuid.UUID,
    folder_id: uuid.UUID,
    folder_data: CaseFolderUpdate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseFolderPublic:
    """Update an existing folder"""

    # Get folder
    folder = session.get(CaseFolder, folder_id)
    if not folder or folder.case_id != case_id:
        raise HTTPException(status_code=404, detail="Folder not found")

    # Check edit permissions
    if not check_folder_edit_access(folder, current_user):
        raise HTTPException(status_code=403, detail="Access denied")

    # Validate parent folder if changing
    if folder_data.parent_folder_id is not None and folder_data.parent_folder_id != folder.parent_folder_id:
        if folder_data.parent_folder_id:
            parent_folder = session.get(CaseFolder, folder_data.parent_folder_id)
            if not parent_folder or parent_folder.case_id != case_id:
                raise HTTPException(status_code=404, detail="Parent folder not found")

            # Prevent circular references
            if folder_data.parent_folder_id == folder.id:
                raise HTTPException(status_code=400, detail="Cannot set folder as its own parent")

    # Check for duplicate names if changing name or parent
    if folder_data.name or folder_data.parent_folder_id is not None:
        new_name = folder_data.name or folder.name
        new_parent_id = folder_data.parent_folder_id if folder_data.parent_folder_id is not None else folder.parent_folder_id

        existing_folder = session.exec(
            select(CaseFolder).where(
                CaseFolder.case_id == case_id,
                CaseFolder.parent_folder_id == new_parent_id,
                CaseFolder.name == new_name,
                CaseFolder.id != folder.id
            )
        ).first()

        if existing_folder:
            raise HTTPException(status_code=400, detail="Folder with this name already exists in this location")

    # Update folder fields
    if folder_data.name is not None:
        folder.name = folder_data.name
    if folder_data.description is not None:
        folder.description = folder_data.description
    if folder_data.parent_folder_id is not None:
        folder.parent_folder_id = folder_data.parent_folder_id
    if folder_data.color is not None:
        folder.color = folder_data.color
    if folder_data.folder_order is not None:
        folder.folder_order = folder_data.folder_order

    folder.updated_at = datetime.utcnow()

    session.add(folder)
    session.commit()
    session.refresh(folder)

    # Get creator and counts
    creator = session.get(User, folder.created_by)
    doc_count = session.exec(
        select(func.count()).where(CaseDocument.folder_id == folder.id)
    ).one()
    subfolder_count = session.exec(
        select(func.count()).where(CaseFolder.parent_folder_id == folder.id)
    ).one()
    total_size = session.exec(
        select(func.coalesce(func.sum(CaseDocument.file_size), 0)).where(
            CaseDocument.folder_id == folder.id
        )
    ).one()

    return CaseFolderPublic(
        **folder.model_dump(),
        creator=creator,
        document_count=doc_count,
        subfolder_count=subfolder_count,
        total_size=total_size,
        can_edit=check_folder_edit_access(folder, current_user),
        can_delete=check_folder_edit_access(folder, current_user) and not folder.is_system_folder,
        path=get_folder_path(session, folder.parent_folder_id),
    )


@router.delete("/{case_id}/folders/{folder_id}")
def delete_case_folder(
    case_id: uuid.UUID,
    folder_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    move_contents_to: uuid.UUID | None = Query(None),
) -> dict[str, Any]:
    """Delete a folder and optionally move its contents"""

    # Get folder
    folder = session.get(CaseFolder, folder_id)
    if not folder or folder.case_id != case_id:
        raise HTTPException(status_code=404, detail="Folder not found")

    # Check delete permissions
    if not check_folder_edit_access(folder, current_user):
        raise HTTPException(status_code=403, detail="Access denied")

    # System folders cannot be deleted by non-admins
    if folder.is_system_folder and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Cannot delete system folder")

    # Check if folder has contents
    doc_count = session.exec(
        select(func.count()).where(CaseDocument.folder_id == folder.id)
    ).one()
    subfolder_count = session.exec(
        select(func.count()).where(CaseFolder.parent_folder_id == folder.id)
    ).one()

    if (doc_count > 0 or subfolder_count > 0) and move_contents_to is None:
        raise HTTPException(
            status_code=400,
            detail="Folder contains documents or subfolders. Specify move_contents_to parameter or move contents manually."
        )

    # Validate destination folder if specified
    if move_contents_to:
        dest_folder = session.get(CaseFolder, move_contents_to)
        if not dest_folder or dest_folder.case_id != case_id:
            raise HTTPException(status_code=404, detail="Destination folder not found")
        if dest_folder.id == folder.id:
            raise HTTPException(status_code=400, detail="Cannot move contents to the same folder")

    # Move contents if specified
    if move_contents_to:
        # Move documents
        documents = session.exec(
            select(CaseDocument).where(CaseDocument.folder_id == folder.id)
        ).all()
        for doc in documents:
            doc.folder_id = move_contents_to
            session.add(doc)

        # Move subfolders
        subfolders = session.exec(
            select(CaseFolder).where(CaseFolder.parent_folder_id == folder.id)
        ).all()
        for subfolder in subfolders:
            subfolder.parent_folder_id = move_contents_to
            session.add(subfolder)

    # Delete the folder
    session.delete(folder)
    session.commit()

    return {"message": "Folder deleted successfully"}


@router.post("/{case_id}/folders/initialize")
def initialize_case_folders(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseFoldersPublic:
    """Initialize default folder structure for a case"""

    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    # Only lawyers and admins can initialize folders
    if current_user.role not in [UserRole.LAWYER, UserRole.ADMIN]:
        raise HTTPException(status_code=403, detail="Access denied")

    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Check if folders already exist
    existing_folders = session.exec(
        select(func.count()).where(CaseFolder.case_id == case_id)
    ).one()

    if existing_folders > 0:
        raise HTTPException(status_code=400, detail="Case already has folders")

    # Create default folders
    folders = create_default_folders(session, case_id, current_user.id)

    # Build response
    folder_publics = []
    for folder in folders:
        creator = session.get(User, folder.created_by)
        folder_public = CaseFolderPublic(
            **folder.model_dump(),
            creator=creator,
            document_count=0,
            subfolder_count=0,
            total_size=0,
            can_edit=True,
            can_delete=False,  # System folders
            path=[],
        )
        folder_publics.append(folder_public)

    return CaseFoldersPublic(data=folder_publics, count=len(folder_publics))
