import uuid
from datetime import datetime
from typing import Any, Dict, List, Annotated

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, and_, or_, desc

from app.api.deps import LawyerScopedUser, SessionDep
from app.core.db import get_session
from app.models import (
    CaseTemplate,
    CaseTemplateCreate,
    CaseTemplateUpdate,
    CaseTemplatePublic,
    CaseTemplatesPublic,
    DocumentTemplate,
    DocumentTemplateCreate,
    DocumentTemplateUpdate,
    DocumentTemplatePublic,
    DocumentTemplatesPublic,
    WorkflowTemplate,
    WorkflowTemplateCreate,
    WorkflowTemplateUpdate,
    WorkflowTemplatePublic,
    WorkflowTemplatesPublic,
    LegalCase,
    LegalCaseCreate,
    CaseMilestone,
    CaseDeadline,
    CaseActivity,
    ActivityType,
    UserRole,
    CaseType,
    User,
)

router = APIRouter()


# Case Templates CRUD
@router.get("/case-templates", response_model=CaseTemplatesPublic)
def get_case_templates(
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    case_type: CaseType | None = Query(None),
    is_public: bool | None = Query(None),
    search: str | None = Query(None),
) -> Any:
    """Get case templates with filtering"""
    
    query = select(CaseTemplate)
    
    # Filter by access permissions
    if current_user.role == UserRole.LAWYER:
        query = query.where(
            or_(
                CaseTemplate.created_by == current_user.id,
                CaseTemplate.is_public == True
            )
        )
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        # Only public templates for clients and assistants
        query = query.where(CaseTemplate.is_public == True)
    
    # Apply filters
    if case_type:
        query = query.where(CaseTemplate.case_type == case_type)
    
    if is_public is not None:
        query = query.where(CaseTemplate.is_public == is_public)
    
    if search:
        search_filter = f"%{search}%"
        query = query.where(
            or_(
                CaseTemplate.name.ilike(search_filter),
                CaseTemplate.description.ilike(search_filter)
            )
        )
    
    # Filter active templates
    query = query.where(CaseTemplate.is_active == True)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()
    
    # Apply pagination and ordering
    query = query.order_by(desc(CaseTemplate.created_at)).offset(skip).limit(limit)
    
    templates = session.exec(query).all()
    
    # Load creator information
    template_data = []
    for template in templates:
        creator = session.get(User, template.created_by)
        template_public = CaseTemplatePublic.model_validate(template)
        if creator:
            template_public.creator = creator
        template_data.append(template_public)
    
    return CaseTemplatesPublic(data=template_data, count=total_count)


@router.post("/case-templates", response_model=CaseTemplatePublic)
def create_case_template(
    template_in: CaseTemplateCreate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Create a new case template"""
    
    # Only lawyers and admins can create templates
    if current_user.role not in [UserRole.LAWYER, UserRole.ADMIN]:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    # Create template
    template = CaseTemplate.model_validate(
        template_in,
        update={"created_by": current_user.id}
    )
    
    session.add(template)
    session.commit()
    session.refresh(template)
    
    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = CaseTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator
    
    return template_public


@router.get("/case-templates/{template_id}", response_model=CaseTemplatePublic)
def get_case_template(
    template_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Get a specific case template"""
    
    template = session.get(CaseTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    # Check access permissions
    if not template.is_public and template.created_by != current_user.id:
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = CaseTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator
    
    return template_public


@router.patch("/case-templates/{template_id}", response_model=CaseTemplatePublic)
def update_case_template(
    template_id: uuid.UUID,
    template_in: CaseTemplateUpdate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Update a case template"""
    
    template = session.get(CaseTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    # Check permissions
    if template.created_by != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    # Update template
    template_data = template_in.model_dump(exclude_unset=True)
    template_data["updated_at"] = datetime.utcnow()
    
    for field, value in template_data.items():
        setattr(template, field, value)
    
    session.add(template)
    session.commit()
    session.refresh(template)
    
    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = CaseTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator
    
    return template_public


@router.delete("/case-templates/{template_id}")
def delete_case_template(
    template_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Delete a case template"""
    
    template = session.get(CaseTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    # Check permissions
    if template.created_by != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    session.delete(template)
    session.commit()
    
    return {"message": "Template deleted successfully"}


@router.post("/case-templates/{template_id}/apply/{case_id}")
def apply_case_template(
    template_id: uuid.UUID,
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Apply a case template to an existing case"""
    
    # Get template
    template = session.get(CaseTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    # Check template access
    if not template.is_public and template.created_by != current_user.id:
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Template access denied")
    
    # Get case
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check case access
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Case access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Case access denied")
    
    # Apply template
    result = apply_template_to_case(session, template, case, current_user)
    
    return result


@router.post("/case-templates/{template_id}/create-case", response_model=Dict[str, Any])
def create_case_from_template(
    template_id: uuid.UUID,
    case_data: Dict[str, Any],
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Create a new case from a template"""
    
    # Get template
    template = session.get(CaseTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    # Check template access
    if not template.is_public and template.created_by != current_user.id:
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Template access denied")
    
    # Only lawyers can create cases
    if current_user.role != UserRole.LAWYER:
        raise HTTPException(status_code=403, detail="Only lawyers can create cases")
    
    # Create case from template
    result = create_case_from_template_data(session, template, case_data, current_user)
    
    return result


def apply_template_to_case(session: Session, template: CaseTemplate, case: LegalCase, user: LawyerScopedUser) -> Dict[str, Any]:
    """Apply template configuration to an existing case"""
    
    template_data = template.template_data
    applied_items = []
    
    # Apply milestones from template
    if "milestones" in template_data:
        for milestone_data in template_data["milestones"]:
            milestone = CaseMilestone(
                case_id=case.id,
                title=milestone_data.get("title", ""),
                description=milestone_data.get("description"),
                milestone_type=milestone_data.get("type", "custom"),
                status="not_started",
                order_index=milestone_data.get("order", 0),
                is_required=milestone_data.get("required", False),
                estimated_hours=milestone_data.get("estimated_hours"),
                assigned_to=milestone_data.get("assigned_to"),
                target_date=milestone_data.get("target_date"),
                created_by=user.id
            )
            session.add(milestone)
            applied_items.append(f"Milestone: {milestone.title}")
    
    # Apply deadlines from template
    if "deadlines" in template_data:
        for deadline_data in template_data["deadlines"]:
            deadline = CaseDeadline(
                case_id=case.id,
                title=deadline_data.get("title", ""),
                description=deadline_data.get("description"),
                deadline_type=deadline_data.get("type", "other"),
                deadline_date=deadline_data.get("deadline_date"),
                is_critical=deadline_data.get("critical", False),
                reminder_days=deadline_data.get("reminder_days", 7),
                assigned_to=deadline_data.get("assigned_to"),
                created_by=user.id
            )
            session.add(deadline)
            applied_items.append(f"Deadline: {deadline.title}")
    
    # Log activity
    activity = CaseActivity(
        case_id=case.id,
        activity_type=ActivityType.MILESTONES_CREATED_FROM_TEMPLATE.value,
        description=f"Applied template '{template.name}' to case",
        user_id=user.id,
        activity_metadata={
            "template_id": str(template.id),
            "template_name": template.name,
            "applied_items": applied_items
        }
    )
    session.add(activity)
    
    session.commit()
    
    return {
        "message": f"Template '{template.name}' applied successfully",
        "applied_items": applied_items,
        "template_id": str(template.id),
        "case_id": str(case.id)
    }


def create_case_from_template_data(session: Session, template: CaseTemplate, case_data: Dict[str, Any], user: LawyerScopedUser) -> Dict[str, Any]:
    """Create a new case from template with provided data"""
    
    # Create the case
    case_create_data = {
        "title": case_data.get("title", f"New {template.case_type} Case"),
        "client_name": case_data.get("client_name", ""),
        "case_type": template.case_type,
        "description": case_data.get("description", template.description),
        "lawyer_id": user.id
    }
    
    case = LegalCase.model_validate(case_create_data)
    session.add(case)
    session.flush()  # Get the case ID
    
    # Apply template to the new case
    result = apply_template_to_case(session, template, case, user)
    
    # Add case creation activity
    activity = CaseActivity(
        case_id=case.id,
        activity_type=ActivityType.CASE_CREATED.value,
        description=f"Case created from template '{template.name}'",
        user_id=user.id,
        activity_metadata={
            "template_id": str(template.id),
            "template_name": template.name
        }
    )
    session.add(activity)
    
    session.commit()
    session.refresh(case)
    
    return {
        "message": f"Case created successfully from template '{template.name}'",
        "case_id": str(case.id),
        "case_title": case.title,
        "template_applied": result
    }


# Document Templates CRUD
@router.get("/document-templates", response_model=DocumentTemplatesPublic)
def get_document_templates(
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    case_type: CaseType | None = Query(None),
    document_type: str | None = Query(None),
    is_public: bool | None = Query(None),
    search: str | None = Query(None),
) -> Any:
    """Get document templates with filtering"""

    query = select(DocumentTemplate)

    # Filter by access permissions
    if current_user.role == UserRole.LAWYER:
        query = query.where(
            or_(
                DocumentTemplate.created_by == current_user.id,
                DocumentTemplate.is_public == True
            )
        )
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        # Only public templates for clients and assistants
        query = query.where(DocumentTemplate.is_public == True)

    # Apply filters
    if case_type:
        query = query.where(
            or_(
                DocumentTemplate.case_type == case_type,
                DocumentTemplate.case_type.is_(None)  # Universal templates
            )
        )

    if document_type:
        query = query.where(DocumentTemplate.document_type == document_type)

    if is_public is not None:
        query = query.where(DocumentTemplate.is_public == is_public)

    if search:
        search_filter = f"%{search}%"
        query = query.where(
            or_(
                DocumentTemplate.name.ilike(search_filter),
                DocumentTemplate.description.ilike(search_filter),
                DocumentTemplate.document_type.ilike(search_filter)
            )
        )

    # Filter active templates
    query = query.where(DocumentTemplate.is_active == True)

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()

    # Apply pagination and ordering
    query = query.order_by(desc(DocumentTemplate.created_at)).offset(skip).limit(limit)

    templates = session.exec(query).all()

    # Load creator information
    template_data = []
    for template in templates:
        creator = session.get(User, template.created_by)
        template_public = DocumentTemplatePublic.model_validate(template)
        if creator:
            template_public.creator = creator
        template_data.append(template_public)

    return DocumentTemplatesPublic(data=template_data, count=total_count)


@router.post("/document-templates", response_model=DocumentTemplatePublic)
def create_document_template(
    template_in: DocumentTemplateCreate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Create a new document template"""

    # Only lawyers and admins can create templates
    if current_user.role not in [UserRole.LAWYER, UserRole.ADMIN]:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # Create template
    template = DocumentTemplate.model_validate(
        template_in,
        update={"created_by": current_user.id}
    )

    session.add(template)
    session.commit()
    session.refresh(template)

    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = DocumentTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator

    return template_public


@router.get("/document-templates/{template_id}", response_model=DocumentTemplatePublic)
def get_document_template(
    template_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Get a specific document template"""

    template = session.get(DocumentTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check access permissions
    if not template.is_public and template.created_by != current_user.id:
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Access denied")

    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = DocumentTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator

    return template_public


@router.patch("/document-templates/{template_id}", response_model=DocumentTemplatePublic)
def update_document_template(
    template_id: uuid.UUID,
    template_in: DocumentTemplateUpdate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Update a document template"""

    template = session.get(DocumentTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check permissions
    if template.created_by != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # Update template
    template_data = template_in.model_dump(exclude_unset=True)
    template_data["updated_at"] = datetime.utcnow()

    for field, value in template_data.items():
        setattr(template, field, value)

    session.add(template)
    session.commit()
    session.refresh(template)

    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = DocumentTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator

    return template_public


@router.delete("/document-templates/{template_id}")
def delete_document_template(
    template_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Delete a document template"""

    template = session.get(DocumentTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check permissions
    if template.created_by != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    session.delete(template)
    session.commit()

    return {"message": "Document template deleted successfully"}


@router.post("/document-templates/{template_id}/generate")
def generate_document_from_template(
    template_id: uuid.UUID,
    placeholder_data: Dict[str, Any],
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Generate a document from a template with placeholder data"""

    # Get template
    template = session.get(DocumentTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check template access
    if not template.is_public and template.created_by != current_user.id:
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Template access denied")

    # Generate document content
    generated_content = generate_document_content(template, placeholder_data)

    return {
        "template_id": str(template.id),
        "template_name": template.name,
        "generated_content": generated_content,
        "placeholders_used": list(placeholder_data.keys()),
        "generated_at": datetime.utcnow().isoformat()
    }


def generate_document_content(template: DocumentTemplate, placeholder_data: Dict[str, Any]) -> str:
    """Generate document content by replacing placeholders"""

    content = template.template_content

    # Replace placeholders in the format {{placeholder_name}}
    for placeholder, value in placeholder_data.items():
        placeholder_pattern = f"{{{{{placeholder}}}}}"
        content = content.replace(placeholder_pattern, str(value))

    return content


# Workflow Templates CRUD
@router.get("/workflow-templates", response_model=WorkflowTemplatesPublic)
def get_workflow_templates(
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    case_type: CaseType | None = Query(None),
    is_public: bool | None = Query(None),
    search: str | None = Query(None),
) -> Any:
    """Get workflow templates with filtering"""

    query = select(WorkflowTemplate)

    # Filter by access permissions
    if current_user.role == UserRole.LAWYER:
        query = query.where(
            or_(
                WorkflowTemplate.created_by == current_user.id,
                WorkflowTemplate.is_public == True
            )
        )
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        # Only public templates for clients and assistants
        query = query.where(WorkflowTemplate.is_public == True)

    # Apply filters
    if case_type:
        query = query.where(WorkflowTemplate.case_type == case_type)

    if is_public is not None:
        query = query.where(WorkflowTemplate.is_public == is_public)

    if search:
        search_filter = f"%{search}%"
        query = query.where(
            or_(
                WorkflowTemplate.name.ilike(search_filter),
                WorkflowTemplate.description.ilike(search_filter)
            )
        )

    # Filter active templates
    query = query.where(WorkflowTemplate.is_active == True)

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()

    # Apply pagination and ordering
    query = query.order_by(desc(WorkflowTemplate.created_at)).offset(skip).limit(limit)

    templates = session.exec(query).all()

    # Load creator information
    template_data = []
    for template in templates:
        creator = session.get(User, template.created_by)
        template_public = WorkflowTemplatePublic.model_validate(template)
        if creator:
            template_public.creator = creator
        template_data.append(template_public)

    return WorkflowTemplatesPublic(data=template_data, count=total_count)


@router.post("/workflow-templates", response_model=WorkflowTemplatePublic)
def create_workflow_template(
    template_in: WorkflowTemplateCreate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Create a new workflow template"""

    # Only lawyers and admins can create templates
    if current_user.role not in [UserRole.LAWYER, UserRole.ADMIN]:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # Create template
    template = WorkflowTemplate.model_validate(
        template_in,
        update={"created_by": current_user.id}
    )

    session.add(template)
    session.commit()
    session.refresh(template)

    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = WorkflowTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator

    return template_public


@router.get("/workflow-templates/{template_id}", response_model=WorkflowTemplatePublic)
def get_workflow_template(
    template_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Get a specific workflow template"""

    template = session.get(WorkflowTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check access permissions
    if not template.is_public and template.created_by != current_user.id:
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Access denied")

    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = WorkflowTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator

    return template_public


@router.patch("/workflow-templates/{template_id}", response_model=WorkflowTemplatePublic)
def update_workflow_template(
    template_id: uuid.UUID,
    template_in: WorkflowTemplateUpdate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Update a workflow template"""

    template = session.get(WorkflowTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check permissions
    if template.created_by != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # Update template
    template_data = template_in.model_dump(exclude_unset=True)
    template_data["updated_at"] = datetime.utcnow()

    for field, value in template_data.items():
        setattr(template, field, value)

    session.add(template)
    session.commit()
    session.refresh(template)

    # Load creator information
    creator = session.get(User, template.created_by)
    template_public = WorkflowTemplatePublic.model_validate(template)
    if creator:
        template_public.creator = creator

    return template_public


@router.delete("/workflow-templates/{template_id}")
def delete_workflow_template(
    template_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> Any:
    """Delete a workflow template"""

    template = session.get(WorkflowTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check permissions
    if template.created_by != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    session.delete(template)
    session.commit()

    return {"message": "Workflow template deleted successfully"}
