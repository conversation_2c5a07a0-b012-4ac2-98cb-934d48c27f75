"""
Advanced Notification Management API Routes

This module provides API endpoints for advanced notification management,
including preferences, templates, rules, and delivery tracking.
"""

import uuid
from datetime import datetime
from typing import Any, Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from sqlmodel import Session, select, func, and_, desc

from app.api.deps import (
    CurrentUser,
    SessionDep,
    get_current_active_superuser,
    get_current_user,
)
from app.models import (
    # User models
    User,
    UserRole,
    
    # Notification models
    Notification,
    NotificationCreate,
    NotificationUpdate,
    NotificationPublic,
    NotificationsPublic,
    NotificationType,
    NotificationPriority,
    NotificationChannel,
    
    # Preferences models
    NotificationPreferences,
    NotificationPreferencesCreate,
    NotificationPreferencesUpdate,
    NotificationPreferencesPublic,
    UserNotificationPreferencesPublic,
    
    # Template models
    NotificationTemplate,
    NotificationTemplateCreate,
    NotificationTemplateUpdate,
    NotificationTemplatePublic,
    NotificationTemplatesPublic,
    
    # Delivery models
    NotificationDelivery,
    NotificationDeliveryPublic,
    NotificationDeliveryStatus,
    
    # Rule models
    NotificationRule,
    NotificationRuleCreate,
    NotificationRuleUpdate,
    NotificationRulePublic,
    NotificationRulesPublic,
)
from app.services.notification_service import NotificationService
from app.services.email_notification_service import EmailNotificationService

router = APIRouter()


# Notification Management
@router.get("/", response_model=NotificationsPublic)
def get_notifications(
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    is_read: bool | None = Query(None),
    notification_type: NotificationType | None = Query(None),
    priority: NotificationPriority | None = Query(None),
) -> Any:
    """Get notifications for the current user"""
    
    # Base query for user's notifications
    query = select(Notification).where(Notification.user_id == current_user.id)
    
    # Apply filters
    if is_read is not None:
        query = query.where(Notification.is_read == is_read)
    
    if notification_type:
        query = query.where(Notification.notification_type == notification_type)
    
    if priority:
        query = query.where(Notification.priority == priority)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()
    
    # Get unread count
    unread_query = select(func.count()).where(
        and_(
            Notification.user_id == current_user.id,
            Notification.is_read == False
        )
    )
    unread_count = session.exec(unread_query).one()
    
    # Apply pagination and ordering
    query = query.order_by(desc(Notification.created_at)).offset(skip).limit(limit)
    notifications = session.exec(query).all()
    
    # Convert to public format
    notification_data = []
    for notification in notifications:
        notification_public = NotificationPublic.model_validate(notification)
        notification_data.append(notification_public)
    
    return NotificationsPublic(
        data=notification_data,
        count=total_count,
        unread_count=unread_count
    )


@router.patch("/{notification_id}", response_model=NotificationPublic)
def update_notification(
    notification_id: uuid.UUID,
    notification_update: NotificationUpdate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Update a notification (mark as read/unread)"""
    
    notification = session.get(Notification, notification_id)
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    # Check ownership
    if notification.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Update fields
    update_data = notification_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(notification, field, value)
    
    # Set read_at timestamp if marking as read
    if notification_update.is_read is True and not notification.read_at:
        notification.read_at = datetime.utcnow()
    elif notification_update.is_read is False:
        notification.read_at = None
    
    session.add(notification)
    session.commit()
    session.refresh(notification)
    
    return NotificationPublic.model_validate(notification)


@router.post("/mark-all-read")
def mark_all_notifications_read(
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Mark all notifications as read for the current user"""
    
    # Update all unread notifications
    notifications = session.exec(
        select(Notification).where(
            and_(
                Notification.user_id == current_user.id,
                Notification.is_read == False
            )
        )
    ).all()
    
    for notification in notifications:
        notification.is_read = True
        notification.read_at = datetime.utcnow()
        session.add(notification)
    
    session.commit()
    
    return {"message": f"Marked {len(notifications)} notifications as read"}


# User Notification Preferences
@router.get("/preferences", response_model=UserNotificationPreferencesPublic)
def get_notification_preferences(
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Get notification preferences for the current user"""
    
    preferences = session.exec(
        select(NotificationPreferences).where(
            NotificationPreferences.user_id == current_user.id
        )
    ).all()
    
    # Create default preferences for missing notification types
    existing_types = {pref.notification_type for pref in preferences}
    all_types = set(NotificationType)
    missing_types = all_types - existing_types
    
    for notification_type in missing_types:
        default_pref = NotificationPreferences(
            user_id=current_user.id,
            notification_type=notification_type,
            channels=[NotificationChannel.IN_APP],
            is_enabled=True,
            frequency="immediate",
            priority_threshold=NotificationPriority.LOW
        )
        session.add(default_pref)
        preferences.append(default_pref)
    
    if missing_types:
        session.commit()
    
    preference_data = [
        NotificationPreferencesPublic.model_validate(pref) for pref in preferences
    ]
    
    return UserNotificationPreferencesPublic(
        data=preference_data,
        count=len(preference_data)
    )


@router.post("/preferences", response_model=NotificationPreferencesPublic)
def create_notification_preference(
    preference_in: NotificationPreferencesCreate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Create or update notification preference for a specific type"""
    
    # Check if preference already exists
    existing_pref = session.exec(
        select(NotificationPreferences).where(
            and_(
                NotificationPreferences.user_id == current_user.id,
                NotificationPreferences.notification_type == preference_in.notification_type
            )
        )
    ).first()
    
    if existing_pref:
        # Update existing preference
        update_data = preference_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(existing_pref, field, value)
        
        existing_pref.updated_at = datetime.utcnow()
        session.add(existing_pref)
        session.commit()
        session.refresh(existing_pref)
        
        return NotificationPreferencesPublic.model_validate(existing_pref)
    
    # Create new preference
    preference = NotificationPreferences.model_validate(
        preference_in,
        update={"user_id": current_user.id}
    )
    
    session.add(preference)
    session.commit()
    session.refresh(preference)
    
    return NotificationPreferencesPublic.model_validate(preference)


@router.patch("/preferences/{preference_id}", response_model=NotificationPreferencesPublic)
def update_notification_preference(
    preference_id: uuid.UUID,
    preference_update: NotificationPreferencesUpdate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Update a notification preference"""
    
    preference = session.get(NotificationPreferences, preference_id)
    if not preference:
        raise HTTPException(status_code=404, detail="Preference not found")
    
    # Check ownership
    if preference.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Update fields
    update_data = preference_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(preference, field, value)
    
    preference.updated_at = datetime.utcnow()
    
    session.add(preference)
    session.commit()
    session.refresh(preference)
    
    return NotificationPreferencesPublic.model_validate(preference)


# Email Tracking
@router.get("/track/open")
def track_email_open(
    session: SessionDep,
    delivery_id: uuid.UUID = Query(...),
) -> Response:
    """Track email open event (1x1 pixel endpoint)"""
    
    try:
        email_service = EmailNotificationService(session)
        email_service.track_email_open(delivery_id)
    except Exception:
        pass  # Fail silently for tracking
    
    # Return 1x1 transparent pixel
    pixel_data = b'\x47\x49\x46\x38\x39\x61\x01\x00\x01\x00\x80\x00\x00\x00\x00\x00\x00\x00\x00\x21\xF9\x04\x01\x00\x00\x00\x00\x2C\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02\x04\x01\x00\x3B'
    
    return Response(
        content=pixel_data,
        media_type="image/gif",
        headers={"Cache-Control": "no-cache, no-store, must-revalidate"}
    )


@router.get("/track/click")
def track_email_click(
    session: SessionDep,
    delivery_id: uuid.UUID = Query(...),
    url: str = Query(...),
) -> Response:
    """Track email click event and redirect"""
    
    try:
        email_service = EmailNotificationService(session)
        email_service.track_email_click(delivery_id, url)
    except Exception:
        pass  # Fail silently for tracking
    
    # Redirect to original URL
    return Response(
        status_code=302,
        headers={"Location": url}
    )


# Admin-only endpoints for templates and rules
@router.get("/templates", response_model=NotificationTemplatesPublic)
def get_notification_templates(
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    notification_type: NotificationType | None = Query(None),
    channel: NotificationChannel | None = Query(None),
    is_active: bool | None = Query(None),
) -> Any:
    """Get notification templates (admin only)"""
    
    # Only admins can manage templates
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    # Build query
    query = select(NotificationTemplate)
    
    # Apply filters
    if notification_type:
        query = query.where(NotificationTemplate.notification_type == notification_type)
    
    if channel:
        query = query.where(NotificationTemplate.channel == channel)
    
    if is_active is not None:
        query = query.where(NotificationTemplate.is_active == is_active)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()
    
    # Apply pagination and ordering
    query = query.order_by(desc(NotificationTemplate.created_at)).offset(skip).limit(limit)
    templates = session.exec(query).all()
    
    # Convert to public format
    template_data = []
    for template in templates:
        creator = session.get(User, template.created_by)
        template_public = NotificationTemplatePublic.model_validate(template)
        template_public.creator = creator
        template_data.append(template_public)
    
    return NotificationTemplatesPublic(data=template_data, count=total_count)
