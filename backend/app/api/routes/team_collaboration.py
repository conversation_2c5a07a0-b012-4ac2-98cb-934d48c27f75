"""
Team Collaboration API Routes

This module provides API endpoints for team collaboration features,
including team member management, internal messaging, and task assignment.
"""

import uuid
from datetime import datetime
from typing import Any, Annotated

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, or_, and_, desc

from app.api.deps import (
    CurrentUser,
    SessionDep,
    get_current_active_superuser,
    get_current_user,
)
from app.core.config import settings
from app.models import (
    # User and auth models
    User,
    UserRole,
    UserPublic,
    
    # Case models
    LegalCase,
    
    # Team collaboration models
    CaseTeamMember,
    CaseTeamMemberCreate,
    CaseTeamMemberUpdate,
    CaseTeamMemberPublic,
    CaseTeamMembersPublic,
    TeamMemberRole,
    
    TeamMessage,
    TeamMessageCreate,
    TeamMessageUpdate,
    TeamMessagePublic,
    TeamMessagesPublic,
    TeamMessageType,
    
    TeamMessageReply,
    TeamMessageReplyCreate,
    TeamMessageReplyPublic,
    
    TeamTask,
    TeamTask<PERSON><PERSON>,
    TeamTaskUpdate,
    TeamTaskPublic,
    TeamTasksPublic,
    TaskStatus,
    TaskPriority,
    
    # Activity models
    CaseActivity,
    ActivityType,
)

router = APIRouter()


# Team Member Management
@router.get("/cases/{case_id}/team", response_model=CaseTeamMembersPublic)
def get_case_team_members(
    case_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    role: TeamMemberRole | None = Query(None),
    is_active: bool | None = Query(None),
) -> Any:
    """Get team members for a specific case"""
    
    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check if user has access to this case (lawyer, team member, or admin)
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            _is_team_member(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Build query
    query = select(CaseTeamMember).where(CaseTeamMember.case_id == case_id)
    
    # Apply filters
    if role:
        query = query.where(CaseTeamMember.role == role)
    
    if is_active is not None:
        query = query.where(CaseTeamMember.is_active == is_active)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()
    
    # Apply pagination and ordering
    query = query.order_by(CaseTeamMember.assigned_at).offset(skip).limit(limit)
    team_members = session.exec(query).all()
    
    # Convert to public format with user info
    team_data = []
    for member in team_members:
        user = session.get(User, member.user_id)
        assigner = session.get(User, member.assigned_by)
        
        member_public = CaseTeamMemberPublic.model_validate(member)
        member_public.user = user
        member_public.assigner = assigner
        
        team_data.append(member_public)
    
    return CaseTeamMembersPublic(data=team_data, count=total_count)


@router.post("/cases/{case_id}/team", response_model=CaseTeamMemberPublic)
def add_team_member(
    case_id: uuid.UUID,
    member_in: CaseTeamMemberCreate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Add a team member to a case"""
    
    # Verify case exists and user has permission to manage team
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Only case lawyer, admins, or lead lawyers can add team members
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            _is_lead_lawyer(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    # Verify user to be added exists
    user_to_add = session.get(User, member_in.user_id)
    if not user_to_add:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Check if user is already a team member
    existing_member = session.exec(
        select(CaseTeamMember).where(
            and_(
                CaseTeamMember.case_id == case_id,
                CaseTeamMember.user_id == member_in.user_id
            )
        )
    ).first()
    
    if existing_member:
        if existing_member.is_active:
            raise HTTPException(status_code=400, detail="User is already a team member")
        else:
            # Reactivate existing member
            existing_member.is_active = True
            existing_member.role = member_in.role
            existing_member.permissions = member_in.permissions
            existing_member.notes = member_in.notes
            existing_member.assigned_by = current_user.id
            existing_member.assigned_at = datetime.utcnow()
            
            session.add(existing_member)
            session.commit()
            session.refresh(existing_member)
            
            team_member = existing_member
    else:
        # Create new team member
        team_member = CaseTeamMember.model_validate(
            member_in,
            update={
                "case_id": case_id,
                "assigned_by": current_user.id
            }
        )
        
        session.add(team_member)
        session.commit()
        session.refresh(team_member)
    
    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.USER_ASSIGNED,
        description=f"Added {user_to_add.full_name} to team as {member_in.role.value.replace('_', ' ').title()}",
        user_id=current_user.id,
        activity_metadata={
            "team_member_id": str(team_member.id),
            "assigned_user_id": str(member_in.user_id),
            "role": member_in.role.value
        }
    )
    session.add(activity)
    session.commit()
    
    # Load related data for response
    user = session.get(User, team_member.user_id)
    assigner = session.get(User, team_member.assigned_by)
    
    member_public = CaseTeamMemberPublic.model_validate(team_member)
    member_public.user = user
    member_public.assigner = assigner
    
    return member_public


@router.patch("/cases/{case_id}/team/{member_id}", response_model=CaseTeamMemberPublic)
def update_team_member(
    case_id: uuid.UUID,
    member_id: uuid.UUID,
    member_update: CaseTeamMemberUpdate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Update a team member's role or permissions"""
    
    # Verify case exists and user has permission
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Only case lawyer, admins, or lead lawyers can update team members
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            _is_lead_lawyer(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    # Get team member
    team_member = session.get(CaseTeamMember, member_id)
    if not team_member or team_member.case_id != case_id:
        raise HTTPException(status_code=404, detail="Team member not found")
    
    # Update fields
    update_data = member_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(team_member, field, value)
    
    session.add(team_member)
    session.commit()
    session.refresh(team_member)
    
    # Log activity
    user = session.get(User, team_member.user_id)
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.USER_ASSIGNED,
        description=f"Updated team member {user.full_name if user else 'Unknown'} role/permissions",
        user_id=current_user.id,
        activity_metadata={
            "team_member_id": str(team_member.id),
            "updated_fields": list(update_data.keys())
        }
    )
    session.add(activity)
    session.commit()
    
    # Load related data for response
    assigner = session.get(User, team_member.assigned_by)
    
    member_public = CaseTeamMemberPublic.model_validate(team_member)
    member_public.user = user
    member_public.assigner = assigner
    
    return member_public


@router.delete("/cases/{case_id}/team/{member_id}")
def remove_team_member(
    case_id: uuid.UUID,
    member_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Remove a team member from a case"""
    
    # Verify case exists and user has permission
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Only case lawyer, admins, or lead lawyers can remove team members
    if not (current_user.is_superuser or 
            case.lawyer_id == current_user.id or
            _is_lead_lawyer(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Permission denied")
    
    # Get team member
    team_member = session.get(CaseTeamMember, member_id)
    if not team_member or team_member.case_id != case_id:
        raise HTTPException(status_code=404, detail="Team member not found")
    
    # Don't allow removing the case lawyer
    if team_member.user_id == case.lawyer_id:
        raise HTTPException(status_code=400, detail="Cannot remove case lawyer from team")
    
    # Deactivate instead of deleting to maintain history
    team_member.is_active = False
    session.add(team_member)
    session.commit()
    
    # Log activity
    user = session.get(User, team_member.user_id)
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.USER_ASSIGNED,
        description=f"Removed {user.full_name if user else 'Unknown'} from team",
        user_id=current_user.id,
        activity_metadata={
            "team_member_id": str(team_member.id),
            "removed_user_id": str(team_member.user_id)
        }
    )
    session.add(activity)
    session.commit()
    
    return {"message": "Team member removed successfully"}


# Helper functions
def _is_team_member(session: Session, case_id: uuid.UUID, user_id: uuid.UUID) -> bool:
    """Check if user is a team member of the case"""
    team_member = session.exec(
        select(CaseTeamMember).where(
            and_(
                CaseTeamMember.case_id == case_id,
                CaseTeamMember.user_id == user_id,
                CaseTeamMember.is_active == True
            )
        )
    ).first()
    return team_member is not None


def _is_lead_lawyer(session: Session, case_id: uuid.UUID, user_id: uuid.UUID) -> bool:
    """Check if user is a lead lawyer on the case"""
    team_member = session.exec(
        select(CaseTeamMember).where(
            and_(
                CaseTeamMember.case_id == case_id,
                CaseTeamMember.user_id == user_id,
                CaseTeamMember.role == TeamMemberRole.LEAD_LAWYER,
                CaseTeamMember.is_active == True
            )
        )
    ).first()
    return team_member is not None


# Team Messaging
@router.get("/cases/{case_id}/messages", response_model=TeamMessagesPublic)
def get_team_messages(
    case_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    message_type: TeamMessageType | None = Query(None),
    is_pinned: bool | None = Query(None),
    search: str | None = Query(None),
) -> Any:
    """Get team messages for a specific case"""

    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    # Check if user has access to this case
    if not (current_user.is_superuser or
            case.lawyer_id == current_user.id or
            _is_team_member(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Access denied")

    # Build query
    query = select(TeamMessage).where(TeamMessage.case_id == case_id)

    # Apply filters
    if message_type:
        query = query.where(TeamMessage.message_type == message_type)

    if is_pinned is not None:
        query = query.where(TeamMessage.is_pinned == is_pinned)

    if search:
        search_filter = f"%{search}%"
        query = query.where(
            or_(
                TeamMessage.subject.ilike(search_filter),
                TeamMessage.content.ilike(search_filter)
            )
        )

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_count = session.exec(count_query).one()

    # Apply pagination and ordering (pinned first, then by date)
    query = query.order_by(desc(TeamMessage.is_pinned), desc(TeamMessage.created_at)).offset(skip).limit(limit)
    messages = session.exec(query).all()

    # Convert to public format with user and case info
    message_data = []
    for message in messages:
        sender = session.get(User, message.sender_id)

        # Count replies
        replies_count = session.exec(
            select(func.count(TeamMessageReply.id)).where(
                TeamMessageReply.parent_message_id == message.id
            )
        ).one()

        message_public = TeamMessagePublic.model_validate(message)
        message_public.sender = sender
        message_public.case = case
        message_public.replies_count = replies_count
        message_public.is_read = True  # TODO: Implement read tracking

        message_data.append(message_public)

    return TeamMessagesPublic(
        data=message_data,
        count=total_count,
        unread_count=0  # TODO: Implement unread tracking
    )


@router.post("/cases/{case_id}/messages", response_model=TeamMessagePublic)
def send_team_message(
    case_id: uuid.UUID,
    message_in: TeamMessageCreate,
    current_user: CurrentUser,
    session: SessionDep,
) -> Any:
    """Send a team message for a case"""

    # Verify case exists and user has access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    # Check if user has access to this case
    if not (current_user.is_superuser or
            case.lawyer_id == current_user.id or
            _is_team_member(session, case_id, current_user.id)):
        raise HTTPException(status_code=403, detail="Access denied")

    # Create message
    message = TeamMessage.model_validate(
        message_in,
        update={
            "case_id": case_id,
            "sender_id": current_user.id
        }
    )

    session.add(message)
    session.commit()
    session.refresh(message)

    # TODO: Create notifications for mentioned users
    # TODO: Create notifications for urgent messages

    # Load related data for response
    sender = session.get(User, message.sender_id)

    message_public = TeamMessagePublic.model_validate(message)
    message_public.sender = sender
    message_public.case = case
    message_public.replies_count = 0
    message_public.is_read = True

    return message_public
