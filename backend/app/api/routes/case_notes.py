from datetime import datetime
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, desc, asc

from app.api.deps import LawyerScopedUser
from app.core.db import get_session
from app.models import (
    CaseNote,
    CaseNoteCreate,
    CaseNoteUpdate,
    CaseNotePublic,
    CaseNotesPublic,
    LegalCase,
    User,
    UserRole,
    NoteType,
    NoteCategory,
    ActivityType,
    CaseActivity,
)

router = APIRouter()


def check_note_access(note: CaseN<PERSON>, user: LawyerScopedUser) -> bool:
    """Check if user has access to read a note based on note type and user role"""
    if user.role == UserRole.ADMIN:
        return True
    
    # Author can always access their own notes
    if note.author_id == user.id:
        return True
    
    # Check note type permissions
    if note.note_type == "private":
        return False  # Only author can see private notes
    elif note.note_type == "team":
        # Team notes: lawyers and assistants on the same case
        return user.role in [UserRole.LAWYER, UserRole.ASSISTANT]
    elif note.note_type == "client":
        # Client notes: visible to clients, lawyers, and assistants
        return True
    elif note.note_type == "admin":
        # Admin notes: only admins and lawyers
        return user.role in [UserRole.ADMIN, UserRole.LAWYER]
    
    return False


def check_note_edit_access(note: CaseNote, user: LawyerScopedUser) -> bool:
    """Check if user can edit/delete a note"""
    if user.role == UserRole.ADMIN:
        return True
    
    # Author can edit their own notes
    if note.author_id == user.id:
        return True
    
    # Lawyers can edit team and client notes
    if user.role == UserRole.LAWYER and note.note_type in ["team", "client"]:
        return True
    
    return False


@router.get("/{case_id}/notes")
def get_case_notes(
    case_id: UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    note_type: NoteType | None = Query(None),
    category: NoteCategory | None = Query(None),
    search: str | None = Query(None),
    sort_by: str = Query("created_at", regex="^(created_at|updated_at|category)$"),
    sort_order: str = Query("desc", regex="^(asc|desc)$"),
) -> CaseNotesPublic:
    """Get notes for a specific case with filtering and pagination"""
    
    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Apply role-based access control for case
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Build notes query
    query = select(CaseNote, User).join(User, CaseNote.author_id == User.id).where(
        CaseNote.case_id == case_id
    )
    
    # Apply note type filtering based on user permissions
    if current_user.role == UserRole.CLIENT:
        # Clients can only see client and team notes
        query = query.where(CaseNote.note_type.in_(["client", "team"]))
    elif current_user.role == UserRole.ASSISTANT:
        # Assistants can see team, client notes, and their own private notes
        query = query.where(
            (CaseNote.note_type.in_(["team", "client"])) |
            ((CaseNote.note_type == "private") & (CaseNote.author_id == current_user.id))
        )
    elif current_user.role == UserRole.LAWYER:
        # Lawyers can see all notes except other lawyers' private notes
        query = query.where(
            (CaseNote.note_type.in_(["team", "client", "admin"])) |
            ((CaseNote.note_type == "private") & (CaseNote.author_id == current_user.id))
        )
    # Admins can see all notes (no additional filtering)
    
    # Apply additional filters
    if note_type:
        query = query.where(CaseNote.note_type == note_type.value)
    if category:
        query = query.where(CaseNote.category == category.value)
    if search:
        query = query.where(CaseNote.content.contains(search))
    
    # Apply sorting
    sort_column = getattr(CaseNote, sort_by)
    if sort_order == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    # Get total count
    count_query = select(func.count()).select_from(
        query.subquery()
    )
    total_count = session.exec(count_query).one()
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    results = session.exec(query).all()
    
    # Build response
    notes = []
    for note, author in results:
        # Count replies for this note
        replies_count = session.exec(
            select(func.count()).where(CaseNote.parent_note_id == note.id)
        ).one()
        
        note_public = CaseNotePublic(
            **note.model_dump(),
            author=author,
            replies_count=replies_count,
            can_edit=check_note_edit_access(note, current_user),
            can_delete=check_note_edit_access(note, current_user),
        )
        notes.append(note_public)
    
    return CaseNotesPublic(data=notes, count=total_count)


@router.post("/{case_id}/notes")
def create_case_note(
    case_id: UUID,
    note_data: CaseNoteCreate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseNotePublic:
    """Create a new note for a case"""
    
    # Verify case exists and user has access
    case_query = select(LegalCase).where(LegalCase.id == case_id)
    case = session.exec(case_query).first()
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Apply role-based access control
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Validate note type permissions
    if note_data.note_type == NoteType.ADMIN and current_user.role not in [UserRole.ADMIN, UserRole.LAWYER]:
        raise HTTPException(status_code=403, detail="Only admins and lawyers can create admin notes")
    
    # Create note
    note = CaseNote(
        case_id=case_id,
        author_id=current_user.id,
        content=note_data.content,
        note_type=note_data.note_type.value,
        category=note_data.category.value,
        is_pinned=note_data.is_pinned,
        tags=note_data.tags,
        mentioned_users=note_data.mentioned_users,
        parent_note_id=note_data.parent_note_id,
    )
    
    session.add(note)
    session.commit()
    session.refresh(note)
    
    # Create activity log
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.NOTE_ADDED.value,
        description=f"Added a {note_data.note_type.value} note in {note_data.category.value} category",
        user_id=current_user.id,
        activity_metadata={
            "note_id": str(note.id),
            "note_type": note_data.note_type.value,
            "category": note_data.category.value,
        }
    )
    session.add(activity)
    session.commit()
    
    # Get author info for response
    author = session.get(User, current_user.id)
    
    return CaseNotePublic(
        **note.model_dump(),
        author=author,
        replies_count=0,
        can_edit=True,
        can_delete=True,
    )


@router.put("/{case_id}/notes/{note_id}")
def update_case_note(
    case_id: UUID,
    note_id: UUID,
    note_data: CaseNoteUpdate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseNotePublic:
    """Update an existing note"""

    # Get note
    note = session.get(CaseNote, note_id)
    if not note or note.case_id != case_id:
        raise HTTPException(status_code=404, detail="Note not found")

    # Check edit permissions
    if not check_note_edit_access(note, current_user):
        raise HTTPException(status_code=403, detail="Access denied")

    # Update note fields
    if note_data.content is not None:
        note.content = note_data.content
    if note_data.note_type is not None:
        note.note_type = note_data.note_type.value
    if note_data.category is not None:
        note.category = note_data.category.value
    if note_data.is_pinned is not None:
        note.is_pinned = note_data.is_pinned
    if note_data.tags is not None:
        note.tags = note_data.tags

    note.updated_at = datetime.utcnow()

    session.add(note)
    session.commit()
    session.refresh(note)

    # Create activity log
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.NOTE_UPDATED.value,
        description=f"Updated a {note.note_type} note",
        user_id=current_user.id,
        activity_metadata={
            "note_id": str(note.id),
            "note_type": note.note_type,
        }
    )
    session.add(activity)
    session.commit()

    # Get author and replies count
    author = session.get(User, note.author_id)
    replies_count = session.exec(
        select(func.count()).where(CaseNote.parent_note_id == note.id)
    ).one()

    return CaseNotePublic(
        **note.model_dump(),
        author=author,
        replies_count=replies_count,
        can_edit=check_note_edit_access(note, current_user),
        can_delete=check_note_edit_access(note, current_user),
    )


@router.delete("/{case_id}/notes/{note_id}")
def delete_case_note(
    case_id: UUID,
    note_id: UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> dict[str, Any]:
    """Delete a note"""

    # Get note
    note = session.get(CaseNote, note_id)
    if not note or note.case_id != case_id:
        raise HTTPException(status_code=404, detail="Note not found")

    # Check delete permissions
    if not check_note_edit_access(note, current_user):
        raise HTTPException(status_code=403, detail="Access denied")

    # Delete all replies first
    replies = session.exec(select(CaseNote).where(CaseNote.parent_note_id == note_id)).all()
    for reply in replies:
        session.delete(reply)

    # Delete the note
    session.delete(note)
    session.commit()

    # Create activity log
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.NOTE_DELETED.value,
        description=f"Deleted a {note.note_type} note",
        user_id=current_user.id,
        activity_metadata={
            "note_id": str(note.id),
            "note_type": note.note_type,
        }
    )
    session.add(activity)
    session.commit()

    return {"message": "Note deleted successfully"}


@router.get("/{case_id}/notes/{note_id}/replies")
def get_note_replies(
    case_id: UUID,
    note_id: UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=50),
) -> CaseNotesPublic:
    """Get replies to a specific note"""

    # Verify parent note exists and user has access
    parent_note = session.get(CaseNote, note_id)
    if not parent_note or parent_note.case_id != case_id:
        raise HTTPException(status_code=404, detail="Note not found")

    if not check_note_access(parent_note, current_user):
        raise HTTPException(status_code=403, detail="Access denied")

    # Get replies
    query = select(CaseNote, User).join(User, CaseNote.author_id == User.id).where(
        CaseNote.parent_note_id == note_id
    ).order_by(asc(CaseNote.created_at))

    # Get total count
    count_query = select(func.count()).where(CaseNote.parent_note_id == note_id)
    total_count = session.exec(count_query).one()

    # Apply pagination
    query = query.offset(skip).limit(limit)
    results = session.exec(query).all()

    # Build response
    replies = []
    for reply, author in results:
        if check_note_access(reply, current_user):
            reply_public = CaseNotePublic(
                **reply.model_dump(),
                author=author,
                replies_count=0,  # Replies don't have sub-replies
                can_edit=check_note_edit_access(reply, current_user),
                can_delete=check_note_edit_access(reply, current_user),
            )
            replies.append(reply_public)

    return CaseNotesPublic(data=replies, count=total_count)
