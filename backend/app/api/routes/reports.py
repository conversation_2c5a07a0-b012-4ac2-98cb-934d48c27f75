import uuid
from datetime import datetime, timedelta, date
from typing import Any, Annotated, Dict, List

from fastapi import APIRouter, Depends, Query
from sqlmodel import Session, select, func, and_, or_

from app.api.deps import LawyerScopedUser, SessionDep
from app.core.db import get_session
from app.models import (
    LegalCase,
    User,
    CaseDocument,
    CaseActivity,
    DocumentCaseLink,
    CaseNote,
    UserRole,
)

router = APIRouter()


@router.get("/dashboard")
def get_dashboard_report(
    current_user: LawyerScopedUser,
    session: SessionDep,
    start_date: date | None = Query(None, description="Start date for filtering"),
    end_date: date | None = Query(None, description="End date for filtering"),
    include_trends: bool = Query(True, description="Include trend calculations"),
) -> Dict[str, Any]:
    """Get comprehensive dashboard report with case statistics, user activity, and document analytics"""
    
    # Set default date range (last 30 days if not specified)
    if not start_date:
        start_date = (datetime.utcnow() - timedelta(days=30)).date()
    if not end_date:
        end_date = datetime.utcnow().date()
    
    # Convert dates to datetime for queries
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    # Base filters based on user role
    if current_user.role == UserRole.ADMIN:
        # Admin sees all data
        case_filter = True
        user_filter = True
    else:
        # Lawyers see only their assigned cases and related data
        case_filter = LegalCase.lawyer_id == current_user.id
        user_filter = or_(
            User.id == current_user.id,
            User.assigned_lawyer_id == current_user.id
        )
    
    # === CASE STATISTICS ===
    case_stats = get_case_statistics(session, case_filter, start_datetime, end_datetime)
    
    # === USER ACTIVITY STATISTICS ===
    user_stats = get_user_activity_statistics(session, user_filter, start_datetime, end_datetime)
    
    # === DOCUMENT STATISTICS ===
    document_stats = get_document_statistics(session, case_filter, start_datetime, end_datetime)
    
    # === ACTIVITY STATISTICS ===
    activity_stats = get_activity_statistics(session, case_filter, start_datetime, end_datetime)
    
    # === PERFORMANCE INDICATORS ===
    performance_indicators = get_performance_indicators(session, case_filter, start_datetime, end_datetime)
    
    # === TRENDS (if requested) ===
    trends = {}
    if include_trends:
        trends = get_trend_analysis(session, case_filter, start_date, end_date)
    
    return {
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "days": (end_date - start_date).days + 1
        },
        "case_statistics": case_stats,
        "user_activity": user_stats,
        "document_analytics": document_stats,
        "activity_analytics": activity_stats,
        "performance_indicators": performance_indicators,
        "trends": trends,
        "generated_at": datetime.utcnow().isoformat(),
        "generated_by": {
            "user_id": current_user.id,
            "user_name": current_user.full_name,
            "user_role": current_user.role
        }
    }


def get_case_statistics(session: Session, case_filter, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
    """Get comprehensive case statistics"""
    
    # Total cases
    total_cases = session.exec(
        select(func.count()).select_from(LegalCase).where(case_filter)
    ).one()
    
    # Cases by status
    status_query = select(LegalCase.status, func.count()).where(case_filter).group_by(LegalCase.status)
    status_results = session.exec(status_query).all()
    cases_by_status = {status: count for status, count in status_results}
    
    # Cases by priority
    priority_query = select(LegalCase.priority, func.count()).where(case_filter).group_by(LegalCase.priority)
    priority_results = session.exec(priority_query).all()
    cases_by_priority = {priority: count for priority, count in priority_results}
    
    # Cases created in period
    cases_in_period = session.exec(
        select(func.count()).select_from(LegalCase).where(
            and_(
                case_filter,
                LegalCase.created_at >= start_datetime,
                LegalCase.created_at <= end_datetime
            )
        )
    ).one()
    
    # Cases by lawyer (for admin view)
    lawyer_stats = []
    if case_filter is True:  # Admin view
        lawyer_query = select(
            User.full_name,
            User.id,
            func.count(LegalCase.id).label('case_count')
        ).join(LegalCase, User.id == LegalCase.lawyer_id).group_by(User.id, User.full_name)
        lawyer_results = session.exec(lawyer_query).all()
        lawyer_stats = [
            {"lawyer_name": name, "lawyer_id": str(lawyer_id), "case_count": count}
            for name, lawyer_id, count in lawyer_results
        ]
    
    return {
        "total_cases": total_cases,
        "cases_in_period": cases_in_period,
        "cases_by_status": cases_by_status,
        "cases_by_priority": cases_by_priority,
        "cases_by_lawyer": lawyer_stats,
        "average_cases_per_day": round(cases_in_period / max(1, (end_datetime - start_datetime).days), 2)
    }


def get_user_activity_statistics(session: Session, user_filter, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
    """Get user activity statistics"""
    
    # Total users
    total_users = session.exec(
        select(func.count()).select_from(User).where(user_filter)
    ).one()
    
    # Users by role
    role_query = select(User.role, func.count()).where(user_filter).group_by(User.role)
    role_results = session.exec(role_query).all()
    users_by_role = {role: count for role, count in role_results}
    
    # Active users (users who created activities in period)
    active_users = session.exec(
        select(func.count(func.distinct(CaseActivity.user_id))).where(
            and_(
                CaseActivity.created_at >= start_datetime,
                CaseActivity.created_at <= end_datetime
            )
        )
    ).one()
    
    return {
        "total_users": total_users,
        "users_by_role": users_by_role,
        "active_users_in_period": active_users,
        "activity_rate": round((active_users / max(1, total_users)) * 100, 2)
    }


def get_document_statistics(session: Session, case_filter, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
    """Get document usage and storage analytics"""
    
    # Build document query with case filter
    if case_filter is True:
        doc_filter = True
    else:
        doc_filter = CaseDocument.case_id.in_(
            select(LegalCase.id).where(case_filter)
        )
    
    # Total documents
    total_documents = session.exec(
        select(func.count()).select_from(CaseDocument).where(doc_filter)
    ).one()
    
    # Total storage size
    total_size = session.exec(
        select(func.coalesce(func.sum(CaseDocument.file_size), 0)).where(doc_filter)
    ).one()
    
    # Documents by category
    category_query = select(CaseDocument.category, func.count()).where(doc_filter).group_by(CaseDocument.category)
    category_results = session.exec(category_query).all()
    documents_by_category = {category: count for category, count in category_results}
    
    # Documents uploaded in period
    docs_in_period = session.exec(
        select(func.count()).select_from(CaseDocument).where(
            and_(
                doc_filter,
                CaseDocument.uploaded_at >= start_datetime,
                CaseDocument.uploaded_at <= end_datetime
            )
        )
    ).one()
    
    # Document links statistics
    total_links = session.exec(
        select(func.count()).select_from(DocumentCaseLink).where(
            DocumentCaseLink.document_id.in_(
                select(CaseDocument.id).where(doc_filter)
            )
        )
    ).one()
    
    return {
        "total_documents": total_documents,
        "total_size_bytes": total_size,
        "total_size_mb": round(total_size / (1024 * 1024), 2),
        "documents_in_period": docs_in_period,
        "documents_by_category": documents_by_category,
        "total_document_links": total_links,
        "average_documents_per_day": round(docs_in_period / max(1, (end_datetime - start_datetime).days), 2)
    }


def get_activity_statistics(session: Session, case_filter, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
    """Get activity analytics"""
    
    # Build activity query with case filter
    if case_filter is True:
        activity_filter = True
    else:
        activity_filter = CaseActivity.case_id.in_(
            select(LegalCase.id).where(case_filter)
        )
    
    # Total activities
    total_activities = session.exec(
        select(func.count()).select_from(CaseActivity).where(activity_filter)
    ).one()
    
    # Activities in period
    activities_in_period = session.exec(
        select(func.count()).select_from(CaseActivity).where(
            and_(
                activity_filter,
                CaseActivity.created_at >= start_datetime,
                CaseActivity.created_at <= end_datetime
            )
        )
    ).one()
    
    # Activities by type
    type_query = select(CaseActivity.activity_type, func.count()).where(
        and_(
            activity_filter,
            CaseActivity.created_at >= start_datetime,
            CaseActivity.created_at <= end_datetime
        )
    ).group_by(CaseActivity.activity_type)
    type_results = session.exec(type_query).all()
    activities_by_type = {activity_type: count for activity_type, count in type_results}
    
    return {
        "total_activities": total_activities,
        "activities_in_period": activities_in_period,
        "activities_by_type": activities_by_type,
        "average_activities_per_day": round(activities_in_period / max(1, (end_datetime - start_datetime).days), 2)
    }


def get_performance_indicators(session: Session, case_filter, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
    """Calculate key performance indicators"""
    
    # Cases with recent activity (last 7 days)
    recent_cutoff = datetime.utcnow() - timedelta(days=7)
    if case_filter is True:
        activity_filter = True
    else:
        activity_filter = CaseActivity.case_id.in_(select(LegalCase.id).where(case_filter))
    
    active_cases = session.exec(
        select(func.count(func.distinct(CaseActivity.case_id))).where(
            and_(
                activity_filter,
                CaseActivity.created_at >= recent_cutoff
            )
        )
    ).one()
    
    # Total cases for comparison
    total_cases = session.exec(
        select(func.count()).select_from(LegalCase).where(case_filter)
    ).one()
    
    # Case activity rate
    activity_rate = round((active_cases / max(1, total_cases)) * 100, 2)
    
    # Average documents per case
    if case_filter is True:
        doc_filter = True
    else:
        doc_filter = CaseDocument.case_id.in_(select(LegalCase.id).where(case_filter))
    
    total_documents = session.exec(
        select(func.count()).select_from(CaseDocument).where(doc_filter)
    ).one()
    
    avg_docs_per_case = round(total_documents / max(1, total_cases), 2)
    
    return {
        "case_activity_rate": activity_rate,
        "active_cases_last_7_days": active_cases,
        "average_documents_per_case": avg_docs_per_case,
        "system_health_score": min(100, round((activity_rate + min(100, avg_docs_per_case * 10)) / 2, 2))
    }


def get_trend_analysis(session: Session, case_filter, start_date: date, end_date: date) -> Dict[str, Any]:
    """Calculate trends by comparing with previous period"""
    
    period_days = (end_date - start_date).days + 1
    previous_start = start_date - timedelta(days=period_days)
    previous_end = start_date - timedelta(days=1)
    
    # Current period stats
    current_start_dt = datetime.combine(start_date, datetime.min.time())
    current_end_dt = datetime.combine(end_date, datetime.max.time())
    
    # Previous period stats
    previous_start_dt = datetime.combine(previous_start, datetime.min.time())
    previous_end_dt = datetime.combine(previous_end, datetime.max.time())
    
    # Cases trend
    current_cases = session.exec(
        select(func.count()).select_from(LegalCase).where(
            and_(
                case_filter,
                LegalCase.created_at >= current_start_dt,
                LegalCase.created_at <= current_end_dt
            )
        )
    ).one()
    
    previous_cases = session.exec(
        select(func.count()).select_from(LegalCase).where(
            and_(
                case_filter,
                LegalCase.created_at >= previous_start_dt,
                LegalCase.created_at <= previous_end_dt
            )
        )
    ).one()
    
    cases_trend = calculate_percentage_change(previous_cases, current_cases)
    
    # Documents trend
    if case_filter is True:
        doc_filter = True
    else:
        doc_filter = CaseDocument.case_id.in_(select(LegalCase.id).where(case_filter))
    
    current_docs = session.exec(
        select(func.count()).select_from(CaseDocument).where(
            and_(
                doc_filter,
                CaseDocument.uploaded_at >= current_start_dt,
                CaseDocument.uploaded_at <= current_end_dt
            )
        )
    ).one()
    
    previous_docs = session.exec(
        select(func.count()).select_from(CaseDocument).where(
            and_(
                doc_filter,
                CaseDocument.uploaded_at >= previous_start_dt,
                CaseDocument.uploaded_at <= previous_end_dt
            )
        )
    ).one()
    
    documents_trend = calculate_percentage_change(previous_docs, current_docs)
    
    return {
        "cases_trend": {
            "current_period": current_cases,
            "previous_period": previous_cases,
            "percentage_change": cases_trend,
            "is_positive": cases_trend >= 0
        },
        "documents_trend": {
            "current_period": current_docs,
            "previous_period": previous_docs,
            "percentage_change": documents_trend,
            "is_positive": documents_trend >= 0
        }
    }


def calculate_percentage_change(old_value: int, new_value: int) -> float:
    """Calculate percentage change between two values"""
    if old_value == 0:
        return 100.0 if new_value > 0 else 0.0
    return round(((new_value - old_value) / old_value) * 100, 2)
