import uuid
from typing import Any, Annotated

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, desc

from app.api.deps import LawyerScopedUser, SessionDep
from app.core.db import get_session
from app.models import (
    DocumentCaseLink,
    DocumentCaseLinkCreate,
    DocumentCaseLinkUpdate,
    DocumentCaseLinkPublic,
    DocumentCaseLinksPublic,
    CaseDocument,
    LegalCase,
    User,
    UserRole,
    ActivityType,
    CaseActivity,
)

router = APIRouter()


def check_document_link_access(document: CaseDocument, case: LegalCase, user: LawyerScopedUser) -> bool:
    """Check if user has access to create/manage document-case links"""
    if user.role == UserRole.ADMIN:
        return True
    
    # User must have access to both the document and the case
    # For documents: uploader or lawyer/assistant with case access
    document_access = (
        document.uploaded_by == user.id or
        user.role in [UserRole.LAWYER, UserRole.ASSISTANT]
    )
    
    # For cases: lawyer assigned to case or admin
    case_access = (
        case.lawyer_id == user.id or
        user.role == UserRole.ADMIN
    )
    
    return document_access and case_access


@router.get("/documents/{document_id}/links")
def get_document_case_links(
    document_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
) -> DocumentCaseLinksPublic:
    """Get all case links for a specific document"""
    
    # Get document and verify access
    document = session.get(CaseDocument, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check if user has access to the document
    if current_user.role != UserRole.ADMIN and document.uploaded_by != current_user.id:
        # Additional access check for lawyers/assistants
        if current_user.role not in [UserRole.LAWYER, UserRole.ASSISTANT]:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Get links with case and user information
    query = select(DocumentCaseLink, LegalCase, User).join(
        LegalCase, DocumentCaseLink.case_id == LegalCase.id
    ).join(
        User, DocumentCaseLink.linked_by == User.id
    ).where(
        DocumentCaseLink.document_id == document_id
    ).order_by(desc(DocumentCaseLink.linked_at)).offset(skip).limit(limit)
    
    count_query = select(func.count()).select_from(DocumentCaseLink).where(
        DocumentCaseLink.document_id == document_id
    )
    
    results = session.exec(query).all()
    total_count = session.exec(count_query).one()
    
    # Build response
    links = []
    for link, case, linked_by_user in results:
        # Check if user has access to this case
        if current_user.role != UserRole.ADMIN and case.lawyer_id != current_user.id:
            continue
            
        link_public = DocumentCaseLinkPublic(
            **link.model_dump(),
            case=case,
            linked_by_user=linked_by_user,
        )
        links.append(link_public)
    
    return DocumentCaseLinksPublic(data=links, count=len(links))


@router.post("/documents/{document_id}/links")
def create_document_case_link(
    document_id: uuid.UUID,
    link_data: DocumentCaseLinkCreate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> DocumentCaseLinkPublic:
    """Create a new link between a document and a case"""
    
    # Get document and case
    document = session.get(CaseDocument, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    case = session.get(LegalCase, link_data.case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check access permissions
    if not check_document_link_access(document, case, current_user):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Check if link already exists
    existing_link = session.exec(
        select(DocumentCaseLink).where(
            DocumentCaseLink.document_id == document_id,
            DocumentCaseLink.case_id == link_data.case_id
        )
    ).first()
    
    if existing_link:
        raise HTTPException(status_code=400, detail="Link already exists")
    
    # Create the link
    link = DocumentCaseLink(
        document_id=document_id,
        case_id=link_data.case_id,
        linked_by=current_user.id,
        link_type=link_data.link_type,
        notes=link_data.notes,
    )
    
    session.add(link)
    session.commit()
    session.refresh(link)
    
    # Create activity log for the target case
    activity = CaseActivity(
        case_id=link_data.case_id,
        activity_type=ActivityType.DOCUMENT_UPLOADED.value,  # Reuse existing type
        description=f"Document linked to case: {document.original_filename}",
        user_id=current_user.id,
        activity_metadata={
            "document_id": str(document_id),
            "link_id": str(link.id),
            "link_type": link_data.link_type,
            "filename": document.original_filename,
        }
    )
    session.add(activity)
    session.commit()
    
    # Get related data for response
    linked_by_user = session.get(User, current_user.id)
    
    return DocumentCaseLinkPublic(
        **link.model_dump(),
        case=case,
        linked_by_user=linked_by_user,
    )


@router.put("/documents/{document_id}/links/{link_id}")
def update_document_case_link(
    document_id: uuid.UUID,
    link_id: uuid.UUID,
    link_data: DocumentCaseLinkUpdate,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> DocumentCaseLinkPublic:
    """Update a document-case link"""
    
    # Get link
    link = session.get(DocumentCaseLink, link_id)
    if not link or link.document_id != document_id:
        raise HTTPException(status_code=404, detail="Link not found")
    
    # Get document and case for access check
    document = session.get(CaseDocument, document_id)
    case = session.get(LegalCase, link.case_id)
    
    if not document or not case:
        raise HTTPException(status_code=404, detail="Document or case not found")
    
    # Check access permissions
    if not check_document_link_access(document, case, current_user):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Update link
    if link_data.link_type is not None:
        link.link_type = link_data.link_type
    if link_data.notes is not None:
        link.notes = link_data.notes
    
    session.add(link)
    session.commit()
    session.refresh(link)
    
    # Get related data for response
    linked_by_user = session.get(User, link.linked_by)
    
    return DocumentCaseLinkPublic(
        **link.model_dump(),
        case=case,
        linked_by_user=linked_by_user,
    )


@router.delete("/documents/{document_id}/links/{link_id}")
def delete_document_case_link(
    document_id: uuid.UUID,
    link_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
) -> dict[str, str]:
    """Delete a document-case link"""
    
    # Get link
    link = session.get(DocumentCaseLink, link_id)
    if not link or link.document_id != document_id:
        raise HTTPException(status_code=404, detail="Link not found")
    
    # Get document and case for access check
    document = session.get(CaseDocument, document_id)
    case = session.get(LegalCase, link.case_id)
    
    if not document or not case:
        raise HTTPException(status_code=404, detail="Document or case not found")
    
    # Check access permissions
    if not check_document_link_access(document, case, current_user):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Create activity log before deletion
    activity = CaseActivity(
        case_id=link.case_id,
        activity_type=ActivityType.DOCUMENT_DELETED.value,
        description=f"Document unlinked from case: {document.original_filename}",
        user_id=current_user.id,
        activity_metadata={
            "document_id": str(document_id),
            "link_id": str(link_id),
            "link_type": link.link_type,
            "filename": document.original_filename,
        }
    )
    session.add(activity)
    
    # Delete the link
    session.delete(link)
    session.commit()
    
    return {"message": "Link deleted successfully"}


@router.get("/cases/{case_id}/linked-documents")
def get_case_linked_documents(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: SessionDep,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    link_type: str | None = Query(None),
) -> dict[str, Any]:
    """Get all documents linked to a specific case"""
    
    # Get case and verify access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check case access
    if current_user.role != UserRole.ADMIN and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Build query
    query = select(DocumentCaseLink, CaseDocument, User).join(
        CaseDocument, DocumentCaseLink.document_id == CaseDocument.id
    ).join(
        User, CaseDocument.uploaded_by == User.id
    ).where(
        DocumentCaseLink.case_id == case_id
    )
    
    # Apply filters
    if link_type:
        query = query.where(DocumentCaseLink.link_type == link_type)
    
    query = query.order_by(desc(DocumentCaseLink.linked_at)).offset(skip).limit(limit)
    
    count_query = select(func.count()).select_from(DocumentCaseLink).where(
        DocumentCaseLink.case_id == case_id
    )
    if link_type:
        count_query = count_query.where(DocumentCaseLink.link_type == link_type)
    
    results = session.exec(query).all()
    total_count = session.exec(count_query).one()
    
    # Build response
    linked_documents = []
    for link, document, uploader in results:
        document_data = {
            **document.model_dump(),
            "uploader": uploader,
            "link": {
                "id": link.id,
                "link_type": link.link_type,
                "notes": link.notes,
                "linked_at": link.linked_at,
            }
        }
        linked_documents.append(document_data)
    
    return {
        "data": linked_documents,
        "count": total_count
    }
