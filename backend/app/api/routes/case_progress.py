import uuid
from datetime import datetime, date, timedelta
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, func, and_, or_, desc, asc

from app.api.deps import LawyerScopedUser
from app.core.db import get_session
from app.models import (
    LegalCase,
    User,
    UserPublic,
    CaseMilestone,
    CaseMilestoneCreate,
    CaseMilestoneUpdate,
    CaseMilestonePublic,
    CaseMilestonesPublic,
    CaseDeadline,
    CaseDeadlineCreate,
    CaseDeadlineUpdate,
    CaseDeadlinePublic,
    CaseDeadlinesPublic,
    CaseProgressSummary,
    MilestoneTemplate,
    MilestoneTemplateCreate,
    MilestoneTemplatePublic,
    MilestoneTemplatesPublic,
    MilestoneStatus,
    MilestoneType,
    DeadlineType,
    CaseType,
    ActivityType,
    CaseActivity,
    UserRole,
)

router = APIRouter()


@router.get("/{case_id}/progress")
def get_case_progress_summary(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseProgressSummary:
    """Get comprehensive progress summary for a case"""
    
    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check access permissions
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Get milestone statistics
    total_milestones = session.exec(
        select(func.count()).select_from(CaseMilestone).where(CaseMilestone.case_id == case_id)
    ).one()
    
    completed_milestones = session.exec(
        select(func.count()).select_from(CaseMilestone).where(
            and_(
                CaseMilestone.case_id == case_id,
                CaseMilestone.status == "completed"
            )
        )
    ).one()
    
    overdue_milestones = session.exec(
        select(func.count()).select_from(CaseMilestone).where(
            and_(
                CaseMilestone.case_id == case_id,
                CaseMilestone.status.in_(["not_started", "in_progress"]),
                CaseMilestone.target_date < date.today()
            )
        )
    ).one()
    
    # Calculate progress percentage
    progress_percentage = (completed_milestones / max(1, total_milestones)) * 100
    
    # Get deadline statistics
    upcoming_cutoff = datetime.utcnow() + timedelta(days=30)
    upcoming_deadlines = session.exec(
        select(func.count()).select_from(CaseDeadline).where(
            and_(
                CaseDeadline.case_id == case_id,
                CaseDeadline.is_completed == False,
                CaseDeadline.deadline_date <= upcoming_cutoff
            )
        )
    ).one()
    
    critical_deadlines = session.exec(
        select(func.count()).select_from(CaseDeadline).where(
            and_(
                CaseDeadline.case_id == case_id,
                CaseDeadline.is_completed == False,
                CaseDeadline.is_critical == True,
                CaseDeadline.deadline_date <= upcoming_cutoff
            )
        )
    ).one()
    
    # Get next milestone
    next_milestone_query = select(CaseMilestone, User.model_validate).outerjoin(
        User, CaseMilestone.assigned_to == User.id
    ).where(
        and_(
            CaseMilestone.case_id == case_id,
            CaseMilestone.status.in_(["not_started", "in_progress"])
        )
    ).order_by(asc(CaseMilestone.order_index), asc(CaseMilestone.target_date))
    
    next_milestone_result = session.exec(next_milestone_query).first()
    next_milestone = None
    if next_milestone_result:
        milestone, assigned_user = next_milestone_result
        next_milestone = CaseMilestonePublic(
            **milestone.model_dump(),
            assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None
        )
    
    # Get next deadline
    next_deadline_query = select(CaseDeadline, User.model_validate).outerjoin(
        User, CaseDeadline.assigned_to == User.id
    ).where(
        and_(
            CaseDeadline.case_id == case_id,
            CaseDeadline.is_completed == False
        )
    ).order_by(asc(CaseDeadline.deadline_date))
    
    next_deadline_result = session.exec(next_deadline_query).first()
    next_deadline = None
    if next_deadline_result:
        deadline, assigned_user = next_deadline_result
        next_deadline = CaseDeadlinePublic(
            **deadline.model_dump(),
            assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None
        )
    
    return CaseProgressSummary(
        case_id=case_id,
        total_milestones=total_milestones,
        completed_milestones=completed_milestones,
        progress_percentage=round(progress_percentage, 2),
        overdue_milestones=overdue_milestones,
        upcoming_deadlines=upcoming_deadlines,
        critical_deadlines=critical_deadlines,
        next_milestone=next_milestone,
        next_deadline=next_deadline
    )


@router.get("/{case_id}/milestones")
def get_case_milestones(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: MilestoneStatus | None = Query(None),
    milestone_type: MilestoneType | None = Query(None),
    assigned_to: uuid.UUID | None = Query(None),
    include_completed: bool = Query(True),
) -> CaseMilestonesPublic:
    """Get milestones for a case with filtering options"""
    
    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    # Check access permissions
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Build query
    query = select(CaseMilestone, User.model_validate, User.model_validate).outerjoin(
        User, CaseMilestone.assigned_to == User.id
    ).outerjoin(
        User, CaseMilestone.created_by == User.id
    ).where(CaseMilestone.case_id == case_id)
    
    count_query = select(func.count()).select_from(CaseMilestone).where(CaseMilestone.case_id == case_id)
    
    # Apply filters
    if status:
        query = query.where(CaseMilestone.status == status.value)
        count_query = count_query.where(CaseMilestone.status == status.value)
    
    if milestone_type:
        query = query.where(CaseMilestone.milestone_type == milestone_type.value)
        count_query = count_query.where(CaseMilestone.milestone_type == milestone_type.value)
    
    if assigned_to:
        query = query.where(CaseMilestone.assigned_to == assigned_to)
        count_query = count_query.where(CaseMilestone.assigned_to == assigned_to)
    
    if not include_completed:
        query = query.where(CaseMilestone.status != "completed")
        count_query = count_query.where(CaseMilestone.status != "completed")
    
    # Order by index and target date
    query = query.order_by(asc(CaseMilestone.order_index), asc(CaseMilestone.target_date))
    
    # Execute queries
    count = session.exec(count_query).one()
    results = session.exec(query.offset(skip).limit(limit)).all()
    
    # Build response
    milestones = []
    for milestone, assigned_user, creator in results:
        milestone_data = CaseMilestonePublic(
            **milestone.model_dump(),
            assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None,
            creator=UserPublic(**creator.model_dump()) if creator else None
        )
        milestones.append(milestone_data)
    
    return CaseMilestonesPublic(data=milestones, count=count)


@router.post("/{case_id}/milestones")
def create_case_milestone(
    case_id: uuid.UUID,
    milestone_data: CaseMilestoneCreate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseMilestonePublic:
    """Create a new milestone for a case"""
    
    # Verify case access and permissions
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")
    
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Validate assigned user if provided
    if milestone_data.assigned_to:
        assigned_user = session.get(User, milestone_data.assigned_to)
        if not assigned_user:
            raise HTTPException(status_code=400, detail="Assigned user not found")
    
    # Create milestone
    milestone = CaseMilestone(
        case_id=case_id,
        created_by=current_user.id,
        **milestone_data.model_dump()
    )
    
    session.add(milestone)
    session.commit()
    session.refresh(milestone)
    
    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.MILESTONE_CREATED.value,
        description=f"Milestone created: {milestone.title}",
        user_id=current_user.id,
        activity_metadata={
            "milestone_id": str(milestone.id),
            "milestone_title": milestone.title,
            "milestone_type": milestone.milestone_type,
            "target_date": milestone.target_date.isoformat() if milestone.target_date else None
        }
    )
    session.add(activity)
    session.commit()
    
    # Get assigned user and creator for response
    assigned_user = session.get(User, milestone.assigned_to) if milestone.assigned_to else None
    creator = session.get(User, milestone.created_by)
    
    return CaseMilestonePublic(
        **milestone.model_dump(),
        assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None,
        creator=UserPublic(**creator.model_dump()) if creator else None
    )


@router.put("/{case_id}/milestones/{milestone_id}")
def update_case_milestone(
    case_id: uuid.UUID,
    milestone_id: uuid.UUID,
    milestone_data: CaseMilestoneUpdate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseMilestonePublic:
    """Update a case milestone"""

    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Get milestone
    milestone = session.get(CaseMilestone, milestone_id)
    if not milestone or milestone.case_id != case_id:
        raise HTTPException(status_code=404, detail="Milestone not found")

    # Validate assigned user if provided
    if milestone_data.assigned_to:
        assigned_user = session.get(User, milestone_data.assigned_to)
        if not assigned_user:
            raise HTTPException(status_code=400, detail="Assigned user not found")

    # Track status change for activity logging
    old_status = milestone.status

    # Update milestone
    update_data = milestone_data.model_dump(exclude_unset=True)

    # Handle status change to completed
    if milestone_data.status == MilestoneStatus.COMPLETED and old_status != "completed":
        update_data["completion_date"] = date.today()
        update_data["progress_percentage"] = 100
    elif milestone_data.status and milestone_data.status != MilestoneStatus.COMPLETED:
        update_data["completion_date"] = None

    # Update timestamp
    update_data["updated_at"] = datetime.utcnow()

    for field, value in update_data.items():
        setattr(milestone, field, value)

    session.add(milestone)
    session.commit()
    session.refresh(milestone)

    # Log activity if status changed
    if milestone_data.status and milestone_data.status.value != old_status:
        activity = CaseActivity(
            case_id=case_id,
            activity_type=ActivityType.MILESTONE_UPDATED.value,
            description=f"Milestone '{milestone.title}' status changed from {old_status} to {milestone.status}",
            user_id=current_user.id,
            activity_metadata={
                "milestone_id": str(milestone.id),
                "milestone_title": milestone.title,
                "old_status": old_status,
                "new_status": milestone.status,
                "progress_percentage": milestone.progress_percentage
            }
        )
        session.add(activity)
        session.commit()

    # Get assigned user and creator for response
    assigned_user = session.get(User, milestone.assigned_to) if milestone.assigned_to else None
    creator = session.get(User, milestone.created_by)

    return CaseMilestonePublic(
        **milestone.model_dump(),
        assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None,
        creator=UserPublic(**creator.model_dump()) if creator else None
    )


@router.delete("/{case_id}/milestones/{milestone_id}")
def delete_case_milestone(
    case_id: uuid.UUID,
    milestone_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> dict[str, str]:
    """Delete a case milestone"""

    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Get milestone
    milestone = session.get(CaseMilestone, milestone_id)
    if not milestone or milestone.case_id != case_id:
        raise HTTPException(status_code=404, detail="Milestone not found")

    # Store title for activity log
    milestone_title = milestone.title

    # Delete milestone
    session.delete(milestone)
    session.commit()

    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.MILESTONE_DELETED.value,
        description=f"Milestone deleted: {milestone_title}",
        user_id=current_user.id,
        activity_metadata={
            "milestone_id": str(milestone_id),
            "milestone_title": milestone_title
        }
    )
    session.add(activity)
    session.commit()

    return {"message": "Milestone deleted successfully"}


@router.get("/{case_id}/deadlines")
def get_case_deadlines(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    deadline_type: DeadlineType | None = Query(None),
    is_critical: bool | None = Query(None),
    assigned_to: uuid.UUID | None = Query(None),
    include_completed: bool = Query(True),
    upcoming_days: int | None = Query(None, ge=1, le=365),
) -> CaseDeadlinesPublic:
    """Get deadlines for a case with filtering options"""

    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    # Check access permissions
    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Build query
    query = select(CaseDeadline, User.model_validate, User.model_validate).outerjoin(
        User, CaseDeadline.assigned_to == User.id
    ).outerjoin(
        User, CaseDeadline.created_by == User.id
    ).where(CaseDeadline.case_id == case_id)

    count_query = select(func.count()).select_from(CaseDeadline).where(CaseDeadline.case_id == case_id)

    # Apply filters
    if deadline_type:
        query = query.where(CaseDeadline.deadline_type == deadline_type.value)
        count_query = count_query.where(CaseDeadline.deadline_type == deadline_type.value)

    if is_critical is not None:
        query = query.where(CaseDeadline.is_critical == is_critical)
        count_query = count_query.where(CaseDeadline.is_critical == is_critical)

    if assigned_to:
        query = query.where(CaseDeadline.assigned_to == assigned_to)
        count_query = count_query.where(CaseDeadline.assigned_to == assigned_to)

    if not include_completed:
        query = query.where(CaseDeadline.is_completed == False)
        count_query = count_query.where(CaseDeadline.is_completed == False)

    if upcoming_days:
        cutoff_date = datetime.utcnow() + timedelta(days=upcoming_days)
        query = query.where(CaseDeadline.deadline_date <= cutoff_date)
        count_query = count_query.where(CaseDeadline.deadline_date <= cutoff_date)

    # Order by deadline date
    query = query.order_by(asc(CaseDeadline.deadline_date))

    # Execute queries
    count = session.exec(count_query).one()
    results = session.exec(query.offset(skip).limit(limit)).all()

    # Build response
    deadlines = []
    for deadline, assigned_user, creator in results:
        deadline_data = CaseDeadlinePublic(
            **deadline.model_dump(),
            assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None,
            creator=UserPublic(**creator.model_dump()) if creator else None
        )
        deadlines.append(deadline_data)

    return CaseDeadlinesPublic(data=deadlines, count=count)


@router.post("/{case_id}/deadlines")
def create_case_deadline(
    case_id: uuid.UUID,
    deadline_data: CaseDeadlineCreate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseDeadlinePublic:
    """Create a new deadline for a case"""

    # Verify case access and permissions
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Validate assigned user if provided
    if deadline_data.assigned_to:
        assigned_user = session.get(User, deadline_data.assigned_to)
        if not assigned_user:
            raise HTTPException(status_code=400, detail="Assigned user not found")

    # Create deadline
    deadline = CaseDeadline(
        case_id=case_id,
        created_by=current_user.id,
        **deadline_data.model_dump()
    )

    session.add(deadline)
    session.commit()
    session.refresh(deadline)

    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.DEADLINE_CREATED.value,
        description=f"Deadline created: {deadline.title}",
        user_id=current_user.id,
        activity_metadata={
            "deadline_id": str(deadline.id),
            "deadline_title": deadline.title,
            "deadline_type": deadline.deadline_type,
            "deadline_date": deadline.deadline_date.isoformat(),
            "is_critical": deadline.is_critical
        }
    )
    session.add(activity)
    session.commit()

    # Get assigned user and creator for response
    assigned_user = session.get(User, deadline.assigned_to) if deadline.assigned_to else None
    creator = session.get(User, deadline.created_by)

    return CaseDeadlinePublic(
        **deadline.model_dump(),
        assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None,
        creator=UserPublic(**creator.model_dump()) if creator else None
    )


@router.put("/{case_id}/deadlines/{deadline_id}")
def update_case_deadline(
    case_id: uuid.UUID,
    deadline_id: uuid.UUID,
    deadline_data: CaseDeadlineUpdate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> CaseDeadlinePublic:
    """Update a case deadline"""

    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Get deadline
    deadline = session.get(CaseDeadline, deadline_id)
    if not deadline or deadline.case_id != case_id:
        raise HTTPException(status_code=404, detail="Deadline not found")

    # Validate assigned user if provided
    if deadline_data.assigned_to:
        assigned_user = session.get(User, deadline_data.assigned_to)
        if not assigned_user:
            raise HTTPException(status_code=400, detail="Assigned user not found")

    # Track completion status change
    was_completed = deadline.is_completed

    # Update deadline
    update_data = deadline_data.model_dump(exclude_unset=True)

    # Handle completion status change
    if deadline_data.is_completed is True and not was_completed:
        update_data["completion_date"] = datetime.utcnow()
    elif deadline_data.is_completed is False:
        update_data["completion_date"] = None

    # Update timestamp
    update_data["updated_at"] = datetime.utcnow()

    for field, value in update_data.items():
        setattr(deadline, field, value)

    session.add(deadline)
    session.commit()
    session.refresh(deadline)

    # Log activity if completion status changed
    if deadline_data.is_completed is not None and deadline_data.is_completed != was_completed:
        status_text = "completed" if deadline.is_completed else "reopened"
        activity = CaseActivity(
            case_id=case_id,
            activity_type=ActivityType.DEADLINE_UPDATED.value,
            description=f"Deadline '{deadline.title}' {status_text}",
            user_id=current_user.id,
            activity_metadata={
                "deadline_id": str(deadline.id),
                "deadline_title": deadline.title,
                "was_completed": was_completed,
                "is_completed": deadline.is_completed,
                "completion_date": deadline.completion_date.isoformat() if deadline.completion_date else None
            }
        )
        session.add(activity)
        session.commit()

    # Get assigned user and creator for response
    assigned_user = session.get(User, deadline.assigned_to) if deadline.assigned_to else None
    creator = session.get(User, deadline.created_by)

    return CaseDeadlinePublic(
        **deadline.model_dump(),
        assigned_user=UserPublic(**assigned_user.model_dump()) if assigned_user else None,
        creator=UserPublic(**creator.model_dump()) if creator else None
    )


@router.delete("/{case_id}/deadlines/{deadline_id}")
def delete_case_deadline(
    case_id: uuid.UUID,
    deadline_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> dict[str, str]:
    """Delete a case deadline"""

    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Get deadline
    deadline = session.get(CaseDeadline, deadline_id)
    if not deadline or deadline.case_id != case_id:
        raise HTTPException(status_code=404, detail="Deadline not found")

    # Store title for activity log
    deadline_title = deadline.title

    # Delete deadline
    session.delete(deadline)
    session.commit()

    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.DEADLINE_DELETED.value,
        description=f"Deadline deleted: {deadline_title}",
        user_id=current_user.id,
        activity_metadata={
            "deadline_id": str(deadline_id),
            "deadline_title": deadline_title
        }
    )
    session.add(activity)
    session.commit()

    return {"message": "Deadline deleted successfully"}


# Milestone Templates Routes

@router.get("/templates/milestones")
def get_milestone_templates(
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    case_type: CaseType | None = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
) -> MilestoneTemplatesPublic:
    """Get milestone templates, optionally filtered by case type"""

    # Build query
    query = select(MilestoneTemplate, User.model_validate).outerjoin(
        User, MilestoneTemplate.created_by == User.id
    ).where(MilestoneTemplate.is_active == True)

    count_query = select(func.count()).select_from(MilestoneTemplate).where(MilestoneTemplate.is_active == True)

    # Apply case type filter
    if case_type:
        query = query.where(MilestoneTemplate.case_type == case_type.value)
        count_query = count_query.where(MilestoneTemplate.case_type == case_type.value)

    # Order by case type and order index
    query = query.order_by(asc(MilestoneTemplate.case_type), asc(MilestoneTemplate.order_index))

    # Execute queries
    count = session.exec(count_query).one()
    results = session.exec(query.offset(skip).limit(limit)).all()

    # Build response
    templates = []
    for template, creator in results:
        template_data = MilestoneTemplatePublic(
            **template.model_dump(),
            creator=UserPublic(**creator.model_dump()) if creator else None
        )
        templates.append(template_data)

    return MilestoneTemplatesPublic(data=templates, count=count)


@router.post("/templates/milestones")
def create_milestone_template(
    template_data: MilestoneTemplateCreate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> MilestoneTemplatePublic:
    """Create a new milestone template (admin/lawyer only)"""

    # Only admins and lawyers can create templates
    if current_user.role not in [UserRole.ADMIN, UserRole.LAWYER]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    # Create template
    template = MilestoneTemplate(
        created_by=current_user.id,
        **template_data.model_dump()
    )

    session.add(template)
    session.commit()
    session.refresh(template)

    # Get creator for response
    creator = session.get(User, template.created_by)

    return MilestoneTemplatePublic(
        **template.model_dump(),
        creator=UserPublic(**creator.model_dump()) if creator else None
    )


@router.post("/{case_id}/milestones/from-template")
def create_milestones_from_template(
    case_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    case_type: CaseType | None = Query(None, description="Case type to filter templates"),
) -> CaseMilestonesPublic:
    """Create milestones for a case based on templates"""

    # Verify case access
    case = session.get(LegalCase, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="Case not found")

    if current_user.role == UserRole.LAWYER and case.lawyer_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    elif current_user.role in [UserRole.CLIENT, UserRole.ASSISTANT]:
        if not current_user.assigned_lawyer_id or case.lawyer_id != current_user.assigned_lawyer_id:
            raise HTTPException(status_code=403, detail="Access denied")

    # Use case type from parameter or case's type
    template_case_type = case_type or case.case_type

    # Get templates for the case type
    templates = session.exec(
        select(MilestoneTemplate).where(
            and_(
                MilestoneTemplate.case_type == template_case_type,
                MilestoneTemplate.is_active == True
            )
        ).order_by(asc(MilestoneTemplate.order_index))
    ).all()

    if not templates:
        raise HTTPException(status_code=404, detail=f"No milestone templates found for case type: {template_case_type}")

    # Create milestones from templates
    created_milestones = []
    case_opening_date = case.created_at.date()

    for template in templates:
        # Calculate target date based on estimated days
        target_date = None
        if template.estimated_days:
            target_date = case_opening_date + timedelta(days=template.estimated_days)

        milestone = CaseMilestone(
            case_id=case_id,
            title=template.title,
            description=template.description,
            milestone_type=template.milestone_type,
            target_date=target_date,
            order_index=template.order_index,
            is_required=template.is_required,
            notes=template.notes,
            created_by=current_user.id
        )

        session.add(milestone)
        created_milestones.append(milestone)

    session.commit()

    # Refresh all milestones
    for milestone in created_milestones:
        session.refresh(milestone)

    # Log activity
    activity = CaseActivity(
        case_id=case_id,
        activity_type=ActivityType.MILESTONES_CREATED_FROM_TEMPLATE.value,
        description=f"Created {len(created_milestones)} milestones from {template_case_type} template",
        user_id=current_user.id,
        activity_metadata={
            "template_case_type": template_case_type,
            "milestones_count": len(created_milestones),
            "milestone_ids": [str(m.id) for m in created_milestones]
        }
    )
    session.add(activity)
    session.commit()

    # Build response
    milestone_data = []
    for milestone in created_milestones:
        creator = session.get(User, milestone.created_by)
        milestone_public = CaseMilestonePublic(
            **milestone.model_dump(),
            creator=UserPublic(**creator.model_dump()) if creator else None
        )
        milestone_data.append(milestone_public)

    return CaseMilestonesPublic(data=milestone_data, count=len(milestone_data))
