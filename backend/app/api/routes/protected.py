from typing import Annotated, Any

from fastapi import APIRouter, Depends

from app.api.deps import CurrentUser, require_role
from app.models import UserRole

router = APIRouter(tags=["protected"])


@router.get("/lawyer-only")
def lawyer_route(
    current_user: Annotated[CurrentUser, Depends(require_role(UserRole.LAWYER))],
) -> Any:
    return {"message": "Lawyer access granted", "user_email": current_user.email}


@router.get("/admin-only")
def admin_only_route(
    current_user: Annotated[CurrentUser, Depends(require_role(UserRole.ADMIN))],
) -> Any:
    """
    Example endpoint that requires ADMIN role
    """
    return {"message": "Admin access granted", "user_email": current_user.email}


@router.get("/client-only")
def client_only_route(
    current_user: Annotated[CurrentUser, Depends(require_role(UserRole.CLIENT))],
) -> Any:
    """
    Example endpoint that requires CLIENT role
    """
    return {"message": "Client access granted", "user_email": current_user.email}


@router.get("/assistant-only")
def assistant_only_route(
    current_user: Annotated[CurrentUser, Depends(require_role(UserRole.ASSISTANT))],
) -> Any:
    """
    Example endpoint that requires ASSISTANT role
    """
    return {"message": "Assistant access granted", "user_email": current_user.email}
