from collections.abc import Callable, Generator
from typing import Annotated

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON>wordBearer
from jwt.exceptions import InvalidTokenError
from pydantic import ValidationError
from sqlmodel import Session

from app.core.config import settings
from app.core.db import engine
from app.models import TokenPayload, User, UserRole

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)


def get_db() -> Generator[Session, None, None]:
    with Session(engine) as session:
        yield session


SessionDep = Annotated[Session, Depends(get_db)]
TokenDep = Annotated[str, Depends(reusable_oauth2)]


def get_current_user(session: SessionDep, token: TokenDep) -> User:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (InvalidTokenError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    user = session.get(User, token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return user


CurrentUser = Annotated[User, Depends(get_current_user)]


def require_role(required_role: UserRole) -> Callable[[CurrentUser], CurrentUser]:
    def dependency(current_user: CurrentUser) -> CurrentUser:
        if not hasattr(current_user, "role") or current_user.role != required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"{required_role.value} access required",
            )
        return current_user

    return dependency


def require_legal_access(current_user: CurrentUser) -> CurrentUser:
    """
    Dependency that allows Admin, Lawyer, or Assistant roles to access legal case functionality.
    """
    allowed_roles = {UserRole.ADMIN, UserRole.LAWYER, UserRole.ASSISTANT}
    if not hasattr(current_user, "role") or current_user.role not in allowed_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin, Lawyer, or Assistant access required",
        )
    return current_user


# Create a type alias for legal access
LegalAccessUser = Annotated[User, Depends(require_legal_access)]


def get_current_client_user(current_user: CurrentUser) -> User:
    """Ensure the current user is a client"""
    from app.models import UserRole
    if current_user.role != UserRole.CLIENT:
        from fastapi import HTTPException
        raise HTTPException(
            status_code=403,
            detail="Access denied. Client portal is only available to client users."
        )
    return current_user


# Create a type alias for client access
ClientUser = Annotated[User, Depends(get_current_client_user)]


def get_current_active_superuser(current_user: CurrentUser) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="The user doesn't have enough privileges"
        )
    return current_user


def get_lawyer_scoped_user(current_user: CurrentUser) -> User:
    """
    Dependency that provides lawyer-scoped access control.
    - Admins can see all data
    - Lawyers can only see their assigned clients' data
    - Clients and assistants can only see their own data
    """
    allowed_roles = {UserRole.ADMIN, UserRole.LAWYER, UserRole.CLIENT, UserRole.ASSISTANT}
    if not hasattr(current_user, "role") or current_user.role not in allowed_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied",
        )
    return current_user


# Type alias for lawyer-scoped access
LawyerScopedUser = Annotated[User, Depends(get_lawyer_scoped_user)]
