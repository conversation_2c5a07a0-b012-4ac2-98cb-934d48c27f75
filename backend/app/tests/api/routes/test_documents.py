import io

from fastapi.testclient import TestClient

from app.core.config import settings


def test_upload_pdf_document(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test uploading a valid PDF document."""
    # Create a mock PDF file
    pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"

    files = {"file": ("test.pdf", io.BytesIO(pdf_content), "application/pdf")}

    response = client.post(
        f"{settings.API_V1_STR}/documents/upload/",
        headers=superuser_token_headers,
        files=files,
    )

    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "File uploaded successfully"
    assert content["filename"] == "test.pdf"
    assert content["content_type"] == "application/pdf"
    assert "file_id" in content
    assert "uploaded_by" in content


def test_upload_docx_document(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test uploading a valid DOCX document."""
    # Create a mock DOCX file (minimal ZIP structure)
    docx_content = b"PK\x03\x04\x14\x00\x00\x00\x08\x00"

    files = {
        "file": (
            "test.docx",
            io.BytesIO(docx_content),
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )
    }

    response = client.post(
        f"{settings.API_V1_STR}/documents/upload/",
        headers=superuser_token_headers,
        files=files,
    )

    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "File uploaded successfully"
    assert content["filename"] == "test.docx"
    assert (
        content["content_type"]
        == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    )


def test_upload_invalid_file_type(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test uploading an invalid file type."""
    files = {"file": ("test.txt", io.BytesIO(b"Hello world"), "text/plain")}

    response = client.post(
        f"{settings.API_V1_STR}/documents/upload/",
        headers=superuser_token_headers,
        files=files,
    )

    assert response.status_code == 400
    content = response.json()
    assert "Invalid file type" in content["detail"]


def test_upload_file_too_large(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test uploading a file that exceeds size limit."""
    # Create a large file (51MB)
    large_content = b"x" * (51 * 1024 * 1024)

    files = {"file": ("large.pdf", io.BytesIO(large_content), "application/pdf")}

    response = client.post(
        f"{settings.API_V1_STR}/documents/upload/",
        headers=superuser_token_headers,
        files=files,
    )

    assert response.status_code == 400
    content = response.json()
    assert "File size exceeds 50MB limit" in content["detail"]


def test_upload_without_authentication(client: TestClient) -> None:
    """Test uploading without authentication."""
    files = {"file": ("test.pdf", io.BytesIO(b"%PDF-1.4"), "application/pdf")}

    response = client.post(
        f"{settings.API_V1_STR}/documents/upload/",
        files=files,
    )

    assert response.status_code == 401


def test_documents_health_check(client: TestClient) -> None:
    """Test documents health check endpoint."""
    response = client.get(f"{settings.API_V1_STR}/documents/health/")

    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "Documents service is healthy"


def test_upload_no_file(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test uploading without providing a file."""
    response = client.post(
        f"{settings.API_V1_STR}/documents/upload/",
        headers=superuser_token_headers,
    )

    assert response.status_code == 422  # Unprocessable Entity
