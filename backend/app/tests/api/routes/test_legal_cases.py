import uuid

from fastapi.testclient import TestClient
from sqlmodel import Session, select

from app import crud
from app.core.config import settings
from app.models import Legal<PERSON>ase, UserCreate, UserRole
from app.tests.utils.legal_case import create_random_lawyer, create_random_legal_case
from app.tests.utils.utils import random_email, random_lower_string


def test_create_legal_case(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    # Create a random lawyer first
    lawyer = create_random_lawyer(db)
    payload = {
        "title": "Case Title",
        "summary": "Case Summary",
        "client_name": "Client Name",
        "lawyer_id": str(lawyer.id),
    }
    response = client.post(
        f"{settings.API_V1_STR}/legal-cases/",
        headers=superuser_token_headers,
        json=payload,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == payload["title"]
    assert data["client_name"] == payload["client_name"]


def test_read_legal_case(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    # Setup
    legal_case = create_random_legal_case(db)
    # Retrieve
    response = client.get(
        f"{settings.API_V1_STR}/legal-cases/{legal_case.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == str(legal_case.id)
    assert data["title"] == legal_case.title


def test_read_legal_cases_list(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    # Ensure at least one exists
    create_random_legal_case(db)
    response = client.get(
        f"{settings.API_V1_STR}/legal-cases/",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    result = response.json()
    assert "data" in result and isinstance(result["data"], list)
    assert result["count"] >= 1


def test_update_legal_case(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    legal_case = create_random_legal_case(db)
    update_payload = {"title": "Updated Title"}
    response = client.put(
        f"{settings.API_V1_STR}/legal-cases/{legal_case.id}",
        headers=superuser_token_headers,
        json=update_payload,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == update_payload["title"]


def test_delete_legal_case(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    legal_case = create_random_legal_case(db)
    response = client.delete(
        f"{settings.API_V1_STR}/legal-cases/{legal_case.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    resp = response.json()
    assert resp["message"] == "Legal case deleted successfully"
    # Verify deletion
    statement = select(LegalCase).where(LegalCase.id == legal_case.id)
    result = db.exec(statement).first()
    assert result is None


def test_unauthorized_access(
    client: TestClient, normal_user_token_headers: dict[str, str], db: Session
) -> None:
    # Normal user should not create
    payload = {
        "title": "Case Title",
        "summary": "Case Summary",
        "client_name": "Client Name",
        "lawyer_id": str(uuid.uuid4()),
    }
    response = client.post(
        f"{settings.API_V1_STR}/legal-cases/",
        headers=normal_user_token_headers,
        json=payload,
    )
    assert response.status_code == 403

    # Create a case to test deletion access
    case = create_random_legal_case(db)
    response = client.delete(
        f"{settings.API_V1_STR}/legal-cases/{case.id}",
        headers=normal_user_token_headers,
    )
    assert response.status_code == 403


# Role-based access control tests
def test_lawyer_can_only_see_own_cases(
    client: TestClient, db: Session
) -> None:
    # Create two lawyers
    lawyer1_email = random_email()
    lawyer1_password = random_lower_string()
    lawyer1_in = UserCreate(email=lawyer1_email, password=lawyer1_password, role=UserRole.LAWYER)
    lawyer1 = crud.create_user(session=db, user_create=lawyer1_in)

    lawyer2_email = random_email()
    lawyer2_password = random_lower_string()
    lawyer2_in = UserCreate(email=lawyer2_email, password=lawyer2_password, role=UserRole.LAWYER)
    lawyer2 = crud.create_user(session=db, user_create=lawyer2_in)

    # Create cases for each lawyer
    case1 = create_random_legal_case(db, lawyer_id=lawyer1.id)
    case2 = create_random_legal_case(db, lawyer_id=lawyer2.id)

    # Login as lawyer1
    login_data = {"username": lawyer1_email, "password": lawyer1_password}
    r = client.post(f"{settings.API_V1_STR}/login/access-token", data=login_data)
    tokens = r.json()
    lawyer1_headers = {"Authorization": f"Bearer {tokens['access_token']}"}

    # Lawyer1 should see only their own case
    response = client.get(
        f"{settings.API_V1_STR}/legal-cases/",
        headers=lawyer1_headers,
    )
    assert response.status_code == 200
    data = response.json()
    case_ids = [case["id"] for case in data["data"]]
    assert str(case1.id) in case_ids
    assert str(case2.id) not in case_ids

    # Lawyer1 should be able to access their own case
    response = client.get(
        f"{settings.API_V1_STR}/legal-cases/{case1.id}",
        headers=lawyer1_headers,
    )
    assert response.status_code == 200

    # Lawyer1 should NOT be able to access lawyer2's case
    response = client.get(
        f"{settings.API_V1_STR}/legal-cases/{case2.id}",
        headers=lawyer1_headers,
    )
    assert response.status_code == 403
