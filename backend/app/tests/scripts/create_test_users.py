from sqlmodel import Session, create_engine, select

from app.core.config import settings
from app.crud import create_user
from app.models import User, UserCreate, UserRole  # Ajout de l'import User


def create_test_users() -> None:
    # Add type annotations to the function
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))

    test_users = [
        {
            "email": "<EMAIL>",
            "password": "Admin123!",
            "full_name": "Admin Test",
            "role": UserRole.ADMIN,
            "is_active": True,
            "is_superuser": True,
        },
        {
            "email": "<EMAIL>",
            "password": "Lawyer123!",
            "full_name": "Lawyer Test",
            "role": UserRole.LAWYER,
            "is_active": True,
            "is_superuser": False,
        },
        {
            "email": "<EMAIL>",
            "password": "Assistant123!",
            "full_name": "Assistant Test",
            "role": UserRole.ASSISTANT,
            "is_active": True,
            "is_superuser": False,
        },
        {
            "email": "<EMAIL>",
            "password": "Client123!",
            "full_name": "Client Test",
            "role": UserRole.CLIENT,
            "is_active": True,
            "is_superuser": False,
        },
    ]

    with Session(engine) as session:
        for user_data in test_users:
            # Vérification de l'existence de l'utilisateur
            existing_user = session.exec(
                select(User).where(User.email == user_data["email"])
            ).first()

            if existing_user:
                session.delete(existing_user)
                session.commit()

            # Création du nouvel utilisateur
            user_create = UserCreate(**user_data)
            user = create_user(session=session, user_create=user_create)
            print(f"✅ Utilisateur {user.email} créé (Rôle: {user.role})")

        session.commit()
    print("🎉 Tous les utilisateurs de test ont été créés avec succès")


if __name__ == "__main__":
    create_test_users()
