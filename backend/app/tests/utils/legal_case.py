import uuid

from sqlmodel import Session

from app.models import <PERSON><PERSON>ase, Legal<PERSON>ase<PERSON><PERSON>, User, User<PERSON><PERSON>


def create_random_lawyer(db: Session) -> User:
    lawyer = User(
        email=f"lawyer_{uuid.uuid4()}@example.com",
        full_name="Random Lawyer",
        role=UserRole.LAWYER,
        hashed_password="fakehashedpassword",  # Met un vrai hash si besoin
        is_active=True,
        is_superuser=False,
    )
    db.add(lawyer)
    db.commit()
    db.refresh(lawyer)
    return lawyer


def create_random_legal_case(db: Session, lawyer_id: uuid.UUID | None = None) -> LegalCase:
    # Crée un avocat avec email unique si lawyer_id n'est pas fourni
    if lawyer_id is None:
        lawyer = create_random_lawyer(db)
        lawyer_id = lawyer.id

    legal_case_data = LegalCaseCreate(
        title=f"Test Case {uuid.uuid4()}",
        summary="Test Summary",
        client_name="Test Client",
        lawyer_id=lawyer_id,  # Ajout de lawyer_id
    )
    legal_case = LegalCase(**legal_case_data.model_dump())
    db.add(legal_case)
    db.commit()
    db.refresh(legal_case)
    return legal_case
