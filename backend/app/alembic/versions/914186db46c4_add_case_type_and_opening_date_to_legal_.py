"""Add case_type and opening_date to legal_case

Revision ID: 914186db46c4
Revises: 25ef66e67da5
Create Date: 2025-05-24 21:33:49.448188

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '914186db46c4'
down_revision = '25ef66e67da5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Create the enum type first
    casetype_enum = sa.Enum('CIVIL', 'CRIMINAL', 'FAMILY', 'CORPORATE', 'IMMIGRATION', 'PERSONAL_INJURY', 'REAL_ESTATE', 'INTELLECTUAL_PROPERTY', 'EMPLOYMENT', 'OTHER', name='casetype')
    casetype_enum.create(op.get_bind())

    op.add_column('legalcase', sa.Column('case_type', casetype_enum, nullable=False, server_default='OTHER'))
    op.add_column('legalcase', sa.Column('opening_date', sa.Date(), nullable=False, server_default=sa.text('CURRENT_DATE')))
    op.add_column('legalcase', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=2000), nullable=True))
    op.drop_column('legalcase', 'summary')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('legalcase', sa.Column('summary', sa.VARCHAR(length=1000), autoincrement=False, nullable=True))
    op.drop_column('legalcase', 'description')
    op.drop_column('legalcase', 'opening_date')
    op.drop_column('legalcase', 'case_type')

    # Drop the enum type
    casetype_enum = sa.Enum(name='casetype')
    casetype_enum.drop(op.get_bind())
    # ### end Alembic commands ###
