"""Add case status and priority fields

Revision ID: 86e4e89f4e6e
Revises: c0c1f679223b
Create Date: 2025-05-25 14:43:43.246704

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '86e4e89f4e6e'
down_revision = 'c0c1f679223b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('caseactivity',
    sa.Column('case_id', sa.Uuid(), nullable=False),
    sa.Column('activity_type', sa.String(50), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('activity_metadata', sa.JSON(), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.Date(), nullable=False),
    sa.ForeignKeyConstraint(['case_id'], ['legalcase.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('legalcase', sa.Column('status', sa.String(50), nullable=False, server_default='open'))
    op.add_column('legalcase', sa.Column('priority', sa.String(20), nullable=False, server_default='medium'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('legalcase', 'priority')
    op.drop_column('legalcase', 'status')
    op.drop_table('caseactivity')
    # ### end Alembic commands ###
