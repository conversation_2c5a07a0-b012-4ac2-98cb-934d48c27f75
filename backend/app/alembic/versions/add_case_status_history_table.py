"""add_case_status_history_table

Revision ID: add_case_status_history
Revises: 86e4e89f4e6e
Create Date: 2025-05-26 03:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_case_status_history'
down_revision = '86e4e89f4e6e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('casestatushistory',
    sa.Column('case_id', sa.Uuid(), nullable=False),
    sa.Column('old_status', sa.String(), nullable=True),
    sa.Column('new_status', sa.String(), nullable=False),
    sa.Column('changed_by', sa.Uuid(), nullable=False),
    sa.Column('notes', sa.String(length=1000), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('changed_at', sa.Date(), nullable=False),
    sa.ForeignKeyConstraint(['case_id'], ['legalcase.id'], ),
    sa.ForeignKeyConstraint(['changed_by'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('casestatushistory')
    # ### end Alembic commands ###
