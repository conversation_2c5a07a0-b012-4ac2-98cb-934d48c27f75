"""Add LegalCase model

Revision ID: 2bc472afb2cb
Revises: 82d29e5fedd9
Create Date: 2025-05-21 23:47:14.651495

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '2bc472afb2cb'
down_revision = '82d29e5fedd9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
