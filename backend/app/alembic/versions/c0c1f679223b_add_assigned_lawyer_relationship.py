"""add_assigned_lawyer_relationship

Revision ID: c0c1f679223b
Revises: 914186db46c4
Create Date: 2025-05-25 05:39:21.441732

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'c0c1f679223b'
down_revision = '914186db46c4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('assigned_lawyer_id', sa.Uuid(), nullable=True))
    op.create_foreign_key(None, 'user', 'user', ['assigned_lawyer_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_column('user', 'assigned_lawyer_id')
    # ### end Alembic commands ###
