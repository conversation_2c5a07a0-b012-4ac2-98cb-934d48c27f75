"""Add bar_association_id and specialization to user

Revision ID: 25ef66e67da5
Revises: 2bc472afb2cb
Create Date: 2025-05-23 22:43:40.836782

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '25ef66e67da5'
down_revision = '2bc472afb2cb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('bar_association_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.add_column('user', sa.Column('specialization', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'specialization')
    op.drop_column('user', 'bar_association_id')
    # ### end Alembic commands ###
