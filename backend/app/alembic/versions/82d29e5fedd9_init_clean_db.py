"""Init clean DB

Revision ID: 82d29e5fedd9
Revises: 
Create Date: 2025-05-21 23:46:40.045743

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '82d29e5fedd9'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user',
    sa.<PERSON>n('email', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.<PERSON>umn('is_active', sa.<PERSON>(), nullable=False),
    sa.<PERSON>umn('is_superuser', sa.<PERSON>(), nullable=False),
    sa.<PERSON>umn('full_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('role', sa.<PERSON>um('LAWYER', 'CLIENT', 'ADMIN', 'ASSISTANT', name='userrole'), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.<PERSON>umn('hashed_password', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_table('item',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('owner_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('legalcase',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('summary', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('client_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('lawyer_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['lawyer_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('legalcase')
    op.drop_table('item')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    # ### end Alembic commands ###
