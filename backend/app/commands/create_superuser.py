import logging
import sys

import click
from sqlmodel import Session

from app.core.db import engine
from app.crud import create_user, get_user_by_email
from app.models import UserCreate, UserRole

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_superuser(
    email: str,
    password: str,
    full_name: str | None = None,
    role: str = UserRole.ADMIN.value,
    session: Session | None = None,
) -> None:
    """Create a superuser account"""
    should_close_session = False
    if session is None:
        session = Session(engine)
        should_close_session = True

    try:
        existing_user = get_user_by_email(session=session, email=email)
        if existing_user:
            logger.error(f"⚠️  User with email {email} already exists.")
            sys.exit(1)

        user_data = UserCreate(
            email=email,
            password=password,
            full_name=full_name,
            is_superuser=True,
            is_active=True,
            role=UserRole(role),
        )
        new_user = create_user(session=session, user_create=user_data)
        session.commit()
        logger.info(f"✅  Superuser {new_user.email} created with role {role}!")
    finally:
        if should_close_session:
            session.close()


@click.command()
@click.option("--email", prompt="Superuser email", help="Email of the superuser")
@click.option(
    "--password",
    prompt=True,
    hide_input=True,
    confirmation_prompt=True,
    help="Password for the superuser",
)
@click.option("--full-name", prompt="Full Name", help="Full name of the superuser")
@click.option(
    "--role",
    type=click.Choice([role.value for role in UserRole]),
    default=UserRole.ADMIN.value,
    help="Role of the superuser",
)
def create_superuser_command(
    email: str, password: str, full_name: str, role: str
) -> None:
    """CLI command to create a superuser account"""
    create_superuser(email=email, password=password, full_name=full_name, role=role)


if __name__ == "__main__":
    create_superuser_command()
