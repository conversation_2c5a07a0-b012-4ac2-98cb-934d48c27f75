"""
Tests for legal cases bulk operations
"""
import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session

from app.core.db import engine
from app.models import LegalCase, User, CaseStatusHistory, CaseActivity
from app.tests.utils.utils import create_random_legal_case, create_random_user


def test_bulk_status_update_success(client: TestClient, superuser_token_headers: dict[str, str]) -> None:
    """Test successful bulk status update"""
    # Create test cases
    with Session(engine) as session:
        # Create a lawyer user
        lawyer = create_random_user(session, role="lawyer")
        session.add(lawyer)
        session.commit()
        session.refresh(lawyer)
        
        # Create test legal cases
        case1 = create_random_legal_case(session, lawyer_id=lawyer.id, status="open")
        case2 = create_random_legal_case(session, lawyer_id=lawyer.id, status="open")
        session.add(case1)
        session.add(case2)
        session.commit()
        session.refresh(case1)
        session.refresh(case2)
        
        case_ids = [str(case1.id), str(case2.id)]
    
    # Test bulk update
    data = {
        "case_ids": case_ids,
        "new_status": "in_progress",
        "notes": "Test bulk update"
    }
    
    response = client.patch(
        "/api/v1/legal-cases/bulk/status",
        headers=superuser_token_headers,
        json=data,
    )
    
    assert response.status_code == 200
    result = response.json()
    
    assert result["success_count"] == 2
    assert result["failed_count"] == 0
    assert "Successfully updated 2 cases" in result["message"]
    assert len(result["failed_cases"]) == 0
    
    # Verify cases were updated
    with Session(engine) as session:
        updated_case1 = session.get(LegalCase, case1.id)
        updated_case2 = session.get(LegalCase, case2.id)
        
        assert updated_case1.status == "in_progress"
        assert updated_case2.status == "in_progress"


def test_bulk_status_update_invalid_case_id(client: TestClient, superuser_token_headers: dict[str, str]) -> None:
    """Test bulk update with invalid case ID"""
    data = {
        "case_ids": ["invalid-uuid"],
        "new_status": "closed",
        "notes": "Test with invalid ID"
    }
    
    response = client.patch(
        "/api/v1/legal-cases/bulk/status",
        headers=superuser_token_headers,
        json=data,
    )
    
    assert response.status_code == 200
    result = response.json()
    
    assert result["success_count"] == 0
    assert result["failed_count"] == 1
    assert len(result["failed_cases"]) == 1
    assert "Case not found" in result["failed_cases"][0]["error"]


def test_bulk_status_update_no_cases(client: TestClient, superuser_token_headers: dict[str, str]) -> None:
    """Test bulk update with no case IDs"""
    data = {
        "case_ids": [],
        "new_status": "closed",
        "notes": "Test with no cases"
    }
    
    response = client.patch(
        "/api/v1/legal-cases/bulk/status",
        headers=superuser_token_headers,
        json=data,
    )
    
    assert response.status_code == 400
    assert "No case IDs provided" in response.json()["detail"]


def test_bulk_status_update_too_many_cases(client: TestClient, superuser_token_headers: dict[str, str]) -> None:
    """Test bulk update with too many cases"""
    # Create 51 fake case IDs
    case_ids = [f"case-{i:03d}" for i in range(51)]
    
    data = {
        "case_ids": case_ids,
        "new_status": "closed",
        "notes": "Test with too many cases"
    }
    
    response = client.patch(
        "/api/v1/legal-cases/bulk/status",
        headers=superuser_token_headers,
        json=data,
    )
    
    assert response.status_code == 400
    assert "Cannot update more than 50 cases at once" in response.json()["detail"]


def test_bulk_status_update_invalid_transition(client: TestClient, superuser_token_headers: dict[str, str]) -> None:
    """Test bulk update with invalid status transition"""
    with Session(engine) as session:
        # Create a lawyer user
        lawyer = create_random_user(session, role="lawyer")
        session.add(lawyer)
        session.commit()
        session.refresh(lawyer)
        
        # Create a closed case
        case = create_random_legal_case(session, lawyer_id=lawyer.id, status="closed")
        session.add(case)
        session.commit()
        session.refresh(case)
    
    # Try to change closed case to open (invalid transition)
    data = {
        "case_ids": [str(case.id)],
        "new_status": "open",
        "notes": "Test invalid transition"
    }
    
    response = client.patch(
        "/api/v1/legal-cases/bulk/status",
        headers=superuser_token_headers,
        json=data,
    )
    
    assert response.status_code == 200
    result = response.json()
    
    assert result["success_count"] == 0
    assert result["failed_count"] == 1
    assert "Invalid transition" in result["failed_cases"][0]["error"]


def test_bulk_status_update_client_forbidden(client: TestClient) -> None:
    """Test that clients cannot perform bulk updates"""
    with Session(engine) as session:
        # Create a client user
        client_user = create_random_user(session, role="client")
        session.add(client_user)
        session.commit()
        session.refresh(client_user)
    
    # Get client token (this would need to be implemented in test utils)
    # For now, we'll test with superuser and check the role validation in the endpoint
    
    data = {
        "case_ids": ["some-case-id"],
        "new_status": "closed",
        "notes": "Test client access"
    }
    
    # This test would need proper client authentication
    # The actual role check happens in the endpoint with LawyerScopedUser dependency


def test_bulk_status_update_creates_history(client: TestClient, superuser_token_headers: dict[str, str]) -> None:
    """Test that bulk update creates status history records"""
    with Session(engine) as session:
        # Create a lawyer user
        lawyer = create_random_user(session, role="lawyer")
        session.add(lawyer)
        session.commit()
        session.refresh(lawyer)
        
        # Create test case
        case = create_random_legal_case(session, lawyer_id=lawyer.id, status="open")
        session.add(case)
        session.commit()
        session.refresh(case)
    
    # Perform bulk update
    data = {
        "case_ids": [str(case.id)],
        "new_status": "in_progress",
        "notes": "Test history creation"
    }
    
    response = client.patch(
        "/api/v1/legal-cases/bulk/status",
        headers=superuser_token_headers,
        json=data,
    )
    
    assert response.status_code == 200
    
    # Verify history was created
    with Session(engine) as session:
        history = session.query(CaseStatusHistory).filter(
            CaseStatusHistory.case_id == case.id
        ).first()
        
        assert history is not None
        assert history.old_status == "open"
        assert history.new_status == "in_progress"
        assert history.notes == "Test history creation"
        
        # Verify activity was created
        activity = session.query(CaseActivity).filter(
            CaseActivity.case_id == case.id
        ).first()
        
        assert activity is not None
        assert activity.activity_type == "status_changed"
        assert "bulk update" in activity.description.lower()
