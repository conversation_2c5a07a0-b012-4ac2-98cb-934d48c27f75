[project]
name = "app"
version = "0.1.0"
description = ""
requires-python = ">=3.10,<4.0"
dependencies = [
    "setuptools>=68.2.2",
    "fastapi[standard]>=0.114.2,<1.0.0",
    "fastapi-users>=10.0.0",
    "python-multipart>=0.0.7,<1.0.0",
    "email-validator>=2.1.0.post1,<*******",
    "passlib[bcrypt]>=1.7.4,<2.0.0",
    "tenacity>=8.2.3,<9.0.0",
    "pydantic>2.0",
    "emails>=0.6,<1.0",
    "jinja2>=3.1.4,<4.0.0",
    "alembic>=1.12.1,<2.0.0",
    "httpx>=0.25.1,<1.0.0",
    "psycopg[binary]>=3.1.13,<4.0.0",
    "sqlmodel>=0.0.21,<1.0.0",
    "bcrypt==4.0.1",
    "pydantic-settings>=2.2.1,<3.0.0",
    "sentry-sdk[fastapi]>=1.40.6,<2.0.0",
    "pyjwt>=2.8.0,<3.0.0",
]

[tool.uv]
dev-dependencies = [
    "pytest<8.0.0,>=7.4.3",
    "mypy<2.0.0,>=1.8.0",
    "ruff<1.0.0,>=0.2.2",
    "pre-commit<4.0.0,>=3.6.2",
    "types-passlib<*******,>=1.7.7.20240106",
    "coverage<8.0.0,>=7.4.3",
    "black>=24.3.0,<25.0.0",
    "isort>=5.13.2,<6.0.0",
    "flake8>=7.0.0,<8.0.0",
    "pytest-asyncio>=0.20.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.mypy]
strict = true
exclude = ["venv", ".venv", "alembic"]

[tool.ruff]
target-version = "py310"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "W191",  # indentation contains tabs
    "B904",  # Allow raising exceptions without from e, for HTTPException
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true

[tool.pytest.ini_options]
markers = [
    "asyncio: mark async tests"
]

[tool.pytest]
plugins = ["pytest_asyncio"]