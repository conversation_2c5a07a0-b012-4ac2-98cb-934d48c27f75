"""Add case status, priority and activity tracking

Revision ID: add_case_status_priority
Revises:
Create Date: 2025-01-25 13:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_case_status_priority'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Add status and priority columns to legal_cases table
    op.add_column('legalcase', sa.Column('status', sa.String(50), nullable=False, server_default='open'))
    op.add_column('legalcase', sa.Column('priority', sa.String(20), nullable=False, server_default='medium'))

    # Create case_activity table
    op.create_table('caseactivity',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('case_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('activity_type', sa.String(50), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('activity_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.Date(), nullable=False),
        sa.ForeignKeyConstraint(['case_id'], ['legalcase.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes for better performance
    op.create_index('idx_case_activity_case_id', 'caseactivity', ['case_id'])
    op.create_index('idx_case_activity_created_at', 'caseactivity', ['created_at'])
    op.create_index('idx_legal_case_status', 'legalcase', ['status'])
    op.create_index('idx_legal_case_priority', 'legalcase', ['priority'])


def downgrade():
    # Remove indexes
    op.drop_index('idx_legal_case_priority', table_name='legalcase')
    op.drop_index('idx_legal_case_status', table_name='legalcase')
    op.drop_index('idx_case_activity_created_at', table_name='caseactivity')
    op.drop_index('idx_case_activity_case_id', table_name='caseactivity')

    # Drop case_activity table
    op.drop_table('caseactivity')

    # Remove columns from legal_cases table
    op.drop_column('legalcase', 'priority')
    op.drop_column('legalcase', 'status')
