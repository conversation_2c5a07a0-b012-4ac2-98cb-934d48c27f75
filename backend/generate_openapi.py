#!/usr/bin/env python3
"""
Generate OpenAPI schema without requiring database connection
"""

import os
import json
from fastapi import FastAPI

# Set minimal environment variables to avoid validation errors
os.environ.setdefault("POSTGRES_SERVER", "localhost")
os.environ.setdefault("POSTGRES_USER", "postgres")
os.environ.setdefault("POSTGRES_PASSWORD", "password")
os.environ.setdefault("POSTGRES_DB", "equinova")
os.environ.setdefault("FIRST_SUPERUSER", "<EMAIL>")
os.environ.setdefault("FIRST_SUPERUSER_PASSWORD", "password")
os.environ.setdefault("SECRET_KEY", "dummy-secret-key-for-openapi-generation")

# Create a minimal FastAPI app with just the routes we need
app = FastAPI(
    title="EquiNova API",
    version="0.1.0",
    description="Legal Case Management System API"
)

# Import and include just the client portal routes
try:
    from app.api.routes.client_portal import router as client_portal_router
    app.include_router(client_portal_router, prefix="/api/v1/client-portal", tags=["client_portal"])
except ImportError as e:
    print(f"Warning: Could not import client portal routes: {e}")

if __name__ == "__main__":
    openapi_schema = app.openapi()
    print(json.dumps(openapi_schema, indent=2))
