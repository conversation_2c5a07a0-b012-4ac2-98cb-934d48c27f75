#!/bin/bash

# Test script for bulk status update functionality
echo "🧪 Testing Bulk Status Update Functionality"
echo "=================================================="

BASE_URL="http://localhost:8000"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="changethis"

# Step 1: Authenticate
echo "1. Authenticating as admin..."
AUTH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/login/access-token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=$ADMIN_EMAIL&password=$ADMIN_PASSWORD")

echo "Auth response: $AUTH_RESPONSE"

TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.access_token // empty')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo "❌ Authentication failed"
    exit 1
fi

echo "✅ Authentication successful"
echo "Token: ${TOKEN:0:20}..."

# Step 2: Get legal cases
echo ""
echo "2. Fetching legal cases..."
CASES_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/legal-cases/" \
  -H "Authorization: Bearer $TOKEN")

echo "Cases response: $CASES_RESPONSE"

# Extract first case ID
FIRST_CASE_ID=$(echo "$CASES_RESPONSE" | jq -r '.data[0].id // empty')

if [ -z "$FIRST_CASE_ID" ] || [ "$FIRST_CASE_ID" = "null" ]; then
    echo "❌ No legal cases found"
    exit 1
fi

echo "✅ Found legal cases"
echo "First case ID: $FIRST_CASE_ID"

# Step 3: Test bulk update
echo ""
echo "3. Testing bulk status update..."

BULK_UPDATE_DATA='{
  "case_ids": ["'$FIRST_CASE_ID'"],
  "new_status": "in_progress",
  "notes": "Test bulk update from script"
}'

echo "Sending bulk update request with data:"
echo "$BULK_UPDATE_DATA"

BULK_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api/v1/legal-cases/bulk/status" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "$BULK_UPDATE_DATA")

echo "Bulk update response: $BULK_RESPONSE"

# Check if bulk update was successful
SUCCESS_COUNT=$(echo "$BULK_RESPONSE" | jq -r '.success_count // 0')
FAILED_COUNT=$(echo "$BULK_RESPONSE" | jq -r '.failed_count // 0')
MESSAGE=$(echo "$BULK_RESPONSE" | jq -r '.message // "No message"')

if [ "$SUCCESS_COUNT" -gt 0 ]; then
    echo "✅ Bulk update successful!"
    echo "   - Success count: $SUCCESS_COUNT"
    echo "   - Failed count: $FAILED_COUNT"
    echo "   - Message: $MESSAGE"
else
    echo "❌ Bulk update failed!"
    echo "   - Response: $BULK_RESPONSE"
fi

# Step 4: Test with invalid case ID
echo ""
echo "4. Testing with invalid case ID..."

INVALID_DATA='{
  "case_ids": ["invalid-uuid"],
  "new_status": "closed",
  "notes": "Test with invalid ID"
}'

INVALID_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api/v1/legal-cases/bulk/status" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "$INVALID_DATA")

echo "Invalid ID test response: $INVALID_RESPONSE"

INVALID_FAILED_COUNT=$(echo "$INVALID_RESPONSE" | jq -r '.failed_count // 0')

if [ "$INVALID_FAILED_COUNT" -gt 0 ]; then
    echo "✅ Invalid case ID properly rejected"
else
    echo "❌ Invalid case ID should have been rejected"
fi

echo ""
echo "🎉 Testing completed!"
