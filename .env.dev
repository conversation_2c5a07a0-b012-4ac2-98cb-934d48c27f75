# TunLeg DEV Environment - Phase 1 Deployment
# Issue #115: Progressive deployment - DEV phase only

# =============================================================================
# DOMAIN AND ENVIRONMENT
# =============================================================================
DOMAIN=tunleg.com
ENVIRONMENT=local

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=tunleg_dev
POSTGRES_USER=tunleg_dev_user
POSTGRES_PASSWORD=DevPassword123!
POSTGRES_PORT=5432

# =============================================================================
# APPLICATION SECURITY
# =============================================================================
# Simple secret key for DEV (will be changed for PROD)
SECRET_KEY=dev_secret_key_for_testing_only_not_secure_enough_for_production

# First superuser for DEV testing
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=DevAdmin123!

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_HOST=https://dev.tunleg.com
BACKEND_CORS_ORIGINS=https://dev.tunleg.com

# =============================================================================
# EMAIL CONFIGURATION (Optional for DEV)
# =============================================================================
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=<EMAIL>

# =============================================================================
# TRAEFIK CONFIGURATION
# =============================================================================
# Basic Auth for Traefik Dashboard (simple for DEV)
USERNAME=admin
HASHED_PASSWORD=$2y$10$K8V2VKDpGOKUjVjVY8XqOu

# Let's Encrypt Email
EMAIL=<EMAIL>

# =============================================================================
# NOTES FOR DEV PHASE
# =============================================================================
# 1. These are DEV-only credentials - NOT for production
# 2. Passwords are simple for testing purposes
# 3. Only dev.tunleg.com will be deployed in this phase
# 4. Database will be shared but with dev-specific schema
