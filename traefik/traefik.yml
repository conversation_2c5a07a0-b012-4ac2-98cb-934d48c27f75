# Traefik Configuration for EquiNova Production
# Issue #107: Deploy Docker + Traefik + PostgreSQL infrastructure

# Global configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API and Dashboard configuration
api:
  dashboard: true
  debug: false
  insecure: false

# Entry points configuration
entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
          permanent: true
  
  websecure:
    address: ":443"
    http:
      tls:
        options: default

# Providers configuration
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: traefik-public
    watch: true

# Certificate resolvers
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /acme.json
      tlsChallenge: {}
      # Uncomment for staging/testing
      # caServer: https://acme-staging-v02.api.letsencrypt.org/directory

# Logging
log:
  level: INFO
  filePath: "/var/log/traefik.log"

accessLog:
  filePath: "/var/log/access.log"
  bufferingSize: 100

# Metrics (optional)
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true

# TLS Options
tls:
  options:
    default:
      sslProtocols:
        - "TLSv1.2"
        - "TLSv1.3"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
