#!/bin/bash

# <PERSON>ript to create all deployment issues for EquiNova

echo "Creating deployment issues for EquiNova..."

# Issue 3: Docker + Traefik + PostgreSQL
gh issue create --title "Deploy Docker + Traefik + PostgreSQL infrastructure" --body "## 🐳 Container Infrastructure Setup

### Objectives
Set up the complete Docker infrastructure with reverse proxy and database.

### Tasks
- [ ] Install Docker and Docker Compose on VPS
- [ ] Prepare production docker-compose.yml with:
  - [ ] Web service (React/Nginx)
  - [ ] API service (FastAPI)
  - [ ] Database service (PostgreSQL with persistent volumes)
  - [ ] Traefik service for reverse proxy + HTTPS
- [ ] Configure Traefik labels for each environment:
  - [ ] Production: equinova.com
  - [ ] QA: qa.equinova.com
  - [ ] Development: dev.equinova.com
- [ ] Configure acme.json for Let's Encrypt SSL certificates
- [ ] Test access to all domains and verify SSL certificates

### Acceptance Criteria
- All services running via Docker Compose
- SSL certificates automatically generated
- All three environments accessible via their domains
- Database persistent storage configured

**Priority:** High | **Estimated Time:** 4-6 hours"

# Issue 4: Automated backups
gh issue create --title "Implement automated database and file backups" --body "## 💾 Backup Strategy Implementation

### Objectives
Ensure data safety with automated backup solutions.

### Tasks
- [ ] Create backup_pgsql.sh script using pg_dump
- [ ] Set up backup directory structure in /backups
- [ ] Configure backup retention policy
- [ ] Optional: Add Restic for cloud backup to Backblaze B2 or Wasabi
- [ ] Schedule via cron:
  - [ ] Database backup: daily at 2 AM
  - [ ] File uploads backup: weekly
- [ ] Test complete restoration process (pg_restore + files)
- [ ] Document backup and restore procedures

### Acceptance Criteria
- Automated daily database backups working
- File backup system in place
- Restoration process tested and documented
- Backup monitoring/alerting configured

**Priority:** Medium | **Estimated Time:** 3-4 hours"

# Issue 5: Monitoring
gh issue create --title "Set up uptime and performance monitoring" --body "## 📊 Basic Monitoring Setup

### Objectives
Monitor application availability and basic performance metrics.

### Tasks
- [ ] Create UptimeRobot.com account
- [ ] Add monitoring for 3 endpoints:
  - [ ] https://equinova.com/health
  - [ ] https://qa.equinova.com/health
  - [ ] https://dev.equinova.com/health
- [ ] Configure alerts for:
  - [ ] Uptime < 99%
  - [ ] Response time > 3 seconds
- [ ] Set up notification channels (email, Slack, etc.)
- [ ] Add public dashboard link to README or wiki

### Acceptance Criteria
- All environments monitored
- Alert thresholds configured
- Notification system working
- Public dashboard accessible

**Priority:** Medium | **Estimated Time:** 2-3 hours"

# Issue 6: Migration preparation
gh issue create --title "Implement anti-vendor lock-in migration preparation" --body "## 🔄 Migration Readiness

### Objectives
Ensure easy migration to different hosting providers in the future.

### Tasks
- [ ] Create complete export script:
  - [ ] pg_dump for database export
  - [ ] rsync for file synchronization
- [ ] Document migration procedure (VPS → Cloud)
- [ ] Store all passwords and tokens in .env.template
- [ ] Document architecture in README:
  - [ ] Complete tech stack
  - [ ] Volume mappings
  - [ ] Upload directory structure
- [ ] Bonus: Test migration to a test VPS

### Acceptance Criteria
- Export scripts created and tested
- Migration documentation complete
- Architecture fully documented
- Test migration successful (if bonus completed)

**Priority:** Low | **Estimated Time:** 2-3 hours"

# Issue 7: CI/CD
gh issue create --title "Implement CI/CD pipeline with GitHub Actions" --body "## 🚀 Automated Deployment Pipeline

### Objectives
Automate build and deployment process for all environments.

### Tasks
- [ ] Create .github/workflows/deploy.yml workflow
- [ ] Implement deployment steps:
  - [ ] Build Docker images (web + api)
  - [ ] Push to DockerHub or GitHub Container Registry
  - [ ] SSH deploy to VPS
  - [ ] Restart services via docker-compose up -d
- [ ] Configure GitHub Secrets for sensitive variables
- [ ] Set up automatic deployment:
  - [ ] QA environment: on push to main branch
  - [ ] Production: manual trigger or on release
- [ ] Add deployment status notifications

### Acceptance Criteria
- CI/CD pipeline working for all environments
- Automatic QA deployments on main branch
- Manual production deployment process
- Deployment notifications configured

**Priority:** Medium | **Estimated Time:** 4-5 hours"

echo "All deployment issues created successfully!"
