#!/bin/bash

# EquiNova DEV Environment Deployment - Phase 1
# Issue #115: Progressive deployment strategy

set -e

echo "🚀 Starting EquiNova DEV Environment Deployment (Phase 1)..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "=== PHASE 1: DEV ENVIRONMENT DEPLOYMENT ==="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.dev exists
if [ ! -f ".env.dev" ]; then
    print_error ".env.dev file not found. Please create it first."
    exit 1
fi

# Load environment variables
print_status "Loading DEV environment variables..."
export $(cat .env.dev | grep -v '^#' | xargs)

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p ./backend/uploads
mkdir -p ./backend/logs
mkdir -p ./backups
mkdir -p ./traefik/logs

# Set proper permissions
print_status "Setting proper permissions..."
chmod 600 .env.dev
chmod 600 traefik/acme.json
chmod 755 ./backend/uploads
chmod 755 ./backups

# Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose -f docker-compose.dev.yml down --remove-orphans || true

# Pull latest images
print_status "Pulling latest images..."
docker-compose -f docker-compose.dev.yml pull

# Build custom images
print_status "Building custom images for DEV..."
docker-compose -f docker-compose.dev.yml build --no-cache

# Start DEV services
print_status "Starting DEV services..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be healthy
print_status "Waiting for services to be healthy..."
sleep 30

# Check service status
print_status "Checking DEV service status..."
docker-compose -f docker-compose.dev.yml ps

# Test database connection
print_status "Testing database connection..."
if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U $POSTGRES_USER -d $POSTGRES_DB; then
    print_success "Database is ready"
else
    print_error "Database is not ready"
    exit 1
fi

# Run database migrations
print_status "Running database migrations..."
docker-compose -f docker-compose.dev.yml exec -T backend-dev alembic upgrade head

# Create first superuser for DEV
print_status "Creating DEV superuser..."
docker-compose -f docker-compose.dev.yml exec -T backend-dev python -c "
from app.core.config import settings
from app.crud import create_user
from app.models import UserCreate
from app.core.db import SessionLocal

db = SessionLocal()
try:
    user = create_user(
        session=db,
        user_create=UserCreate(
            email='${FIRST_SUPERUSER}',
            password='${FIRST_SUPERUSER_PASSWORD}',
            is_superuser=True
        )
    )
    print(f'DEV Superuser created: {user.email}')
except Exception as e:
    print(f'DEV Superuser might already exist: {e}')
finally:
    db.close()
"

# Test DEV endpoints
print_status "Testing DEV endpoints..."
sleep 10

# Test if dev.tunleg.com is accessible (this will work once DNS is configured)
print_status "DEV environment should be accessible at:"
echo "🌐 Frontend: https://dev.tunleg.com"
echo "🔧 API Docs: https://dev.tunleg.com/api/docs"
echo "🗄️ Database Admin: https://adminer.tunleg.com"
echo "📊 Traefik Dashboard: https://traefik.tunleg.com"

# Display deployment information
print_success "🎉 DEV Environment (Phase 1) deployed successfully!"
echo ""
echo "📋 DEV Deployment Information:"
echo "=============================="
echo "🌐 DEV URL: https://dev.tunleg.com"
echo "🔧 API URL: https://dev.tunleg.com/api"
echo "📚 API Docs: https://dev.tunleg.com/api/docs"
echo ""
echo "👤 DEV Admin Credentials:"
echo "Email: $FIRST_SUPERUSER"
echo "Password: $FIRST_SUPERUSER_PASSWORD"
echo ""
echo "🔍 Useful Commands:"
echo "==================="
echo "View logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "Stop DEV: docker-compose -f docker-compose.dev.yml down"
echo "Restart DEV: docker-compose -f docker-compose.dev.yml restart"
echo "Check status: docker-compose -f docker-compose.dev.yml ps"
echo ""
print_warning "Next Steps:"
print_warning "1. Test https://dev.tunleg.com in your browser"
print_warning "2. Verify SSL certificate is working"
print_warning "3. Test API at https://dev.tunleg.com/api/docs"
print_warning "4. Login with DEV admin credentials"
print_warning "5. If everything works, proceed to Phase 2 (QA deployment)"
echo ""
print_success "Phase 1 (DEV) completed! Ready for Phase 2 (QA) when you're ready."
