# EquiNova Production Deployment Issues

## Issue 1: Purchase and configure domain name equinova.com

**Title:** Purchase and configure domain name equinova.com
**Labels:** infrastructure, production, domain

### 🌐 Domain Setup for EquiNova Production

#### Objectives
Set up the main domain and subdomains for our multi-environment deployment strategy.

#### Tasks
- [ ] Check availability of equinova.com (or alternatives: .legal, .tn)
- [ ] Purchase domain on Namecheap.com (or Gandi if EU preference)
- [ ] Enable domain protection features:
  - [ ] Auto-renewal
  - [ ] Transfer protection
  - [ ] DNSSEC if available
- [ ] Configure DNS records:
  - [ ] A record for equinova.com → VPS IP
  - [ ] CNAME/A records for qa.equinova.com
  - [ ] CNAME/A records for dev.equinova.com
- [ ] Document domain management in repo/wiki
- [ ] Store registrar credentials securely

#### Acceptance Criteria
- Domain purchased and configured
- All subdomains pointing to VPS
- Documentation updated with domain management info
- Credentials stored in secure shared location

**Priority:** High | **Estimated Time:** 2-4 hours

---

## Issue 2: Create and secure Hetzner VPS

**Title:** Create and secure Hetzner VPS for initial deployment
**Labels:** infrastructure, security, vps

### 🖥️ VPS Setup and Security Configuration

#### Objectives
Create a secure VPS environment on Hetzner for hosting all environments.

#### Tasks
- [ ] Create Hetzner VPS CX31 (2 vCPU, 8GB RAM, 80GB SSD)
- [ ] Install Ubuntu 22.04 LTS
- [ ] Configure SSH access:
  - [ ] Add SSH users with key-based authentication
  - [ ] Disable root password login
  - [ ] Configure SSH hardening
- [ ] Install and configure UFW firewall:
  - [ ] Allow SSH (port 22)
  - [ ] Allow HTTP (port 80)
  - [ ] Allow HTTPS (port 443)
  - [ ] Deny all other incoming traffic
- [ ] Add VPS to Hetzner firewall (optional but recommended)
- [ ] Document access credentials in .env.example or wiki

#### Acceptance Criteria
- VPS created and accessible via SSH
- Firewall properly configured
- Security hardening applied
- Access documentation updated

**Priority:** High | **Estimated Time:** 3-5 hours

---

## Issue 3: Deploy Docker, Traefik and PostgreSQL on VPS

**Title:** Deploy Docker + Traefik + PostgreSQL infrastructure
**Labels:** infrastructure, docker, traefik, database

### 🐳 Container Infrastructure Setup

#### Objectives
Set up the complete Docker infrastructure with reverse proxy and database.

#### Tasks
- [ ] Install Docker and Docker Compose on VPS
- [ ] Prepare production docker-compose.yml with:
  - [ ] Web service (React/Nginx)
  - [ ] API service (FastAPI)
  - [ ] Database service (PostgreSQL with persistent volumes)
  - [ ] Traefik service for reverse proxy + HTTPS
- [ ] Configure Traefik labels for each environment:
  - [ ] Production: equinova.com
  - [ ] QA: qa.equinova.com
  - [ ] Development: dev.equinova.com
- [ ] Configure acme.json for Let's Encrypt SSL certificates
- [ ] Test access to all domains and verify SSL certificates

#### Acceptance Criteria
- All services running via Docker Compose
- SSL certificates automatically generated
- All three environments accessible via their domains
- Database persistent storage configured

**Priority:** High | **Estimated Time:** 4-6 hours

---

## Issue 4: Set up automated backups

**Title:** Implement automated database and file backups
**Labels:** infrastructure, backup, database

### 💾 Backup Strategy Implementation

#### Objectives
Ensure data safety with automated backup solutions.

#### Tasks
- [ ] Create backup_pgsql.sh script using pg_dump
- [ ] Set up backup directory structure in /backups
- [ ] Configure backup retention policy
- [ ] Optional: Add Restic for cloud backup to Backblaze B2 or Wasabi
- [ ] Schedule via cron:
  - [ ] Database backup: daily at 2 AM
  - [ ] File uploads backup: weekly
- [ ] Test complete restoration process (pg_restore + files)
- [ ] Document backup and restore procedures

#### Acceptance Criteria
- Automated daily database backups working
- File backup system in place
- Restoration process tested and documented
- Backup monitoring/alerting configured

**Priority:** Medium | **Estimated Time:** 3-4 hours

---

## Issue 5: Configure basic monitoring with UptimeRobot

**Title:** Set up uptime and performance monitoring
**Labels:** monitoring, uptime, performance

### 📊 Basic Monitoring Setup

#### Objectives
Monitor application availability and basic performance metrics.

#### Tasks
- [ ] Create UptimeRobot.com account
- [ ] Add monitoring for 3 endpoints:
  - [ ] https://equinova.com/health
  - [ ] https://qa.equinova.com/health
  - [ ] https://dev.equinova.com/health
- [ ] Configure alerts for:
  - [ ] Uptime < 99%
  - [ ] Response time > 3 seconds
- [ ] Set up notification channels (email, Slack, etc.)
- [ ] Add public dashboard link to README or wiki

#### Acceptance Criteria
- All environments monitored
- Alert thresholds configured
- Notification system working
- Public dashboard accessible

**Priority:** Medium | **Estimated Time:** 2-3 hours

---

## Issue 6: Prepare future migration strategy (anti-vendor lock-in)

**Title:** Implement anti-vendor lock-in migration preparation
**Labels:** infrastructure, migration, documentation

### 🔄 Migration Readiness

#### Objectives
Ensure easy migration to different hosting providers in the future.

#### Tasks
- [ ] Create complete export script:
  - [ ] pg_dump for database export
  - [ ] rsync for file synchronization
- [ ] Document migration procedure (VPS → Cloud)
- [ ] Store all passwords and tokens in .env.template
- [ ] Document architecture in README:
  - [ ] Complete tech stack
  - [ ] Volume mappings
  - [ ] Upload directory structure
- [ ] Bonus: Test migration to a test VPS

#### Acceptance Criteria
- Export scripts created and tested
- Migration documentation complete
- Architecture fully documented
- Test migration successful (if bonus completed)

**Priority:** Low | **Estimated Time:** 2-3 hours

---

## Issue 7: Add CI/CD GitHub Actions for deployment

**Title:** Implement CI/CD pipeline with GitHub Actions
**Labels:** ci-cd, github-actions, deployment

### 🚀 Automated Deployment Pipeline

#### Objectives
Automate build and deployment process for all environments.

#### Tasks
- [ ] Create .github/workflows/deploy.yml workflow
- [ ] Implement deployment steps:
  - [ ] Build Docker images (web + api)
  - [ ] Push to DockerHub or GitHub Container Registry
  - [ ] SSH deploy to VPS
  - [ ] Restart services via docker-compose up -d
- [ ] Configure GitHub Secrets for sensitive variables
- [ ] Set up automatic deployment:
  - [ ] QA environment: on push to main branch
  - [ ] Production: manual trigger or on release
- [ ] Add deployment status notifications

#### Acceptance Criteria
- CI/CD pipeline working for all environments
- Automatic QA deployments on main branch
- Manual production deployment process
- Deployment notifications configured

**Priority:** Medium | **Estimated Time:** 4-5 hours
