#!/bin/bash

# EquiNova LOCAL Development Environment Manager
# Simple commands for daily development work

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[LOCAL]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    echo "EquiNova LOCAL Development Environment Manager"
    echo ""
    echo "Usage: ./local-dev.sh [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start LOCAL development environment"
    echo "  stop      Stop LOCAL development environment"
    echo "  restart   Restart LOCAL development environment"
    echo "  logs      Show logs from all LOCAL services"
    echo "  status    Show status of LOCAL services"
    echo "  clean     Stop and remove LOCAL containers and volumes"
    echo "  build     Rebuild LOCAL containers"
    echo "  shell     Open shell in backend container"
    echo "  db        Open database shell"
    echo "  help      Show this help message"
    echo ""
    echo "LOCAL Environment URLs:"
    echo "  Frontend:  http://localhost:3000"
    echo "  Backend:   http://localhost:8000"
    echo "  API Docs:  http://localhost:8000/docs"
    echo "  Adminer:   http://localhost:8080"
}

# Start LOCAL environment
start_local() {
    print_status "Starting EquiNova LOCAL development environment..."
    
    # Check if .env.local exists
    if [ ! -f ".env.local" ]; then
        print_error ".env.local file not found!"
        exit 1
    fi
    
    # Create necessary directories
    mkdir -p ./backend/uploads
    mkdir -p ./backend/logs
    
    # Start services
    docker-compose -f docker-compose.local.yml --env-file .env.local up -d
    
    print_status "Waiting for services to start..."
    sleep 10
    
    print_success "LOCAL environment started!"
    echo ""
    echo "🌐 Frontend:  http://localhost:3000"
    echo "⚡ Backend:   http://localhost:8000"
    echo "📚 API Docs:  http://localhost:8000/docs"
    echo "🗄️ Adminer:   http://localhost:8080"
    echo ""
    echo "👤 Admin credentials:"
    echo "   Email: admin@localhost"
    echo "   Password: admin123"
}

# Stop LOCAL environment
stop_local() {
    print_status "Stopping EquiNova LOCAL development environment..."
    docker-compose -f docker-compose.local.yml down
    print_success "LOCAL environment stopped!"
}

# Restart LOCAL environment
restart_local() {
    print_status "Restarting EquiNova LOCAL development environment..."
    docker-compose -f docker-compose.local.yml --env-file .env.local restart
    print_success "LOCAL environment restarted!"
}

# Show logs
show_logs() {
    print_status "Showing LOCAL environment logs..."
    docker-compose -f docker-compose.local.yml logs -f
}

# Show status
show_status() {
    print_status "LOCAL environment status:"
    docker-compose -f docker-compose.local.yml ps
}

# Clean environment
clean_local() {
    print_warning "This will remove all LOCAL containers and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning LOCAL environment..."
        docker-compose -f docker-compose.local.yml down -v --remove-orphans
        docker system prune -f
        print_success "LOCAL environment cleaned!"
    else
        print_status "Clean cancelled."
    fi
}

# Build containers
build_local() {
    print_status "Building LOCAL containers..."
    docker-compose -f docker-compose.local.yml --env-file .env.local build --no-cache
    print_success "LOCAL containers built!"
}

# Open backend shell
backend_shell() {
    print_status "Opening backend shell..."
    docker-compose -f docker-compose.local.yml exec backend-local bash
}

# Open database shell
db_shell() {
    print_status "Opening database shell..."
    docker-compose -f docker-compose.local.yml exec db-local psql -U equinova_user -d equinova_local
}

# Main script logic
case "${1:-help}" in
    start)
        start_local
        ;;
    stop)
        stop_local
        ;;
    restart)
        restart_local
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    clean)
        clean_local
        ;;
    build)
        build_local
        ;;
    shell)
        backend_shell
        ;;
    db)
        db_shell
        ;;
    help|*)
        show_help
        ;;
esac
