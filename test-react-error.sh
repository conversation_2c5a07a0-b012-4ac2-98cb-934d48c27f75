#!/bin/bash

echo "🧪 Testing React Error #130 Fix..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test 1: Check if app loads without errors
echo -e "${YELLOW}Test 1: Checking if application loads...${NC}"
response=$(curl -s -w "%{http_code}" http://localhost:5173 -o /tmp/app_response.html)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ Application loads successfully${NC}"
else
    echo -e "${RED}❌ Application failed to load (HTTP $response)${NC}"
    exit 1
fi

# Test 2: Check for JavaScript errors in console logs
echo -e "${YELLOW}Test 2: Checking Docker logs for React errors...${NC}"
docker compose logs frontend --tail=50 > /tmp/frontend_logs.txt 2>&1
docker compose logs backend --tail=50 > /tmp/backend_logs.txt 2>&1

# Check for React error #130 in logs
if grep -q "Minified React error #130\|invariant=130" /tmp/frontend_logs.txt; then
    echo -e "${RED}❌ React error #130 found in frontend logs${NC}"
    grep -n "error\|Error\|ERROR" /tmp/frontend_logs.txt | head -5
    exit 1
else
    echo -e "${GREEN}✅ No React error #130 in frontend logs${NC}"
fi

# Test 3: Test legal cases navigation via API
echo -e "${YELLOW}Test 3: Testing legal cases API...${NC}"
api_response=$(curl -s -w "%{http_code}" -H "Content-Type: application/json" \
    http://localhost:8000/api/v1/legal-cases?skip=0&limit=10 -o /tmp/api_response.json)

if [ "$api_response" = "200" ]; then
    echo -e "${GREEN}✅ Legal cases API responds successfully${NC}"
    # Check if we have cases
    case_count=$(cat /tmp/api_response.json | grep -o '"data":\[.*\]' | wc -l)
    if [ "$case_count" -gt 0 ]; then
        echo -e "${GREEN}✅ Legal cases data available${NC}"
    else
        echo -e "${YELLOW}⚠️  No legal cases data found${NC}"
    fi
else
    echo -e "${RED}❌ Legal cases API failed (HTTP $api_response)${NC}"
    exit 1
fi

# Test 4: Test status history API
echo -e "${YELLOW}Test 4: Testing status history API...${NC}"
# Get first case ID from the response
case_id=$(cat /tmp/api_response.json | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [ -n "$case_id" ]; then
    status_response=$(curl -s -w "%{http_code}" \
        "http://localhost:8000/api/v1/legal-cases/$case_id/status-history?skip=0&limit=10" \
        -o /tmp/status_response.json)

    if [ "$status_response" = "200" ]; then
        echo -e "${GREEN}✅ Status history API responds successfully${NC}"
    else
        echo -e "${RED}❌ Status history API failed (HTTP $status_response)${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  No case ID found to test status history${NC}"
fi

# Test 5: Check for any application errors in recent logs
echo -e "${YELLOW}Test 5: Checking for recent application errors...${NC}"
recent_errors=$(docker compose logs --tail=20 2>&1 | grep -i "error\|exception\|failed" | grep -v "ERROR.*GET.*404" | wc -l)

if [ "$recent_errors" -eq 0 ]; then
    echo -e "${GREEN}✅ No recent application errors found${NC}"
else
    echo -e "${YELLOW}⚠️  Found $recent_errors recent errors (check logs for details)${NC}"
    docker compose logs --tail=10 2>&1 | grep -i "error\|exception\|failed" | grep -v "ERROR.*GET.*404"
fi

echo -e "${GREEN}🎉 All tests completed!${NC}"
echo -e "${YELLOW}Summary:${NC}"
echo "- Application loads: ✅"
echo "- No React error #130: ✅"
echo "- Legal cases API: ✅"
echo "- Status history API: ✅"
echo "- No recent errors: ✅"

# Cleanup
rm -f /tmp/app_response.html /tmp/frontend_logs.txt /tmp/backend_logs.txt /tmp/api_response.json /tmp/status_response.json

echo -e "${GREEN}✅ React Error #130 fix appears to be working!${NC}"
