# EquiNova Production Environment Variables
# Issue #107: Deploy Docker + <PERSON>rae<PERSON><PERSON> + PostgreSQL infrastructure
# Domain: tunleg.com

# =============================================================================
# DOMAIN AND ENVIRONMENT
# =============================================================================
DOMAIN=tunleg.com
ENVIRONMENT=production
STACK_NAME=equinova-prod

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=equinova
POSTGRES_USER=equinova_user
POSTGRES_PASSWORD=CHANGE_THIS_SECURE_PASSWORD_123!
POSTGRES_PORT=5432

# =============================================================================
# APPLICATION SECURITY
# =============================================================================
# Generate with: openssl rand -hex 32
SECRET_KEY=CHANGE_THIS_SECRET_KEY_64_CHARACTERS_LONG_RANDOM_STRING_HERE

# First superuser (admin)
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=CHANGE_THIS_ADMIN_PASSWORD_123!

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_HOST=https://tunleg.com

# CORS Origins (comma-separated)
BACKEND_CORS_ORIGINS=https://tunleg.com,https://dev.tunleg.com,https://qa.tunleg.com

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=true
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=EquiNova

# =============================================================================
# DOCKER IMAGES
# =============================================================================
DOCKER_IMAGE_BACKEND=equinova-backend
DOCKER_IMAGE_FRONTEND=equinova-frontend
TAG=latest

# =============================================================================
# TRAEFIK CONFIGURATION
# =============================================================================
# Basic Auth for Traefik Dashboard
# Generate with: htpasswd -nb admin your_password
USERNAME=admin
HASHED_PASSWORD=$2y$10$K8V2VKDpGOKUjVjVY8XqOuH8h8h8h8h8h8h8h8h8h8

# Let's Encrypt Email
EMAIL=<EMAIL>

# =============================================================================
# MONITORING (Optional)
# =============================================================================
SENTRY_DSN=

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# =============================================================================
# SECURITY HEADERS
# =============================================================================
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000
SECURE_CONTENT_TYPE_NOSNIFF=true
SECURE_BROWSER_XSS_FILTER=true

# =============================================================================
# NOTES
# =============================================================================
# 1. Change all passwords and secret keys before deployment
# 2. Store this file securely and never commit to version control
# 3. Use environment-specific values for each deployment
# 4. Generate strong passwords: openssl rand -base64 32
# 5. Generate secret key: openssl rand -hex 32
