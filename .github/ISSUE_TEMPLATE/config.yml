blank_issues_enabled: true
contact_links:
  - name: 🐛 Bug Report
    about: Report technical issues or application errors
    url: https://github.com/mouinemed/EquiNova-app/issues/new?template=bug_report.md
  - name: ✨ Feature Request
    about: Suggest new functionality for EquiNova
    url: https://github.com/mouinemed/EquiNova-app/issues/new?template=feature_request.md
  - name: 📚 Documentation
    about: Request or suggest improvements to documentation
    url: https://github.com/mouinemed/EquiNova-app/issues/new?template=documentation.md
  - name: 🧪 Test Request
    about: Request new tests or improvements to existing tests
    url: https://github.com/mouinemed/EquiNova-app/issues/new?template=test_request.md
  - name: 🔒 Security Issue
    about: Report a security vulnerability or request security improvements
    url: https://github.com/mouinemed/EquiNova-app/issues/new?template=security_issue.md
  - name: ❓ Question
    about: General usage questions
    url: https://github.com/mouinemed/EquiNova-app/discussions