name: Test Docker Compose

on:
  push:
    branches:
      - master
  pull_request:
    types:
      - opened
      - synchronize

jobs:

  test-docker-compose:
    runs-on: ubuntu-latest
    env:  
      STACK_NAME: ${{ github.repository }}
      DOMAIN: localhost
      DOCKER_IMAGE_BACKEND: backend
      DOCKER_IMAGE_FRONTEND: frontend
      FRONTEND_HOST: http://localhost:3000
      POSTGRES_DB: app
      POSTGRES_SERVER: db  
      POSTGRES_USER: postgres
      POSTGRES_PORT: 5432
      POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
      ENVIRONMENT: local
      SECRET_KEY: ${{ secrets.SECRET_KEY }}
      FIRST_SUPERUSER: ${{ secrets.FIRST_SUPERUSER }}
      FIRST_SUPERUSER_PASSWORD: ${{ secrets.FIRST_SUPERUSER_PASSWORD }}
      BA<PERSON><PERSON><PERSON>_CORS_ORIGINS: "[\"http://localhost:3000\"]"
      SMTP_USER: ""
      SMTP_PASSWORD: ""
      SMTP_HOST: ${{ secrets.SMTP_HOST }}
      EMAILS_FROM_EMAIL: ${{ secrets.EMAILS_FROM_EMAIL }}
      SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
      EMAILS_ENABLED: "true"
      SMTP_PORT: 1025 

      
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Create dummy .env file
        run: touch .env
      - run: docker compose build
      - run: docker compose down -v --remove-orphans
      - run: docker compose up -d --wait backend frontend adminer
      - name: Test backend is up
        run: curl http://localhost:8000/api/v1/utils/health-check
      - name: Test frontend is up
        run: curl http://localhost:5173
      - run: docker compose down -v --remove-orphans
