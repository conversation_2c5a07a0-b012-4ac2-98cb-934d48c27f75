name: Generate Client

on:
  pull_request:
    types:
    - opened
    - synchronize

jobs:
  generate-client:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.head_ref }}
        token: ${{ secrets.FULL_STACK_FASTAPI_TEMPLATE_REPO_TOKEN }}

    - uses: actions/setup-node@v4
      with:
        node-version: lts/*

    - uses: actions/setup-python@v5
      with:
        python-version: "3.10"

    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        version: "0.4.15"
        enable-cache: true

    - name: Install dependencies
      run: npm ci
      working-directory: frontend

    - run: uv sync
      working-directory: backend

    - run: uv run bash scripts/generate-client.sh
      working-directory: .
      env:
        VIRTUAL_ENV: backend/.venv
        POSTGRES_SERVER: db
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
        FIRST_SUPERUSER: ${{ secrets.FIRST_SUPERUSER }}
        FIRST_SUPERUSER_PASSWORD: ${{ secrets.FIRST_SUPERUSER_PASSWORD }}

    - name: Add changes to git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "github-actions"
        git add frontend/src/client

    - name: Push changes
      run: |
        git diff --staged --quiet || git commit -m "✨ Autogenerate frontend client"
        git push