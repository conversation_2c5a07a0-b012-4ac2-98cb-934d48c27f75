name: Test Backend

on:
  push:
    branches:
      - master
  pull_request:
    types:
      - opened
      - synchronize


jobs:
  test-backend:
    runs-on: ubuntu-latest
    env:
      STACK_NAME: ${{ github.repository }}
      POSTGRES_DB: app
      POSTGRES_SERVER: localhost  
      POSTGRES_USER: postgres
      POSTGRES_PORT: 5432
      POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
      DOMAIN: localhost
      DOCKER_IMAGE_BACKEND: backend
      FRONTEND_HOST: http://localhost:3000
      BACKEND_CORS_ORIGINS: "[\"http://localhost:3000\"]"
      ENVIRONMENT: local
      SECRET_KEY: ${{ secrets.SECRET_KEY }}
      FIRST_SUPERUSER: ${{ secrets.FIRST_SUPERUSER }}
      FIRST_SUPERUSER_PASSWORD: ${{ secrets.FIRST_SUPERUSER_PASSWORD }}
      SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
      DOCKER_IMAGE_FRONTEND: frontend
      SMTP_HOST: ${{ secrets.SMTP_HOST }}
      SMTP_PORT: 1025  # Port par défaut de mailcatcher
      SMTP_USER: ""  # Pas d'authentification nécessaire
      SMTP_PASSWORD: ""
      EMAILS_FROM_EMAIL: ${{ secrets.EMAILS_FROM_EMAIL }}
      EMAILS_ENABLED: "true"  # Activer explicitement les emails
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          version: "0.4.15"
          enable-cache: true
      - name: Create dummy .env file
        run: touch .env
      - name: Create traefik-public network
        run: docker network create traefik-public || true
      - run: docker compose down -v --remove-orphans
      - run: docker compose up -d db mailcatcher
      - name: Wait for db to be ready  # <-- Ajoutez cette étape
        run: |
          timeout 60 bash -c 'until docker compose exec db pg_isready -U postgres; do sleep 2; done'
      - name: Migrate DB
        run: uv run bash scripts/prestart.sh
        working-directory: backend
      - name: Run tests
        run: uv run bash scripts/tests-start.sh "Coverage for ${{ github.sha }}"
        working-directory: backend
      - run: docker compose down -v --remove-orphans
      - name: Store coverage files
        uses: actions/upload-artifact@v4
        with:
          name: coverage-html
          path: backend/htmlcov
          include-hidden-files: true