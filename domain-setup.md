
# Domain Setup for Tunleg.com

## 1. Domain Overview

- **Domain Name**: tunleg.com
- **Registrar**: Namecheap
- **Purchase Date**: July 2025
- **Primary Use**: LegalTech / Multi-environment web app
- **Linked VPS IP**: *************

---

## 2. Domain Protections Enabled

| Feature                | Status     |
|------------------------|------------|
| Auto-Renewal           | ✅ Enabled |
| Transfer Protection    | ✅ Enabled |
| DNSSEC                 | ✅ If available |

---

## 3. DNS Records Configuration (on Namecheap)

| Type | Host  | Value           | TTL        | Purpose                      |
|------|-------|------------------|------------|------------------------------|
| A    | @     | *************    | Automatic  | Main domain (`tunleg.com`)   |
| A    | dev   | *************    | Automatic  | Development subdomain        |
| A    | qa    | *************    | Automatic  | QA/staging subdomain         |

- DNS Records configured under the **Advanced DNS** tab.
- All traffic points to the VPS where Nginx and SSL will later be configured.

---

## 4. Subdomains Planned

- **prod** → tunleg.com
- **dev** → dev.tunleg.com
- **qa**  → qa.tunleg.com

---

## 5. Credentials & Storage

- **Registrar Login**: [Namecheap](https://www.namecheap.com)
- **Credentials Storage**: Should be saved in a shared secure vault (Bitwarden, 1Password, Vault, etc.)
- **2FA**: Recommended for Namecheap account

---

## 6. Notes

- DNS propagation may take up to 24 hours.
- Be sure to configure SSL certificates (Let's Encrypt) and redirects once web services are live.
- Document any changes to DNS records.

---

## 7. Next Steps

- Set up Nginx reverse proxy on VPS
- Install SSL via Let's Encrypt
- Route environments to appropriate containers or directories
