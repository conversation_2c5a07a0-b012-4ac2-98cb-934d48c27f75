#!/usr/bin/env python3
"""
Test script for bulk status update functionality
"""
import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "changethis"

def get_auth_token(email, password):
    """Get authentication token"""
    response = requests.post(
        f"{BASE_URL}/api/v1/login/access-token",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        data=f"username={email}&password={password}"
    )
    
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"Authentication failed: {response.status_code} - {response.text}")
        return None

def get_legal_cases(token):
    """Get list of legal cases"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/v1/legal-cases/", headers=headers)
    
    if response.status_code == 200:
        return response.json()["data"]
    else:
        print(f"Failed to get legal cases: {response.status_code} - {response.text}")
        return []

def test_bulk_status_update(token, case_ids, new_status, notes=None):
    """Test bulk status update"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "case_ids": case_ids,
        "new_status": new_status,
        "notes": notes
    }
    
    print(f"Testing bulk update with data: {json.dumps(data, indent=2)}")
    
    response = requests.patch(
        f"{BASE_URL}/api/v1/legal-cases/bulk/status",
        headers=headers,
        json=data
    )
    
    print(f"Response status: {response.status_code}")
    print(f"Response body: {json.dumps(response.json(), indent=2)}")
    
    return response.status_code == 200, response.json()

def main():
    print("🧪 Testing Bulk Status Update Functionality")
    print("=" * 50)
    
    # Step 1: Authenticate
    print("1. Authenticating as admin...")
    token = get_auth_token(ADMIN_EMAIL, ADMIN_PASSWORD)
    if not token:
        print("❌ Authentication failed")
        sys.exit(1)
    print("✅ Authentication successful")
    
    # Step 2: Get legal cases
    print("\n2. Fetching legal cases...")
    cases = get_legal_cases(token)
    if not cases:
        print("❌ No legal cases found")
        sys.exit(1)
    
    print(f"✅ Found {len(cases)} legal cases")
    for case in cases[:3]:  # Show first 3 cases
        print(f"   - {case['id']}: {case['title']} (Status: {case['status']})")
    
    # Step 3: Test bulk update with first 2 cases
    print("\n3. Testing bulk status update...")
    test_case_ids = [cases[0]['id'], cases[1]['id']] if len(cases) >= 2 else [cases[0]['id']]
    
    success, result = test_bulk_status_update(
        token=token,
        case_ids=test_case_ids,
        new_status="in_progress",
        notes="Test bulk update from script"
    )
    
    if success:
        print("✅ Bulk update successful!")
        print(f"   - Success count: {result['success_count']}")
        print(f"   - Failed count: {result['failed_count']}")
        print(f"   - Message: {result['message']}")
        
        if result['failed_cases']:
            print("   - Failed cases:")
            for failed in result['failed_cases']:
                print(f"     * {failed['case_id']}: {failed['error']}")
    else:
        print("❌ Bulk update failed!")
        print(f"   Error: {result}")
    
    # Step 4: Test with invalid data
    print("\n4. Testing with invalid case ID...")
    success, result = test_bulk_status_update(
        token=token,
        case_ids=["invalid-uuid"],
        new_status="closed",
        notes="Test with invalid ID"
    )
    
    if not success or result['failed_count'] > 0:
        print("✅ Invalid case ID properly rejected")
    else:
        print("❌ Invalid case ID should have been rejected")
    
    print("\n🎉 Testing completed!")

if __name__ == "__main__":
    main()
