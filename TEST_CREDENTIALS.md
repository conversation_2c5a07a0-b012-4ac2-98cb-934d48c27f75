# 🔐 EquiNova Test Credentials

This document contains test user credentials for different roles in the EquiNova application.

## 🚀 Quick Start

1. **Start the application**: `docker compose up -d`
2. **Access the frontend**: http://localhost:5173
3. **Use any of the credentials below to test different user roles**

## 👥 Test User Accounts

### 👑 **ADMIN**
- **Name**: System Administrator
- **Email**: `<EMAIL>`
- **Password**: `AdminTest123!`
- **Role**: Admin (Full system access)

### ⚖️ **LAWYERS**

#### Lawyer 1: <PERSON>
- **Email**: `<EMAIL>`
- **Password**: `LawyerTest123!`
- **Specialization**: Corporate Law
- **Bar ID**: BAR-2024-001
- **Assigned Users**: 
  - Assistant: <PERSON>s: <PERSON>, <PERSON>

#### Lawyer 2: <PERSON>
- **Email**: `<EMAIL>`
- **Password**: `LawyerTest123!`
- **Specialization**: Family Law
- **Bar ID**: BAR-2024-002
- **Assigned Users**:
  - Assistant: <PERSON>
  - Clients: <PERSON>, <PERSON>

#### Lawyer 3: <PERSON>
- **Email**: `<EMAIL>`
- **Password**: `LawyerTest123!`
- **Specialization**: Criminal Law
- **Bar ID**: BAR-2024-003
- **Assigned Users**: None (independent lawyer)

### 📋 **ASSISTANTS**

#### Assistant 1: Claire Moreau
- **Email**: `<EMAIL>`
- **Password**: `AssistantTest123!`
- **Assigned to**: Marie Dubois (lawyer1)

#### Assistant 2: Pierre Leroy
- **Email**: `<EMAIL>`
- **Password**: `AssistantTest123!`
- **Assigned to**: Jean Martin (lawyer2)

### 👤 **CLIENTS**

#### Client 1: Alice Johnson
- **Email**: `<EMAIL>`
- **Password**: `ClientTest123!`
- **Assigned to**: Marie Dubois (lawyer1)

#### Client 2: Robert Smith
- **Email**: `<EMAIL>`
- **Password**: `ClientTest123!`
- **Assigned to**: Marie Dubois (lawyer1)

#### Client 3: Emma Wilson
- **Email**: `<EMAIL>`
- **Password**: `ClientTest123!`
- **Assigned to**: Jean Martin (lawyer2)

#### Client 4: David Brown
- **Email**: `<EMAIL>`
- **Password**: `ClientTest123!`
- **Assigned to**: Jean Martin (lawyer2)

## 📋 Sample Legal Cases

The system includes 8 sample legal cases across different practice areas:

1. **Corporate Merger** - TechCorp & InnovateInc (Corporate Law)
2. **Employment Discrimination Case** - Alice Johnson (Employment Law)
3. **Divorce and Child Custody** - Emma Wilson (Family Law)
4. **Personal Injury** - Car Accident (Personal Injury)
5. **Criminal Defense** - Fraud Allegations (Criminal Law)
6. **Real Estate Transaction** - Commercial Property (Real Estate)
7. **Immigration Visa Application** - Family Reunification (Immigration)
8. **Intellectual Property Dispute** - Patent Infringement (IP Law)

## 📄 Sample Items/Documents

The system includes sample items like:
- Contract templates
- Legal research documents
- Client meeting notes
- Court filings
- Settlement agreements

## 🔄 Role-Based Access Testing

### What to Test by Role:

#### **Admin** (`<EMAIL>`)
- ✅ Full access to all features
- ✅ User management (create, edit, delete users)
- ✅ System administration
- ✅ All legal cases and documents

#### **Lawyer** (`<EMAIL>` or `<EMAIL>`)
- ✅ Own legal cases and client management
- ✅ Document creation and management
- ✅ Assigned assistants and clients
- ❌ Cannot access other lawyers' cases
- ❌ Limited admin functions

#### **Assistant** (`<EMAIL>`)
- ✅ Cases assigned to their lawyer
- ✅ Document management for assigned cases
- ✅ Client communication (when implemented)
- ❌ Cannot create new cases
- ❌ Limited to assigned lawyer's work

#### **Client** (`<EMAIL>`)
- ✅ Own cases and documents
- ✅ Communication with assigned lawyer
- ❌ Cannot access other clients' information
- ❌ Read-only access to most features

## 🛠️ Regenerating Test Data

To regenerate test data (if needed):

```bash
# Run the seeding script
docker compose exec backend python -m app.scripts.seed_test_data
```

## 🎯 Testing Navigation & UI

With these credentials, you can test:
- **Role-based navigation menus**
- **Sidebar collapsing/expanding**
- **Breadcrumb navigation**
- **User profile display**
- **Access control** (different users see different content)
- **Dashboard statistics** (varies by role)

## 📝 Notes

- All passwords follow the pattern: `[Role]Test123!`
- Users are pre-assigned to lawyers for testing hierarchical relationships
- Sample data includes realistic legal case types and descriptions
- The system maintains proper role-based access control

---

**🚀 Ready to test! Start with the admin account for full system overview, then try different roles to see how the UI adapts.**
