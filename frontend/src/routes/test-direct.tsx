import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/test-direct')({
  component: TestDirect,
})

function TestDirect() {
  return (
    <div style={{ padding: '32px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ fontSize: '32px', marginBottom: '16px', color: '#2563eb' }}>
        🧪 Test Direct - TunLeg
      </h1>
      <p style={{ fontSize: '18px', marginBottom: '16px' }}>
        Cette page de test bypasse le layout et l'authentification.
      </p>
      <div style={{ 
        padding: '16px', 
        backgroundColor: '#dcfce7', 
        border: '1px solid #16a34a',
        borderRadius: '8px',
        marginBottom: '16px'
      }}>
        <p style={{ color: '#16a34a', fontWeight: 'bold', margin: 0 }}>
          ✅ TunLeg Frontend fonctionne !
        </p>
      </div>
      <div style={{ 
        padding: '16px', 
        backgroundColor: '#fef3c7', 
        border: '1px solid #d97706',
        borderRadius: '8px'
      }}>
        <h3 style={{ color: '#d97706', marginTop: 0 }}>Diagnostic :</h3>
        <ul style={{ color: '#92400e' }}>
          <li>✅ React Router fonctionne</li>
          <li>✅ Vite dev server fonctionne</li>
          <li>✅ JavaScript s'exécute</li>
          <li>⚠️ Le problème est probablement dans le layout ou l'authentification</li>
        </ul>
      </div>
      <div style={{ marginTop: '24px' }}>
        <a 
          href="/login" 
          style={{ 
            color: '#2563eb', 
            textDecoration: 'underline',
            marginRight: '16px'
          }}
        >
          Aller à la page de login
        </a>
        <a 
          href="/" 
          style={{ 
            color: '#2563eb', 
            textDecoration: 'underline'
          }}
        >
          Aller à l'accueil
        </a>
      </div>
    </div>
  )
}
