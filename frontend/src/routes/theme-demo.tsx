/**
=========================================================
* EquiNova Material Theme Demo Page
=========================================================

* Demo page to showcase Material-UI theme integration
* Used for testing theme switching and components

=========================================================
*/

import React from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  TextField,
  Grid,
  Paper,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';

import { ThemeControls, FloatingThemeButton } from '../themes/components/ThemeToggle';
import ThemeConfigurator from '../themes/components/ThemeConfigurator';

const ThemeDemoPage: React.FC = () => {
  const sampleData = [
    { id: 1, title: 'Contract Review', client: 'ABC Corp', status: 'In Progress', priority: 'High' },
    { id: 2, title: 'Patent Filing', client: 'XYZ Ltd', status: 'Completed', priority: 'Medium' },
    { id: 3, title: 'Litigation Support', client: 'DEF Inc', status: 'Open', priority: 'Urgent' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'In Progress': return 'warning';
      case 'Open': return 'info';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom>
            Material-UI Theme Demo
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Testing EquiNova's new Material Design theme system
          </Typography>
        </Box>
        <ThemeControls />
      </Box>

      {/* Alert */}
      <Alert severity="info" sx={{ mb: 4 }}>
        This is a demo page showcasing the new Material-UI theme integration.
        Use the theme controls above or the floating button to customize the appearance.
      </Alert>

      {/* Progress */}
      <Box mb={4}>
        <Typography variant="h6" gutterBottom>
          Material-UI Integration Progress
        </Typography>
        <LinearProgress variant="determinate" value={85} sx={{ height: 8, borderRadius: 4 }} color="success" />
        <Typography variant="body2" color="text.secondary" mt={1}>
          85% - Phase 1 & 2 Complete: Theme foundation + Core layout components
        </Typography>
      </Box>

      {/* Material Layout Status */}
      <Alert severity="success" sx={{ mb: 4 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          🎉 Material-UI Layout System Ready!
        </Typography>
        <Typography variant="body2" mb={2}>
          Phase 1 & 2 completed: Theme foundation and core layout components are fully implemented.
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          <Button variant="outlined" size="small" href="/material-demo">
            View Material Layout Demo
          </Button>
          <Button variant="text" size="small" color="inherit">
            ✅ Professional Sidebar • ✅ Responsive Design • ✅ Theme Integration
          </Button>
        </Box>
      </Alert>

      <Grid container spacing={3}>
        {/* Cards Section */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              <Typography variant="h4" color="primary" gutterBottom>
                24
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Legal Cases
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h4" color="success.main" gutterBottom>
                156
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Cases This Year
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Revenue
              </Typography>
              <Typography variant="h4" color="secondary.main" gutterBottom>
                $2.4M
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total This Quarter
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Form Section */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Sample Form
            </Typography>
            <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Case Title"
                variant="outlined"
                fullWidth
                placeholder="Enter case title..."
              />
              <TextField
                label="Client Name"
                variant="outlined"
                fullWidth
                placeholder="Enter client name..."
              />
              <TextField
                label="Description"
                variant="outlined"
                fullWidth
                multiline
                rows={3}
                placeholder="Enter case description..."
              />
              <Box display="flex" gap={2}>
                <Button variant="contained" startIcon={<AddIcon />}>
                  Create Case
                </Button>
                <Button variant="outlined">
                  Cancel
                </Button>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Table Section */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Cases
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Title</TableCell>
                    <TableCell>Client</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sampleData.map((row) => (
                    <TableRow key={row.id} hover>
                      <TableCell>{row.title}</TableCell>
                      <TableCell>{row.client}</TableCell>
                      <TableCell>
                        <Chip
                          label={row.status}
                          color={getStatusColor(row.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={row.priority}
                          color={getPriorityColor(row.priority) as any}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={0.5}>
                          <Button size="small" startIcon={<ViewIcon />}>
                            View
                          </Button>
                          <Button size="small" startIcon={<EditIcon />}>
                            Edit
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Floating Theme Button */}
      <FloatingThemeButton />

      {/* Theme Configurator */}
      <ThemeConfigurator />
    </Container>
  );
};

export const Route = createFileRoute('/theme-demo')({
  component: ThemeDemoPage,
});

export default ThemeDemoPage;
