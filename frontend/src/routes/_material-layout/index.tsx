/**
=========================================================
* EquiNova Material Dashboard
=========================================================

* Material-UI version of the main dashboard
* Migrated from Chakra UI to Material Design

=========================================================
*/

import React from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Grid,
  Typography,
  Box,
  Card,
  CardContent,
  Alert,
  Container,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Gavel as GavelIcon,
} from '@mui/icons-material';

import useAuth from '@/hooks/useAuth';
import MDBox from '../../themes/components/MDBox';
import MDTypography from '../../themes/components/MDTypography';

// Material-UI versions of dashboard components
import MaterialStatisticsCards from '../../components/Dashboard/MaterialStatisticsCards';
import MaterialRecentCases from '../../components/Dashboard/MaterialRecentCases';
import MaterialQuickActions from '../../components/Dashboard/MaterialQuickActions';
import MaterialActivityFeed from '../../components/Dashboard/MaterialActivityFeed';

export const Route = createFileRoute('/_material-layout/')({
  component: MaterialDashboard,
});

function MaterialDashboard() {
  const { user: currentUser } = useAuth();

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const getUserRole = () => {
    if (currentUser?.is_superuser) return 'Administrator';
    if (currentUser?.role) {
      return currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);
    }
    return 'User';
  };

  return (
    <MDBox>
      {/* Welcome Section */}
      <MDBox mb={4}>
        <MDTypography variant="h4" fontWeight="bold" color="dark" gutterBottom>
          {getGreeting()}, {currentUser?.full_name || currentUser?.email} 👋🏼
        </MDTypography>
        <MDTypography variant="body1" color="text">
          Welcome to your dashboard, {getUserRole()}. Here's what's happening today.
        </MDTypography>
      </MDBox>

      {/* Migration Notice */}
      <Alert severity="success" sx={{ mb: 4 }}>
        <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
          🎉 Material-UI Dashboard Active!
        </Typography>
        <Typography variant="body2">
          You're now viewing the new Material Design dashboard. All features have been migrated to Material-UI components.
        </Typography>
      </Alert>

      {/* Statistics Cards Section */}
      <MDBox mb={4}>
        <MDTypography variant="h6" fontWeight="medium" color="dark" gutterBottom>
          Overview
        </MDTypography>
        <MaterialStatisticsCards />
      </MDBox>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Left Column */}
        <Grid item xs={12} lg={6}>
          <MDBox display="flex" flexDirection="column" gap={3}>
            <MaterialRecentCases />
            <MaterialQuickActions />
          </MDBox>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} lg={6}>
          <MDBox display="flex" flexDirection="column" gap={3}>
            {/* Status Distribution Chart Placeholder */}
            <Card>
              <CardContent>
                <Box 
                  display="flex" 
                  alignItems="center" 
                  justifyContent="center" 
                  minHeight={200}
                  bgcolor="primary.50"
                  borderRadius={2}
                  border="1px solid"
                  borderColor="primary.200"
                >
                  <Box textAlign="center">
                    <GavelIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h6" color="primary.main" gutterBottom>
                      📊 Status Distribution Chart
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Coming soon in Phase 4 - Enhanced Components
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
            
            <MaterialActivityFeed />
          </MDBox>
        </Grid>
      </Grid>

      {/* Phase 3 Progress */}
      <MDBox mt={6}>
        <Card elevation={2}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🚀 Phase 3: Page Migration Progress
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  ✅ Migrated to Material-UI:
                </Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem', fontSize: '0.875rem' }}>
                  <li>Dashboard (this page)</li>
                  <li>Theme system integration</li>
                  <li>Layout components</li>
                  <li>Navigation system</li>
                </ul>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  🔄 Next to Migrate:
                </Typography>
                <ul style={{ margin: 0, paddingLeft: '1.5rem', fontSize: '0.875rem' }}>
                  <li>Legal Cases management</li>
                  <li>Admin/User management</li>
                  <li>Items management</li>
                  <li>Settings page</li>
                </ul>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </MDBox>
    </MDBox>
  );
}

export default MaterialDashboard;
