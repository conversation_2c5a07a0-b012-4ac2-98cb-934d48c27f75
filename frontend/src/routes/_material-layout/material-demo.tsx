/**
=========================================================
* EquiNova Material Layout Demo
=========================================================

* Demo page to showcase the new Material-UI layout
* Shows the complete Material Dashboard experience

=========================================================
*/

import React from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';

import MDBox from '../../themes/components/MDBox';
import MDTypography from '../../themes/components/MDTypography';

const MaterialLayoutDemo: React.FC = () => {
  const sampleData = [
    { id: 1, title: 'Contract Review', client: 'ABC Corp', status: 'In Progress', priority: 'High' },
    { id: 2, title: 'Patent Filing', client: 'XYZ Ltd', status: 'Completed', priority: 'Medium' },
    { id: 3, title: 'Litigation Support', client: 'DEF Inc', status: 'Open', priority: 'Urgent' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'In Progress': return 'warning';
      case 'Open': return 'info';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  return (
    <MDBox>
      {/* Header */}
      <MDBox mb={4}>
        <MDTypography variant="h4" fontWeight="bold" color="dark">
          Material-UI Layout Demo
        </MDTypography>
        <MDTypography variant="body1" color="text" mt={1}>
          Experience the new Material Design layout with professional sidebar navigation
        </MDTypography>
      </MDBox>

      {/* Alert */}
      <Alert severity="success" sx={{ mb: 4 }}>
        🎉 Material-UI layout is working perfectly! The sidebar, navigation, and theme system are fully integrated.
      </Alert>

      {/* Progress */}
      <MDBox mb={4}>
        <MDTypography variant="h6" fontWeight="medium" mb={2}>
          Migration Progress
        </MDTypography>
        <LinearProgress 
          variant="determinate" 
          value={85} 
          sx={{ height: 8, borderRadius: 4 }} 
          color="success"
        />
        <MDTypography variant="body2" color="text" mt={1}>
          85% - Core layout components migrated to Material-UI
        </MDTypography>
      </MDBox>

      <Grid container spacing={3}>
        {/* Statistics Cards */}
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: 2,
                    backgroundColor: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                  }}
                >
                  <TrendingUpIcon />
                </Box>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    24
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Legal Cases
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: 2,
                    backgroundColor: 'success.main',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                  }}
                >
                  <PeopleIcon />
                </Box>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    156
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Clients
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: 2,
                    backgroundColor: 'secondary.main',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                  }}
                >
                  <MoneyIcon />
                </Box>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="secondary.main">
                    $2.4M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Revenue This Quarter
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Form Section */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Quick Case Creation
              </Typography>
              <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
                <TextField
                  label="Case Title"
                  variant="outlined"
                  fullWidth
                  placeholder="Enter case title..."
                />
                <TextField
                  label="Client Name"
                  variant="outlined"
                  fullWidth
                  placeholder="Enter client name..."
                />
                <TextField
                  label="Description"
                  variant="outlined"
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Enter case description..."
                />
                <Box display="flex" gap={2}>
                  <Button variant="contained" startIcon={<AddIcon />} fullWidth>
                    Create Case
                  </Button>
                  <Button variant="outlined" fullWidth>
                    Cancel
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Table Section */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Recent Cases
              </Typography>
              <TableContainer sx={{ mt: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Title</TableCell>
                      <TableCell>Client</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Priority</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sampleData.map((row) => (
                      <TableRow key={row.id} hover>
                        <TableCell>{row.title}</TableCell>
                        <TableCell>{row.client}</TableCell>
                        <TableCell>
                          <Chip
                            label={row.status}
                            color={getStatusColor(row.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={row.priority}
                            color={getPriorityColor(row.priority) as any}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Box display="flex" gap={0.5}>
                            <Button size="small" startIcon={<ViewIcon />}>
                              View
                            </Button>
                            <Button size="small" startIcon={<EditIcon />}>
                              Edit
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Features List */}
      <MDBox mt={4}>
        <MDTypography variant="h6" fontWeight="medium" mb={2}>
          ✅ Material-UI Layout Features Working:
        </MDTypography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
              <li>Professional Material Design sidebar</li>
              <li>Collapsible navigation with smooth animations</li>
              <li>Role-based menu filtering</li>
              <li>Responsive mobile drawer</li>
              <li>Material-UI breadcrumb navigation</li>
            </ul>
          </Grid>
          <Grid item xs={12} md={6}>
            <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
              <li>Dark/Light theme integration</li>
              <li>Real-time theme switching</li>
              <li>Material Design components</li>
              <li>Consistent typography and spacing</li>
              <li>Professional card layouts</li>
            </ul>
          </Grid>
        </Grid>
      </MDBox>
    </MDBox>
  );
};

export const Route = createFileRoute('/_material-layout/material-demo')({
  component: MaterialLayoutDemo,
});

export default MaterialLayoutDemo;
