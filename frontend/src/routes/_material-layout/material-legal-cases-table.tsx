/**
=========================================================
* EquiNova Material Legal Cases Table Demo
=========================================================

* Demo page for the new Material-UI legal cases table
* Testing the migration from Chakra UI to Material-UI

=========================================================
*/

import React from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Container,
  Typography,
  Box,
  Alert,
  Chip,
  Paper,
  Stack,
} from '@mui/material';
import {
  TableChart as TableIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';

import MaterialLegalCasesTable from '../../components/LegalCases/MaterialLegalCasesTable';
import MaterialAddLegalCase from '../../components/LegalCases/MaterialAddLegalCase';

const MaterialLegalCasesTableDemo: React.FC = () => {
  const handleCaseClick = (caseId: string) => {
    console.log('🎯 Material table case clicked:', caseId);
    // TODO: Navigate to case detail page
    alert(`Case ${caseId} clicked! (Material-UI implementation)`);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <TableIcon color="primary" sx={{ fontSize: 32 }} />
          <Typography variant="h4" fontWeight="bold">
            Material-UI Legal Cases Table
          </Typography>
          <Chip
            label="Phase 3"
            color="primary"
            variant="outlined"
            size="small"
          />
        </Box>
        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
          Enhanced DataGrid implementation with Material Design components
        </Typography>
      </Box>

      {/* Status Alert */}
      <Alert 
        severity="success" 
        sx={{ mb: 4, borderRadius: 2 }}
        icon={<CheckIcon />}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          🚀 Material-UI DataGrid Implementation Ready!
        </Typography>
        <Typography variant="body2" mb={2}>
          Phase 3 progress: Legal cases table migrated to Material-UI DataGrid with enhanced features.
        </Typography>
        <Stack direction="row" spacing={1} flexWrap="wrap">
          <Chip label="✅ DataGrid Integration" size="small" variant="outlined" />
          <Chip label="✅ Server-side Pagination" size="small" variant="outlined" />
          <Chip label="✅ Advanced Filtering" size="small" variant="outlined" />
          <Chip label="✅ Bulk Operations" size="small" variant="outlined" />
          <Chip label="✅ Material Status Indicators" size="small" variant="outlined" />
          <Chip label="✅ Responsive Design" size="small" variant="outlined" />
          <Chip label="✅ Add/Edit Forms" size="small" variant="outlined" />
          <Chip label="✅ Material Design Dialogs" size="small" variant="outlined" />
        </Stack>
      </Alert>

      {/* Features Overview */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          🎯 Enhanced Features
        </Typography>
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
          <Box flex={1}>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              DataGrid Advantages
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • Built-in sorting, filtering, and pagination<br />
              • Column resizing and reordering<br />
              • Export functionality<br />
              • Virtualization for performance
            </Typography>
          </Box>
          <Box flex={1}>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              Material Design
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • Consistent visual language<br />
              • Improved accessibility<br />
              • Theme-aware components<br />
              • Professional appearance
            </Typography>
          </Box>
          <Box flex={1}>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              Enhanced UX
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • Material Design forms and dialogs<br />
              • Improved bulk operations<br />
              • Advanced search and filters<br />
              • Floating Action Button (FAB)<br />
              • Mobile-responsive design
            </Typography>
          </Box>
        </Stack>
      </Paper>

      {/* Material Legal Cases Table */}
      <MaterialLegalCasesTable onCaseClick={handleCaseClick} />

      {/* Floating Add Button */}
      <MaterialAddLegalCase variant="fab" size="large" />

      {/* Footer Info */}
      <Box mt={4} textAlign="center">
        <Typography variant="caption" color="text.secondary">
          Material-UI DataGrid • Phase 3 Implementation • EquiNova Legal Management
        </Typography>
      </Box>
    </Container>
  );
};

export const Route = createFileRoute('/_material-layout/material-legal-cases-table')({
  component: MaterialLegalCasesTableDemo,
});

export default MaterialLegalCasesTableDemo;
