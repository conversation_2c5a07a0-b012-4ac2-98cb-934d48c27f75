/**
=========================================================
* EquiNova Theme Studio Demo
=========================================================

* Demo page for Phase 4 advanced theming features
* Showcases brand customization and preferences management

=========================================================
*/

import React from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Container,
  Typography,
  Box,
  Alert,
  Chip,
  Paper,
  Stack,
  Grid,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import {
  Palette as PaletteIcon,
  CheckCircle as CheckIcon,
  Settings as SettingsIcon,
  Brush as BrushIcon,
  CloudSync as SyncIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';

import BrandCustomizer from '../../themes/components/BrandCustomizer';
import { useThemePreferences } from '../../themes/hooks/useThemePreferences';
import {
  useMaterialUIController,
  setOpenConfigurator,
} from '../../themes/context';

const ThemeStudioDemo: React.FC = () => {
  const { dispatch } = useMaterialUIController();
  const {
    savePreferences,
    exportPreferences,
    resetToDefaults,
  } = useThemePreferences();

  const handleOpenConfigurator = () => {
    setOpenConfigurator(dispatch, true);
  };

  const handleExportDemo = () => {
    const jsonData = exportPreferences();
    console.log('Exported preferences:', jsonData);
    alert('Check console for exported preferences JSON');
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={4}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <PaletteIcon color="primary" sx={{ fontSize: 32 }} />
          <Typography variant="h4" fontWeight="bold">
            Theme Studio
          </Typography>
          <Chip
            label="Phase 4"
            color="primary"
            variant="outlined"
            size="small"
          />
        </Box>
        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
          Advanced theming features with brand customization and preferences management
        </Typography>
      </Box>

      {/* Status Alert */}
      <Alert 
        severity="success" 
        sx={{ mb: 4, borderRadius: 2 }}
        icon={<CheckIcon />}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          🎨 Phase 4: Advanced Theming Features Ready!
        </Typography>
        <Typography variant="body2" mb={2}>
          Enhanced theme system with brand customization, preferences persistence, and export/import functionality.
        </Typography>
        <Stack direction="row" spacing={1} flexWrap="wrap">
          <Chip label="✅ Preferences Persistence" size="small" variant="outlined" />
          <Chip label="✅ Export/Import Themes" size="small" variant="outlined" />
          <Chip label="✅ Brand Customization" size="small" variant="outlined" />
          <Chip label="✅ Custom Colors" size="small" variant="outlined" />
          <Chip label="✅ Logo Upload" size="small" variant="outlined" />
          <Chip label="✅ Font Selection" size="small" variant="outlined" />
        </Stack>
      </Alert>

      {/* Features Overview */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <SyncIcon color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Preferences Management
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Automatic saving, export/import functionality, and cross-session persistence.
              </Typography>
              <Stack spacing={1}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={savePreferences}
                  startIcon={<SyncIcon />}
                >
                  Save Preferences
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleExportDemo}
                  startIcon={<DownloadIcon />}
                >
                  Export Demo
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={resetToDefaults}
                  color="warning"
                >
                  Reset to Defaults
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <BrushIcon color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Brand Customization
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Custom colors, logo upload, and typography selection for brand identity.
              </Typography>
              <Typography variant="body2">
                • Custom color palettes<br />
                • Logo upload and management<br />
                • Font family selection<br />
                • Real-time preview
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <SettingsIcon color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Enhanced Controls
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Advanced theme configurator with tabbed interface and improved UX.
              </Typography>
              <Button
                variant="contained"
                size="small"
                onClick={handleOpenConfigurator}
                startIcon={<SettingsIcon />}
                fullWidth
              >
                Open Theme Studio
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Brand Customizer Demo */}
      <Paper sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Brand Customization Demo
        </Typography>
        <Typography variant="body2" color="text.secondary" mb={3}>
          Try out the brand customization features below. Changes are applied in real-time.
        </Typography>
        
        <BrandCustomizer
          onCustomColorsChange={(colors) => {
            console.log('Demo: Custom colors changed', colors);
          }}
          onCustomLogoChange={(logo) => {
            console.log('Demo: Custom logo changed', logo ? 'Logo uploaded' : 'Logo removed');
          }}
          onCustomFontChange={(font) => {
            console.log('Demo: Custom font changed', font);
          }}
        />
      </Paper>

      {/* Technical Details */}
      <Paper sx={{ p: 3, mt: 4, borderRadius: 2, bgcolor: 'grey.50' }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          🔧 Technical Implementation
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              Persistence Layer
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • localStorage for client-side persistence<br />
              • Auto-save with debouncing (2s delay)<br />
              • Version compatibility checking<br />
              • Graceful fallback to defaults
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              Export/Import System
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • JSON format with metadata<br />
              • Validation and error handling<br />
              • Cross-user theme sharing<br />
              • Backup and restore functionality
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Footer Info */}
      <Box mt={4} textAlign="center">
        <Typography variant="caption" color="text.secondary">
          Theme Studio • Phase 4 Implementation • EquiNova Advanced Theming
        </Typography>
      </Box>
    </Container>
  );
};

export const Route = createFileRoute('/_material-layout/theme-studio')({
  component: ThemeStudioDemo,
});

export default ThemeStudioDemo;
