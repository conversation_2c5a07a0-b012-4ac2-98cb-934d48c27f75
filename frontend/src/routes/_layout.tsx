import { Flex, Box, useBreakpointValue } from "@chakra-ui/react"
import { Outlet, createFileRoute, redirect } from "@tanstack/react-router"

import Navbar from "@/components/Common/Navbar"
import Sidebar from "@/components/Common/Sidebar"
import Breadcrumb from "@/components/Common/Breadcrumb"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { SkipToMainContent } from "@/components/ui/accessibility"
import { isLoggedIn } from "@/hooks/useAuth"

export const Route = createFileRoute("/_layout")({
  component: Layout,
  beforeLoad: async () => {
    if (!isLoggedIn()) {
      throw redirect({
        to: "/login",
      })
    }
  },
})

function Layout() {
  // Responsive design values
  const contentPadding = useBreakpointValue({
    base: 4,
    md: 6,
    lg: 8
  })

  const cardPadding = useBreakpointValue({
    base: 4,
    md: 6
  })

  const showNavbar = useBreakpointValue({
    base: false,
    md: true
  })

  return (
    <ErrorBoundary>
      {/* Skip to main content link for accessibility */}
      <SkipToMainContent />

      <Flex direction="column" h="100vh" bg="gray.50">
        {/* Navbar - Hidden on mobile */}
        {showNavbar && <Navbar />}

        <Flex flex="1" overflow="hidden">
          <Sidebar />

          {/* Main Content Area */}
          <Box
            flex="1"
            overflowY="auto"
            bg="gray.50"
            position="relative"
            role="main"
            id="main-content"
          >
            <Box
              p={contentPadding}
              maxW="100%"
              mx="auto"
              minH="100%"
            >
              {/* Breadcrumb Navigation */}
              <Breadcrumb />

              {/* Content Container */}
              <Box
                bg="white"
                borderRadius={{ base: "md", md: "lg" }}
                p={cardPadding}
                shadow="sm"
                borderWidth="1px"
                borderColor="gray.200"
                minH={{ base: "calc(100vh - 120px)", md: "auto" }}
                position="relative"
              >
                <ErrorBoundary>
                  <Outlet />
                </ErrorBoundary>
              </Box>
            </Box>
          </Box>
        </Flex>
      </Flex>
    </ErrorBoundary>
  )
}

export default Layout
