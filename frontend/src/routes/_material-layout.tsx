/**
=========================================================
* EquiNova Material Layout Route
=========================================================

* Material-UI based layout route
* Alternative to the Chakra UI layout for testing

=========================================================
*/

import React from 'react';
import { Box, useTheme, useMediaQuery } from '@mui/material';
import { Outlet, createFileRoute, redirect } from '@tanstack/react-router';

import { ErrorBoundary } from '@/components/ui/error-boundary';
import { SkipToMainContent } from '@/components/ui/accessibility';
import { isLoggedIn } from '@/hooks/useAuth';

import Sidenav from '../themes/components/Sidenav';
import routes from '../themes/components/Sidenav/routes';
import MaterialBreadcrumb from '../components/Layout/MaterialBreadcrumb';
import { FloatingThemeButton } from '../themes/components/ThemeToggle';
import ThemeConfigurator from '../themes/components/ThemeConfigurator';
import MDBox from '../themes/components/MDBox';

import { useMaterialUIController } from '../themes/context';

export const Route = createFileRoute('/_material-layout')({
  component: MaterialLayout,
  beforeLoad: async () => {
    if (!isLoggedIn()) {
      throw redirect({
        to: '/login',
      });
    }
  },
});

function MaterialLayout() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const { state } = useMaterialUIController();
  const { miniSidenav, darkMode } = state;

  const sidenavWidth = miniSidenav ? 96 : 250;

  return (
    <ErrorBoundary>
      {/* Skip to main content link for accessibility */}
      <SkipToMainContent />

      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* Sidenav */}
        <Sidenav
          color="info"
          brand="/assets/images/fastapi-logo.svg"
          brandName="EquiNova"
          routes={routes}
        />

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            width: isMobile ? '100%' : `calc(100% - ${sidenavWidth}px)`,
            ml: isMobile ? 0 : `${sidenavWidth}px`,
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
            backgroundColor: darkMode ? theme.palette.grey[100] : theme.palette.grey[50],
            minHeight: '100vh',
            position: 'relative',
          }}
        >
          {/* Content Container */}
          <MDBox
            sx={{
              p: { xs: 2, sm: 3 },
              maxWidth: '100%',
              mx: 'auto',
              minHeight: '100vh',
            }}
          >
            {/* Breadcrumb Navigation */}
            <MaterialBreadcrumb />

            {/* Main Content Card */}
            <MDBox
              bgColor="white"
              borderRadius="lg"
              shadow="md"
              sx={{
                p: { xs: 2, sm: 3 },
                minHeight: 'calc(100vh - 140px)',
                position: 'relative',
              }}
            >
              <ErrorBoundary>
                <Outlet />
              </ErrorBoundary>
            </MDBox>
          </MDBox>
        </Box>
      </Box>

      {/* Floating Theme Button */}
      <FloatingThemeButton />
      
      {/* Theme Configurator */}
      <ThemeConfigurator />
    </ErrorBoundary>
  );
}

export default MaterialLayout;
