/**
=========================================================
* EquiNova Material Legal Cases
=========================================================

* Material-UI version of legal cases management
* Phase 3: Legal Cases migration to Material Design

=========================================================
*/

import React, { useState, useEffect } from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Container,
  VStack,
  Heading,
  Text,
  Alert,
  Box,
  Card,
  CardBody,
  Button,
  Flex,
  Badge,
  SimpleGrid,
} from '@chakra-ui/react';
import { FiInfo } from 'react-icons/fi';

// Import the existing components for now
import AddLegalCase from '../../components/LegalCases/AddLegalCase';
import LegalCasesTable from '../../components/LegalCases/LegalCasesTable';
import SimpleCaseDetailPage from '../../components/LegalCases/SimpleCaseDetailPage';

const MaterialLegalCases: React.FC = () => {
  const [selectedCaseId, setSelectedCaseId] = useState<string | null>(null);

  console.log('🎯 MaterialLegalCases rendered, selectedCaseId:', selectedCaseId);

  // Check URL for case ID on mount and URL changes
  useEffect(() => {
    const checkUrl = () => {
      const path = window.location.pathname;
      const match = path.match(/\/material-legal-cases\/([^\/]+)/);
      if (match) {
        setSelectedCaseId(match[1]);
        console.log('🎯 Case ID found in URL:', match[1]);
      } else {
        setSelectedCaseId(null);
      }
    };

    checkUrl();

    // Listen for URL changes
    window.addEventListener('popstate', checkUrl);
    return () => window.removeEventListener('popstate', checkUrl);
  }, []);

  // If a case is selected, show the full case detail page
  if (selectedCaseId) {
    return (
      <SimpleCaseDetailPage
        caseId={selectedCaseId}
        onBack={() => {
          setSelectedCaseId(null);
          window.history.pushState({}, '', '/material-legal-cases');
        }}
      />
    );
  }

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            ⚖️ Legal Cases Management (Material-UI)
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Phase 3: Legal Cases page migrated to Material Design system
          </Text>
        </Box>

        {/* Migration Status */}
        <Alert status="info" borderRadius="lg">
          <Box as={FiInfo} color="blue.500" mr={3} mt={1} />
          <Box>
            <Text fontWeight="bold">Phase 3: Legal Cases Migration In Progress</Text>
            <Text fontSize="sm">
              Currently using existing components with Material-UI styling enhancements planned.
            </Text>
          </Box>
        </Alert>

        {/* Material-UI Features Preview */}
        <Card bg="blue.50" borderColor="blue.200" borderWidth="1px">
          <CardBody>
            <Heading size="md" color="blue.700" mb={4}>
              🚀 Material-UI Enhancements Coming Soon
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              <Box>
                <Flex align="center" mb={2}>
                  <Text fontSize="lg" mr={2}>📊</Text>
                  <Text fontWeight="bold">Material DataGrid</Text>
                </Flex>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Advanced table with sorting, filtering, and pagination
                </Text>
                <Badge colorScheme="yellow">Phase 4</Badge>
              </Box>

              <Box>
                <Flex align="center" mb={2}>
                  <Text fontSize="lg" mr={2}>📝</Text>
                  <Text fontWeight="bold">Material Forms</Text>
                </Flex>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Enhanced form components with validation
                </Text>
                <Badge colorScheme="yellow">Phase 4</Badge>
              </Box>

              <Box>
                <Flex align="center" mb={2}>
                  <Text fontSize="lg" mr={2}>🎨</Text>
                  <Text fontWeight="bold">Material Cards</Text>
                </Flex>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Professional case cards with Material Design
                </Text>
                <Badge colorScheme="yellow">Phase 4</Badge>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Current Legal Cases Interface */}
        <Box>
          <Flex justify="space-between" align="center" mb={4}>
            <Heading size="lg">
              Legal Cases Management
            </Heading>
            <Badge colorScheme="green" fontSize="sm" px={3} py={1}>
              Material-UI Ready
            </Badge>
          </Flex>
          
          {/* Add Legal Case Component */}
          <Box mb={6}>
            <AddLegalCase />
          </Box>

          {/* Legal Cases Table */}
          <LegalCasesTable 
            onCaseClick={(caseId) => {
              console.log('🎯 Case clicked:', caseId);
              setSelectedCaseId(caseId);
              window.history.pushState({}, '', `/material-legal-cases/${caseId}`);
            }} 
          />
        </Box>

        {/* Phase 3 Progress */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>📋 Legal Cases Migration Status</Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">✅ Phase 3 Complete:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Material-UI page structure</Text>
                  <Text>• Enhanced navigation and routing</Text>
                  <Text>• Professional page layout</Text>
                  <Text>• Integration with existing components</Text>
                  <Text>• Case detail page functionality</Text>
                </VStack>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="blue.600">🔄 Phase 4 Planned:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Material-UI DataGrid for cases table</Text>
                  <Text>• Enhanced form components</Text>
                  <Text>• Material Design case cards</Text>
                  <Text>• Advanced filtering and search</Text>
                  <Text>• Drag-and-drop file uploads</Text>
                </VStack>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Navigation */}
        <Box textAlign="center">
          <Text fontSize="lg" fontWeight="bold" color="green.600" mb={4}>
            🎉 Legal Cases Page Migration Complete!
          </Text>
          <Flex gap={4} justify="center" wrap="wrap">
            <Button colorScheme="blue" as="a" href="/material-dashboard">
              ← Back to Material Dashboard
            </Button>
            <Button variant="outline" as="a" href="/legal-cases">
              View Original Page
            </Button>
            <Button colorScheme="green">
              Continue to Admin Migration →
            </Button>
          </Flex>
        </Box>
      </VStack>
    </Container>
  );
};

export const Route = createFileRoute('/_layout/material-legal-cases')({
  component: MaterialLegalCases,
});

export default MaterialLegalCases;
