import { Container, VStack, Text, Button } from "@chakra-ui/react"
import { useEffect, useState } from "react"
import { createFileRoute } from "@tanstack/react-router"

// Simple URL-based case detail component
export const Route = createFileRoute("/_layout/legal-cases/$caseId")({
  component: CaseDetailPage,
})
function CaseDetailPage() {
  const [caseId, setCaseId] = useState<string | null>(null)

  useEffect(() => {
    // Extract case ID from URL path
    const path = window.location.pathname
    const match = path.match(/\/legal-cases\/([^\/]+)/)
    if (match) {
      setCaseId(match[1])
      console.log('🎯 Case ID extracted from URL:', match[1])
    }
  }, [])

  const handleBackToList = () => {
    window.history.pushState({}, '', '/legal-cases')
    window.location.reload()
  }

  console.log('🎯 CaseDetailPage COMPONENT RENDERED!')
  console.log('🎯 Current URL:', window.location.href)
  console.log('🎯 Extracted Case ID:', caseId)

  if (!caseId) {
    return (
      <Container maxW="full" py={8}>
        <VStack gap={4} align="center" justify="center" minH="400px">
          <Text color="red.500" fontSize="lg">
            No case ID found in URL
          </Text>
          <Button onClick={handleBackToList}>
            ← Back to Legal Cases
          </Button>
        </VStack>
      </Container>
    )
  }

  // Simple test to ensure component renders
  return (
    <Container maxW="full" py={8}>
      <VStack gap={4} align="center" justify="center" minH="400px">
        <Text color="green.500" fontSize="2xl" fontWeight="bold">
          🎯 CASE DETAIL COMPONENT IS WORKING! 🎯
        </Text>
        <Text color="blue.600" fontSize="lg">
          Case ID: {caseId}
        </Text>
        <Text color="gray.600" fontSize="sm">
          URL: {window.location.href}
        </Text>
        <Button colorScheme="blue" onClick={handleBackToList}>
          ← Back to Legal Cases
        </Button>
      </VStack>
    </Container>
  )
}
