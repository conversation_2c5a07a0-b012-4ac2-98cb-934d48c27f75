import {
  Box,
  Container,
  Text,
  VStack,
  SimpleGrid,
  Heading
} from "@chakra-ui/react"
import { createFileRoute } from "@tanstack/react-router"

import useAuth from "@/hooks/useAuth"
import StatisticsCards from "@/components/Dashboard/StatisticsCards"
import RecentCases from "@/components/Dashboard/RecentCases"
import QuickActions from "@/components/Dashboard/QuickActions"
import ActivityFeed from "@/components/Dashboard/ActivityFeed"
import StatusDistributionChart from "@/components/Dashboard/StatusDistributionChart"

export const Route = createFileRoute("/_layout/")({
  component: Dashboard,
})

function Dashboard() {
  const { user: currentUser } = useAuth()

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Good morning"
    if (hour < 18) return "Good afternoon"
    return "Good evening"
  }

  const getUserRole = () => {
    if (currentUser?.is_superuser) return "Administrator"
    if (currentUser?.role) {
      return currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1)
    }
    return "User"
  }

  return (
    <Container maxW="full" py={8}>
      <VStack gap={8} align="stretch">
        {/* Welcome Section */}
        <Box
          bg="white"
          p={8}
          borderRadius="xl"
          shadow="sm"
          border="1px solid"
          borderColor="gray.200"
        >
          <VStack align="start" spacing={4}>
            <Box>
              <Heading
                size="2xl"
                mb={2}
                color="gray.800"
                fontWeight="700"
                letterSpacing="-0.025em"
              >
                🏛️ {getGreeting()}, {currentUser?.full_name || currentUser?.email}
              </Heading>
              <Text color="gray.600" fontSize="lg">
                Welcome to <Text as="span" fontWeight="600" color="ui.primary">TunLeg</Text>, your professional legal management platform.
              </Text>
              <Text color="gray.500" fontSize="sm" mt={1}>
                {getUserRole()} • {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Text>
            </Box>
          </VStack>
        </Box>

        {/* Statistics Cards */}
        <Box>
          <Heading size="md" mb={4}>
            Overview
          </Heading>
          <StatisticsCards />
        </Box>

        {/* Main Content Grid */}
        <SimpleGrid columns={{ base: 1, lg: 2 }} gap={8}>
          {/* Left Column */}
          <VStack gap={6} align="stretch">
            <RecentCases />
            <QuickActions />
          </VStack>

          {/* Right Column */}
          <VStack gap={6} align="stretch">
            <StatusDistributionChart size="md" />
            <ActivityFeed />
          </VStack>
        </SimpleGrid>
      </VStack>
    </Container>
  )
}
