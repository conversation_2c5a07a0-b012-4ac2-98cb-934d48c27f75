import {
  Box,
  Container,
  Text,
  VStack,
  SimpleGrid,
  Heading
} from "@chakra-ui/react"
import { createFileRoute } from "@tanstack/react-router"

import useAuth from "@/hooks/useAuth"

export const Route = createFileRoute("/_layout/")({
  component: Dashboard,
})

function Dashboard() {
  const { user: currentUser } = useAuth()

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Bonjour"
    if (hour < 18) return "Bon après-midi"
    return "Bonsoir"
  }

  const getUserRole = () => {
    if (currentUser?.is_superuser) return "Administrateur"
    if (currentUser?.role) {
      return currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1)
    }
    return "Utilisateur"
  }

  // Simplified dashboard to debug the blank page issue
  return (
    <Container maxW="full" py={8}>
      <VStack gap={8} align="stretch">
        {/* Welcome Section */}
        <Box>
          <Heading size="lg" mb={2}>
            🏛️ Bienvenue sur TunLeg
          </Heading>
          <Text color="gray.600">
            {getGreeting()}, {currentUser?.full_name || currentUser?.email || "Utilisateur"} 👋🏼
          </Text>
          <Text color="gray.500" fontSize="sm">
            Plateforme de gestion juridique - {getUserRole()}
          </Text>
        </Box>

        {/* Quick Status */}
        <Box p={6} bg="blue.50" borderRadius="lg" border="1px solid" borderColor="blue.200">
          <VStack spacing={3}>
            <Heading size="md" color="blue.700">
              🚀 TunLeg est opérationnel !
            </Heading>
            <Text color="blue.600" textAlign="center">
              Votre environnement de développement local fonctionne parfaitement.
            </Text>
            <SimpleGrid columns={{ base: 1, md: 3 }} gap={4} w="full">
              <Box p={3} bg="white" borderRadius="md" textAlign="center">
                <Text fontWeight="bold" color="green.600">✅ Frontend</Text>
                <Text fontSize="sm" color="gray.600">React + Vite</Text>
              </Box>
              <Box p={3} bg="white" borderRadius="md" textAlign="center">
                <Text fontWeight="bold" color="green.600">✅ Backend</Text>
                <Text fontSize="sm" color="gray.600">FastAPI</Text>
              </Box>
              <Box p={3} bg="white" borderRadius="md" textAlign="center">
                <Text fontWeight="bold" color="green.600">✅ Database</Text>
                <Text fontSize="sm" color="gray.600">PostgreSQL</Text>
              </Box>
            </SimpleGrid>
          </VStack>
        </Box>

        {/* Navigation Help */}
        <Box p={6} bg="purple.50" borderRadius="lg" border="1px solid" borderColor="purple.200">
          <Heading size="md" mb={4} color="purple.700">
            🧭 Navigation Rapide
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2 }} gap={4}>
            <Box>
              <Text fontWeight="bold" color="purple.600" mb={2}>📋 Fonctionnalités Principales</Text>
              <VStack align="start" spacing={1}>
                <Text fontSize="sm">• Gestion des dossiers juridiques</Text>
                <Text fontSize="sm">• Documents et templates</Text>
                <Text fontSize="sm">• Communication équipe</Text>
                <Text fontSize="sm">• Notifications</Text>
              </VStack>
            </Box>
            <Box>
              <Text fontWeight="bold" color="purple.600" mb={2}>🎨 Nouveau : Thème Material-UI</Text>
              <VStack align="start" spacing={1}>
                <Text fontSize="sm">• Interface Material Design</Text>
                <Text fontSize="sm">• Mode sombre/clair</Text>
                <Text fontSize="sm">• Personnalisation avancée</Text>
                <Text fontSize="sm">• Voir "Material-UI Theme" dans le menu</Text>
              </VStack>
            </Box>
          </SimpleGrid>
        </Box>
      </VStack>
    </Container>
  )
}
