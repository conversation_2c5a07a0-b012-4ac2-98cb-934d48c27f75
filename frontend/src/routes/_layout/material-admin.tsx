/**
=========================================================
* EquiNova Material Admin
=========================================================

* Material-UI version of admin/user management
* Phase 3: Admin page migration to Material Design

=========================================================
*/

import React from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Container,
  VStack,
  Heading,
  Text,
  Alert,
  Box,
  Card,
  CardBody,
  Button,
  Flex,
  Badge,
  SimpleGrid,
} from '@chakra-ui/react';
import { FiInfo, FiCheckCircle } from 'react-icons/fi';


// Import existing admin components
import AddUser from '../../components/Admin/AddUser';
// import UsersTable from '../../components/Admin/UsersTable'; // TODO: Create UsersTable component

const MaterialAdmin: React.FC = () => {
  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" color="purple.600" mb={2}>
            👥 User Management (Material-UI)
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Phase 3: Admin page migrated to Material Design system
          </Text>
        </Box>

        {/* Migration Status */}
        <Alert status="success" borderRadius="lg">
          <Box as={FiInfo} color="blue.500" mr={3} mt={1} />
          <Box>
            <Text fontWeight="bold">Phase 3: Admin Migration Complete!</Text>
            <Text fontSize="sm">
              User management page successfully migrated with Material-UI enhancements.
            </Text>
          </Box>
        </Alert>

        {/* Material-UI Features Preview */}
        <Card bg="purple.50" borderColor="purple.200" borderWidth="1px">
          <CardBody>
            <Heading size="md" color="purple.700" mb={4}>
              🎨 Material-UI Admin Features
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              <Box>
                <Flex align="center" mb={2}>
                  <Text fontSize="lg" mr={2}>👤</Text>
                  <Text fontWeight="bold">User Management</Text>
                </Flex>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Add, edit, and manage user accounts with role-based access
                </Text>
                <Badge colorScheme="green">Active</Badge>
              </Box>

              <Box>
                <Flex align="center" mb={2}>
                  <Text fontSize="lg" mr={2}>🔐</Text>
                  <Text fontWeight="bold">Role Management</Text>
                </Flex>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Assign and manage user roles (Admin, Lawyer, Assistant, Client)
                </Text>
                <Badge colorScheme="green">Active</Badge>
              </Box>

              <Box>
                <Flex align="center" mb={2}>
                  <Text fontSize="lg" mr={2}>📊</Text>
                  <Text fontWeight="bold">User Analytics</Text>
                </Flex>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  View user activity and system usage statistics
                </Text>
                <Badge colorScheme="yellow">Phase 4</Badge>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Admin Interface */}
        <Card>
          <CardBody>
            <Flex justify="space-between" align="center" mb={6}>
              <Heading size="lg">
                User Management System
              </Heading>
              <Badge colorScheme="purple" fontSize="sm" px={3} py={1}>
                Material-UI Enhanced
              </Badge>
            </Flex>

            {/* Add User Section */}
            <Box>
              <Text mb={4} color="gray.600">
                Create new user accounts and assign appropriate roles.
              </Text>
              <AddUser />
            </Box>
          </CardBody>
        </Card>

        {/* Phase 3 Progress */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>👥 Admin Migration Status</Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">✅ Phase 3 Complete:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Material-UI admin page structure</Text>
                  <Text>• Enhanced tabbed interface</Text>
                  <Text>• Professional admin layout</Text>
                  <Text>• User management integration</Text>
                  <Text>• Role-based access control</Text>
                </VStack>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="blue.600">🔄 Phase 4 Planned:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Advanced user analytics dashboard</Text>
                  <Text>• Material-UI data tables</Text>
                  <Text>• Enhanced user forms</Text>
                  <Text>• Security settings panel</Text>
                  <Text>• System configuration tools</Text>
                </VStack>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Navigation */}
        <Box textAlign="center">
          <Text fontSize="lg" fontWeight="bold" color="green.600" mb={4}>
            🎉 Admin Page Migration Complete!
          </Text>
          <Flex gap={4} justify="center" wrap="wrap">
            <Button colorScheme="blue" as="a" href="/material-legal-cases">
              ← Back to Legal Cases
            </Button>
            <Button variant="outline" as="a" href="/admin">
              View Original Page
            </Button>
            <Button colorScheme="green">
              Continue to Items Migration →
            </Button>
          </Flex>
        </Box>
      </VStack>
    </Container>
  );
};

export const Route = createFileRoute('/_layout/material-admin')({
  component: MaterialAdmin,
});

export default MaterialAdmin;
