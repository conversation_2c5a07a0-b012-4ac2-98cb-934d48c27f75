/**
=========================================================
* TunLeg Material-UI Demo Page
=========================================================

* Simple demo page to showcase Material-UI components
* Working within the existing Chakra UI layout

=========================================================
*/

import React, { useState } from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  Button,
  Alert,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Flex,
  Spacer,
} from '@chakra-ui/react';
import { FiCheckCircle, FiSettings, FiSun, FiMoon } from 'react-icons/fi';

// Material-UI imports
import {
  But<PERSON> as <PERSON><PERSON><PERSON><PERSON>on,
  Card as <PERSON>i<PERSON>ard,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  Chip,
  Paper,
  Grid,
  IconButton,
  Tooltip,
  Fab,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Palette as PaletteIcon,
} from '@mui/icons-material';

// Material-UI theme context
import {
  useMaterialUIController,
  setDarkMode,
  setOpenConfigurator,
} from '../../themes/context';

// Simple theme configurator
import SimpleThemeConfigurator from '../../components/MaterialUI/SimpleThemeConfigurator';

export const Route = createFileRoute('/_layout/material-ui-demo')({
  component: MaterialUIDemo,
});

function MaterialUIDemo() {
  const { state, dispatch } = useMaterialUIController();
  const { darkMode, openConfigurator } = state;
  const [showMaterialComponents, setShowMaterialComponents] = useState(true);

  const handleToggleDarkMode = () => {
    setDarkMode(dispatch, !darkMode);
  };

  const handleOpenConfigurator = () => {
    setOpenConfigurator(dispatch, true);
  };

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            🎨 TunLeg Material-UI Demo
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Découvrez le système de thème Material-UI intégré dans TunLeg
          </Text>
        </Box>

        {/* Success Alert */}
        <Alert status="success" borderRadius="lg">
          <FiCheckCircle color="green.500" style={{ marginRight: '12px', marginTop: '4px' }} />
          <Box>
            <Text fontWeight="bold">Système Material-UI Opérationnel !</Text>
            <Text fontSize="sm">
              Thème Material-UI intégré avec succès dans l'application TunLeg
            </Text>
          </Box>
        </Alert>

        {/* Theme Controls */}
        <Card>
          <CardBody>
            <Flex align="center" mb={4}>
              <Heading size="md">🎛️ Contrôles de Thème</Heading>
              <Spacer />
              <Badge colorScheme={darkMode ? 'purple' : 'blue'}>
                {darkMode ? 'Mode Sombre' : 'Mode Clair'}
              </Badge>
            </Flex>
            
            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
              <Button
                leftIcon={darkMode ? <FiSun /> : <FiMoon />}
                colorScheme="blue"
                onClick={handleToggleDarkMode}
              >
                {darkMode ? 'Mode Clair' : 'Mode Sombre'}
              </Button>
              
              <Button
                leftIcon={<FiSettings />}
                colorScheme="purple"
                onClick={handleOpenConfigurator}
              >
                Configurateur de Thème
              </Button>
              
              <Button
                leftIcon={<FiCheckCircle />}
                colorScheme="green"
                onClick={() => setShowMaterialComponents(!showMaterialComponents)}
              >
                {showMaterialComponents ? 'Masquer' : 'Afficher'} Material-UI
              </Button>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Material-UI Components Demo */}
        {showMaterialComponents && (
          <Box>
            <Heading size="lg" mb={4} color="purple.600">
              🧩 Composants Material-UI
            </Heading>
            
            <Grid container spacing={3}>
              {/* Material-UI Buttons */}
              <Grid item xs={12} md={6}>
                <Paper elevation={2} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Boutons Material-UI
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <MuiButton variant="contained" color="primary">
                      Primaire
                    </MuiButton>
                    <MuiButton variant="outlined" color="secondary">
                      Secondaire
                    </MuiButton>
                    <MuiButton variant="text" color="success">
                      Succès
                    </MuiButton>
                  </Box>
                </Paper>
              </Grid>

              {/* Material-UI Cards */}
              <Grid item xs={12} md={6}>
                <MuiCard elevation={3}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Carte Material-UI
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cette carte utilise les composants Material-UI natifs avec le thème personnalisé TunLeg.
                    </Typography>
                    <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                      <Chip label="Material-UI" color="primary" size="small" />
                      <Chip label="TunLeg" color="secondary" size="small" />
                    </Box>
                  </CardContent>
                </MuiCard>
              </Grid>

              {/* Theme Controls */}
              <Grid item xs={12}>
                <Paper elevation={2} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Contrôles de Thème Material-UI
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={darkMode}
                          onChange={handleToggleDarkMode}
                          color="primary"
                        />
                      }
                      label="Mode Sombre"
                    />
                    
                    <Tooltip title="Ouvrir le configurateur de thème">
                      <IconButton color="primary" onClick={handleOpenConfigurator}>
                        <SettingsIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title={darkMode ? 'Passer en mode clair' : 'Passer en mode sombre'}>
                      <IconButton color="secondary" onClick={handleToggleDarkMode}>
                        {darkMode ? <LightModeIcon /> : <DarkModeIcon />}
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Instructions */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>📋 Instructions</Heading>
            <VStack align="start" spacing={2}>
              <Text>• <strong>Mode Sombre/Clair</strong> : Utilisez les boutons ci-dessus pour basculer</Text>
              <Text>• <strong>Configurateur</strong> : Cliquez sur "Configurateur de Thème" pour accéder aux options avancées</Text>
              <Text>• <strong>Composants</strong> : Les composants Material-UI s'adaptent automatiquement au thème</Text>
              <Text>• <strong>Persistance</strong> : Vos préférences de thème sont sauvegardées automatiquement</Text>
            </VStack>
          </CardBody>
        </Card>

        {/* Status */}
        <Alert status="info" borderRadius="lg">
          <Box>
            <Text fontWeight="bold">État du Système</Text>
            <Text fontSize="sm">
              Mode actuel: <strong>{darkMode ? 'Sombre' : 'Clair'}</strong> | 
              Configurateur: <strong>{openConfigurator ? 'Ouvert' : 'Fermé'}</strong> | 
              Thème: <strong>TunLeg Material-UI</strong>
            </Text>
          </Box>
        </Alert>
      </VStack>

      {/* Floating Action Button */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 1000,
        }}
      >
        <Tooltip title="Personnaliser le thème">
          <Fab
            color="primary"
            onClick={handleOpenConfigurator}
            sx={{
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              '&:hover': {
                boxShadow: '0 6px 16px rgba(0,0,0,0.2)',
              },
            }}
          >
            <PaletteIcon />
          </Fab>
        </Tooltip>
      </Box>

      {/* Simple Theme Configurator */}
      <SimpleThemeConfigurator />
    </Container>
  );
}
