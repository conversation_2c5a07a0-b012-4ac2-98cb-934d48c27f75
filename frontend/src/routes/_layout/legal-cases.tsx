import { Container, Heading, VStack } from "@chakra-ui/react"
import { createFileRoute } from "@tanstack/react-router"
import { useState, useEffect } from "react"
import AddLegalCase from "../../components/LegalCases/AddLegalCase"
import LegalCasesTable from "../../components/LegalCases/LegalCasesTable"
import SimpleCaseDetailPage from "../../components/LegalCases/SimpleCaseDetailPage"

export const Route = createFileRoute("/_layout/legal-cases")({
  component: LegalCasesPage,
})

function LegalCasesPage() {
  const [selectedCaseId, setSelectedCaseId] = useState<string | null>(null)

  console.log('🎯 LegalCasesPage rendered, selectedCaseId:', selectedCaseId)

  // Check URL for case ID on mount and URL changes
  useEffect(() => {
    const checkUrl = () => {
      const path = window.location.pathname
      const match = path.match(/\/legal-cases\/([^\/]+)/)
      if (match) {
        setSelectedCaseId(match[1])
        console.log('🎯 Case ID found in URL:', match[1])
      } else {
        setSelectedCaseId(null)
      }
    }

    checkUrl()

    // Listen for URL changes
    window.addEventListener('popstate', checkUrl)
    return () => window.removeEventListener('popstate', checkUrl)
  }, [])

  // If a case is selected, show the full case detail page
  if (selectedCaseId) {
    return (
      <SimpleCaseDetailPage
        caseId={selectedCaseId}
        onBack={() => {
          setSelectedCaseId(null)
          window.history.pushState({}, '', '/legal-cases')
        }}
      />
    )
  }

  // Otherwise show the normal legal cases list
  return (
    <Container maxW="full">
      <VStack gap={6} py={8}>
        <Heading size="lg" textAlign="center">
          Legal Cases Management
        </Heading>
        
        {/* 🚨 TEST BUTTON - Pour diagnostiquer le problème */}
        <button
          onClick={() => {
            console.log('🚨 TEST BUTTON CLICKED - Event handlers fonctionnent!')
            alert('Event handlers fonctionnent! Le problème est ailleurs.')
          }}
          style={{
            padding: '12px 24px',
            backgroundColor: '#e53e3e',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontWeight: 'bold',
            fontSize: '16px'
          }}
        >
          🚨 TEST - Cliquez pour diagnostiquer
        </button>
        
        <AddLegalCase />
        <LegalCasesTable onCaseClick={(caseId) => {
          console.log('🎯 Case clicked:', caseId)
          setSelectedCaseId(caseId)
          window.history.pushState({}, '', `/legal-cases/${caseId}`)
        }} />
      </VStack>
    </Container>
  )
}
