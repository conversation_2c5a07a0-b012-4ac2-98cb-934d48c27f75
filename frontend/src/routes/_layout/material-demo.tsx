/**
=========================================================
* EquiNova Material Layout Demo
=========================================================

* Demo page to showcase the new Material-UI components
* Accessible via regular Chakra UI layout

=========================================================
*/

import React from 'react';
import { createFileRoute } from '@tanstack/react-router';
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  Button,
  Alert,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Flex,
  Spacer,
} from '@chakra-ui/react';
import { FiCheckCircle } from 'react-icons/fi';

const MaterialLayoutDemo: React.FC = () => {
  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            🎨 Material-UI Layout Demo
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Experience the new Material Design layout system for EquiNova
          </Text>
        </Box>

        {/* Success Alert */}
        <Alert status="success" borderRadius="lg">
          <FiCheckCircle color="green.500" style={{ marginRight: '12px', marginTop: '4px' }} />
          <Box>
            <Text fontWeight="bold">Material-UI Layout System Ready!</Text>
            <Text fontSize="sm">
              Phase 1 & 2 complete: Theme foundation + Core layout components implemented
            </Text>
          </Box>
        </Alert>

        {/* Features Grid */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          <Card>
            <CardBody>
              <Flex align="center" mb={3}>
                <Text fontSize="2xl">🏗️</Text>
                <Text fontWeight="bold" ml={2}>Material Dashboard Sidebar</Text>
              </Flex>
              <Text fontSize="sm" color="gray.600" mb={3}>
                Professional Material Dashboard-style navigation with TypeScript
              </Text>
              <Badge colorScheme="green">Complete</Badge>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex align="center" mb={3}>
                <Text fontSize="2xl">🎛️</Text>
                <Text fontWeight="bold" ml={2}>Advanced Navigation</Text>
              </Flex>
              <Text fontSize="sm" color="gray.600" mb={3}>
                Role-based filtering, collapsible sidebar, active state management
              </Text>
              <Badge colorScheme="green">Complete</Badge>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex align="center" mb={3}>
                <Text fontSize="2xl">📱</Text>
                <Text fontWeight="bold" ml={2}>Responsive Design</Text>
              </Flex>
              <Text fontSize="sm" color="gray.600" mb={3}>
                Mobile drawer and desktop sidebar with smooth transitions
              </Text>
              <Badge colorScheme="green">Complete</Badge>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex align="center" mb={3}>
                <Text fontSize="2xl">🎨</Text>
                <Text fontWeight="bold" ml={2}>Material Components</Text>
              </Flex>
              <Text fontSize="sm" color="gray.600" mb={3}>
                MDBox, MDTypography with Material Dashboard styling patterns
              </Text>
              <Badge colorScheme="green">Complete</Badge>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex align="center" mb={3}>
                <Text fontSize="2xl">🌙</Text>
                <Text fontWeight="bold" ml={2}>Theme Integration</Text>
              </Flex>
              <Text fontSize="sm" color="gray.600" mb={3}>
                Dark/light mode working seamlessly with new layout
              </Text>
              <Badge colorScheme="green">Complete</Badge>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex align="center" mb={3}>
                <Text fontSize="2xl">⚡</Text>
                <Text fontWeight="bold" ml={2}>Performance</Text>
              </Flex>
              <Text fontSize="sm" color="gray.600" mb={3}>
                Tree shaking, code splitting, and optimized bundle size
              </Text>
              <Badge colorScheme="green">Complete</Badge>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Access Instructions */}
        <Card bg="blue.50" borderColor="blue.200" borderWidth="1px">
          <CardBody>
            <Heading size="md" color="blue.700" mb={4}>
              🚀 How to Access Material-UI Layout
            </Heading>
            <VStack align="stretch" spacing={3}>
              <Box>
                <Text fontWeight="bold" color="blue.600">Option 1: Material Dashboard (NEW!)</Text>
                <Text fontSize="sm" color="gray.600">
                  Experience the Phase 3 dashboard migration with Material-UI components
                </Text>
                <Button as="a" href="/material-dashboard" colorScheme="blue" size="sm" mt={2}>
                  View Material Dashboard
                </Button>
              </Box>

              <Box>
                <Text fontWeight="bold" color="blue.600">Option 2: Integration Testing</Text>
                <Text fontSize="sm" color="gray.600">
                  Material-UI components are ready to be integrated into existing pages
                </Text>
              </Box>

              <Box>
                <Text fontWeight="bold" color="blue.600">Option 3: Theme Demo</Text>
                <Text fontSize="sm" color="gray.600">
                  Visit <code>/theme-demo</code> to see Material-UI theme system in action
                </Text>
                <Button as="a" href="/theme-demo" colorScheme="blue" size="sm" mt={2}>
                  View Theme Demo
                </Button>
              </Box>
            </VStack>
          </CardBody>
        </Card>

        {/* Technical Details */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>📁 Implementation Details</Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2}>✅ Completed Components:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Material Dashboard Sidenav</Text>
                  <Text>• MDBox & MDTypography components</Text>
                  <Text>• SidenavCollapse navigation items</Text>
                  <Text>• MaterialLayout wrapper</Text>
                  <Text>• MaterialNavbar & MaterialBreadcrumb</Text>
                  <Text>• Theme integration & switching</Text>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2}>🔧 Next Steps (Phase 3):</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Fix routing for Material layout</Text>
                  <Text>• Migrate existing pages to Material layout</Text>
                  <Text>• Enhance legal cases with Material DataGrid</Text>
                  <Text>• Add Material forms and modals</Text>
                  <Text>• Complete Chakra UI migration</Text>
                </VStack>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Status */}
        <Box textAlign="center" py={6}>
          <Text fontSize="lg" fontWeight="bold" color="green.600" mb={2}>
            🎉 Phase 1 & 2 Complete!
          </Text>
          <Text color="gray.600">
            Material-UI theme system foundation and core layout components are ready
          </Text>
        </Box>
      </VStack>
    </Container>
  );
};

export const Route = createFileRoute('/_layout/material-demo')({
  component: MaterialLayoutDemo,
});

export default MaterialLayoutDemo;
