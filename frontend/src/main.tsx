import React from "react"
import ReactDOM from "react-dom/client"

// Version simplifiée qui fonctionne
function App() {
  return (
    <div style={{ padding: '32px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#2563eb' }}>🏛️ TunLeg - Plateforme Juridique</h1>
      <p>Bienvenue sur TunLeg ! L'application fonctionne.</p>

      <div style={{
        padding: '16px',
        backgroundColor: '#dcfce7',
        border: '1px solid #16a34a',
        borderRadius: '8px',
        marginTop: '16px'
      }}>
        <p style={{ color: '#16a34a', fontWeight: 'bold', margin: 0 }}>
          ✅ Frontend opérationnel
        </p>
      </div>

      <div style={{ marginTop: '24px' }}>
        <h3>🔧 Diagnostic du problème :</h3>
        <ul>
          <li>✅ React fonctionne</li>
          <li>✅ Vite dev server fonctionne</li>
          <li>⚠️ Problème identifié : routeTree.gen.ts ou routes complexes</li>
          <li>🔄 Solution : Simplifier progressivement les imports</li>
        </ul>
      </div>

      <div style={{
        marginTop: '24px',
        padding: '16px',
        backgroundColor: '#fef3c7',
        border: '1px solid #d97706',
        borderRadius: '8px'
      }}>
        <h4 style={{ color: '#d97706', marginTop: 0 }}>Prochaines étapes :</h4>
        <ol style={{ color: '#92400e' }}>
          <li>Vérifier les erreurs dans routeTree.gen.ts</li>
          <li>Simplifier les routes problématiques</li>
          <li>Réactiver progressivement les providers</li>
          <li>Tester chaque composant individuellement</li>
        </ol>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
