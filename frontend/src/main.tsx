import React from "react"
import ReactDOM from "react-dom/client"

// Test ultra-simple pour diagnostiquer le problème
function App() {
  return (
    <div style={{ padding: '32px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#2563eb' }}>🧪 TunLeg - Test de Base</h1>
      <p>Si vous voyez ce message, React fonctionne !</p>
      <div style={{
        padding: '16px',
        backgroundColor: '#dcfce7',
        border: '1px solid #16a34a',
        borderRadius: '8px',
        marginTop: '16px'
      }}>
        <p style={{ color: '#16a34a', fontWeight: 'bold', margin: 0 }}>
          ✅ React se charge correctement
        </p>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
