import React, { StrictMode } from "react"
import ReactD<PERSON> from "react-dom/client"
import { <PERSON>r<PERSON><PERSON><PERSON>, createRouter, createRootRoute, createRoute } from "@tanstack/react-router"
import { CustomProvider } from "./components/ui/provider"
import { Box, Container, Heading, Text, Button, VStack, SimpleGrid } from "@chakra-ui/react"

// Créer une route racine simple
const rootRoute = createRootRoute({
  component: () => (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            🏛️ TunLeg - Plateforme Juridique
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Bienvenue sur TunLeg ! L'application fonctionne avec Chakra UI.
          </Text>
        </Box>

        <SimpleGrid columns={{ base: 1, md: 2 }} gap={6}>
          <Box p={6} bg="green.50" borderRadius="lg" border="1px solid" borderColor="green.200">
            <Heading size="md" color="green.700" mb={3}>
              ✅ Statut Système
            </Heading>
            <VStack align="start" spacing={2}>
              <Text fontSize="sm">• React: Opérationnel</Text>
              <Text fontSize="sm">• TanStack Router: Opérationnel</Text>
              <Text fontSize="sm">• Chakra UI: Opérationnel</Text>
              <Text fontSize="sm">• Vite Dev Server: Opérationnel</Text>
            </VStack>
          </Box>

          <Box p={6} bg="blue.50" borderRadius="lg" border="1px solid" borderColor="blue.200">
            <Heading size="md" color="blue.700" mb={3}>
              🧪 Tests Disponibles
            </Heading>
            <VStack spacing={3}>
              <Button as="a" href="/simple-test" colorScheme="blue" size="sm" w="full">
                Test Simple Router
              </Button>
              <Button as="a" href="/login" colorScheme="green" size="sm" w="full">
                Page de Connexion
              </Button>
              <Button as="a" href="/dashboard" colorScheme="purple" size="sm" w="full">
                Dashboard TunLeg
              </Button>
            </VStack>
          </Box>
        </SimpleGrid>
      </VStack>
    </Container>
  ),
})

// Créer une route de test simple
const simpleTestRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/simple-test',
  component: () => (
    <Container maxW="4xl" py={8}>
      <VStack spacing={6}>
        <Heading size="lg" color="blue.600">🧪 Test Simple avec Routeur</Heading>
        <Text>Cette page utilise TanStack Router et Chakra UI !</Text>

        <Box p={6} bg="green.50" borderRadius="lg" border="1px solid" borderColor="green.200" w="full">
          <Text color="green.700" fontWeight="bold" textAlign="center">
            ✅ TanStack Router + Chakra UI fonctionnent parfaitement
          </Text>
        </Box>

        <Button as="a" href="/" colorScheme="blue">
          Retour à l'accueil
        </Button>
      </VStack>
    </Container>
  ),
})

// Créer une route de login simple
const loginRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: () => (
    <Container maxW="md" py={16}>
      <VStack spacing={8}>
        <Box textAlign="center">
          <Heading size="xl" color="blue.600" mb={2}>🏛️ TunLeg</Heading>
          <Text color="gray.600">Connexion à la plateforme juridique</Text>
        </Box>

        <Box p={8} bg="white" borderRadius="lg" shadow="lg" border="1px solid" borderColor="gray.200" w="full">
          <VStack spacing={6}>
            <Heading size="md">Connexion</Heading>

            <Box p={4} bg="blue.50" borderRadius="md" w="full">
              <Text fontSize="sm" color="blue.700" textAlign="center">
                <strong>Credentials par défaut :</strong><br/>
                Email: admin@localhost<br/>
                Password: admin123
              </Text>
            </Box>

            <VStack spacing={4} w="full">
              <Button colorScheme="blue" size="lg" w="full">
                Se connecter
              </Button>
              <Button as="a" href="/" variant="outline" size="sm" w="full">
                Retour à l'accueil
              </Button>
            </VStack>
          </VStack>
        </Box>
      </VStack>
    </Container>
  ),
})

// Créer l'arbre de routes
const routeTree = rootRoute.addChildren([simpleTestRoute, loginRoute])

// Créer le routeur
const router = createRouter({ routeTree })

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <CustomProvider>
      <RouterProvider router={router} />
    </CustomProvider>
  </StrictMode>,
)
