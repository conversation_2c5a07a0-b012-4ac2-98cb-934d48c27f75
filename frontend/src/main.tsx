import React, { StrictMode } from "react"
import ReactD<PERSON> from "react-dom/client"
import { <PERSON>r<PERSON><PERSON><PERSON>, createRouter, createRootRoute, createRoute } from "@tanstack/react-router"
import { CustomProvider } from "./components/ui/provider"
import { Box, Container, Heading, Text, Button, VStack, SimpleGrid } from "@chakra-ui/react"

// Créer une route racine simple
const rootRoute = createRootRoute({
  component: () => (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            🏛️ TunLeg - Plateforme Juridique
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Bienvenue sur TunLeg ! L'application fonctionne avec Chakra UI.
          </Text>
        </Box>

        <SimpleGrid columns={{ base: 1, md: 2 }} gap={6}>
          <Box p={6} bg="green.50" borderRadius="lg" border="1px solid" borderColor="green.200">
            <Heading size="md" color="green.700" mb={3}>
              ✅ Statut Système
            </Heading>
            <VStack align="start" spacing={2}>
              <Text fontSize="sm">• React: Opérationnel</Text>
              <Text fontSize="sm">• TanStack Router: Opérationnel</Text>
              <Text fontSize="sm">• Chakra UI: Opérationnel</Text>
              <Text fontSize="sm">• Vite Dev Server: Opérationnel</Text>
            </VStack>
          </Box>

          <Box p={6} bg="blue.50" borderRadius="lg" border="1px solid" borderColor="blue.200">
            <Heading size="md" color="blue.700" mb={3}>
              🧪 Tests Disponibles
            </Heading>
            <VStack spacing={3}>
              <Button as="a" href="/simple-test" colorScheme="blue" size="sm" w="full">
                Test Simple Router
              </Button>
              <Button as="a" href="/material-ui-demo" colorScheme="purple" size="sm" w="full">
                Test Material-UI
              </Button>
            </VStack>
          </Box>
        </SimpleGrid>
      </VStack>
    </Container>
  ),
})

// Créer une route de test simple
const simpleTestRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/simple-test',
  component: () => (
    <div style={{ padding: '32px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#2563eb' }}>🧪 Test Simple avec Routeur</h1>
      <p>Cette page utilise TanStack Router et fonctionne !</p>

      <div style={{
        padding: '16px',
        backgroundColor: '#dcfce7',
        border: '1px solid #16a34a',
        borderRadius: '8px',
        marginTop: '16px'
      }}>
        <p style={{ color: '#16a34a', fontWeight: 'bold', margin: 0 }}>
          ✅ TanStack Router fonctionne
        </p>
      </div>

      <div style={{ marginTop: '24px' }}>
        <a
          href="/"
          style={{
            color: '#2563eb',
            textDecoration: 'underline'
          }}
        >
          Retour à l'accueil
        </a>
      </div>
    </div>
  ),
})

// Créer l'arbre de routes
const routeTree = rootRoute.addChildren([simpleTestRoute])

// Créer le routeur
const router = createRouter({ routeTree })

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
)
