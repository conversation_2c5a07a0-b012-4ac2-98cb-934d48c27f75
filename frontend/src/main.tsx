import React, { StrictMode } from "react"
import ReactD<PERSON> from "react-dom/client"
import { RouterProvider, createRouter, createRootRoute, createRoute } from "@tanstack/react-router"

// Créer une route racine simple
const rootRoute = createRootRoute({
  component: () => (
    <div style={{ padding: '32px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#2563eb' }}>🏛️ TunLeg - Test Routeur</h1>
      <p>Test du routeur TanStack Router</p>

      <div style={{ marginTop: '24px' }}>
        <a
          href="/simple-test"
          style={{
            color: '#2563eb',
            textDecoration: 'underline',
            marginRight: '16px'
          }}
        >
          Aller au test simple
        </a>
      </div>
    </div>
  ),
})

// Créer une route de test simple
const simpleTestRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/simple-test',
  component: () => (
    <div style={{ padding: '32px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#2563eb' }}>🧪 Test Simple avec Routeur</h1>
      <p>Cette page utilise TanStack Router et fonctionne !</p>

      <div style={{
        padding: '16px',
        backgroundColor: '#dcfce7',
        border: '1px solid #16a34a',
        borderRadius: '8px',
        marginTop: '16px'
      }}>
        <p style={{ color: '#16a34a', fontWeight: 'bold', margin: 0 }}>
          ✅ TanStack Router fonctionne
        </p>
      </div>

      <div style={{ marginTop: '24px' }}>
        <a
          href="/"
          style={{
            color: '#2563eb',
            textDecoration: 'underline'
          }}
        >
          Retour à l'accueil
        </a>
      </div>
    </div>
  ),
})

// Créer l'arbre de routes
const routeTree = rootRoute.addChildren([simpleTestRoute])

// Créer le routeur
const router = createRouter({ routeTree })

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
)
