import React, { useEffect, useRef } from "react"
import { Box, VisuallyHidden } from "@chakra-ui/react"

// Skip to main content link for keyboard navigation
export const SkipToMainContent: React.FC = () => (
  <Box
    position="absolute"
    top="-40px"
    left="6px"
    bg="blue.600"
    color="white"
    px={4}
    py={2}
    borderRadius="md"
    fontSize="sm"
    fontWeight="medium"
    zIndex={9999}
    _focus={{
      top: "6px",
      outline: "2px solid",
      outlineColor: "blue.300",
      outlineOffset: "2px"
    }}
    transition="top 0.2s"
  >
    <a href="#main-content">
      Skip to main content
    </a>
  </Box>
)

// Focus trap for modals and dialogs
interface FocusTrapProps {
  children: React.ReactNode
  isActive?: boolean
}

export const FocusTrap: React.FC<FocusTrapProps> = ({ 
  children, 
  isActive = true 
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const firstFocusableRef = useRef<HTMLElement | null>(null)
  const lastFocusableRef = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!isActive || !containerRef.current) return

    const container = containerRef.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )

    if (focusableElements.length === 0) return

    firstFocusableRef.current = focusableElements[0] as HTMLElement
    lastFocusableRef.current = focusableElements[focusableElements.length - 1] as HTMLElement

    // Focus first element
    firstFocusableRef.current?.focus()

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstFocusableRef.current) {
          e.preventDefault()
          lastFocusableRef.current?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastFocusableRef.current) {
          e.preventDefault()
          firstFocusableRef.current?.focus()
        }
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    return () => container.removeEventListener('keydown', handleKeyDown)
  }, [isActive])

  return (
    <div ref={containerRef}>
      {children}
    </div>
  )
}

// Screen reader announcements
interface ScreenReaderAnnouncementProps {
  message: string
  priority?: 'polite' | 'assertive'
}

export const ScreenReaderAnnouncement: React.FC<ScreenReaderAnnouncementProps> = ({
  message,
  priority = 'polite'
}) => (
  <VisuallyHidden>
    <div aria-live={priority} aria-atomic="true">
      {message}
    </div>
  </VisuallyHidden>
)

// Keyboard navigation helper
export const useKeyboardNavigation = (
  items: HTMLElement[],
  options: {
    loop?: boolean
    orientation?: 'horizontal' | 'vertical'
  } = {}
) => {
  const { loop = true, orientation = 'vertical' } = options
  const currentIndexRef = useRef(0)

  const handleKeyDown = (e: KeyboardEvent) => {
    const isVertical = orientation === 'vertical'
    const nextKey = isVertical ? 'ArrowDown' : 'ArrowRight'
    const prevKey = isVertical ? 'ArrowUp' : 'ArrowLeft'

    if (![nextKey, prevKey, 'Home', 'End'].includes(e.key)) return

    e.preventDefault()

    switch (e.key) {
      case nextKey:
        if (currentIndexRef.current < items.length - 1) {
          currentIndexRef.current++
        } else if (loop) {
          currentIndexRef.current = 0
        }
        break

      case prevKey:
        if (currentIndexRef.current > 0) {
          currentIndexRef.current--
        } else if (loop) {
          currentIndexRef.current = items.length - 1
        }
        break

      case 'Home':
        currentIndexRef.current = 0
        break

      case 'End':
        currentIndexRef.current = items.length - 1
        break
    }

    items[currentIndexRef.current]?.focus()
  }

  return { handleKeyDown, currentIndex: currentIndexRef.current }
}

// ARIA live region for dynamic content updates
interface LiveRegionProps {
  children: React.ReactNode
  priority?: 'polite' | 'assertive' | 'off'
  atomic?: boolean
}

export const LiveRegion: React.FC<LiveRegionProps> = ({
  children,
  priority = 'polite',
  atomic = true
}) => (
  <div
    aria-live={priority}
    aria-atomic={atomic}
    style={{
      position: 'absolute',
      left: '-10000px',
      width: '1px',
      height: '1px',
      overflow: 'hidden'
    }}
  >
    {children}
  </div>
)

// Enhanced button with better accessibility
interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  isLoading?: boolean
  loadingText?: string
  describedBy?: string
}

export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  children,
  isLoading,
  loadingText,
  describedBy,
  disabled,
  ...props
}) => (
  <button
    {...props}
    disabled={disabled || isLoading}
    aria-disabled={disabled || isLoading}
    aria-describedby={describedBy}
    aria-busy={isLoading}
  >
    {isLoading ? (
      <>
        <VisuallyHidden>{loadingText || 'Loading'}</VisuallyHidden>
        {children}
      </>
    ) : (
      children
    )}
  </button>
)

// Form field with enhanced accessibility
interface AccessibleFormFieldProps {
  children: React.ReactNode
  label: string
  error?: string
  description?: string
  required?: boolean
  id: string
}

export const AccessibleFormField: React.FC<AccessibleFormFieldProps> = ({
  children,
  label,
  error,
  description,
  required,
  id
}) => {
  const errorId = error ? `${id}-error` : undefined
  const descriptionId = description ? `${id}-description` : undefined
  const describedBy = [errorId, descriptionId].filter(Boolean).join(' ')

  return (
    <Box>
      <label htmlFor={id}>
        {label}
        {required && (
          <Box as="span" color="red.500" ml={1} aria-label="required">
            *
          </Box>
        )}
      </label>
      
      {description && (
        <Box id={descriptionId} fontSize="sm" color="gray.600" mt={1}>
          {description}
        </Box>
      )}
      
      <Box mt={2}>
        {React.cloneElement(children as React.ReactElement, {
          id,
          'aria-describedby': describedBy || undefined,
          'aria-invalid': !!error,
          'aria-required': required
        })}
      </Box>
      
      {error && (
        <Box
          id={errorId}
          color="red.500"
          fontSize="sm"
          mt={1}
          role="alert"
          aria-live="polite"
        >
          {error}
        </Box>
      )}
    </Box>
  )
}

// High contrast mode detection
export const useHighContrastMode = () => {
  const [isHighContrast, setIsHighContrast] = React.useState(false)

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    setIsHighContrast(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return isHighContrast
}
