import { Progress as ChakraProgress } from "@chakra-ui/react"
import * as React from "react"

export interface ProgressProps extends ChakraProgress.RootProps {
  value?: number
  colorScheme?: string
  size?: "xs" | "sm" | "md" | "lg"
  borderRadius?: string
}

export const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  function Progress(props, ref) {
    const { value = 0, colorScheme, size = "md", borderRadius, ...rest } = props
    
    return (
      <ChakraProgress.Root 
        ref={ref} 
        value={value} 
        colorPalette={colorScheme}
        size={size}
        borderRadius={borderRadius}
        {...rest}
      >
        <ChakraProgress.Track>
          <ChakraProgress.Range />
        </ChakraProgress.Track>
      </ChakraProgress.Root>
    )
  },
)
