import React from "react"
import {
  Box,
  Flex,
  Text,
  VStack,
  HStack,
  Spinner,
  Skeleton,
  SkeletonText,
  Center,
} from "@chakra-ui/react"

// Page-level loading component
interface PageLoadingProps {
  message?: string
  size?: "sm" | "md" | "lg" | "xl"
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  message = "Loading...",
  size = "lg"
}) => (
  <Center h="50vh" w="full">
    <VStack gap={4}>
      <Spinner size={size} color="blue.500" thickness="3px" />
      <Text color="gray.600" fontSize="md">
        {message}
      </Text>
    </VStack>
  </Center>
)

// Inline loading component for smaller sections
interface InlineLoadingProps {
  message?: string
  size?: "sm" | "md" | "lg"
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
  message = "Loading...",
  size = "md"
}) => (
  <HStack gap={3} justify="center" py={4}>
    <Spinner size={size} color="blue.500" />
    <Text color="gray.600" fontSize="sm">
      {message}
    </Text>
  </HStack>
)

// Button loading state
interface ButtonLoadingProps {
  children: React.ReactNode
  isLoading?: boolean
  loadingText?: string
  size?: "sm" | "md" | "lg"
}

export const ButtonLoading: React.FC<ButtonLoadingProps> = ({
  children,
  isLoading,
  loadingText,
  size = "sm"
}) => (
  <>
    {isLoading && <Spinner size={size} mr={2} />}
    {isLoading ? loadingText || "Loading..." : children}
  </>
)

// Table loading skeleton
interface TableLoadingProps {
  rows?: number
  columns?: number
}

export const TableLoading: React.FC<TableLoadingProps> = ({
  rows = 5,
  columns = 4
}) => (
  <VStack gap={3} w="full">
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <HStack key={rowIndex} gap={4} w="full">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton key={colIndex} h="40px" flex="1" borderRadius="md" />
        ))}
      </HStack>
    ))}
  </VStack>
)

// Card loading skeleton
interface CardLoadingProps {
  count?: number
}

export const CardLoading: React.FC<CardLoadingProps> = ({ count = 3 }) => (
  <VStack gap={4} w="full">
    {Array.from({ length: count }).map((_, index) => (
      <Box
        key={index}
        p={6}
        borderWidth="1px"
        borderRadius="lg"
        w="full"
        bg="white"
      >
        <VStack align="start" gap={3}>
          <Skeleton h="20px" w="60%" />
          <SkeletonText noOfLines={3} spacing="2" skeletonHeight="2" />
          <HStack gap={2} w="full">
            <Skeleton h="32px" w="80px" borderRadius="md" />
            <Skeleton h="32px" w="80px" borderRadius="md" />
          </HStack>
        </VStack>
      </Box>
    ))}
  </VStack>
)

// List loading skeleton
interface ListLoadingProps {
  items?: number
  showAvatar?: boolean
}

export const ListLoading: React.FC<ListLoadingProps> = ({
  items = 5,
  showAvatar = false
}) => (
  <VStack gap={3} w="full">
    {Array.from({ length: items }).map((_, index) => (
      <HStack key={index} gap={3} w="full" p={3}>
        {showAvatar && <Skeleton w="40px" h="40px" borderRadius="full" />}
        <VStack align="start" flex="1" gap={1}>
          <Skeleton h="16px" w="70%" />
          <Skeleton h="12px" w="50%" />
        </VStack>
        <Skeleton h="24px" w="60px" borderRadius="md" />
      </HStack>
    ))}
  </VStack>
)

// Form loading skeleton
export const FormLoading: React.FC = () => (
  <VStack gap={4} w="full">
    <VStack align="start" gap={2} w="full">
      <Skeleton h="16px" w="100px" />
      <Skeleton h="40px" w="full" borderRadius="md" />
    </VStack>
    <VStack align="start" gap={2} w="full">
      <Skeleton h="16px" w="120px" />
      <Skeleton h="40px" w="full" borderRadius="md" />
    </VStack>
    <VStack align="start" gap={2} w="full">
      <Skeleton h="16px" w="80px" />
      <Skeleton h="100px" w="full" borderRadius="md" />
    </VStack>
    <HStack gap={3} justify="end" w="full">
      <Skeleton h="40px" w="80px" borderRadius="md" />
      <Skeleton h="40px" w="100px" borderRadius="md" />
    </HStack>
  </VStack>
)

// Loading overlay for existing content
interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  message?: string
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  children,
  message = "Loading..."
}) => (
  <Box position="relative">
    {children}
    {isLoading && (
      <Flex
        position="absolute"
        top="0"
        left="0"
        right="0"
        bottom="0"
        bg="rgba(255, 255, 255, 0.8)"
        align="center"
        justify="center"
        zIndex="overlay"
        borderRadius="inherit"
      >
        <VStack gap={3}>
          <Spinner size="lg" color="blue.500" thickness="3px" />
          <Text color="gray.600" fontSize="sm">
            {message}
          </Text>
        </VStack>
      </Flex>
    )}
  </Box>
)

// Dashboard stats loading
export const StatsLoading: React.FC = () => (
  <HStack gap={4} w="full">
    {Array.from({ length: 4 }).map((_, index) => (
      <Box
        key={index}
        p={6}
        borderWidth="1px"
        borderRadius="lg"
        flex="1"
        bg="white"
      >
        <VStack align="start" gap={3}>
          <HStack justify="between" w="full">
            <Skeleton h="16px" w="60%" />
            <Skeleton w="24px" h="24px" borderRadius="md" />
          </HStack>
          <Skeleton h="32px" w="40%" />
          <Skeleton h="12px" w="80%" />
        </VStack>
      </Box>
    ))}
  </HStack>
)

// Search results loading
interface SearchLoadingProps {
  count?: number
}

export const SearchLoading: React.FC<SearchLoadingProps> = ({ count = 6 }) => (
  <VStack gap={3} w="full">
    {Array.from({ length: count }).map((_, index) => (
      <Box
        key={index}
        p={4}
        borderWidth="1px"
        borderRadius="md"
        w="full"
        bg="white"
      >
        <VStack align="start" gap={2}>
          <HStack justify="between" w="full">
            <Skeleton h="18px" w="70%" />
            <Skeleton h="20px" w="60px" borderRadius="full" />
          </HStack>
          <SkeletonText noOfLines={2} spacing="1" skeletonHeight="2" />
          <HStack gap={2}>
            <Skeleton h="16px" w="80px" />
            <Skeleton h="16px" w="100px" />
          </HStack>
        </VStack>
      </Box>
    ))}
  </VStack>
)

// Generic content loading with customizable layout
interface ContentLoadingProps {
  layout?: "card" | "list" | "table" | "form" | "stats"
  count?: number
  message?: string
}

export const ContentLoading: React.FC<ContentLoadingProps> = ({
  layout = "card",
  count = 3,
  message
}) => {
  const renderLoading = () => {
    switch (layout) {
      case "card":
        return <CardLoading count={count} />
      case "list":
        return <ListLoading items={count} />
      case "table":
        return <TableLoading rows={count} />
      case "form":
        return <FormLoading />
      case "stats":
        return <StatsLoading />
      default:
        return <CardLoading count={count} />
    }
  }

  return (
    <VStack gap={4} w="full">
      {message && (
        <Text color="gray.600" fontSize="sm" textAlign="center">
          {message}
        </Text>
      )}
      {renderLoading()}
    </VStack>
  )
}
