import { Tooltip as ChakraTooltip } from "@chakra-ui/react"
import * as React from "react"

export interface TooltipProps extends ChakraTooltip.RootProps {
  label: React.ReactNode
  placement?: "top" | "bottom" | "left" | "right"
  isDisabled?: boolean
  children: React.ReactElement
}

export const Tooltip = React.forwardRef<HTMLDivElement, TooltipProps>(
  function Tooltip(props, ref) {
    const { label, placement, isDisabled, children, ...rest } = props

    if (isDisabled) {
      return children
    }

    return (
      <ChakraTooltip.Root positioning={{ placement }} {...rest}>
        <ChakraTooltip.Trigger asChild>
          {children}
        </ChakraTooltip.Trigger>
        <ChakraTooltip.Positioner>
          <ChakraTooltip.Content ref={ref}>
            <ChakraTooltip.Arrow />
            {label}
          </ChakraTooltip.Content>
        </ChakraTooltip.Positioner>
      </ChakraTooltip.Root>
    )
  },
)
