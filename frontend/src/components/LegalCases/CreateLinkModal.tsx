import {
  Modal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  FormControl,
  FormLabel,
  Select,
  Textarea,
  Box,
  Badge,
  Spinner,
  Alert,
} from "@chakra-ui/react"
import { FiLink, FiSave } from "react-icons/fi"
import { useState, useEffect } from "react"
import { useMutation, useQuery } from "@tanstack/react-query"

import { 
  DocumentCaseLinksService, 
  DocumentCaseLinkCreate,
  LINK_TYPE_CONFIG,
  getLinkTypeInfo,
  validateLinkData 
} from "@/client/document-case-links"
import { LegalCasesService } from "@/client/legal-cases"
import { toaster } from "@/components/ui/toaster"

interface CreateLinkModalProps {
  isOpen: boolean
  onClose: () => void
  documentId: string
  onSuccess: () => void
}

const CreateLinkModal = ({ 
  isOpen, 
  onClose, 
  documentId,
  onSuccess 
}: CreateLinkModalProps) => {
  // Form state
  const [selectedCaseId, setSelectedCaseId] = useState("")
  const [linkType, setLinkType] = useState("related")
  const [notes, setNotes] = useState("")

  // Fetch available cases
  const { 
    data: casesData, 
    isLoading: isLoadingCases,
    error: casesError 
  } = useQuery({
    queryKey: ["legal-cases", { limit: 100 }], // Get more cases for selection
    queryFn: () => LegalCasesService.getLegalCases({ limit: 100 }),
    enabled: isOpen,
  })

  const cases = casesData?.data || []

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: DocumentCaseLinkCreate) => 
      DocumentCaseLinksService.createDocumentCaseLink(documentId, data),
    onSuccess: () => {
      toaster.create({
        title: "Link created",
        description: "Document has been linked to the case successfully.",
        status: "success",
      })
      onSuccess()
      handleReset()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create link",
        status: "error",
      })
    },
  })

  const handleSubmit = () => {
    const linkData: DocumentCaseLinkCreate = {
      case_id: selectedCaseId,
      link_type: linkType,
      notes: notes.trim() || undefined,
    }

    const validationErrors = validateLinkData(linkData)
    if (validationErrors.length > 0) {
      toaster.create({
        title: "Validation Error",
        description: validationErrors.join(", "),
        status: "error",
      })
      return
    }

    createMutation.mutate(linkData)
  }

  const handleReset = () => {
    setSelectedCaseId("")
    setLinkType("related")
    setNotes("")
  }

  const handleClose = () => {
    if (!createMutation.isPending) {
      handleReset()
      onClose()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handleSubmit()
    }
  }

  const selectedCase = cases.find(c => c.id === selectedCaseId)
  const linkTypeInfo = getLinkTypeInfo(linkType)

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={2}>
            <FiLink />
            <Text>Link Document to Case</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton isDisabled={createMutation.isPending} />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* Case Selection */}
            <FormControl isRequired>
              <FormLabel fontSize="sm">Select Case</FormLabel>
              {isLoadingCases ? (
                <Box p={3} textAlign="center">
                  <Spinner size="sm" />
                  <Text fontSize="sm" color="gray.600" mt={2}>
                    Loading cases...
                  </Text>
                </Box>
              ) : casesError ? (
                <Alert status="error" size="sm">
                  Failed to load cases
                </Alert>
              ) : (
                <Select
                  placeholder="Choose a case to link to..."
                  value={selectedCaseId}
                  onChange={(e) => setSelectedCaseId(e.target.value)}
                  onKeyDown={handleKeyPress}
                >
                  {cases.map((case_) => (
                    <option key={case_.id} value={case_.id}>
                      {case_.title} (#{case_.case_number})
                    </option>
                  ))}
                </Select>
              )}
            </FormControl>

            {/* Selected Case Preview */}
            {selectedCase && (
              <Box p={3} bg="blue.50" borderRadius="md">
                <Text fontSize="sm" fontWeight="medium" mb={2}>
                  Selected Case:
                </Text>
                <VStack align="start" spacing={1}>
                  <Text fontSize="sm" fontWeight="medium">
                    {selectedCase.title}
                  </Text>
                  <HStack spacing={2}>
                    <Text fontSize="xs" color="gray.600">
                      #{selectedCase.case_number}
                    </Text>
                    <Badge
                      colorScheme={
                        selectedCase.status === 'active' ? 'green' :
                        selectedCase.status === 'closed' ? 'gray' :
                        selectedCase.status === 'pending' ? 'yellow' : 'blue'
                      }
                      variant="subtle"
                      fontSize="xs"
                    >
                      {selectedCase.status}
                    </Badge>
                    <Badge
                      colorScheme={
                        selectedCase.priority === 'high' ? 'red' :
                        selectedCase.priority === 'medium' ? 'orange' : 'green'
                      }
                      variant="outline"
                      fontSize="xs"
                    >
                      {selectedCase.priority} priority
                    </Badge>
                  </HStack>
                </VStack>
              </Box>
            )}

            {/* Link Type */}
            <FormControl>
              <FormLabel fontSize="sm">Link Type</FormLabel>
              <Select
                value={linkType}
                onChange={(e) => setLinkType(e.target.value)}
                onKeyDown={handleKeyPress}
              >
                {Object.entries(LINK_TYPE_CONFIG).map(([type, config]) => (
                  <option key={type} value={type}>
                    {config.icon} {config.label}
                  </option>
                ))}
              </Select>
              <Text fontSize="xs" color="gray.500" mt={1}>
                {linkTypeInfo.description}
              </Text>
            </FormControl>

            {/* Notes */}
            <FormControl>
              <FormLabel fontSize="sm">Notes (Optional)</FormLabel>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add any additional context or notes about this link..."
                rows={3}
                maxLength={1000}
                onKeyDown={handleKeyPress}
              />
              <Text fontSize="xs" color="gray.500" mt={1}>
                {notes.length}/1000 characters
              </Text>
            </FormControl>

            {/* Preview */}
            {selectedCase && (
              <Box p={3} bg="gray.50" borderRadius="md">
                <Text fontSize="sm" fontWeight="medium" mb={2}>Preview:</Text>
                <HStack spacing={3}>
                  <Box
                    fontSize="xl"
                    color={`${linkTypeInfo.color}.500`}
                  >
                    {linkTypeInfo.icon}
                  </Box>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm" fontWeight="medium">
                      {selectedCase.title}
                    </Text>
                    <Text fontSize="xs" color="gray.600">
                      {linkTypeInfo.label} • #{selectedCase.case_number}
                    </Text>
                    {notes && (
                      <Text fontSize="xs" color="gray.600" fontStyle="italic">
                        "{notes}"
                      </Text>
                    )}
                  </VStack>
                </HStack>
              </Box>
            )}

            {/* Help text */}
            <Text fontSize="xs" color="gray.500">
              Tip: Use Cmd/Ctrl + Enter to create quickly
            </Text>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button 
              variant="ghost" 
              onClick={handleClose}
              isDisabled={createMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={createMutation.isPending}
              leftIcon={<FiSave />}
              isDisabled={!selectedCaseId || isLoadingCases}
            >
              Create Link
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default CreateLinkModal
