import {
  Box,
  VStack,
  HStack,
  Text,
  SimpleGrid,
  Progress,
  Badge,




  useColorModeValue,
} from "@chakra-ui/react"
import {
  FiActivity,
  FiClock,
  FiUsers,
  FiTrendingUp,
} from "react-icons/fi"

import { ACTIVITY_TYPE_CONFIG } from "@/client/case-activities"

interface ActivityStatsData {
  total: number
  byType: Record<string, number>
  byUser: Record<string, number>
  recentCount: number
}

interface ActivityStatsProps {
  stats: ActivityStatsData
}

const ActivityStats = ({ stats }: ActivityStatsProps) => {
  const bgColor = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")

  // Get top activity types
  const topActivityTypes = Object.entries(stats.byType)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)

  // Get top users
  const topUsers = Object.entries(stats.byUser)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      border="1px solid"
      borderColor={borderColor}
    >
      <VStack spacing={4} align="stretch">
        {/* Header */}
        <HStack spacing={2}>
          <FiActivity />
          <Text fontWeight="medium">Activity Statistics</Text>
        </HStack>

        {/* Overview Stats */}
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
          <Box textAlign="center">
            <Box textAlign="center">Total Activities</Text>
            <Box textAlign="center">{stats.total}</Text>
            <Box textAlign="center">
              <FiTrendingUp style={{ display: 'inline', marginRight: '4px' }} />
              All time
            </Text>
          </Box>

          <Box textAlign="center">
            <Box textAlign="center">Recent (24h)</Text>
            <Box textAlign="center">{stats.recentCount}</Text>
            <Box textAlign="center">
              <FiClock style={{ display: 'inline', marginRight: '4px' }} />
              Last day
            </Text>
          </Box>

          <Box textAlign="center">
            <Box textAlign="center">Activity Types</Text>
            <Box textAlign="center">{Object.keys(stats.byType).length}</Text>
            <Box textAlign="center">
              Different types
            </Text>
          </Box>

          <Box textAlign="center">
            <Box textAlign="center">Active Users</Text>
            <Box textAlign="center">{Object.keys(stats.byUser).length}</Text>
            <Box textAlign="center">
              <FiUsers style={{ display: 'inline', marginRight: '4px' }} />
              Contributors
            </Text>
          </Box>
        </SimpleGrid>

        {/* Activity Types Breakdown */}
        {topActivityTypes.length > 0 && (
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={3}>
              Top Activity Types
            </Text>
            <VStack spacing={2} align="stretch">
              {topActivityTypes.map(([type, count]) => {
                const config = ACTIVITY_TYPE_CONFIG[type as keyof typeof ACTIVITY_TYPE_CONFIG]
                const percentage = stats.total > 0 ? (count / stats.total) * 100 : 0
                
                return (
                  <Box key={type}>
                    <HStack justify="space-between" mb={1}>
                      <HStack spacing={2}>
                        <Text fontSize="sm">
                          {config?.icon || "❓"} {config?.label || type}
                        </Text>
                      </HStack>
                      <HStack spacing={2}>
                        <Text fontSize="sm" color="gray.600">
                          {count}
                        </Text>
                        <Badge
                          colorScheme={config?.color || "gray"}
                          variant="subtle"
                          fontSize="xs"
                        >
                          {percentage.toFixed(1)}%
                        </Badge>
                      </HStack>
                    </HStack>
                    <Progress
                      value={percentage}
                      size="sm"
                      colorScheme={config?.color || "gray"}
                      borderRadius="full"
                    />
                  </Box>
                )
              })}
            </VStack>
          </Box>
        )}

        {/* Top Contributors */}
        {topUsers.length > 0 && (
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={3}>
              Top Contributors
            </Text>
            <VStack spacing={2} align="stretch">
              {topUsers.map(([user, count]) => {
                const percentage = stats.total > 0 ? (count / stats.total) * 100 : 0
                
                return (
                  <HStack key={user} justify="space-between">
                    <Text fontSize="sm" flex={1} noOfLines={1}>
                      👤 {user}
                    </Text>
                    <HStack spacing={2}>
                      <Text fontSize="sm" color="gray.600">
                        {count}
                      </Text>
                      <Badge variant="subtle" fontSize="xs" minW="45px">
                        {percentage.toFixed(1)}%
                      </Badge>
                    </HStack>
                  </HStack>
                )
              })}
            </VStack>
          </Box>
        )}

        {/* Activity Distribution */}
        {stats.total > 0 && (
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={3}>
              Activity Distribution
            </Text>
            <SimpleGrid columns={2} spacing={4}>
              <Box textAlign="center">
                <Text fontSize="xs" color="gray.600" mb={1}>
                  Recent Activity Rate
                </Text>
                <Text fontSize="lg" fontWeight="bold" color="blue.500">
                  {stats.total > 0 ? ((stats.recentCount / stats.total) * 100).toFixed(1) : 0}%
                </Text>
                <Text fontSize="xs" color="gray.500">
                  of total in last 24h
                </Text>
              </Box>
              <Box textAlign="center">
                <Text fontSize="xs" color="gray.600" mb={1}>
                  Avg per User
                </Text>
                <Text fontSize="lg" fontWeight="bold" color="green.500">
                  {Object.keys(stats.byUser).length > 0 
                    ? (stats.total / Object.keys(stats.byUser).length).toFixed(1)
                    : 0
                  }
                </Text>
                <Text fontSize="xs" color="gray.500">
                  activities per user
                </Text>
              </Box>
            </SimpleGrid>
          </Box>
        )}

        {/* Empty State */}
        {stats.total === 0 && (
          <Box textAlign="center" py={4}>
            <Text color="gray.500" fontSize="sm">
              No activity data available
            </Text>
          </Box>
        )}
      </VStack>
    </Box>
  )
}

export default ActivityStats
