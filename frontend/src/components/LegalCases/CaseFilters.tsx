import { useState, useEffect } from "react"

interface CaseFiltersProps {
  onFiltersChange: (filters: CaseFilters) => void
  initialFilters?: CaseFilters
}

export interface CaseFilters {
  search?: string
  status?: string
  priority?: string
  case_type?: string
  sort_by?: string
  sort_order?: string
}

// Status options matching backend enum
const STATUS_OPTIONS = [
  { value: "", label: "All Statuses" },
  { value: "open", label: "Open" },
  { value: "in_progress", label: "In Progress" },
  { value: "under_review", label: "Under Review" },
  { value: "closed", label: "Closed" },
  { value: "archived", label: "Archived" }
]

// Priority options matching backend enum
const PRIORITY_OPTIONS = [
  { value: "", label: "All Priorities" },
  { value: "low", label: "Low" },
  { value: "medium", label: "Medium" },
  { value: "high", label: "High" },
  { value: "urgent", label: "Urgent" }
]

// Case type options matching backend enum
const CASE_TYPE_OPTIONS = [
  { value: "", label: "All Types" },
  { value: "civil", label: "Civil" },
  { value: "criminal", label: "Criminal" },
  { value: "corporate", label: "Corporate" },
  { value: "family", label: "Family" },
  { value: "immigration", label: "Immigration" },
  { value: "real_estate", label: "Real Estate" },
  { value: "intellectual_property", label: "Intellectual Property" },
  { value: "employment", label: "Employment" },
  { value: "tax", label: "Tax" },
  { value: "other", label: "Other" }
]

// Sort options
const SORT_OPTIONS = [
  { value: "opening_date", label: "Opening Date" },
  { value: "title", label: "Title" },
  { value: "client_name", label: "Client Name" },
  { value: "status", label: "Status" },
  { value: "priority", label: "Priority" },
  { value: "case_type", label: "Case Type" }
]

const SORT_ORDER_OPTIONS = [
  { value: "desc", label: "Descending" },
  { value: "asc", label: "Ascending" }
]

export default function CaseFilters({ onFiltersChange, initialFilters = {} }: CaseFiltersProps) {
  const [filters, setFilters] = useState<CaseFilters>(initialFilters)

  // Update filters when they change
  useEffect(() => {
    onFiltersChange(filters)
  }, [filters]) // Remove onFiltersChange from dependencies to avoid infinite loops

  const handleFilterChange = (key: keyof CaseFilters, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value || undefined
    }
    setFilters(newFilters)
  }

  const clearFilters = () => {
    setFilters({
      sort_by: "opening_date",
      sort_order: "desc"
    })
  }

  const hasActiveFilters = Boolean(
    filters.search || filters.status || filters.priority || filters.case_type
  )

  return (
    <div style={{ padding: '16px', backgroundColor: '#f7fafc', borderRadius: '8px', marginBottom: '16px' }}>
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ fontWeight: 'bold', fontSize: '18px' }}>Filter Cases</h3>
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              style={{
                padding: '8px 16px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              Clear Filters
            </button>
          )}
        </div>

        {/* Search Input */}
        <div style={{ marginBottom: '16px' }}>
          <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Search</label>
          <input
            type="text"
            placeholder="Search by title or client name..."
            value={filters.search || ""}
            onChange={(e) => handleFilterChange("search", e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #e2e8f0',
              borderRadius: '6px',
              backgroundColor: 'white'
            }}
          />
        </div>

        {/* Filter Row 1: Status, Priority, Case Type */}
        <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
          <div style={{ flex: 1 }}>
            <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Status</label>
            <select
              value={filters.status || ""}
              onChange={(e) => handleFilterChange("status", e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white'
              }}
            >
              {STATUS_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div style={{ flex: 1 }}>
            <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Priority</label>
            <select
              value={filters.priority || ""}
              onChange={(e) => handleFilterChange("priority", e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white'
              }}
            >
              {PRIORITY_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div style={{ flex: 1 }}>
            <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Case Type</label>
            <select
              value={filters.case_type || ""}
              onChange={(e) => handleFilterChange("case_type", e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white'
              }}
            >
              {CASE_TYPE_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Filter Row 2: Sort Options */}
        <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
          <div style={{ flex: 1 }}>
            <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Sort By</label>
            <select
              value={filters.sort_by || "opening_date"}
              onChange={(e) => handleFilterChange("sort_by", e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white'
              }}
            >
              {SORT_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div style={{ flex: 1 }}>
            <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Sort Order</label>
            <select
              value={filters.sort_order || "desc"}
              onChange={(e) => handleFilterChange("sort_order", e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white'
              }}
            >
              {SORT_ORDER_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div style={{ flex: 1 }}>
            {/* Empty space for alignment */}
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div>
            <p style={{ fontSize: '14px', color: '#718096' }}>
              Active filters: {[
                filters.search && `Search: "${filters.search}"`,
                filters.status && `Status: ${STATUS_OPTIONS.find(o => o.value === filters.status)?.label}`,
                filters.priority && `Priority: ${PRIORITY_OPTIONS.find(o => o.value === filters.priority)?.label}`,
                filters.case_type && `Type: ${CASE_TYPE_OPTIONS.find(o => o.value === filters.case_type)?.label}`
              ].filter(Boolean).join(", ")}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
