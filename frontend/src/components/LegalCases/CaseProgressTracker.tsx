import {
  <PERSON>,
  <PERSON><PERSON>ta<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  Flex,
  Spacer,
  useDisclosure,
  Spinner,
  Alert,
  Badge,
  IconButton,
  Tooltip,

  Progress,
  SimpleGrid,
} from "@chakra-ui/react"
import {
  FiTarget,
  FiPlus,
  FiRefreshCw,
  FiClock,
  FiTrendingUp,
  FiCalendar,
  FiFlag,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"

import { 
  CaseProgressService, 
  CaseProgressSummary,
  formatProgressPercentage,
  getProgressColor 
} from "@/client/case-progress"
import { toaster } from "@/components/ui/toaster"
import useAuth from "@/hooks/useAuth"
import ProgressSummaryCard from "./ProgressSummaryCard"
import MilestonesSection from "./MilestonesSection"
import DeadlinesSection from "./DeadlinesSection"
import CreateMilestoneModal from "./CreateMilestoneModal"
import CreateDeadlineModal from "./CreateDeadlineModal"
import ProgressVisualization from "./ProgressVisualization"

interface CaseProgressTrackerProps {
  caseId: string
  caseTitle?: string
  caseType?: string
  onProgressUpdate?: () => void
}

const CaseProgressTracker = ({ 
  caseId, 
  caseTitle,
  caseType,
  onProgressUpdate 
}: CaseProgressTrackerProps) => {
  const { user } = useAuth()
  

  const { isOpen: isMilestoneModalOpen, onOpen: onMilestoneModalOpen, onClose: onMilestoneModalClose } = useDisclosure()
  const { isOpen: isDeadlineModalOpen, onOpen: onDeadlineModalOpen, onClose: onDeadlineModalClose } = useDisclosure()

  const borderColor = "gray.200"
  const bgColor = "white"

  // Fetch progress summary
  const { 
    data: progressSummary, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ["case-progress-summary", caseId],
    queryFn: () => CaseProgressService.getCaseProgressSummary(caseId),
    enabled: !!caseId,
  })

  const handleMilestoneSuccess = () => {
    onMilestoneModalClose()
    refetch()
    onProgressUpdate?.()
  }

  const handleDeadlineSuccess = () => {
    onDeadlineModalClose()
    refetch()
    onProgressUpdate?.()
  }

  const handleProgressUpdate = () => {
    refetch()
    onProgressUpdate?.()
  }

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load case progress. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={2}>
            <FiTarget />
            <Text fontSize="xl" fontWeight="bold">
              Case Progress Tracking
            </Text>
            {progressSummary && (
              <Badge 
                variant="subtle" 
                colorScheme={getProgressColor(progressSummary.progress_percentage)}
              >
                {formatProgressPercentage(progressSummary.progress_percentage)} Complete
              </Badge>
            )}
          </HStack>
          <Text fontSize="sm" color="gray.600">
            {caseTitle ? `Progress tracking for "${caseTitle}"` : "Monitor milestones and deadlines"}
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Refresh progress">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
          <Button
            leftIcon={<FiFlag />}
            size="sm"
            variant="outline"
            onClick={onMilestoneModalOpen}
          >
            Add Milestone
          </Button>
          <Button
            leftIcon={<FiClock />}
            size="sm"
            variant="outline"
            onClick={onDeadlineModalOpen}
          >
            Add Deadline
          </Button>
        </HStack>
      </Flex>

      {/* Content */}
      {isLoading ? (
        <Box textAlign="center" py={12}>
          <Spinner size="xl" />
          <Text mt={4} color="gray.600">Loading progress data...</Text>
        </Box>
      ) : !progressSummary ? (
        <Box textAlign="center" py={12}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            No progress data available
          </Text>
          <Text fontSize="sm" color="gray.600" mb={6}>
            Start by adding milestones and deadlines to track case progress.
          </Text>
          <HStack spacing={3} justify="center">
            <Button
              leftIcon={<FiFlag />}
              colorScheme="blue"
              onClick={onMilestoneModalOpen}
            >
              Add First Milestone
            </Button>
            <Button
              leftIcon={<FiClock />}
              variant="outline"
              onClick={onDeadlineModalOpen}
            >
              Add Deadline
            </Button>
          </HStack>
        </Box>
      ) : (
        <VStack spacing={8} align="stretch">
          {/* Progress Summary Cards */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
            <ProgressSummaryCard
              title="Overall Progress"
              value={formatProgressPercentage(progressSummary.progress_percentage)}
              subtitle={`${progressSummary.completed_milestones}/${progressSummary.total_milestones} milestones`}
              colorScheme={getProgressColor(progressSummary.progress_percentage)}
              icon={FiTrendingUp}
            >
              <Progress 
                value={progressSummary.progress_percentage} 
                colorScheme={getProgressColor(progressSummary.progress_percentage)}
                size="sm"
                mt={2}
              />
            </ProgressSummaryCard>

            <ProgressSummaryCard
              title="Upcoming Deadlines"
              value={progressSummary.upcoming_deadlines.toString()}
              subtitle="Next 30 days"
              colorScheme={progressSummary.upcoming_deadlines > 0 ? "orange" : "green"}
              icon={FiCalendar}
            />

            <ProgressSummaryCard
              title="Critical Deadlines"
              value={progressSummary.critical_deadlines.toString()}
              subtitle="Require immediate attention"
              colorScheme={progressSummary.critical_deadlines > 0 ? "red" : "green"}
              icon={FiFlag}
            />

            <ProgressSummaryCard
              title="Overdue Items"
              value={progressSummary.overdue_milestones.toString()}
              subtitle="Past target date"
              colorScheme={progressSummary.overdue_milestones > 0 ? "red" : "green"}
              icon={FiClock}
            />
          </SimpleGrid>

          {/* Progress Visualization */}
          <Box
            p={6}
            bg={bgColor}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <ProgressVisualization 
              progressSummary={progressSummary}
              caseId={caseId}
            />
          </Box>

          {/* Milestones Section */}
          <Box>
            <MilestonesSection
              caseId={caseId}
              caseType={caseType}
              onProgressUpdate={handleProgressUpdate}
            />
          </Box>

          {/* Deadlines Section */}
          <Box>
            <DeadlinesSection
              caseId={caseId}
              onProgressUpdate={handleProgressUpdate}
            />
          </Box>
        </VStack>
      )}

      {/* Create Milestone Modal */}
      <CreateMilestoneModal
        isOpen={isMilestoneModalOpen}
        onClose={onMilestoneModalClose}
        caseId={caseId}
        caseType={caseType}
        onSuccess={handleMilestoneSuccess}
      />

      {/* Create Deadline Modal */}
      <CreateDeadlineModal
        isOpen={isDeadlineModalOpen}
        onClose={onDeadlineModalClose}
        caseId={caseId}
        onSuccess={handleDeadlineSuccess}
      />
    </Box>
  )
}

export default CaseProgressTracker
