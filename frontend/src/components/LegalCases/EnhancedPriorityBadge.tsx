import {
  Badge,
  H<PERSON>tack,
  Text,
  Tooltip,
  Box,
  useBreakpointValue,
} from "@chakra-ui/react"
import { keyframes } from "@emotion/react"
import { FiAlertTriangle, FiArrowUp, FiMinus, FiArrowDown } from "react-icons/fi"

interface EnhancedPriorityBadgeProps {
  priority: string
  size?: "sm" | "md" | "lg"
  showIcon?: boolean
  showTooltip?: boolean
  animated?: boolean
  clickable?: boolean
  onClick?: () => void
}

// Pulse animation for urgent priority
const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`

const EnhancedPriorityBadge = ({
  priority,
  size = "md",
  showIcon = true,
  showTooltip = true,
  animated = true,
  clickable = false,
  onClick
}: EnhancedPriorityBadgeProps) => {
  const isMobile = useBreakpointValue({ base: true, md: false })
  
  // Priority configuration
  const getPriorityConfig = (priority: string) => {
    const normalizedPriority = String(priority || '').toLowerCase()
    
    switch (normalizedPriority) {
      case 'urgent':
        return {
          colorScheme: 'red',
          icon: FiAlertTriangle,
          label: 'Urgent',
          description: 'Requires immediate attention',
          shouldPulse: true
        }
      case 'high':
        return {
          colorScheme: 'orange',
          icon: FiArrowUp,
          label: 'High',
          description: 'High priority case',
          shouldPulse: false
        }
      case 'medium':
        return {
          colorScheme: 'blue',
          icon: FiMinus,
          label: 'Medium',
          description: 'Standard priority case',
          shouldPulse: false
        }
      case 'low':
        return {
          colorScheme: 'gray',
          icon: FiArrowDown,
          label: 'Low',
          description: 'Low priority case',
          shouldPulse: false
        }
      default:
        return {
          colorScheme: 'gray',
          icon: FiMinus,
          label: 'Unknown',
          description: 'Priority not set',
          shouldPulse: false
        }
    }
  }

  const config = getPriorityConfig(priority)
  const IconComponent = config.icon

  const badgeContent = (
    <Badge
      colorScheme={config.colorScheme}
      variant={config.colorScheme === 'red' ? 'solid' : 'subtle'}
      size={size}
      cursor={clickable ? "pointer" : "default"}
      _hover={clickable ? { 
        transform: "scale(1.05)", 
        shadow: "md" 
      } : {}}
      transition="all 0.2s"
      onClick={clickable ? onClick : undefined}
      borderRadius="full"
      px={3}
      py={1}
      animation={animated && config.shouldPulse ? `${pulse} 2s infinite` : undefined}
    >
      <HStack spacing={1} align="center">
        {showIcon && !isMobile && (
          <Box 
            as={IconComponent} 
            boxSize="12px"
            color={config.colorScheme === 'red' ? 'white' : 'current'}
          />
        )}
        <Text 
          fontSize={size === "sm" ? "xs" : "sm"} 
          fontWeight="medium"
          color={config.colorScheme === 'red' ? 'white' : 'current'}
        >
          {config.label}
        </Text>
      </HStack>
    </Badge>
  )

  if (showTooltip) {
    return (
      <Tooltip 
        label={config.description} 
        placement="top"
        hasArrow
        bg="gray.700"
        color="white"
        fontSize="sm"
      >
        {badgeContent}
      </Tooltip>
    )
  }

  return badgeContent
}

export default EnhancedPriorityBadge
