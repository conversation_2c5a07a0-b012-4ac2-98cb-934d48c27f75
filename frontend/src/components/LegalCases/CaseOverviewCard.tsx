import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Card,
  CardBody,
  CardHeader,
  Heading,





  Avatar,
  Tooltip,
} from "@chakra-ui/react"
import {
  FiUser,
  FiCalendar,
  FiFlag,
  FiClock,
  FiActivity,
  FiFileText,
  FiMessageSquare,
} from "react-icons/fi"

import { CaseOverview, formatDuration } from "@/client/case-analytics"

interface CaseOverviewCardProps {
  overview: CaseOverview
}

const CaseOverviewCard = ({ overview }: CaseOverviewCardProps) => {
  const getCaseTypeColor = (caseType: string) => {
    const colors: Record<string, string> = {
      civil: "blue",
      criminal: "red",
      family: "purple",
      corporate: "green",
      immigration: "orange",
      personal_injury: "yellow",
      real_estate: "teal",
      intellectual_property: "pink",
      employment: "cyan",
      contract_dispute: "indigo",
      other: "gray"
    }
    return colors[caseType] || "gray"
  }

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      open: "green",
      in_progress: "blue",
      on_hold: "yellow",
      closed: "gray",
      cancelled: "red"
    }
    return colors[status] || "gray"
  }

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      urgent: "red",
      high: "orange",
      medium: "yellow",
      low: "green"
    }
    return colors[priority] || "gray"
  }

  const formatCaseType = (caseType: string) => {
    return caseType.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())
  }

  const formatStatus = (status: string) => {
    return status.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())
  }

  const formatPriority = (priority: string) => {
    return priority.charAt(0).toUpperCase() + priority.slice(1)
  }

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiFlag />
          <Heading size="sm">Case Overview</Heading>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Case basic info */}
          <Box>
            <Text fontSize="lg" fontWeight="bold" mb={2} noOfLines={2}>
              {overview.title}
            </Text>
            
            <VStack spacing={2} align="stretch">
              <HStack justify="space-between">
                <HStack spacing={2}>
                  <FiUser size={14} />
                  <Text fontSize="sm" color="gray.600">Client:</Text>
                </HStack>
                <Text fontSize="sm" fontWeight="medium">
                  {overview.client_name}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <HStack spacing={2}>
                  <FiCalendar size={14} />
                  <Text fontSize="sm" color="gray.600">Opened:</Text>
                </HStack>
                <Text fontSize="sm">
                  {new Date(overview.opening_date).toLocaleDateString()}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <HStack spacing={2}>
                  <FiClock size={14} />
                  <Text fontSize="sm" color="gray.600">Age:</Text>
                </HStack>
                <Text fontSize="sm" fontWeight="medium">
                  {formatDuration(overview.case_age_days)}
                </Text>
              </HStack>
            </VStack>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Status badges */}
          <HStack spacing={2} flexWrap="wrap">
            <Badge 
              colorScheme={getCaseTypeColor(overview.case_type)} 
              variant="subtle"
              fontSize="xs"
            >
              {formatCaseType(overview.case_type)}
            </Badge>
            
            <Badge 
              colorScheme={getStatusColor(overview.status)} 
              variant="solid"
              fontSize="xs"
            >
              {formatStatus(overview.status)}
            </Badge>
            
            <Badge 
              colorScheme={getPriorityColor(overview.priority)} 
              variant="outline"
              fontSize="xs"
            >
              {formatPriority(overview.priority)} Priority
            </Badge>
          </HStack>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Lawyer info */}
          {overview.lawyer && (
            <Box>
              <Text fontSize="sm" color="gray.600" mb={2}>Assigned Lawyer:</Text>
              <HStack spacing={3}>
                <Avatar size="sm" name={overview.lawyer.name} />
                <VStack align="start" spacing={0}>
                  <Text fontSize="sm" fontWeight="medium">
                    {overview.lawyer.name}
                  </Text>
                  <Text fontSize="xs" color="gray.500">
                    {overview.lawyer.email}
                  </Text>
                </VStack>
              </HStack>
            </Box>
          )}

          <Box height="1px" bg="gray.200" my={2} />

          {/* Activity summary */}
          <VStack spacing={3} align="stretch">
            <Text fontSize="sm" fontWeight="medium" color="gray.700">
              Activity Summary
            </Text>
            
            <HStack justify="space-between">
              <Tooltip label="Total activities recorded">
                <HStack spacing={2}>
                  <FiActivity size={14} color="blue" />
                  <Text fontSize="sm">Activities:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="blue.600">
                {overview.totals.activities}
              </Text>
            </HStack>
            
            <HStack justify="space-between">
              <Tooltip label="Documents uploaded">
                <HStack spacing={2}>
                  <FiFileText size={14} color="green" />
                  <Text fontSize="sm">Documents:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="green.600">
                {overview.totals.documents}
              </Text>
            </HStack>
            
            <HStack justify="space-between">
              <Tooltip label="Notes and comments">
                <HStack spacing={2}>
                  <FiMessageSquare size={14} color="purple" />
                  <Text fontSize="sm">Notes:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="purple.600">
                {overview.totals.notes}
              </Text>
            </HStack>
            
            <HStack justify="space-between">
              <Tooltip label="Recent activities in analysis period">
                <HStack spacing={2}>
                  <FiClock size={14} color="orange" />
                  <Text fontSize="sm">Recent:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="orange.600">
                {overview.totals.recent_activities}
              </Text>
            </HStack>
          </VStack>

          {/* Quick stats */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <HStack justify="space-around">
              <Box textAlign="center">
                <Text fontSize="lg" fontWeight="bold" color="blue.600">
                  {overview.case_age_days}
                </Text>
                <Text fontSize="xs" color="gray.600">Days Active</Text>
              </Box>

              <Box textAlign="center">
                <Text fontSize="lg" fontWeight="bold" color="green.600">
                  {Math.round(overview.totals.activities / Math.max(1, overview.case_age_days) * 7)}
                </Text>
                <Text fontSize="xs" color="gray.600">Weekly Activity</Text>
              </Box>

              <Box textAlign="center">
                <Text fontSize="lg" fontWeight="bold" color="purple.600">
                  {overview.totals.recent_activities > 0 ? "Active" : "Quiet"}
                </Text>
                <Text fontSize="xs" color="gray.600">Status</Text>
              </Box>
            </HStack>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default CaseOverviewCard
