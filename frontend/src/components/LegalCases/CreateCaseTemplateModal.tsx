import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Switch,
  Badge,
  Text,
  Box,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  IconButton,
  Tooltip,
} from "@chakra-ui/react"
import { FiPlus, FiTrash2, FiCalendar, FiFlag } from "react-icons/fi"
import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import {
  CaseTemplatesService,
  CaseTemplateCreate,
  CASE_TYPE_CONFIG
} from "@/client/case-templates"
import useCustomToast from "@/hooks/useCustomToast"

interface CreateCaseTemplateModalProps {
  isOpen: boolean
  onClose: () => void
}

interface MilestoneData {
  title: string
  description: string
  type: string
  order: number
  required: boolean
  estimated_hours?: number
  target_date?: string
}

interface DeadlineData {
  title: string
  description: string
  type: string
  deadline_date?: string
  critical: boolean
  reminder_days: number
}

const CreateCaseTemplateModal = ({ isOpen, onClose }: CreateCaseTemplateModalProps) => {
  const [formData, setFormData] = useState<CaseTemplateCreate>({
    name: "",
    description: "",
    case_type: "civil",
    template_data: {},
    is_active: true,
    is_public: false,
    tags: []
  })
  
  const [milestones, setMilestones] = useState<MilestoneData[]>([])
  const [deadlines, setDeadlines] = useState<DeadlineData[]>([])
  const [tagInput, setTagInput] = useState("")

  const toast = useCustomToast()
  const queryClient = useQueryClient()

  const createTemplateMutation = useMutation({
    mutationFn: CaseTemplatesService.createCaseTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["case-templates"] })
      toast({
        title: "Template created",
        description: "Case template has been created successfully",
        status: "success",
        duration: 3000,
      })
      handleClose()
    },
    onError: (error: any) => {
      toast({
        title: "Error creating template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      case_type: "civil",
      template_data: {},
      is_active: true,
      is_public: false,
      tags: []
    })
    setMilestones([])
    setDeadlines([])
    setTagInput("")
    onClose()
  }

  const handleSubmit = () => {
    const templateData = {
      ...formData,
      template_data: {
        milestones: milestones,
        deadlines: deadlines,
        metadata: {
          created_with: "template_builder",
          version: "1.0"
        }
      }
    }
    
    createTemplateMutation.mutate(templateData)
  }

  const addMilestone = () => {
    setMilestones([...milestones, {
      title: "",
      description: "",
      type: "custom",
      order: milestones.length + 1,
      required: false,
      estimated_hours: 1,
      reminder_days: 7
    }])
  }

  const updateMilestone = (index: number, field: keyof MilestoneData, value: any) => {
    const updated = [...milestones]
    updated[index] = { ...updated[index], [field]: value }
    setMilestones(updated)
  }

  const removeMilestone = (index: number) => {
    setMilestones(milestones.filter((_, i) => i !== index))
  }

  const addDeadline = () => {
    setDeadlines([...deadlines, {
      title: "",
      description: "",
      type: "other",
      critical: false,
      reminder_days: 7
    }])
  }

  const updateDeadline = (index: number, field: keyof DeadlineData, value: any) => {
    const updated = [...deadlines]
    updated[index] = { ...updated[index], [field]: value }
    setDeadlines(updated)
  }

  const removeDeadline = (index: number) => {
    setDeadlines(deadlines.filter((_, i) => i !== index))
  }

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, tagInput.trim()]
      })
      setTagInput("")
    }
  }

  const removeTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    })
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="4xl">
      <ModalOverlay />
      <ModalContent maxH="90vh" overflowY="auto">
        <ModalHeader>Create Case Template</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <Tabs>
            <TabList>
              <Tab>Basic Info</Tab>
              <Tab>Milestones</Tab>
              <Tab>Deadlines</Tab>
              <Tab>Settings</Tab>
            </TabList>

            <TabPanels>
              {/* Basic Info */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel>Template Name</FormLabel>
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter template name"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel>Description</FormLabel>
                    <Textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Describe what this template is for"
                      rows={3}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Case Type</FormLabel>
                    <Select
                      value={formData.case_type}
                      onChange={(e) => setFormData({ ...formData, case_type: e.target.value })}
                    >
                      {Object.entries(CASE_TYPE_CONFIG).map(([key, config]) => (
                        <option key={key} value={key}>
                          {config.icon} {config.label}
                        </option>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel>Tags</FormLabel>
                    <HStack>
                      <Input
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      />
                      <Button onClick={addTag} size="sm">Add</Button>
                    </HStack>
                    <HStack mt={2} flexWrap="wrap">
                      {formData.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="solid"
                          colorScheme="blue"
                          cursor="pointer"
                          onClick={() => removeTag(tag)}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </HStack>
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Milestones */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="medium">Template Milestones</Text>
                    <Button leftIcon={<FiPlus />} onClick={addMilestone} size="sm">
                      Add Milestone
                    </Button>
                  </HStack>

                  {milestones.map((milestone, index) => (
                    <Box key={index} p={4} border="1px" borderColor="gray.200" borderRadius="md">
                      <VStack spacing={3} align="stretch">
                        <HStack justify="space-between">
                          <Text fontWeight="medium">Milestone {index + 1}</Text>
                          <IconButton
                            icon={<FiTrash2 />}
                            size="sm"
                            variant="ghost"
                            colorScheme="red"
                            onClick={() => removeMilestone(index)}
                          />
                        </HStack>

                        <FormControl isRequired>
                          <FormLabel size="sm">Title</FormLabel>
                          <Input
                            value={milestone.title}
                            onChange={(e) => updateMilestone(index, 'title', e.target.value)}
                            placeholder="Milestone title"
                            size="sm"
                          />
                        </FormControl>

                        <FormControl>
                          <FormLabel size="sm">Description</FormLabel>
                          <Textarea
                            value={milestone.description}
                            onChange={(e) => updateMilestone(index, 'description', e.target.value)}
                            placeholder="Milestone description"
                            size="sm"
                            rows={2}
                          />
                        </FormControl>

                        <HStack>
                          <FormControl>
                            <FormLabel size="sm">Type</FormLabel>
                            <Select
                              value={milestone.type}
                              onChange={(e) => updateMilestone(index, 'type', e.target.value)}
                              size="sm"
                            >
                              <option value="custom">Custom</option>
                              <option value="filing">Filing</option>
                              <option value="discovery">Discovery</option>
                              <option value="hearing">Hearing</option>
                              <option value="settlement">Settlement</option>
                              <option value="trial">Trial</option>
                            </Select>
                          </FormControl>

                          <FormControl>
                            <FormLabel size="sm">Estimated Hours</FormLabel>
                            <Input
                              type="number"
                              value={milestone.estimated_hours || ""}
                              onChange={(e) => updateMilestone(index, 'estimated_hours', parseFloat(e.target.value))}
                              size="sm"
                              min="0"
                              step="0.5"
                            />
                          </FormControl>
                        </HStack>

                        <HStack>
                          <Switch
                            isChecked={milestone.required}
                            onChange={(e) => updateMilestone(index, 'required', e.target.checked)}
                          />
                          <Text fontSize="sm">Required milestone</Text>
                        </HStack>
                      </VStack>
                    </Box>
                  ))}

                  {milestones.length === 0 && (
                    <Box textAlign="center" py={8} color="gray.500">
                      <Text>No milestones added yet</Text>
                      <Button
                        leftIcon={<FiPlus />}
                        onClick={addMilestone}
                        size="sm"
                        mt={2}
                        variant="outline"
                      >
                        Add your first milestone
                      </Button>
                    </Box>
                  )}
                </VStack>
              </TabPanel>

              {/* Deadlines */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="medium">Template Deadlines</Text>
                    <Button leftIcon={<FiPlus />} onClick={addDeadline} size="sm">
                      Add Deadline
                    </Button>
                  </HStack>

                  {deadlines.map((deadline, index) => (
                    <Box key={index} p={4} border="1px" borderColor="gray.200" borderRadius="md">
                      <VStack spacing={3} align="stretch">
                        <HStack justify="space-between">
                          <Text fontWeight="medium">Deadline {index + 1}</Text>
                          <IconButton
                            icon={<FiTrash2 />}
                            size="sm"
                            variant="ghost"
                            colorScheme="red"
                            onClick={() => removeDeadline(index)}
                          />
                        </HStack>

                        <FormControl isRequired>
                          <FormLabel size="sm">Title</FormLabel>
                          <Input
                            value={deadline.title}
                            onChange={(e) => updateDeadline(index, 'title', e.target.value)}
                            placeholder="Deadline title"
                            size="sm"
                          />
                        </FormControl>

                        <FormControl>
                          <FormLabel size="sm">Description</FormLabel>
                          <Textarea
                            value={deadline.description}
                            onChange={(e) => updateDeadline(index, 'description', e.target.value)}
                            placeholder="Deadline description"
                            size="sm"
                            rows={2}
                          />
                        </FormControl>

                        <HStack>
                          <FormControl>
                            <FormLabel size="sm">Type</FormLabel>
                            <Select
                              value={deadline.type}
                              onChange={(e) => updateDeadline(index, 'type', e.target.value)}
                              size="sm"
                            >
                              <option value="other">Other</option>
                              <option value="filing">Filing Deadline</option>
                              <option value="discovery">Discovery Deadline</option>
                              <option value="hearing">Hearing Date</option>
                              <option value="trial">Trial Date</option>
                              <option value="settlement">Settlement Deadline</option>
                            </Select>
                          </FormControl>

                          <FormControl>
                            <FormLabel size="sm">Reminder (days)</FormLabel>
                            <Input
                              type="number"
                              value={deadline.reminder_days}
                              onChange={(e) => updateDeadline(index, 'reminder_days', parseInt(e.target.value))}
                              size="sm"
                              min="1"
                            />
                          </FormControl>
                        </HStack>

                        <HStack>
                          <Switch
                            isChecked={deadline.critical}
                            onChange={(e) => updateDeadline(index, 'critical', e.target.checked)}
                          />
                          <Text fontSize="sm">Critical deadline</Text>
                        </HStack>
                      </VStack>
                    </Box>
                  ))}

                  {deadlines.length === 0 && (
                    <Box textAlign="center" py={8} color="gray.500">
                      <Text>No deadlines added yet</Text>
                      <Button
                        leftIcon={<FiPlus />}
                        onClick={addDeadline}
                        size="sm"
                        mt={2}
                        variant="outline"
                      >
                        Add your first deadline
                      </Button>
                    </Box>
                  )}
                </VStack>
              </TabPanel>

              {/* Settings */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="medium">Active Template</Text>
                        <Text fontSize="sm" color="gray.600">
                          Active templates can be used to create cases
                        </Text>
                      </VStack>
                      <Switch
                        isChecked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                      />
                    </HStack>
                  </FormControl>

                  <FormControl>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="medium">Public Template</Text>
                        <Text fontSize="sm" color="gray.600">
                          Public templates can be used by other users
                        </Text>
                      </VStack>
                      <Switch
                        isChecked={formData.is_public}
                        onChange={(e) => setFormData({ ...formData, is_public: e.target.checked })}
                      />
                    </HStack>
                  </FormControl>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={createTemplateMutation.isPending}
            isDisabled={!formData.name.trim()}
          >
            Create Template
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default CreateCaseTemplateModal
