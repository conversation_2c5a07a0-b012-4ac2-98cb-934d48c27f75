import { Box, Progress, Text, VStack, HStack } from "@chakra-ui/react"

interface CaseProgressBarProps {
  status: string
  size?: "sm" | "md" | "lg"
  showLabel?: boolean
  showPercentage?: boolean
  animated?: boolean
  className?: string
}

const CaseProgressBar = ({
  status,
  size = "md",
  showLabel = true,
  showPercentage = true,
  animated = true,
  className = ""
}: CaseProgressBarProps) => {

  // Define progress percentages and colors for each status
  const getStatusProgress = (status: string) => {
    const statusStr = String(status || '').toLowerCase()
    switch (statusStr) {
      case 'open':
        return {
          progress: 10,
          colorScheme: 'blue',
          label: 'Just Started',
          description: 'Case has been opened'
        }
      case 'in_progress':
        return {
          progress: 50,
          colorScheme: 'orange',
          label: 'In Progress',
          description: 'Case is being actively worked on'
        }
      case 'under_review':
        return {
          progress: 80,
          colorScheme: 'purple',
          label: 'Under Review',
          description: 'Case is being reviewed for completion'
        }
      case 'closed':
        return {
          progress: 100,
          colorScheme: 'green',
          label: 'Completed',
          description: 'Case has been successfully closed'
        }
      case 'archived':
        return {
          progress: 100,
          colorScheme: 'gray',
          label: 'Archived',
          description: 'Case has been archived'
        }
      default:
        return {
          progress: 0,
          colorScheme: 'gray',
          label: 'Unknown',
          description: 'Unknown status'
        }
    }
  }

  const statusConfig = getStatusProgress(status)

  // Size configurations
  const sizeConfig = {
    sm: {
      height: '4px',
      fontSize: 'xs',
      spacing: 1
    },
    md: {
      height: '6px',
      fontSize: 'sm',
      spacing: 2
    },
    lg: {
      height: '8px',
      fontSize: 'md',
      spacing: 3
    }
  }

  const config = sizeConfig[size]

  return (
    <VStack spacing={config.spacing} align="stretch" className={className}>
      {showLabel && (
        <HStack justify="space-between" align="center">
          <Text fontSize={config.fontSize} fontWeight="medium" color="gray.700">
            {statusConfig.label}
          </Text>
          {showPercentage && (
            <Text fontSize={config.fontSize} color="gray.500">
              {statusConfig.progress}%
            </Text>
          )}
        </HStack>
      )}

      <Progress
        value={statusConfig.progress}
        colorScheme={statusConfig.colorScheme}
        size={size}
        height={config.height}
        borderRadius="full"
        bg="gray.100"
        transition={animated ? "all 0.3s ease-in-out" : "none"}
        title={statusConfig.description}
      />
    </VStack>
  )
}

export default CaseProgressBar
