import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, I<PERSON> } from "@chakra-ui/react"
import {
  FiCircle,
  FiPlay,
  FiEye,
  <PERSON>Check,
  FiArchive
} from "react-icons/fi"

interface CaseStatusBadgeProps {
  status: string
  size?: "sm" | "md" | "lg"
  showIcon?: boolean
  animated?: boolean
}

const CaseStatusBadge = ({
  status,
  size = "sm",
  showIcon = true,
  animated = false
}: CaseStatusBadgeProps) => {

  const getStatusConfig = (status: string) => {
    const statusStr = String(status || '').toLowerCase()
    switch (statusStr) {
      case 'open':
        return {
          color: 'blue',
          icon: FiCircle,
          label: 'Open',
          description: 'Case is open and ready for work'
        }
      case 'in_progress':
        return {
          color: 'orange',
          icon: FiPlay,
          label: 'In Progress',
          description: 'Case is actively being worked on'
        }
      case 'under_review':
        return {
          color: 'purple',
          icon: FiEye,
          label: 'Under Review',
          description: 'Case is being reviewed'
        }
      case 'closed':
        return {
          color: 'green',
          icon: <PERSON><PERSON><PERSON><PERSON>,
          label: 'Closed',
          description: 'Case has been completed'
        }
      case 'archived':
        return {
          color: 'gray',
          icon: FiArchive,
          label: 'Archived',
          description: 'Case has been archived'
        }
      default:
        return {
          color: 'gray',
          icon: FiCircle,
          label: statusStr ? statusStr.charAt(0).toUpperCase() + statusStr.slice(1) : 'Unknown',
          description: 'Unknown status'
        }
    }
  }

  const statusConfig = getStatusConfig(status)
  const IconComponent = statusConfig.icon

  return (
    <Badge
      colorScheme={statusConfig.color}
      size={size}
      textTransform="none"
      title={statusConfig.description}
      cursor="help"
      transition={animated ? "all 0.2s ease-in-out" : "none"}
      _hover={animated ? {
        transform: "scale(1.05)",
        shadow: "md"
      } : {}}
    >
      <HStack spacing={1} align="center">
        {showIcon && (
          <Icon
            as={IconComponent}
            boxSize={size === "sm" ? "12px" : size === "md" ? "14px" : "16px"}
          />
        )}
        <span>{statusConfig.label}</span>
      </HStack>
    </Badge>
  )
}

export default CaseStatusBadge
