/**
=========================================================
* EquiNova Material Edit Legal Case
=========================================================

* Material-UI implementation of Edit Legal Case modal
* Replaces Chakra UI dialog with Material Design

=========================================================
*/

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Stack,
  IconButton,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Close as CloseIcon,
  Save as SaveIcon,
  Gavel as LegalIcon,
  Person as ClientIcon,
} from '@mui/icons-material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { type SubmitHandler, useForm } from 'react-hook-form';

import { type LegalCaseUpdate, type LegalCasePublic, LegalCasesService } from '@/client';
import type { ApiError } from '@/client/core/ApiError';
import useCustomToast from '@/hooks/useCustomToast';
import { handleError } from '@/utils';

interface MaterialEditLegalCaseProps {
  legalCase: LegalCasePublic;
  variant?: 'button' | 'icon';
  size?: 'small' | 'medium' | 'large';
}

const MaterialEditLegalCase: React.FC<MaterialEditLegalCaseProps> = ({
  legalCase,
  variant = 'button',
  size = 'small',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const queryClient = useQueryClient();
  const { showSuccessToast } = useCustomToast();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting, isDirty },
  } = useForm<LegalCaseUpdate>({
    mode: 'onBlur',
    criteriaMode: 'all',
    defaultValues: {
      title: legalCase.title || '',
      client_name: legalCase.client_name || '',
    },
  });

  const mutation = useMutation({
    mutationFn: (data: LegalCaseUpdate) =>
      LegalCasesService.updateLegalCase({
        legalCaseId: legalCase.id,
        requestBody: data,
      }),
    onSuccess: () => {
      showSuccessToast('Legal case updated successfully.');
      reset();
      setIsOpen(false);
    },
    onError: (err: ApiError) => {
      handleError(err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['legal-cases'] });
    },
  });

  const onSubmit: SubmitHandler<LegalCaseUpdate> = async (data) => {
    mutation.mutate(data);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setIsOpen(false);
      reset();
    }
  };

  const TriggerButton = variant === 'icon' ? (
    <IconButton
      onClick={() => setIsOpen(true)}
      size={size}
      color="primary"
      title="Edit Legal Case"
    >
      <EditIcon />
    </IconButton>
  ) : (
    <Button
      variant="outlined"
      startIcon={<EditIcon />}
      onClick={() => setIsOpen(true)}
      size={size}
    >
      Edit
    </Button>
  );

  return (
    <>
      {TriggerButton}

      <Dialog
        open={isOpen}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            minHeight: 450,
          },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            pb: 1,
          }}
        >
          <Box display="flex" alignItems="center" gap={1}>
            <EditIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Edit Legal Case
            </Typography>
          </Box>
          <IconButton
            onClick={handleClose}
            size="small"
            disabled={isSubmitting}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogContent sx={{ pt: 2 }}>
            {/* Case Info Header */}
            <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Editing Case
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Chip
                  label={`ID: ${legalCase.id.slice(-8)}`}
                  size="small"
                  variant="outlined"
                />
                <Chip
                  label={legalCase.status || 'Unknown Status'}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Update the case details below. Changes will be saved immediately.
              </Typography>
            </Box>

            <Stack spacing={3}>
              {/* Case Title Field */}
              <TextField
                label="Case Title"
                placeholder="Enter the case title..."
                fullWidth
                required
                error={!!errors.title}
                helperText={errors.title?.message}
                disabled={isSubmitting}
                {...register('title', {
                  required: 'Case title is required.',
                  minLength: {
                    value: 3,
                    message: 'Case title must be at least 3 characters.',
                  },
                  maxLength: {
                    value: 200,
                    message: 'Case title must not exceed 200 characters.',
                  },
                })}
                InputProps={{
                  startAdornment: (
                    <LegalIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  ),
                }}
              />

              {/* Client Name Field */}
              <TextField
                label="Client Name"
                placeholder="Enter the client name..."
                fullWidth
                required
                error={!!errors.client_name}
                helperText={errors.client_name?.message}
                disabled={isSubmitting}
                {...register('client_name', {
                  required: 'Client name is required.',
                  minLength: {
                    value: 2,
                    message: 'Client name must be at least 2 characters.',
                  },
                  maxLength: {
                    value: 100,
                    message: 'Client name must not exceed 100 characters.',
                  },
                })}
                InputProps={{
                  startAdornment: (
                    <ClientIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  ),
                }}
              />

              {/* Change Indicator */}
              {isDirty && (
                <Alert severity="info" sx={{ borderRadius: 2 }}>
                  <Typography variant="body2">
                    <strong>Unsaved changes detected.</strong> Click "Save Changes" to update the legal case.
                  </Typography>
                </Alert>
              )}

              {/* Error Display */}
              {mutation.isError && (
                <Alert severity="error" sx={{ borderRadius: 2 }}>
                  <Typography variant="body2">
                    Failed to update legal case. Please check your input and try again.
                  </Typography>
                </Alert>
              )}
            </Stack>
          </DialogContent>

          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleClose}
              disabled={isSubmitting}
              color="inherit"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={!isDirty || isSubmitting}
              startIcon={
                isSubmitting ? (
                  <CircularProgress size={16} />
                ) : (
                  <SaveIcon />
                )
              }
              sx={{ minWidth: 140 }}
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};

export default MaterialEditLegalCase;
