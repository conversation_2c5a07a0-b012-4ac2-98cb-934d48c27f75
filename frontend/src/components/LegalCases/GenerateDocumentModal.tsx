import {
  Modal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Text,
  Box,
  Badge,

  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
} from "@chakra-ui/react"
import { FiDownload, FiCopy, FiFileText } from "react-icons/fi"
import { useState } from "react"
import { useMutation } from "@tanstack/react-query"

import { 
  DocumentTemplate,
  CaseTemplatesService,
  getDocumentTypeInfo,
  getCaseTypeInfo 
} from "@/client/case-templates"
import useCustomToast from "@/hooks/useCustomToast"

interface GenerateDocumentModalProps {
  isOpen: boolean
  onClose: () => void
  template: DocumentTemplate
  onGenerate?: (templateId: string, content: string) => void
}

const GenerateDocumentModal = ({ 
  isO<PERSON>, 
  onClose, 
  template,
  onGenerate 
}: GenerateDocumentModalProps) => {
  const [placeholderValues, setPlaceholderValues] = useState<Record<string, string>>({})
  const [generatedContent, setGeneratedContent] = useState("")
  const [activeTab, setActiveTab] = useState(0)

  const toast = useCustomToast()
  const documentTypeInfo = getDocumentTypeInfo(template.document_type)
  const caseTypeInfo = template.case_type ? getCaseTypeInfo(template.case_type) : null

  // Generate document mutation
  const generateDocumentMutation = useMutation({
    mutationFn: (placeholderData: Record<string, any>) =>
      CaseTemplatesService.generateDocumentFromTemplate(template.id, placeholderData),
    onSuccess: (result) => {
      setGeneratedContent(result.generated_content)
      setActiveTab(1) // Switch to preview tab
      
      toast({
        title: "Document generated",
        description: "Document has been generated successfully",
        status: "success",
        duration: 3000,
      })
      
      onGenerate?.(template.id, result.generated_content)
    },
    onError: (error: any) => {
      toast({
        title: "Error generating document",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleClose = () => {
    setPlaceholderValues({})
    setGeneratedContent("")
    setActiveTab(0)
    onClose()
  }

  const handleGenerate = () => {
    // Check if all placeholders are filled
    const missingPlaceholders = template.placeholders.filter(
      placeholder => !placeholderValues[placeholder]?.trim()
    )

    if (missingPlaceholders.length > 0) {
      toast({
        title: "Missing placeholder values",
        description: `Please fill in: ${missingPlaceholders.join(', ')}`,
        status: "warning",
        duration: 5000,
      })
      return
    }

    generateDocumentMutation.mutate(placeholderValues)
  }

  const handleCopyContent = () => {
    navigator.clipboard.writeText(generatedContent)
    toast({
      title: "Content copied",
      description: "Document content has been copied to clipboard",
      status: "success",
      duration: 3000,
    })
  }

  const handleDownload = () => {
    const blob = new Blob([generatedContent], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `${template.name.toLowerCase().replace(/\s+/g, '-')}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Document downloaded",
      description: "Document has been downloaded successfully",
      status: "success",
      duration: 3000,
    })
  }

  const updatePlaceholder = (placeholder: string, value: string) => {
    setPlaceholderValues({
      ...placeholderValues,
      [placeholder]: value
    })
  }

  const getPlaceholderLabel = (placeholder: string): string => {
    return placeholder
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
  }

  const getPlaceholderPlaceholder = (placeholder: string): string => {
    const examples: Record<string, string> = {
      client_name: "John Doe",
      client_address: "123 Main St, City, State 12345",
      client_phone: "(*************",
      client_email: "<EMAIL>",
      lawyer_name: "Jane Smith",
      lawyer_firm: "Smith & Associates",
      lawyer_address: "456 Legal Ave, City, State 12345",
      lawyer_phone: "(*************",
      case_number: "2024-CV-001234",
      case_title: "Doe v. Smith",
      case_type: "Contract Dispute",
      date: new Date().toLocaleDateString(),
      court_name: "Superior Court of [County]",
      judge_name: "The Honorable [Judge Name]",
      opposing_party: "ABC Corporation",
      amount: "$10,000.00"
    }
    
    return examples[placeholder] || `Enter ${getPlaceholderLabel(placeholder).toLowerCase()}`
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="4xl">
      <ModalOverlay />
      <ModalContent maxH="90vh" overflowY="auto">
        <ModalHeader>Generate Document</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Template Info */}
            <Box p={4} bg="green.50" borderRadius="md">
              <VStack spacing={3} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="lg" fontWeight="bold">
                    {template.name}
                  </Text>
                  <HStack spacing={2}>
                    <Badge colorScheme={documentTypeInfo.color} variant="subtle">
                      {documentTypeInfo.icon} {documentTypeInfo.label}
                    </Badge>
                    {caseTypeInfo && (
                      <Badge colorScheme={caseTypeInfo.color} variant="outline">
                        {caseTypeInfo.icon} {caseTypeInfo.label}
                      </Badge>
                    )}
                  </HStack>
                </HStack>
                
                {template.description && (
                  <Text fontSize="sm" color="gray.600">
                    {template.description}
                  </Text>
                )}
              </VStack>
            </Box>

            <Tabs index={activeTab} onChange={setActiveTab}>
              <TabList>
                <Tab>
                  <HStack spacing={2}>
                    <FiFileText />
                    <Text>Fill Placeholders</Text>
                    <Badge size="sm" colorScheme="blue">
                      {template.placeholders.length}
                    </Badge>
                  </HStack>
                </Tab>
                <Tab isDisabled={!generatedContent}>
                  <HStack spacing={2}>
                    <FiFileText />
                    <Text>Preview Document</Text>
                  </HStack>
                </Tab>
              </TabList>

              <TabPanels>
                {/* Placeholders Tab */}
                <TabPanel px={0}>
                  <VStack spacing={4} align="stretch">
                    {template.placeholders.length > 0 ? (
                      <>
                        <Text fontSize="md" fontWeight="medium">
                          Fill in the placeholder values:
                        </Text>
                        
                        <VStack spacing={4} align="stretch">
                          {template.placeholders.map((placeholder) => (
                            <FormControl key={placeholder}>
                              <FormLabel fontSize="sm">
                                {getPlaceholderLabel(placeholder)}
                                <Badge ml={2} variant="outline" fontSize="xs">
                                  {`{{${placeholder}}}`}
                                </Badge>
                              </FormLabel>
                              <Input
                                value={placeholderValues[placeholder] || ""}
                                onChange={(e) => updatePlaceholder(placeholder, e.target.value)}
                                placeholder={getPlaceholderPlaceholder(placeholder)}
                                size="sm"
                              />
                            </FormControl>
                          ))}
                        </VStack>
                      </>
                    ) : (
                      <Alert status="info">
                        <AlertIcon />
                        <Text>This template has no placeholders. You can generate the document directly.</Text>
                      </Alert>
                    )}
                  </VStack>
                </TabPanel>

                {/* Preview Tab */}
                <TabPanel px={0}>
                  <VStack spacing={4} align="stretch">
                    <HStack justify="space-between">
                      <Text fontSize="md" fontWeight="medium">
                        Generated Document
                      </Text>
                      <HStack spacing={2}>
                        <Button
                          leftIcon={<FiCopy />}
                          size="sm"
                          variant="outline"
                          onClick={handleCopyContent}
                          isDisabled={!generatedContent}
                        >
                          Copy
                        </Button>
                        <Button
                          leftIcon={<FiDownload />}
                          size="sm"
                          variant="outline"
                          onClick={handleDownload}
                          isDisabled={!generatedContent}
                        >
                          Download
                        </Button>
                      </HStack>
                    </HStack>
                    
                    {generatedContent ? (
                      <Box
                        p={4}
                        bg="gray.50"
                        borderRadius="md"
                        border="1px"
                        borderColor="gray.200"
                        maxH="400px"
                        overflowY="auto"
                      >
                        <Text
                          fontSize="sm"
                          fontFamily="monospace"
                          whiteSpace="pre-wrap"
                          lineHeight="1.6"
                        >
                          {generatedContent}
                        </Text>
                      </Box>
                    ) : (
                      <Box textAlign="center" py={8} color="gray.500">
                        <Text>No document generated yet</Text>
                        <Text fontSize="sm" mt={1}>
                          Fill in the placeholders and click "Generate Document"
                        </Text>
                      </Box>
                    )}
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            Close
          </Button>
          {activeTab === 0 && (
            <Button
              colorScheme="green"
              onClick={handleGenerate}
              isLoading={generateDocumentMutation.isPending}
              leftIcon={<FiFileText />}
            >
              Generate Document
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default GenerateDocumentModal
