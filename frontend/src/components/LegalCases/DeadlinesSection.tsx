import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>lex,
  <PERSON>r,
  <PERSON>ge,
  IconButton,
  Tooltip,
} from "@chakra-ui/react"
import {
  <PERSON><PERSON>lock,
  FiPlus,
  FiRefreshCw,
} from "react-icons/fi"

interface DeadlinesSectionProps {
  caseId: string
  onProgressUpdate?: () => void
}

const DeadlinesSection = ({ 
  caseId, 
  onProgressUpdate 
}: DeadlinesSectionProps) => {
  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={2}>
            <FiClock />
            <Text fontSize="lg" fontWeight="bold">
              Deadlines
            </Text>
            <Badge variant="subtle" colorScheme="orange">
              0 upcoming
            </Badge>
          </HStack>
          <Text fontSize="sm" color="gray.600">
            Manage important deadlines and due dates for this case
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Refresh deadlines">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => {}}
            />
          </Tooltip>
          <Button
            leftIcon={<FiPlus />}
            colorScheme="orange"
            size="sm"
            onClick={() => {}}
          >
            Add Deadline
          </Button>
        </HStack>
      </Flex>

      {/* Content */}
      <Box textAlign="center" py={8}>
        <Text fontSize="lg" color="gray.500" mb={4}>
          No deadlines found
        </Text>
        <Text fontSize="sm" color="gray.600" mb={6}>
          Create deadlines to track important due dates and court deadlines.
        </Text>
        <Button
          leftIcon={<FiPlus />}
          colorScheme="orange"
          onClick={() => {}}
        >
          Create First Deadline
        </Button>
      </Box>
    </Box>
  )
}

export default DeadlinesSection
