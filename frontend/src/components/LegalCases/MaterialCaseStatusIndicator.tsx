/**
=========================================================
* EquiNova Material Case Status Indicator
=========================================================

* Material-UI implementation of case status indicators
* Replaces Chakra UI status badges with Material Design

=========================================================
*/

import React from 'react';
import {
  Chip,
  Box,
  LinearProgress,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import {
  RadioButtonUnchecked as OpenIcon,
  PlayArrow as InProgressIcon,
  Visibility as ReviewIcon,
  CheckCircle as ClosedIcon,
  Archive as ArchivedIcon,
} from '@mui/icons-material';

interface MaterialCaseStatusIndicatorProps {
  status: string;
  size?: 'small' | 'medium' | 'large';
  showProgress?: boolean;
  showIcon?: boolean;
  animated?: boolean;
  layout?: 'horizontal' | 'vertical';
}

const MaterialCaseStatusIndicator: React.FC<MaterialCaseStatusIndicatorProps> = ({
  status,
  size = 'medium',
  showProgress = false,
  showIcon = true,
  animated = false,
  layout = 'horizontal',
}) => {
  const theme = useTheme();

  const getStatusConfig = (status: string) => {
    const statusStr = String(status || '').toLowerCase();
    
    switch (statusStr) {
      case 'open':
        return {
          color: 'info' as const,
          icon: OpenIcon,
          label: 'Open',
          description: 'Case is open and ready for work',
          progress: 10,
          bgColor: theme.palette.info.light,
          textColor: theme.palette.info.contrastText,
        };
      case 'in_progress':
        return {
          color: 'warning' as const,
          icon: InProgressIcon,
          label: 'In Progress',
          description: 'Case is actively being worked on',
          progress: 50,
          bgColor: theme.palette.warning.light,
          textColor: theme.palette.warning.contrastText,
        };
      case 'under_review':
        return {
          color: 'secondary' as const,
          icon: ReviewIcon,
          label: 'Under Review',
          description: 'Case is being reviewed',
          progress: 75,
          bgColor: theme.palette.secondary.light,
          textColor: theme.palette.secondary.contrastText,
        };
      case 'closed':
        return {
          color: 'success' as const,
          icon: ClosedIcon,
          label: 'Closed',
          description: 'Case has been completed',
          progress: 100,
          bgColor: theme.palette.success.light,
          textColor: theme.palette.success.contrastText,
        };
      case 'archived':
        return {
          color: 'default' as const,
          icon: ArchivedIcon,
          label: 'Archived',
          description: 'Case has been archived',
          progress: 100,
          bgColor: theme.palette.grey[300],
          textColor: theme.palette.text.primary,
        };
      default:
        return {
          color: 'default' as const,
          icon: OpenIcon,
          label: statusStr ? statusStr.charAt(0).toUpperCase() + statusStr.slice(1) : 'Unknown',
          description: 'Unknown status',
          progress: 0,
          bgColor: theme.palette.grey[300],
          textColor: theme.palette.text.primary,
        };
    }
  };

  const statusConfig = getStatusConfig(status);
  const IconComponent = statusConfig.icon;

  const chipSize = size === 'small' ? 'small' : 'medium';
  const iconSize = size === 'small' ? 16 : size === 'medium' ? 20 : 24;

  if (layout === 'vertical') {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 1,
          minWidth: 80,
        }}
      >
        <Tooltip title={statusConfig.description} arrow>
          <Chip
            icon={showIcon ? <IconComponent sx={{ fontSize: iconSize }} /> : undefined}
            label={statusConfig.label}
            color={statusConfig.color}
            size={chipSize}
            sx={{
              fontWeight: 500,
              transition: animated ? 'all 0.2s ease-in-out' : 'none',
              '&:hover': animated ? {
                transform: 'scale(1.05)',
                boxShadow: theme.shadows[4],
              } : {},
            }}
          />
        </Tooltip>
        
        {showProgress && (
          <Box sx={{ width: '100%', maxWidth: 60 }}>
            <LinearProgress
              variant="determinate"
              value={statusConfig.progress}
              color={statusConfig.color}
              sx={{
                height: 4,
                borderRadius: 2,
                backgroundColor: theme.palette.grey[200],
              }}
            />
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: '0.7rem', textAlign: 'center', display: 'block', mt: 0.5 }}
            >
              {statusConfig.progress}%
            </Typography>
          </Box>
        )}
      </Box>
    );
  }

  // Horizontal layout (default)
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: showProgress ? 1 : 0,
      }}
    >
      <Tooltip title={statusConfig.description} arrow>
        <Chip
          icon={showIcon ? <IconComponent sx={{ fontSize: iconSize }} /> : undefined}
          label={statusConfig.label}
          color={statusConfig.color}
          size={chipSize}
          sx={{
            fontWeight: 500,
            transition: animated ? 'all 0.2s ease-in-out' : 'none',
            '&:hover': animated ? {
              transform: 'scale(1.05)',
              boxShadow: theme.shadows[4],
            } : {},
          }}
        />
      </Tooltip>
      
      {showProgress && (
        <Box sx={{ minWidth: 60, ml: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LinearProgress
              variant="determinate"
              value={statusConfig.progress}
              color={statusConfig.color}
              sx={{
                flexGrow: 1,
                height: 4,
                borderRadius: 2,
                backgroundColor: theme.palette.grey[200],
              }}
            />
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: '0.7rem', minWidth: 30 }}
            >
              {statusConfig.progress}%
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default MaterialCaseStatusIndicator;
