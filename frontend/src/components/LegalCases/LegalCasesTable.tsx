import {
  EmptyState,
  <PERSON>lex,
  Table,
  VStack,
  <PERSON><PERSON><PERSON>ck,
  Text,
  Badge,
  <PERSON><PERSON>,
  IconButton,
  <PERSON>lt<PERSON>,
  useBreakpointValue,
  Box,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { <PERSON><PERSON><PERSON>ch, <PERSON>Eye, FiEdit, FiTrash2, FiMoreVertical } from "react-icons/fi"
import { useState, useEffect } from "react"


// Legal cases table component
import PendingLegalCases from "@/components/Pending/PendingLegalCases"
import CaseStatusIndicator from "./CaseStatusIndicator"
import PriorityBadge from "./PriorityBadge"
import { LegalCaseActionsMenu } from "@/components/Common/LegalCaseActionsMenu"
import ViewLegalCase from "./ViewLegalCase"
import BulkStatusUpdateModal from "./BulkStatusUpdateModal"
import EnhancedCaseRowActions from "./EnhancedCaseRowActions"
import EnhancedCaseStatusBadge from "./EnhancedCaseStatusBadge"
import EnhancedPriorityBadge from "./EnhancedPriorityBadge"
// import CaseFilters, { type CaseFilters as CaseFiltersType } from "./CaseFilters"

// Temporary interface for filters
interface CaseFiltersType {
  search?: string
  status?: string
  priority?: string
  case_type?: string
  sort_by?: string
  sort_order?: string
}

import {
  PaginationItems,
  PaginationNextTrigger,
  PaginationPrevTrigger,
  PaginationRoot,
} from "@/components/ui/pagination.tsx"

const PER_PAGE = 5

function getLegalCasesQueryOptions(params: {
  skip?: number
  limit?: number
  search?: string
  case_type?: string
  status?: string
  priority?: string
  sort_by?: string
  sort_order?: string
}) {
  return {
    queryFn: async () => {
      // Use the OpenAPI client with proper authentication
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      const queryParams: Record<string, string> = {}
      if (params.skip !== undefined) queryParams.skip = params.skip.toString()
      if (params.limit !== undefined) queryParams.limit = params.limit.toString()
      if (params.search) queryParams.search = params.search
      if (params.case_type) queryParams.case_type = params.case_type
      if (params.status) queryParams.status = params.status
      if (params.priority) queryParams.priority = params.priority
      if (params.sort_by) queryParams.sort_by = params.sort_by
      if (params.sort_order) queryParams.sort_order = params.sort_order

      return request(OpenAPI, {
        method: 'GET',
        url: '/api/v1/legal-cases/',
        query: queryParams,
      })
    },
    queryKey: ["legal-cases", params],
  }
}

interface LegalCasesTableProps {
  onCaseClick?: (caseId: string) => void
  enableRowClick?: boolean
  showEnhancedActions?: boolean
}

function LegalCasesTable({
  onCaseClick,
  enableRowClick = true,
  showEnhancedActions = true
}: LegalCasesTableProps = {}) {
  const [filters, setFilters] = useState<CaseFiltersType>({
    sort_by: "opening_date",
    sort_order: "desc"
  })
  const [debouncedSearch, setDebouncedSearch] = useState("")
  const [page, setPage] = useState(1)

  // Bulk selection state
  const [selectedCases, setSelectedCases] = useState<Set<string>>(new Set())
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [showBulkModal, setShowBulkModal] = useState(false)

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(filters.search || "")
      setPage(1) // Reset to first page when search changes
    }, 300)

    return () => clearTimeout(timer)
  }, [filters.search])

  // Reset page when any filter changes
  useEffect(() => {
    setPage(1)
  }, [filters.status, filters.priority, filters.case_type, filters.sort_by, filters.sort_order])

  // Bulk selection functions
  const handleSelectCase = (caseId: string, checked: boolean) => {
    const newSelected = new Set(selectedCases)
    if (checked) {
      newSelected.add(caseId)
    } else {
      newSelected.delete(caseId)
    }
    setSelectedCases(newSelected)
    setShowBulkActions(newSelected.size > 0)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allCaseIds = new Set(legalCases.map(c => c.id))
      setSelectedCases(allCaseIds)
      setShowBulkActions(true)
    } else {
      setSelectedCases(new Set())
      setShowBulkActions(false)
    }
  }

  const clearSelection = () => {
    setSelectedCases(new Set())
    setShowBulkActions(false)
  }

  // Handle row click for navigation
  const handleRowClick = (caseId: string, event: React.MouseEvent) => {
    // Don't navigate if clicking on interactive elements
    const target = event.target as HTMLElement
    if (target.closest('button') || target.closest('[role="button"]') || target.closest('input')) {
      return
    }

    if (enableRowClick && onCaseClick) {
      onCaseClick(caseId)
    }
  }

  const { data, isLoading, isPlaceholderData } = useQuery({
    ...getLegalCasesQueryOptions({
      skip: (page - 1) * PER_PAGE,
      limit: PER_PAGE,
      search: debouncedSearch || undefined,
      case_type: filters.case_type || undefined,
      status: filters.status || undefined,
      priority: filters.priority || undefined,
      sort_by: filters.sort_by || "opening_date",
      sort_order: filters.sort_order || "desc",
    }),
    placeholderData: (prevData) => prevData,
    // Add enabled to ensure query runs when search changes
    enabled: true,
  })

  // Work with the current API response format
  const legalCases = (data as any)?.data ?? []
  const count = (data as any)?.count ?? 0

  return (
    <>
      {/* 🚨 DIAGNOSTIC SECTION - Test des event handlers */}
      <div style={{
        padding: '16px',
        backgroundColor: '#fed7d7',
        borderRadius: '8px',
        marginBottom: '16px',
        border: '2px solid #e53e3e'
      }}>
        <h3 style={{ fontWeight: 'bold', fontSize: '18px', marginBottom: '16px', color: '#c53030' }}>
          🚨 DIAGNOSTIC EVENT HANDLERS
        </h3>
        <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
          <button
            onClick={() => {
              console.log('🚨 DIRECT BUTTON CLICK - Fonctionne!')
              alert('Direct button click fonctionne!')
            }}
            style={{
              padding: '8px 16px',
              backgroundColor: '#e53e3e',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: 'medium'
            }}
          >
            Test Direct Click
          </button>
          
          <button
            onClick={() => {
              console.log('🚨 CALLBACK TEST - onCaseClick prop:', typeof onCaseClick)
              if (onCaseClick) {
                console.log('🚨 Calling onCaseClick avec test-id')
                onCaseClick('test-case-id')
              } else {
                console.log('🚨 onCaseClick is undefined!')
              }
            }}
            style={{
              padding: '8px 16px',
              backgroundColor: '#38a169',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: 'medium'
            }}
          >
            Test onCaseClick Callback
          </button>
        </div>
      </div>

      {/* Temporary Simple Filters */}
      <div style={{ padding: '16px', backgroundColor: '#f7fafc', borderRadius: '8px', marginBottom: '16px' }}>
        <h3 style={{ fontWeight: 'bold', fontSize: '18px', marginBottom: '16px' }}>Filter Cases</h3>

        <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
          <div style={{ flex: 1 }}>
            <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Search</label>
            <input
              type="text"
              placeholder="Search by title or client name..."
              value={filters.search || ""}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white'
              }}
            />
          </div>

          <div style={{ flex: 1 }}>
            <label style={{ fontWeight: 'medium', marginBottom: '8px', display: 'block' }}>Status</label>
            <select
              value={filters.status || ""}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white'
              }}
            >
              <option value="">All Statuses</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="under_review">Under Review</option>
              <option value="closed">Closed</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <div style={{
          padding: '12px 16px',
          backgroundColor: '#e6fffa',
          borderRadius: '8px',
          marginBottom: '16px',
          border: '1px solid #38b2ac',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <span style={{ fontWeight: 'medium', color: '#2c7a7b' }}>
            {selectedCases.size} case{selectedCases.size !== 1 ? 's' : ''} selected
          </span>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={() => setShowBulkModal(true)}
              style={{
                padding: '8px 16px',
                backgroundColor: '#38b2ac',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontWeight: 'medium'
              }}
            >
              Update Status
            </button>
            <button
              onClick={clearSelection}
              style={{
                padding: '8px 16px',
                backgroundColor: 'transparent',
                color: '#2c7a7b',
                border: '1px solid #38b2ac',
                borderRadius: '6px',
                cursor: 'pointer',
                fontWeight: 'medium'
              }}
            >
              Clear Selection
            </button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && <PendingLegalCases />}

      {/* Results Table */}
      <Table.Root size={{ base: "sm", md: "md" }}>
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader w="xs">
              <input
                type="checkbox"
                checked={selectedCases.size === legalCases.length && legalCases.length > 0}
                onChange={(e) => handleSelectAll(e.target.checked)}
                style={{ cursor: 'pointer' }}
              />
            </Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Case Title</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Client Name</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Case Type</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Status</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Priority</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Opening Date</Table.ColumnHeader>
            <Table.ColumnHeader w="sm">Actions</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {/* Empty State Row */}
          {legalCases.length === 0 && !isLoading && (
            <Table.Row>
              <Table.Cell colSpan={8} textAlign="center" py={8}>
                <div style={{ padding: '32px', color: '#666' }}>
                  <h3 style={{ marginBottom: '8px', fontWeight: 'bold' }}>No legal cases found</h3>
                  <p>Add a new legal case to get started or adjust your search filters.</p>
                </div>
              </Table.Cell>
            </Table.Row>
          )}
          
          {/* Actual Data Rows */}
          {legalCases?.map((legalCase: any) => (
            <Table.Row
              key={legalCase.id}
              opacity={isPlaceholderData ? 0.5 : 1}
              _hover={{
                bg: "blue.50",
                transform: "scale(1.01)",
                shadow: "md",
                cursor: enableRowClick ? "pointer" : "default"
              }}
              transition="all 0.2s ease"
              onClick={(e) => handleRowClick(legalCase.id, e)}
            >
              <Table.Cell onClick={(e) => e.stopPropagation()}>
                <Tooltip label="Select case for bulk operations">
                  <input
                    type="checkbox"
                    checked={selectedCases.has(legalCase.id)}
                    onChange={(e) => handleSelectCase(legalCase.id, e.target.checked)}
                    style={{ cursor: 'pointer', transform: 'scale(1.2)' }}
                  />
                </Tooltip>
              </Table.Cell>
              <Table.Cell truncate maxW="sm">
                <Tooltip label="Click to view case details">
                  <Text
                    fontWeight="semibold"
                    color="blue.700"
                    _hover={{ color: "blue.900", textDecoration: "underline" }}
                    cursor="pointer"
                    onClick={(e) => {
                      e.stopPropagation()
                      console.log('Navigating to case:', legalCase.id)
                      if (onCaseClick) {
                        onCaseClick(legalCase.id)
                      } else {
                        // Fallback to URL navigation
                        window.history.pushState({}, '', `/legal-cases/${legalCase.id}`)
                        window.location.reload()
                      }
                    }}
                  >
                    {String(legalCase.title || "N/A")}
                  </Text>
                </Tooltip>
              </Table.Cell>
              <Table.Cell truncate maxW="sm">
                <Text fontWeight="medium" color="gray.700">
                  {String(legalCase.client_name || "N/A")}
                </Text>
              </Table.Cell>
              <Table.Cell truncate maxW="sm">
                <Badge
                  colorScheme="purple"
                  textTransform="capitalize"
                  variant="subtle"
                  borderRadius="full"
                  px={3}
                  py={1}
                >
                  {String(legalCase.case_type || "N/A").replace('_', ' ')}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                <EnhancedCaseStatusBadge
                  status={String(legalCase.status || 'open')}
                  size="sm"
                  showIcon={true}
                  showTooltip={true}
                />
              </Table.Cell>
              <Table.Cell>
                <EnhancedPriorityBadge
                  priority={String(legalCase.priority || 'medium')}
                  size="sm"
                  showIcon={true}
                  showTooltip={true}
                  animated={true}
                />
              </Table.Cell>
              <Table.Cell truncate maxW="sm">
                <Text fontSize="sm" color="gray.600">
                  {legalCase.opening_date ?
                    new Date(String(legalCase.opening_date)).toLocaleDateString() :
                    "N/A"
                  }
                </Text>
              </Table.Cell>
              <Table.Cell>
                {showEnhancedActions ? (
                  <EnhancedCaseRowActions
                    legalCase={legalCase}
                    onViewClick={onCaseClick}
                  />
                ) : (
                  <HStack gap={2} onClick={(e) => e.stopPropagation()}>
                    <ViewLegalCase legalCase={legalCase} />
                    <LegalCaseActionsMenu legalCase={legalCase} />
                  </HStack>
                )}
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
      <Flex justifyContent="flex-end" mt={4}>
        <PaginationRoot
          count={count}
          pageSize={PER_PAGE}
          page={page}
          onPageChange={({ page }) => setPage(page)}
        >
          <Flex>
            <PaginationPrevTrigger />
            <PaginationItems />
            <PaginationNextTrigger />
          </Flex>
        </PaginationRoot>
      </Flex>

      {/* Bulk Status Update Modal */}
      <BulkStatusUpdateModal
        isOpen={showBulkModal}
        onClose={() => setShowBulkModal(false)}
        selectedCaseIds={Array.from(selectedCases)}
        onSuccess={() => {
          setSelectedCases(new Set())
          setShowBulkActions(false)
          setShowBulkModal(false)
        }}
      />
    </>
  )
}



export default LegalCasesTable
