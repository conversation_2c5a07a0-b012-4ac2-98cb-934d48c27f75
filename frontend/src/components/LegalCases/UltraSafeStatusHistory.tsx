import {
  <PERSON><PERSON>,
  <PERSON>,
  Card,
  CardBody,
  <PERSON>ing,
  <PERSON><PERSON><PERSON>ck,
  Text,
  VStack,
  Spin<PERSON>,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"

import { LegalCasesService } from "../../client"

interface UltraSafeStatusHistoryProps {
  caseId: string
}

const UltraSafeStatusHistory = ({ caseId }: UltraSafeStatusHistoryProps) => {
  const { data: statusHistory, isLoading, error } = useQuery({
    queryKey: ["case-status-history", caseId],
    queryFn: () => LegalCasesService.readCaseStatusHistory({
      legalCaseId: caseId,
      skip: 0,
      limit: 10,
    }),
    enabled: !!caseId,
  })

  // Ultra-ultra-safe text conversion - handles ANY type
  const ultraSafeText = (value: any): string => {
    try {
      // Handle null/undefined
      if (value === null || value === undefined) {
        return "N/A"
      }
      
      // Handle strings
      if (typeof value === "string") {
        return value || "N/A"
      }
      
      // Handle numbers
      if (typeof value === "number") {
        return isNaN(value) ? "N/A" : String(value)
      }
      
      // Handle booleans
      if (typeof value === "boolean") {
        return value ? "Yes" : "No"
      }
      
      // Handle objects - extract specific properties safely
      if (typeof value === "object") {
        // Don't try to stringify objects, just return N/A
        return "N/A"
      }
      
      // Fallback for any other type
      return String(value) || "N/A"
    } catch (error) {
      console.warn("ultraSafeText conversion error:", error)
      return "N/A"
    }
  }

  // Ultra-safe date formatting - completely bulletproof
  const ultraSafeFormatDate = (dateValue: any): string => {
    try {
      // First, convert to safe string
      let dateString: string

      // Handle null/undefined
      if (dateValue === null || dateValue === undefined) {
        return "Unknown date"
      }

      // Handle Date objects
      if (dateValue instanceof Date) {
        if (isNaN(dateValue.getTime())) {
          return "Invalid date"
        }
        dateString = dateValue.toISOString()
      }
      // Handle strings
      else if (typeof dateValue === "string") {
        dateString = dateValue
      }
      // Handle numbers (timestamps)
      else if (typeof dateValue === "number") {
        dateString = new Date(dateValue).toISOString()
      }
      // Handle objects with date properties
      else if (typeof dateValue === "object") {
        // Try to extract date from object
        if (dateValue.toISOString && typeof dateValue.toISOString === "function") {
          dateString = dateValue.toISOString()
        } else if (dateValue.toString && typeof dateValue.toString === "function") {
          dateString = dateValue.toString()
        } else {
          return "Unknown date"
        }
      }
      // Fallback
      else {
        dateString = String(dateValue)
      }

      // Validate and format the date string
      if (!dateString || dateString === "N/A") {
        return "Unknown date"
      }

      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return "Invalid date"
      }

      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    } catch (error) {
      console.warn("Date formatting error:", error, "Input:", dateValue)
      return "Unknown date"
    }
  }

  // Ultra-safe user name extraction
  const ultraSafeUserName = (userObject: any): string => {
    try {
      if (!userObject || typeof userObject !== "object") {
        return "Unknown User"
      }
      
      // Try to get full_name first
      const fullName = ultraSafeText(userObject.full_name)
      if (fullName && fullName !== "N/A") {
        return fullName
      }
      
      // Fallback to email
      const email = ultraSafeText(userObject.email)
      if (email && email !== "N/A") {
        return email
      }
      
      return "Unknown User"
    } catch (error) {
      console.warn("User name extraction error:", error)
      return "Unknown User"
    }
  }

  if (isLoading) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={2}>Loading status history...</Text>
        </Box>
      </Box>
    )
  }

  if (error) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box p={4} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
          <Text color="red.600">
            Failed to load status history
          </Text>
        </Box>
      </Box>
    )
  }

  if (!statusHistory?.data || statusHistory.data.length === 0) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box p={4} bg="gray.50" borderRadius="md" border="1px solid" borderColor="gray.200">
          <Text color="gray.600" textAlign="center">
            No status changes recorded yet
          </Text>
        </Box>
      </Box>
    )
  }

  return (
    <Box>
      <Heading size="md" mb={4}>Status History</Heading>
      <VStack spacing={3} align="stretch">
        {statusHistory.data.map((historyItem, index) => {
          // Ultra-safe extraction of ALL properties - prevent React Error #130
          const historyId = ultraSafeText(historyItem?.id) || `history-${index}`
          const oldStatus = ultraSafeText(historyItem?.old_status)
          const newStatus = ultraSafeText(historyItem?.new_status) || "UNKNOWN"

          // CRITICAL FIX: Convert date to safe string immediately to prevent React serialization issues
          const changedAtSafe = ultraSafeText(historyItem?.changed_at)

          // CRITICAL FIX: Extract user info safely to prevent object serialization issues
          const changedByUser = ultraSafeUserName(historyItem?.changed_by_user)
          const notes = ultraSafeText(historyItem?.notes)

          // Status color mapping - ultra safe
          const getStatusColor = (status: string): string => {
            const safeStatus = ultraSafeText(status).toLowerCase()
            switch (safeStatus) {
              case "open": return "blue"
              case "in_progress": return "orange"
              case "under_review": return "purple"
              case "closed": return "green"
              case "archived": return "gray"
              default: return "gray"
            }
          }

          return (
            <Card key={historyId} size="sm">
              <CardBody>
                <VStack spacing={2} align="stretch">
                  <HStack justify="space-between" align="center">
                    <HStack spacing={2}>
                      {oldStatus && oldStatus !== "N/A" && (
                        <>
                          <Badge colorScheme={getStatusColor(oldStatus)} variant="outline">
                            {oldStatus}
                          </Badge>
                          <Text fontSize="sm" color="gray.500">→</Text>
                        </>
                      )}
                      <Badge colorScheme={getStatusColor(newStatus)} variant="solid">
                        {newStatus}
                      </Badge>
                    </HStack>
                    <Text fontSize="sm" color="gray.500">
                      {ultraSafeFormatDate(changedAtSafe)}
                    </Text>
                  </HStack>

                  <HStack justify="space-between" align="center">
                    <Text fontSize="sm" color="gray.600">
                      Changed by: {changedByUser}
                    </Text>
                  </HStack>

                  {notes && notes !== "N/A" && (
                    <Box mt={2} p={2} bg="gray.50" borderRadius="md">
                      <Text fontSize="sm" color="gray.700">
                        <strong>Notes:</strong> {notes}
                      </Text>
                    </Box>
                  )}
                </VStack>
              </CardBody>
            </Card>
          )
        })}
      </VStack>

      {/* Debug info */}
      <Box mt={4} p={2} bg="green.50" borderRadius="md" border="1px solid" borderColor="green.200">
        <Text fontSize="xs" color="green.600" textAlign="center">
          ✅ Ultra Safe Status History - Bulletproof against React Error #130
        </Text>
      </Box>
    </Box>
  )
}

export default UltraSafeStatusHistory
