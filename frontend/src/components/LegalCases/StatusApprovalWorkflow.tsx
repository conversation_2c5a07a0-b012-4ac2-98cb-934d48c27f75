import { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON>,
  Text,
  VStack,
  HStack,
  Badge,
  Textarea,
  Alert,

  AlertTitle,
  AlertDescription,
} from "@chakra-ui/react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { FiCheck, FiX, FiClock, FiInfo } from "react-icons/fi"
import CaseStatusBadge from "./CaseStatusBadge"
import { toaster } from "@/components/ui/toaster"

interface StatusApprovalWorkflowProps {
  caseId: string
  caseTitle: string
  currentStatus: string
  requestedStatus: string
  requestedBy: string
  requestedAt: string
  requestNotes?: string
  onApprove?: () => void
  onReject?: () => void
  onCancel?: () => void
  userRole: string
}

const StatusApprovalWorkflow = ({
  caseId,
  caseTitle,
  currentStatus,
  requestedStatus,
  requestedBy,
  requestedAt,
  requestNotes,
  onApprove,
  onReject,
  onCancel,
  userRole
}: StatusApprovalWorkflowProps) => {
  
  const [approvalNotes, setApprovalNotes] = useState("")
  const [rejectionNotes, setRejectionNotes] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  
  const queryClient = useQueryClient()

  // Check if user can approve status changes
  const canApprove = userRole === "admin" || userRole === "lawyer"
  
  // Determine if this status change requires approval
  const requiresApproval = (from: string, to: string) => {
    const criticalTransitions = [
      { from: "in_progress", to: "closed" },
      { from: "under_review", to: "closed" },
      { from: "closed", to: "archived" },
      { from: "any", to: "archived" }
    ]
    
    return criticalTransitions.some(transition => 
      (transition.from === from || transition.from === "any") && transition.to === to
    )
  }

  const needsApproval = requiresApproval(currentStatus, requestedStatus)

  // Mock API calls - in real app these would be actual API endpoints
  const approveMutation = useMutation({
    mutationFn: async (data: { notes: string }) => {
      setIsProcessing(true)
      
      // Simulate API call to approve status change
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      return await request(OpenAPI, {
        method: 'POST',
        url: `/api/v1/legal-cases/${caseId}/status-approval`,
        body: {
          action: "approve",
          notes: data.notes,
          new_status: requestedStatus
        }
      })
    },
    onSuccess: () => {
      toaster.create({
        title: "Status Change Approved",
        description: `Case "${caseTitle}" status has been updated to ${requestedStatus}`,
        type: "success",
        duration: 3000,
      })
      
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
      queryClient.invalidateQueries({ queryKey: ["legal-case", caseId] })
      onApprove?.()
    },
    onError: (error) => {
      toaster.create({
        title: "Approval Failed",
        description: `Failed to approve status change: ${error.message}`,
        type: "error",
        duration: 5000,
      })
    },
    onSettled: () => {
      setIsProcessing(false)
    }
  })

  const rejectMutation = useMutation({
    mutationFn: async (data: { notes: string }) => {
      setIsProcessing(true)
      
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      return await request(OpenAPI, {
        method: 'POST',
        url: `/api/v1/legal-cases/${caseId}/status-approval`,
        body: {
          action: "reject",
          notes: data.notes
        }
      })
    },
    onSuccess: () => {
      toaster.create({
        title: "Status Change Rejected",
        description: `Status change request for "${caseTitle}" has been rejected`,
        type: "info",
        duration: 3000,
      })
      
      onReject?.()
    },
    onError: (error) => {
      toaster.create({
        title: "Rejection Failed",
        description: `Failed to reject status change: ${error.message}`,
        type: "error",
        duration: 5000,
      })
    },
    onSettled: () => {
      setIsProcessing(false)
    }
  })

  const handleApprove = () => {
    approveMutation.mutate({ notes: approvalNotes })
  }

  const handleReject = () => {
    rejectMutation.mutate({ notes: rejectionNotes })
  }

  if (!needsApproval) {
    return (
      <Alert status="info" borderRadius="md">
        <Box as={FiInfo} color="blue.500" mr={3} mt={1} />
        <Box>
          <AlertTitle>No Approval Required</AlertTitle>
          <AlertDescription>
            This status change can be applied directly without approval.
          </AlertDescription>
        </Box>
      </Alert>
    )
  }

  if (!canApprove) {
    return (
      <Alert status="warning" borderRadius="md">
        <Box as={FiInfo} color="blue.500" mr={3} mt={1} />
        <Box>
          <AlertTitle>Pending Approval</AlertTitle>
          <AlertDescription>
            This status change requires approval from a lawyer or administrator.
          </AlertDescription>
        </Box>
      </Alert>
    )
  }

  return (
    <Box border="1px solid" borderColor="orange.200" borderRadius="md" p={4} bg="orange.50">
      <VStack spacing={4} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <HStack spacing={2}>
            <FiClock color="orange" />
            <Text fontWeight="bold" color="orange.800">
              Status Change Approval Required
            </Text>
          </HStack>
          <Badge colorScheme="orange" variant="solid">
            Pending
          </Badge>
        </HStack>

        {/* Request Details */}
        <Box bg="white" p={3} borderRadius="md" border="1px solid" borderColor="gray.200">
          <VStack spacing={2} align="stretch">
            <Text fontSize="sm" color="gray.600">
              <strong>Case:</strong> {caseTitle}
            </Text>
            <Text fontSize="sm" color="gray.600">
              <strong>Requested by:</strong> {requestedBy}
            </Text>
            <Text fontSize="sm" color="gray.600">
              <strong>Requested at:</strong> {new Date(requestedAt).toLocaleString()}
            </Text>
            
            <HStack spacing={3} align="center">
              <Text fontSize="sm" color="gray.600">
                <strong>Status Change:</strong>
              </Text>
              <CaseStatusBadge status={currentStatus} size="sm" />
              <Text fontSize="sm" color="gray.500">→</Text>
              <CaseStatusBadge status={requestedStatus} size="sm" />
            </HStack>

            {requestNotes && (
              <Box>
                <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={1}>
                  Request Notes:
                </Text>
                <Text fontSize="sm" color="gray.600" fontStyle="italic">
                  "{requestNotes}"
                </Text>
              </Box>
            )}
          </VStack>
        </Box>

        {/* Approval Actions */}
        <VStack spacing={3} align="stretch">
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              Approval Notes (optional):
            </Text>
            <Textarea
              value={approvalNotes}
              onChange={(e) => setApprovalNotes(e.target.value)}
              placeholder="Add notes about this approval..."
              size="sm"
              rows={2}
            />
          </Box>

          <HStack spacing={2}>
            <Button
              colorScheme="green"
              size="sm"
              leftIcon={<FiCheck />}
              onClick={handleApprove}
              isLoading={isProcessing}
              loadingText="Approving..."
            >
              Approve
            </Button>
            
            <Button
              colorScheme="red"
              variant="outline"
              size="sm"
              leftIcon={<FiX />}
              onClick={handleReject}
              isLoading={isProcessing}
              loadingText="Rejecting..."
            >
              Reject
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              disabled={isProcessing}
            >
              Cancel
            </Button>
          </HStack>
        </VStack>
      </VStack>
    </Box>
  )
}

export default StatusApprovalWorkflow
