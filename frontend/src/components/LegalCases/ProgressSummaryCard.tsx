import {
  Box,
  VStack,
  HStack,
  Text,
  Icon,
} from "@chakra-ui/react"
import { IconType } from "react-icons"

interface ProgressSummaryCardProps {
  title: string
  value: string
  subtitle?: string
  colorScheme?: string
  icon?: IconType
  children?: React.ReactNode
  isLoading?: boolean
}

const ProgressSummaryCard = ({ 
  title, 
  value, 
  subtitle, 
  colorScheme = "blue",
  icon,
  children,
  isLoading = false 
}: ProgressSummaryCardProps) => {
  const cardBg = "white"
  const borderColor = "gray.200"
  const titleColor = "gray.600"
  const valueColor = "gray.900"
  const subtitleColor = "gray.500"

  return (
    <Box
      bg={cardBg}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="lg"
      p={6}
      transition="all 0.2s"
      _hover={{ 
        shadow: "md", 
        transform: "translateY(-2px)",
        borderColor: `${colorScheme}.200`
      }}
    >
      <VStack align="stretch" spacing={3}>
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Text
            fontSize="sm"
            fontWeight="medium"
            color={titleColor}
            textTransform="uppercase"
            letterSpacing="wide"
          >
            {title}
          </Text>
          {icon && (
            <Icon
              as={icon}
              boxSize={5}
              color={`${colorScheme}.500`}
            />
          )}
        </HStack>

        {/* Value */}
        <Text
          fontSize="3xl"
          fontWeight="bold"
          color={valueColor}
          lineHeight="1"
        >
          {isLoading ? "..." : value}
        </Text>

        {/* Subtitle */}
        {subtitle && (
          <Text
            fontSize="sm"
            color={subtitleColor}
            noOfLines={2}
          >
            {subtitle}
          </Text>
        )}

        {/* Additional Content */}
        {children}
      </VStack>
    </Box>
  )
}

export default ProgressSummaryCard
