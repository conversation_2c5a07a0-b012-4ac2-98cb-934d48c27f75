import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useStatusChangeToast } from "./StatusChangeToast"

interface BulkStatusUpdateModalProps {
  isOpen: boolean
  onClose: () => void
  selectedCaseIds: string[]
  onSuccess: () => void
}

const STATUS_OPTIONS = [
  { value: "open", label: "Open" },
  { value: "in_progress", label: "In Progress" },
  { value: "under_review", label: "Under Review" },
  { value: "closed", label: "Closed" },
  { value: "archived", label: "Archived" }
]

export default function BulkStatusUpdateModal({
  isOpen,
  onClose,
  selectedCaseIds,
  onSuccess
}: BulkStatusUpdateModalProps) {
  const [newStatus, setNewStatus] = useState("")
  const [notes, setNotes] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const queryClient = useQueryClient()
  const { showBulkStatusChangeToast } = useStatusChangeToast()

  const bulkUpdateMutation = useMutation({
    mutationFn: async (data: { case_ids: string[], new_status: string, notes?: string }) => {
      console.log("🚀 Starting bulk update with data:", data)
      console.log("🔍 Case IDs type check:", data.case_ids.map(id => ({ id, type: typeof id, length: id.length })))
      console.log("🔍 New status:", data.new_status, typeof data.new_status)
      console.log("🔍 Notes:", data.notes, typeof data.notes)

      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      try {
        const result = await request(OpenAPI, {
          method: 'PATCH',
          url: '/api/v1/legal-cases/bulk/status',
          body: data,
        })
        return result
      } catch (error) {
        console.error("Bulk update failed:", error)
        throw error
      }
    },
    onSuccess: (result: any) => {
      // Invalidate and refetch legal cases
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })

      // Show toast notifications
      if (result.success_count > 0 || result.failed_count > 0) {
        showBulkStatusChangeToast(
          "previous", // We don't have the old status here, could be improved
          newStatus,
          result.success_count,
          result.failed_count
        )
      }

      // Close modal and clear selection
      onSuccess()
      onClose()

      // Reset form
      setNewStatus("")
      setNotes("")
    },
    onError: (error) => {
      console.error("Bulk update failed:", error)
      alert(`Bulk update failed: ${error.message || 'Unknown error'}`)
    },
    onSettled: () => {
      setIsSubmitting(false)
    }
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newStatus) {
      alert("Please select a status")
      return
    }

    if (selectedCaseIds.length === 0) {
      alert("No cases selected")
      return
    }

    setIsSubmitting(true)

    const requestData = {
      case_ids: selectedCaseIds,
      new_status: newStatus,
      ...(notes && notes.trim() ? { notes: notes.trim() } : {})
    }

    bulkUpdateMutation.mutate(requestData)
  }

  if (!isOpen) return null

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '24px',
        minWidth: '400px',
        maxWidth: '500px',
        maxHeight: '80vh',
        overflow: 'auto'
      }}>
        <h2 style={{
          fontSize: '20px',
          fontWeight: 'bold',
          marginBottom: '16px',
          color: '#2d3748'
        }}>
          Update Status for {selectedCaseIds.length} Case{selectedCaseIds.length !== 1 ? 's' : ''}
        </h2>

        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '16px' }}>
            <label style={{
              display: 'block',
              fontWeight: 'medium',
              marginBottom: '8px',
              color: '#4a5568'
            }}>
              New Status *
            </label>
            <select
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value)}
              required
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white',
                fontSize: '14px'
              }}
            >
              <option value="">Select a status...</option>
              {STATUS_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div style={{ marginBottom: '24px' }}>
            <label style={{
              display: 'block',
              fontWeight: 'medium',
              marginBottom: '8px',
              color: '#4a5568'
            }}>
              Notes (Optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add notes about this status change..."
              rows={3}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                backgroundColor: 'white',
                fontSize: '14px',
                resize: 'vertical'
              }}
            />
          </div>

          <div style={{
            display: 'flex',
            gap: '12px',
            justifyContent: 'flex-end'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              style={{
                padding: '8px 16px',
                backgroundColor: 'transparent',
                color: '#4a5568',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                fontWeight: 'medium',
                opacity: isSubmitting ? 0.6 : 1
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !newStatus}
              style={{
                padding: '8px 16px',
                backgroundColor: isSubmitting || !newStatus ? '#a0aec0' : '#38b2ac',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: isSubmitting || !newStatus ? 'not-allowed' : 'pointer',
                fontWeight: 'medium'
              }}
            >
              {isSubmitting ? 'Updating...' : 'Update Status'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
