import {
  Badge,
  HStack,
  Text,
  Tooltip,
  Box,
  useBreakpointValue,
} from "@chakra-ui/react"
import { <PERSON><PERSON>lock, FiPlay, FiEye, FiCheck, FiArchive } from "react-icons/fi"

interface EnhancedCaseStatusBadgeProps {
  status: string
  size?: "sm" | "md" | "lg"
  showIcon?: boolean
  showTooltip?: boolean
  clickable?: boolean
  onClick?: () => void
}

const EnhancedCaseStatusBadge = ({
  status,
  size = "md",
  showIcon = true,
  showTooltip = true,
  clickable = false,
  onClick
}: EnhancedCaseStatusBadgeProps) => {
  const isMobile = useBreakpointValue({ base: true, md: false })
  
  // Status configuration
  const getStatusConfig = (status: string) => {
    const normalizedStatus = String(status || '').toLowerCase()
    
    switch (normalizedStatus) {
      case 'open':
        return {
          colorScheme: 'blue',
          icon: FiClock,
          label: 'Open',
          description: 'Case is open and awaiting action'
        }
      case 'in_progress':
        return {
          colorScheme: 'orange',
          icon: FiPlay,
          label: 'In Progress',
          description: 'Case is actively being worked on'
        }
      case 'under_review':
        return {
          colorScheme: 'purple',
          icon: FiEye,
          label: 'Under Review',
          description: 'Case is under review or awaiting approval'
        }
      case 'closed':
        return {
          colorScheme: 'green',
          icon: FiCheck,
          label: 'Closed',
          description: 'Case has been completed and closed'
        }
      case 'archived':
        return {
          colorScheme: 'gray',
          icon: FiArchive,
          label: 'Archived',
          description: 'Case has been archived'
        }
      default:
        return {
          colorScheme: 'gray',
          icon: FiClock,
          label: 'Unknown',
          description: 'Status unknown'
        }
    }
  }

  const config = getStatusConfig(status)
  const IconComponent = config.icon

  const badgeContent = (
    <Badge
      colorScheme={config.colorScheme}
      variant="solid"
      size={size}
      cursor={clickable ? "pointer" : "default"}
      _hover={clickable ? { 
        transform: "scale(1.05)", 
        shadow: "md" 
      } : {}}
      transition="all 0.2s"
      onClick={clickable ? onClick : undefined}
      borderRadius="full"
      px={3}
      py={1}
    >
      <HStack spacing={1} align="center">
        {showIcon && !isMobile && (
          <Box as={IconComponent} boxSize="12px" />
        )}
        <Text fontSize={size === "sm" ? "xs" : "sm"} fontWeight="medium">
          {config.label}
        </Text>
      </HStack>
    </Badge>
  )

  if (showTooltip) {
    return (
      <Tooltip 
        label={config.description} 
        placement="top"
        hasArrow
        bg="gray.700"
        color="white"
        fontSize="sm"
      >
        {badgeContent}
      </Tooltip>
    )
  }

  return badgeContent
}

export default EnhancedCaseStatusBadge
