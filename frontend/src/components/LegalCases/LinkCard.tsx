import {
  Box,
  <PERSON><PERSON>tack,
  VStack,
  Text,
  Badge,
  IconButton,
  Tooltip,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Link,
} from "@chakra-ui/react"
import {
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  FiExternalLink,
  FiUser,
  <PERSON><PERSON>lock,
} from "react-icons/fi"
import { Link as RouterLink } from "react-router-dom"

import { 
  DocumentCaseLink, 
  getLinkTypeInfo,
  formatLinkDate 
} from "@/client/document-case-links"
import useAuth from "@/hooks/useAuth"

interface LinkCardProps {
  link: DocumentCaseLink
  onEdit: () => void
  onDelete: () => void
  isDeleting?: boolean
}

const LinkCard = ({ link, onEdit, onDelete, isDeleting = false }: LinkCardProps) => {
  const { user } = useAuth()
  
  const cardBg = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const hoverBg = useColorModeValue("gray.50", "gray.700")

  const linkTypeInfo = getLinkTypeInfo(link.link_type)
  const canEdit = user?.id === link.linked_by || user?.role === 'admin'

  return (
    <Box
      bg={cardBg}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="lg"
      p={4}
      transition="all 0.2s"
      _hover={{ 
        shadow: "md", 
        bg: hoverBg,
        transform: "translateY(-1px)" 
      }}
    >
      <HStack spacing={4} align="start">
        {/* Link Type Icon */}
        <Box
          fontSize="2xl"
          color={`${linkTypeInfo.color}.500`}
          minW="40px"
          textAlign="center"
        >
          {linkTypeInfo.icon}
        </Box>

        {/* Link Info */}
        <VStack align="start" spacing={2} flex={1}>
          {/* Case Info */}
          <HStack spacing={2} align="center" wrap="wrap">
            <Link
              as={RouterLink}
              to={`/legal-cases/${link.case_id}`}
              fontWeight="medium"
              fontSize="md"
              color="blue.600"
              _hover={{ textDecoration: "underline" }}
              noOfLines={1}
            >
              {link.case?.title || "Unknown Case"}
            </Link>
            <IconButton
              as={RouterLink}
              to={`/legal-cases/${link.case_id}`}
              variant="ghost"
              size="xs"
              icon={<FiExternalLink />}
              aria-label="Open case"
            />
          </HStack>

          {/* Case Details */}
          {link.case && (
            <HStack spacing={4} fontSize="sm" color="gray.600">
              <Text>#{link.case.case_number}</Text>
              <Badge
                colorScheme={
                  link.case.status === 'active' ? 'green' :
                  link.case.status === 'closed' ? 'gray' :
                  link.case.status === 'pending' ? 'yellow' : 'blue'
                }
                variant="subtle"
                fontSize="xs"
              >
                {link.case.status}
              </Badge>
              <Badge
                colorScheme={
                  link.case.priority === 'high' ? 'red' :
                  link.case.priority === 'medium' ? 'orange' : 'green'
                }
                variant="outline"
                fontSize="xs"
              >
                {link.case.priority} priority
              </Badge>
            </HStack>
          )}

          {/* Link Type and Notes */}
          <VStack align="start" spacing={1}>
            <HStack spacing={2}>
              <Badge
                colorScheme={linkTypeInfo.color}
                variant="subtle"
                fontSize="xs"
              >
                {linkTypeInfo.label}
              </Badge>
              <Text fontSize="xs" color="gray.500">
                {linkTypeInfo.description}
              </Text>
            </HStack>
            
            {link.notes && (
              <Text fontSize="sm" color="gray.700" fontStyle="italic">
                "{link.notes}"
              </Text>
            )}
          </VStack>

          {/* Link Metadata */}
          <HStack spacing={4} fontSize="xs" color="gray.500">
            {link.linked_by_user && (
              <HStack spacing={1}>
                <FiUser size={12} />
                <Text>
                  Linked by {link.linked_by_user.full_name || link.linked_by_user.email}
                </Text>
              </HStack>
            )}
            <HStack spacing={1}>
              <FiClock size={12} />
              <Text>{formatLinkDate(link.linked_at)}</Text>
            </HStack>
          </HStack>
        </VStack>

        {/* Actions Menu */}
        {canEdit && (
          <Menu>
            <MenuButton
              as={IconButton}
              variant="ghost"
              size="sm"
              icon={<FiMoreVertical />}
              isLoading={isDeleting}
            />
            <MenuList>
              <MenuItem icon={<FiEdit />} onClick={onEdit}>
                Edit Link
              </MenuItem>
              <MenuItem
                icon={<FiTrash2 />}
                onClick={onDelete}
                color="red.500"
              >
                Remove Link
              </MenuItem>
            </MenuList>
          </Menu>
        )}
      </HStack>
    </Box>
  )
}

export default LinkCard
