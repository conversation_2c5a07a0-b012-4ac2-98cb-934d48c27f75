import { useState, useEffect } from "react"
import { Box, Button, Text, Badge, Select, VStack, HStack } from "@chakra-ui/react"
import type { LegalCasePublic } from "../../client"

interface StatusManagementProps {
  legalCase: LegalCasePublic
  onStatusUpdate?: () => void
}

// Status options matching backend enum
const STATUS_OPTIONS = ["OPEN", "IN_PROGRESS", "UNDER_REVIEW", "CLOSED", "ARCHIVED"]

// Status colors mapping
const STATUS_COLORS: Record<string, string> = {
  OPEN: "blue",
  IN_PROGRESS: "yellow",
  UNDER_REVIEW: "orange",
  CLOSED: "green",
  ARCHIVED: "gray"
}

// Status labels mapping
const STATUS_LABELS: Record<string, string> = {
  OPEN: "Open",
  IN_PROGRESS: "In Progress",
  UNDER_REVIEW: "Under Review",
  CLOSED: "Closed",
  ARCHIVED: "Archived"
}

export default function StatusManagement({ legalCase, onStatusUpdate }: StatusManagementProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState("")
  const [validTransitions, setValidTransitions] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  // Safely extract and convert all values to strings
  const currentStatusRaw = legalCase?.status || "OPEN"
  const currentStatus = String(currentStatusRaw).toUpperCase()
  const caseId = String(legalCase?.id || "")
  const caseTitle = String(legalCase?.title || "No title")

  const statusColor = String(STATUS_COLORS[currentStatus] || "gray")
  const statusLabel = String(STATUS_LABELS[currentStatus] || String(currentStatus))

  // Fetch valid transitions when editing starts
  useEffect(() => {
    if (isEditing) {
      fetchValidTransitions()
    }
  }, [isEditing, caseId])

  const fetchValidTransitions = async () => {
    try {
      setLoading(true)
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      const transitions = await request(OpenAPI, {
        method: 'GET',
        url: `/api/v1/legal-cases/${caseId}/valid-status-transitions`,
      })

      // Convert to uppercase to match our frontend format
      const upperTransitions = transitions.map((t: string) => t.toUpperCase())
      setValidTransitions(upperTransitions)
    } catch (error) {
      console.error('Error fetching valid transitions:', error)
      // Fallback to all options if API fails
      setValidTransitions(STATUS_OPTIONS.filter(status => status !== currentStatus))
    } finally {
      setLoading(false)
    }
  }



  const handleEdit = () => {
    setIsEditing(true)
  }
  const handleCancel = () => {
    setIsEditing(false)
    setSelectedStatus("")
  }

  const handleSave = async () => {
    if (!selectedStatus) return

    try {
      // Import the request function dynamically
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      // Use the dedicated status endpoint with proper format
      await request(OpenAPI, {
        method: 'PATCH',
        url: `/api/v1/legal-cases/${caseId}/status`,
        body: {
          new_status: selectedStatus.toLowerCase(),
          notes: null
        }
      })

      // Success! Reset form and trigger refetch
      setIsEditing(false)
      setSelectedStatus("")

      // Trigger refetch of case data
      if (onStatusUpdate) {
        onStatusUpdate()
      }

    } catch (error) {
      console.error('Error updating status:', error)
      // TODO: Add error notification here
    }
  }

  if (isEditing) {
    return (
      <Box border="1px solid" borderColor="gray.200" p={4} borderRadius="md">
        <Text fontWeight="bold" mb={4}>Change Case Status</Text>

        <Box mb={4}>
          <Text fontSize="sm" color="gray.600" mb={2}>
            Current Status: {statusLabel}
          </Text>
        </Box>

        <Box mb={4}>
          <Text fontWeight="medium" mb={2}>Select New Status:</Text>
          {loading ? (
            <Text fontSize="sm" color="gray.500">Loading valid transitions...</Text>
          ) : (
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            >
              <option value="">-- Select new status --</option>
              {validTransitions.map((status) => (
                <option key={String(status)} value={String(status)}>
                  {String(STATUS_LABELS[String(status)] || String(status))}
                </option>
              ))}
            </select>
          )}
          {validTransitions.length === 0 && !loading && (
            <Text fontSize="sm" color="gray.500" mt={2}>
              No valid status transitions available for your role.
            </Text>
          )}
        </Box>

        <Box>
          <Button variant="ghost" onClick={handleCancel} mr={2}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSave}
            disabled={!selectedStatus}
          >
            Update Status
          </Button>
        </Box>
      </Box>
    )
  }

  return (
    <Box border="1px solid" borderColor="gray.200" p={4} borderRadius="md">
      <Text fontWeight="bold" mb={3}>Case Status</Text>

      <Box mb={3}>
        <Text display="inline" mr={2}>Status:</Text>
        <Text
          display="inline"
          px={2}
          py={1}
          bg={`${statusColor}.100`}
          color={`${statusColor}.800`}
          borderRadius="md"
          fontSize="sm"
          fontWeight="medium"
        >
          {statusLabel}
        </Text>
        <Button size="sm" ml={4} onClick={handleEdit}>
          Change Status
        </Button>
      </Box>

      <Text fontSize="sm" color="gray.600">
        Case: {caseTitle}
      </Text>
    </Box>
  )
}
