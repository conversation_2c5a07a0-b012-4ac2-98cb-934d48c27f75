import {
  Container,
  VStack,
  Heading,
  Text,
  Alert,
  Box,
  Badge,
  SimpleGrid,
  Card,
  CardBody,
  HStack,
  List,
  ListItem,
  ListIcon,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
} from "@chakra-ui/react"
import { FiInfo, FiLink, FiFile, FiSearch, FiUsers } from "react-icons/fi"

import DocumentCaseLinksManager from "./DocumentCaseLinksManager"
import CaseLinkedDocuments from "./CaseLinkedDocuments"
import { LINK_TYPE_CONFIG } from "@/client/document-case-links"

const DocumentCaseLinksDemo = () => {
  // Demo IDs (you can replace with real IDs for testing)
  const demoDocumentId = "demo-document-links-789"
  const demoCaseId = "demo-case-links-123"

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            🔗 Document-Case Linking System Demo
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Issue #56 - Complete document-case relationship management system
          </Text>
        </Box>

        {/* Status Alert */}
        <Alert status="success" borderRadius="lg">
          <Box as={FiInfo} color="green.500" mr={3} mt={1} />
          <Box>
            <Text fontWeight="bold">System Status: Complete ✅</Text>
            <Text fontSize="sm">
              Full document-case linking system implemented with many-to-many relationships and comprehensive management interface.
            </Text>
          </Box>
        </Alert>

        {/* Features Overview */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>🚀 Implemented Features</Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">✅ Relationship Management:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Many-to-many document-case relationships</Text>
                  <Text>• Multiple link types with context</Text>
                  <Text>• Link metadata and notes</Text>
                  <Text>• Automatic activity logging</Text>
                  <Text>• Role-based access control</Text>
                  <Text>• Link creation and deletion tracking</Text>
                </VStack>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="blue.600">📋 Link Types:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <HStack><FiLink size={12} /><Text>Related - General relationship</Text></HStack>
                  <HStack><Text>📸</Text><Text>Evidence - Document as evidence</Text></HStack>
                  <HStack><Text>📚</Text><Text>Reference - Reference material</Text></HStack>
                  <HStack><Text>📄</Text><Text>Contract - Legal agreements</Text></HStack>
                  <HStack><Text>📧</Text><Text>Correspondence - Communications</Text></HStack>
                  <HStack><Text>⚖️</Text><Text>Court Filing - Legal documents</Text></HStack>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2} color="purple.600">🎨 User Interface:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Document link manager interface</Text>
                  <Text>• Case linked documents viewer</Text>
                  <Text>• Link creation and editing modals</Text>
                  <Text>• Visual link type indicators</Text>
                  <Text>• Link statistics and analytics</Text>
                  <Text>• Responsive design for all devices</Text>
                </VStack>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Demo Section */}
        <Box>
          <Heading size="lg" mb={4}>
            🔗 Interactive Demo
          </Heading>
          <Text mb={6} color="gray.600">
            Explore the document-case linking system below. Create links between documents and cases, manage relationships, and view linked documents.
          </Text>
          
          {/* Demo Tabs */}
          <Tabs>
            <TabList>
              <Tab>
                <HStack spacing={2}>
                  <FiFile />
                  <Text>Document Links Manager</Text>
                </HStack>
              </Tab>
              <Tab>
                <HStack spacing={2}>
                  <FiLink />
                  <Text>Case Linked Documents</Text>
                </HStack>
              </Tab>
            </TabList>

            <TabPanels>
              {/* Document Links Manager */}
              <TabPanel px={0}>
                <Box
                  p={6}
                  bg="white"
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="gray.200"
                  shadow="sm"
                >
                  <DocumentCaseLinksManager 
                    documentId={demoDocumentId}
                    documentTitle="Sample Legal Document.pdf"
                  />
                </Box>
              </TabPanel>

              {/* Case Linked Documents */}
              <TabPanel px={0}>
                <Box
                  p={6}
                  bg="white"
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="gray.200"
                  shadow="sm"
                >
                  <CaseLinkedDocuments caseId={demoCaseId} />
                </Box>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>

        {/* Implementation Status */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>📊 Implementation Status</Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">✅ Completed:</Text>
                <List spacing={1} fontSize="sm">
                  <ListItem>
                    <ListIcon as={FiLink} color="green.500" />
                    DocumentCaseLink model with many-to-many relationships
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiFile} color="green.500" />
                    Complete API for link management (CRUD operations)
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiUsers} color="green.500" />
                    Role-based access control for link operations
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiSearch} color="green.500" />
                    Document links manager with filtering
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiLink} color="green.500" />
                    Case linked documents viewer with grouping
                  </ListItem>
                </List>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="orange.600">🔄 Future Enhancements:</Text>
                <List spacing={1} fontSize="sm">
                  <ListItem>• Bulk link operations for multiple documents</ListItem>
                  <ListItem>• Link templates for common scenarios</ListItem>
                  <ListItem>• Advanced link analytics and reporting</ListItem>
                  <ListItem>• Link approval workflows for sensitive documents</ListItem>
                  <ListItem>• Integration with external document systems</ListItem>
                  <ListItem>• Link expiration and automatic cleanup</ListItem>
                  <ListItem>• Link visualization with network graphs</ListItem>
                  <ListItem>• Export linked documents as case packages</ListItem>
                </List>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Link Types Reference */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>🏷️ Link Types Reference</Heading>
            <Text mb={4} fontSize="sm" color="gray.600">
              The system supports multiple link types to categorize document-case relationships:
            </Text>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={3}>
              {Object.entries(LINK_TYPE_CONFIG).map(([type, config]) => (
                <Box key={type} p={3} bg="white" borderRadius="md" border="1px solid" borderColor="gray.200">
                  <HStack spacing={3}>
                    <Box fontSize="xl" color={`${config.color}.500`}>
                      {config.icon}
                    </Box>
                    <VStack align="start" spacing={0}>
                      <Text fontWeight="medium" fontSize="sm">
                        {config.label}
                      </Text>
                      <Text fontSize="xs" color="gray.600">
                        {config.description}
                      </Text>
                    </VStack>
                  </HStack>
                </Box>
              ))}
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Technical Details */}
        <Card bg="gray.50">
          <CardBody>
            <Heading size="md" mb={4}>🛠️ Technical Implementation</Heading>
            <VStack align="stretch" spacing={3} fontSize="sm">
              <Box>
                <Text fontWeight="bold">Backend Architecture:</Text>
                <Text>• DocumentCaseLink model with many-to-many relationships</Text>
                <Text>• Unique constraints to prevent duplicate links</Text>
                <Text>• RESTful API with full CRUD operations</Text>
                <Text>• Automatic activity logging for audit trails</Text>
                <Text>• Role-based access control with permission checks</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">Frontend Components:</Text>
                <Text>• DocumentCaseLinksManager: Main link management interface</Text>
                <Text>• CaseLinkedDocuments: Case-centric document viewer</Text>
                <Text>• CreateLinkModal: Link creation with case selection</Text>
                <Text>• EditLinkModal: Link editing with metadata updates</Text>
                <Text>• LinkCard: Individual link display with actions</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">Data Flow:</Text>
                <Text>• Real-time link statistics and counts</Text>
                <Text>• Optimistic UI updates with React Query</Text>
                <Text>• Efficient queries with proper indexing</Text>
                <Text>• Link validation and error handling</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">User Experience:</Text>
                <Text>• Intuitive link creation with case search</Text>
                <Text>• Visual link type indicators and grouping</Text>
                <Text>• Link metadata with notes and context</Text>
                <Text>• Mobile-responsive design for all devices</Text>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

export default DocumentCaseLinksDemo
