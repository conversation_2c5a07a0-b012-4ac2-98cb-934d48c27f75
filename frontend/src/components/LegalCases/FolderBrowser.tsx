import {
  Box,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Button,
  IconButton,
  Flex,
  Spacer,
  useDisclosure,
  Spinner,
  Alert,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tooltip,
  Badge,
  useColorModeValue,
} from "@chakra-ui/react"
import {
  FiFolder,
  FiFolderPlus,
  FiHome,
  FiChevronRight,
  FiRefreshCw,
  FiSettings,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"

import { 
  CaseFoldersService, 
  CaseFolder,
  sortFolders,
  getFolderIcon,
  getFolderColor,
  formatFolderSize 
} from "@/client/case-folders"
import useAuth from "@/hooks/useAuth"
import FolderCreateModal from "./FolderCreateModal"
import FolderCard from "./FolderCard"

interface FolderBrowserProps {
  caseId: string
  currentFolderId?: string
  onFolderSelect: (folderId: string | null) => void
  onFolderChange?: () => void
}

const FolderBrowser = ({ 
  caseId, 
  currentFolderId, 
  onFolderSelect,
  onFolderChange 
}: FolderBrowserProps) => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure()
  
  const [currentPath, setCurrentPath] = useState<CaseFolder[]>([])

  const cardBg = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const hoverBg = useColorModeValue("gray.50", "gray.700")

  // Fetch folders for current directory
  const { 
    data: foldersData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ["case-folders", caseId, currentFolderId],
    queryFn: () => CaseFoldersService.getCaseFolders(caseId, {
      parent_folder_id: currentFolderId,
    }),
    enabled: !!caseId,
  })

  const folders = foldersData?.data || []
  const sortedFolders = sortFolders(folders)

  const handleFolderCreated = () => {
    queryClient.invalidateQueries({ queryKey: ["case-folders", caseId] })
    onCreateClose()
    onFolderChange?.()
  }

  const handleFolderUpdated = () => {
    queryClient.invalidateQueries({ queryKey: ["case-folders", caseId] })
    onFolderChange?.()
  }

  const handleFolderDeleted = () => {
    queryClient.invalidateQueries({ queryKey: ["case-folders", caseId] })
    onFolderChange?.()
  }

  const handleFolderClick = (folder: CaseFolder) => {
    setCurrentPath([...currentPath, folder])
    onFolderSelect(folder.id)
  }

  const handleBreadcrumbClick = (index: number) => {
    if (index === -1) {
      // Root
      setCurrentPath([])
      onFolderSelect(null)
    } else {
      // Navigate to specific folder in path
      const newPath = currentPath.slice(0, index + 1)
      setCurrentPath(newPath)
      onFolderSelect(newPath[newPath.length - 1]?.id || null)
    }
  }

  const getCurrentFolderName = () => {
    if (currentPath.length === 0) return "Root"
    return currentPath[currentPath.length - 1].name
  }

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load folders. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header with breadcrumb */}
      <Flex align="center" mb={4}>
        <VStack align="start" spacing={1} flex={1}>
          <Text fontSize="lg" fontWeight="bold">
            📁 {getCurrentFolderName()}
          </Text>
          
          {/* Breadcrumb */}
          <Breadcrumb spacing="8px" separator={<FiChevronRight color="gray.500" />}>
            <BreadcrumbItem>
              <BreadcrumbLink onClick={() => handleBreadcrumbClick(-1)}>
                <HStack spacing={1}>
                  <FiHome size={14} />
                  <Text fontSize="sm">Root</Text>
                </HStack>
              </BreadcrumbLink>
            </BreadcrumbItem>
            
            {currentPath.map((folder, index) => (
              <BreadcrumbItem key={folder.id} isCurrentPage={index === currentPath.length - 1}>
                <BreadcrumbLink 
                  onClick={() => handleBreadcrumbClick(index)}
                  isCurrentPage={index === currentPath.length - 1}
                >
                  <Text fontSize="sm">{folder.name}</Text>
                </BreadcrumbLink>
              </BreadcrumbItem>
            ))}
          </Breadcrumb>
        </VStack>

        <HStack spacing={2}>
          <Tooltip label="Refresh folders">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
          <Button
            leftIcon={<FiFolderPlus />}
            colorScheme="blue"
            size="sm"
            onClick={onCreateOpen}
          >
            New Folder
          </Button>
        </HStack>
      </Flex>

      {/* Folders Grid */}
      {isLoading ? (
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={4} color="gray.600">Loading folders...</Text>
        </Box>
      ) : sortedFolders.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            No folders in this location
          </Text>
          <Button
            leftIcon={<FiFolderPlus />}
            colorScheme="blue"
            onClick={onCreateOpen}
          >
            Create First Folder
          </Button>
        </Box>
      ) : (
        <VStack spacing={3} align="stretch">
          {sortedFolders.map((folder) => (
            <FolderCard
              key={folder.id}
              folder={folder}
              caseId={caseId}
              onClick={() => handleFolderClick(folder)}
              onUpdated={handleFolderUpdated}
              onDeleted={handleFolderDeleted}
            />
          ))}
        </VStack>
      )}

      {/* Create Folder Modal */}
      <FolderCreateModal
        isOpen={isCreateOpen}
        onClose={onCreateClose}
        caseId={caseId}
        parentFolderId={currentFolderId}
        onSuccess={handleFolderCreated}
      />
    </Box>
  )
}

export default FolderBrowser
