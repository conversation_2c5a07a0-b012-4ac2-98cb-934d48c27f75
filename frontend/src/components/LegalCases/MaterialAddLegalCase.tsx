/**
=========================================================
* EquiNova Material Add Legal Case
=========================================================

* Material-UI implementation of Add Legal Case modal
* Replaces Chakra UI dialog with Material Design

=========================================================
*/

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Stack,
  IconButton,
  Alert,
  CircularProgress,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Close as CloseIcon,
  Save as SaveIcon,
  Gavel as LegalIcon,
} from '@mui/icons-material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { type SubmitHandler, useForm } from 'react-hook-form';

import { type LegalCaseCreate, LegalCasesService } from '@/client';
import type { ApiError } from '@/client/core/ApiError';
import useCustomToast from '@/hooks/useCustomToast';
import { handleError } from '@/utils';

interface MaterialAddLegalCaseProps {
  variant?: 'button' | 'fab';
  size?: 'small' | 'medium' | 'large';
}

const MaterialAddLegalCase: React.FC<MaterialAddLegalCaseProps> = ({
  variant = 'button',
  size = 'medium',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const queryClient = useQueryClient();
  const { showSuccessToast } = useCustomToast();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid, isSubmitting },
  } = useForm<LegalCaseCreate>({
    mode: 'onBlur',
    criteriaMode: 'all',
    defaultValues: {
      title: '',
      client_name: '',
    },
  });

  const mutation = useMutation({
    mutationFn: (data: LegalCaseCreate) =>
      LegalCasesService.createLegalCase({
        requestBody: {
          ...data,
          lawyer_id: 'f51bee7a-79c8-4155-82d1-77a711441031', // Using admin user for now
        },
      }),
    onSuccess: () => {
      showSuccessToast('Legal case created successfully.');
      reset();
      setIsOpen(false);
    },
    onError: (err: ApiError) => {
      handleError(err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['legal-cases'] });
    },
  });

  const onSubmit: SubmitHandler<LegalCaseCreate> = (data) => {
    mutation.mutate(data);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setIsOpen(false);
      reset();
    }
  };

  const TriggerButton = variant === 'fab' ? (
    <Fab
      color="primary"
      aria-label="add legal case"
      onClick={() => setIsOpen(true)}
      size={size}
      sx={{
        position: 'fixed',
        bottom: 24,
        right: 24,
        zIndex: 1000,
      }}
    >
      <AddIcon />
    </Fab>
  ) : (
    <Button
      variant="contained"
      startIcon={<AddIcon />}
      onClick={() => setIsOpen(true)}
      size={size}
      sx={{ my: 2 }}
    >
      Add Legal Case
    </Button>
  );

  return (
    <>
      {TriggerButton}

      <Dialog
        open={isOpen}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            minHeight: 400,
          },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            pb: 1,
          }}
        >
          <Box display="flex" alignItems="center" gap={1}>
            <LegalIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Add New Legal Case
            </Typography>
          </Box>
          <IconButton
            onClick={handleClose}
            size="small"
            disabled={isSubmitting}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogContent sx={{ pt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Fill in the details to create a new legal case in the system.
            </Typography>

            <Stack spacing={3} sx={{ mt: 3 }}>
              {/* Case Title Field */}
              <TextField
                label="Case Title"
                placeholder="Enter the case title..."
                fullWidth
                required
                error={!!errors.title}
                helperText={errors.title?.message}
                disabled={isSubmitting}
                {...register('title', {
                  required: 'Case title is required.',
                  minLength: {
                    value: 3,
                    message: 'Case title must be at least 3 characters.',
                  },
                  maxLength: {
                    value: 200,
                    message: 'Case title must not exceed 200 characters.',
                  },
                })}
                InputProps={{
                  startAdornment: (
                    <LegalIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  ),
                }}
              />

              {/* Client Name Field */}
              <TextField
                label="Client Name"
                placeholder="Enter the client name..."
                fullWidth
                required
                error={!!errors.client_name}
                helperText={errors.client_name?.message}
                disabled={isSubmitting}
                {...register('client_name', {
                  required: 'Client name is required.',
                  minLength: {
                    value: 2,
                    message: 'Client name must be at least 2 characters.',
                  },
                  maxLength: {
                    value: 100,
                    message: 'Client name must not exceed 100 characters.',
                  },
                })}
              />

              {/* Additional Information */}
              <Alert severity="info" sx={{ borderRadius: 2 }}>
                <Typography variant="body2">
                  <strong>Note:</strong> Additional case details such as case type, priority, and description can be added after creation through the edit functionality.
                </Typography>
              </Alert>

              {/* Error Display */}
              {mutation.isError && (
                <Alert severity="error" sx={{ borderRadius: 2 }}>
                  <Typography variant="body2">
                    Failed to create legal case. Please check your input and try again.
                  </Typography>
                </Alert>
              )}
            </Stack>
          </DialogContent>

          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleClose}
              disabled={isSubmitting}
              color="inherit"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={!isValid || isSubmitting}
              startIcon={
                isSubmitting ? (
                  <CircularProgress size={16} />
                ) : (
                  <SaveIcon />
                )
              }
              sx={{ minWidth: 120 }}
            >
              {isSubmitting ? 'Creating...' : 'Create Case'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};

export default MaterialAddLegalCase;
