import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Button,
  <PERSON>lex,
  Spacer,
  useDisclosure,
  Spinner,
  Alert,
  Badge,
  IconButton,
  Tooltip,
  useColorModeValue,
} from "@chakra-ui/react"
import {
  FiLink,
  FiPlus,
  FiRefreshCw,
  FiExternalLink,
  FiTrash2,
  FiEdit,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  DocumentCaseLinksService, 
  DocumentCaseLink,
  getLinkTypeInfo,
  formatLinkDate,
  sortLinksByDate,
  getLinkStats 
} from "@/client/document-case-links"
import { toaster } from "@/components/ui/toaster"
import useAuth from "@/hooks/useAuth"
import CreateLinkModal from "./CreateLinkModal"
import EditLinkModal from "./EditLinkModal"
import LinkCard from "./LinkCard"

interface DocumentCaseLinksManagerProps {
  documentId: string
  documentTitle?: string
  onLinksChange?: () => void
}

const DocumentCaseLinksManager = ({ 
  documentId, 
  documentTitle,
  onLinksChange 
}: DocumentCaseLinksManagerProps) => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  
  const [selectedLink, setSelectedLink] = useState<DocumentCaseLink | null>(null)
  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure()
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()

  const borderColor = useColorModeValue("gray.200", "gray.600")
  const bgColor = useColorModeValue("white", "gray.800")

  // Fetch document links
  const { 
    data: linksData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ["document-case-links", documentId],
    queryFn: () => DocumentCaseLinksService.getDocumentCaseLinks(documentId),
    enabled: !!documentId,
  })

  const links = linksData?.data || []
  const totalCount = linksData?.count || 0
  const sortedLinks = sortLinksByDate(links)
  const linkStats = getLinkStats(links)

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: ({ linkId }: { linkId: string }) => 
      DocumentCaseLinksService.deleteDocumentCaseLink(documentId, linkId),
    onSuccess: () => {
      toaster.create({
        title: "Link deleted",
        description: "Document link has been removed successfully.",
        status: "success",
      })
      queryClient.invalidateQueries({ queryKey: ["document-case-links", documentId] })
      onLinksChange?.()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to delete link",
        status: "error",
      })
    },
  })

  const handleCreateSuccess = () => {
    onCreateClose()
    queryClient.invalidateQueries({ queryKey: ["document-case-links", documentId] })
    onLinksChange?.()
  }

  const handleEditSuccess = () => {
    onEditClose()
    setSelectedLink(null)
    queryClient.invalidateQueries({ queryKey: ["document-case-links", documentId] })
    onLinksChange?.()
  }

  const handleEdit = (link: DocumentCaseLink) => {
    setSelectedLink(link)
    onEditOpen()
  }

  const handleDelete = (link: DocumentCaseLink) => {
    const confirmMessage = `Are you sure you want to remove the link to "${link.case?.title || 'Unknown Case'}"?`
    
    if (window.confirm(confirmMessage)) {
      deleteMutation.mutate({ linkId: link.id })
    }
  }

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load document links. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={2}>
            <FiLink />
            <Text fontSize="lg" fontWeight="bold">
              Case Links
            </Text>
            <Badge variant="subtle" colorScheme="blue">
              {totalCount} linked
            </Badge>
          </HStack>
          <Text fontSize="sm" color="gray.600">
            {documentTitle ? `Links for "${documentTitle}"` : "Manage document-case relationships"}
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Refresh links">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
          <Button
            leftIcon={<FiPlus />}
            colorScheme="blue"
            size="sm"
            onClick={onCreateOpen}
          >
            Link to Case
          </Button>
        </HStack>
      </Flex>

      {/* Stats Summary */}
      {totalCount > 0 && (
        <Box
          p={4}
          bg="gray.50"
          borderRadius="md"
          mb={6}
          border="1px solid"
          borderColor={borderColor}
        >
          <HStack spacing={6} fontSize="sm">
            <Text>
              <Text as="span" fontWeight="bold">{linkStats.total}</Text> total links
            </Text>
            <Text>
              <Text as="span" fontWeight="bold">{Object.keys(linkStats.byType).length}</Text> link types
            </Text>
            <Text>
              <Text as="span" fontWeight="bold">{Object.keys(linkStats.byCase).length}</Text> cases
            </Text>
            {linkStats.recentCount > 0 && (
              <Text>
                <Text as="span" fontWeight="bold">{linkStats.recentCount}</Text> recent (24h)
              </Text>
            )}
          </HStack>
        </Box>
      )}

      {/* Links List */}
      {isLoading ? (
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={4} color="gray.600">Loading links...</Text>
        </Box>
      ) : sortedLinks.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            No case links found
          </Text>
          <Text fontSize="sm" color="gray.600" mb={6}>
            Link this document to cases to establish relationships and improve organization.
          </Text>
          <Button
            leftIcon={<FiPlus />}
            colorScheme="blue"
            onClick={onCreateOpen}
          >
            Create First Link
          </Button>
        </Box>
      ) : (
        <VStack spacing={3} align="stretch">
          {sortedLinks.map((link) => (
            <LinkCard
              key={link.id}
              link={link}
              onEdit={() => handleEdit(link)}
              onDelete={() => handleDelete(link)}
              isDeleting={deleteMutation.isPending}
            />
          ))}
        </VStack>
      )}

      {/* Create Link Modal */}
      <CreateLinkModal
        isOpen={isCreateOpen}
        onClose={onCreateClose}
        documentId={documentId}
        onSuccess={handleCreateSuccess}
      />

      {/* Edit Link Modal */}
      {selectedLink && (
        <EditLinkModal
          isOpen={isEditOpen}
          onClose={onEditClose}
          documentId={documentId}
          link={selectedLink}
          onSuccess={handleEditSuccess}
        />
      )}
    </Box>
  )
}

export default DocumentCaseLinksManager
