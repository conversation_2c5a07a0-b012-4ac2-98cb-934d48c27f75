import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ody,
  <PERSON>ing,
  HStack,
  Text,
  VStack,
  Spinner,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { format } from "date-fns"

import { LegalCasesService } from "../../client"

// Utility function to safely format dates
const safeFormatDate = (dateString: string): string => {
  try {
    if (!dateString) return "Unknown date"
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return "Invalid date"
    return format(date, "MMM dd, yyyy")
  } catch (error) {
    console.warn("Date formatting error:", error)
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return "Unknown date"
    }
  }
}

interface StatusHistoryProps {
  caseId: string
}

const statusColors: Record<string, string> = {
  // Uppercase versions
  OPEN: "blue",
  IN_PROGRESS: "yellow",
  UNDER_REVIEW: "orange",
  CLOSED: "green",
  ON_HOLD: "gray",
  CANCELLED: "red",
  // Lowercase versions (for compatibility)
  open: "blue",
  in_progress: "yellow",
  under_review: "orange",
  closed: "green",
  on_hold: "gray",
  cancelled: "red",
}

const statusLabels: Record<string, string> = {
  // Uppercase versions
  OPEN: "Open",
  IN_PROGRESS: "In Progress",
  UNDER_REVIEW: "Under Review",
  CLOSED: "Closed",
  ON_HOLD: "On Hold",
  CANCELLED: "Cancelled",
  // Lowercase versions (for compatibility)
  open: "Open",
  in_progress: "In Progress",
  under_review: "Under Review",
  closed: "Closed",
  on_hold: "On Hold",
  cancelled: "Cancelled",
}

export default function StatusHistory({ caseId }: StatusHistoryProps) {
  const { data: statusHistory, isLoading, error } = useQuery({
    queryKey: ["legal-case", caseId, "status-history"],
    queryFn: () => LegalCasesService.readCaseStatusHistory({
      legalCaseId: caseId,
      limit: 50,
      skip: 0
    }),
  })

  if (isLoading) {
    return (
      <Box textAlign="center" py={8}>
        <Spinner size="lg" />
        <Text mt={2}>Loading status history...</Text>
      </Box>
    )
  }

  if (error) {
    return (
      <Box p={4} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
        <Text color="red.600">
          Failed to load status history
        </Text>
      </Box>
    )
  }

  if (!statusHistory?.data || statusHistory.data.length === 0) {
    return (
      <Box textAlign="center" py={8}>
        <Text color="gray.500">No status changes recorded yet</Text>
      </Box>
    )
  }

  return (
    <Box>
      <Heading size="md" mb={4}>Status History</Heading>
      <VStack spacing={3} align="stretch">
        {statusHistory.data.map((history, index) => {
          // Ultra-safe conversion to prevent React error #130
          const safeString = (value: any): string => {
            if (value === null || value === undefined) return ""
            if (typeof value === "string") return value
            if (typeof value === "number" || typeof value === "boolean") return String(value)
            try {
              return String(value)
            } catch {
              return ""
            }
          }

          const historyId = safeString(history?.id) || `history-${index}`
          const oldStatus = safeString(history?.old_status) || ""
          const newStatus = safeString(history?.new_status) || "UNKNOWN"
          const changedAt = safeString(history?.changed_at) || new Date().toISOString()
          const changedByUser = safeString(history?.changed_by_user?.full_name) || "Unknown User"
          const notes = safeString(history?.notes) || ""

          return (
            <Card key={historyId} size="sm">
              <CardBody>
                <VStack spacing={2} align="stretch">
                  <HStack justify="space-between">
                    <HStack spacing={3}>
                      {oldStatus && (
                        <>
                          <Badge colorScheme={statusColors[oldStatus] || "gray"} variant="outline">
                            {String(statusLabels[oldStatus] || oldStatus)}
                          </Badge>
                          <Text fontSize="sm" color="gray.500">→</Text>
                        </>
                      )}
                      <Badge colorScheme={statusColors[newStatus] || "gray"} variant="solid">
                        {String(statusLabels[newStatus] || newStatus)}
                      </Badge>
                    </HStack>
                    <Text fontSize="sm" color="gray.500">
                      {safeFormatDate(changedAt)}
                    </Text>
                  </HStack>

                  <HStack justify="space-between" fontSize="sm">
                    <Text color="gray.600">
                      Changed by: {String(changedByUser)}
                    </Text>
                  </HStack>

                  {notes && (
                    <Box bg="gray.50" p={2} borderRadius="md">
                      <Text fontSize="sm" color="gray.700">
                        <strong>Notes:</strong> {String(notes)}
                      </Text>
                    </Box>
                  )}
                </VStack>
              </CardBody>
            </Card>
          )
        })}
      </VStack>
    </Box>
  )
}
