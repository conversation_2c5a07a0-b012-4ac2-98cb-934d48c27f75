import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>tack,
  Text,
  Badge,
  IconButton,
  Tooltip,


  Progress,
  Button,
} from "@chakra-ui/react"
import {
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  FiUser,
  FiClock,
  FiCalendar,
  FiFlag,
  FiPlay,
  FiCheck,
} from "react-icons/fi"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseMilestone,
  CaseProgressService,
  getMilestoneStatusInfo,
  getMilestoneTypeInfo,
  formatProgressPercentage,
  getProgressColor 
} from "@/client/case-progress"
import { toaster } from "@/components/ui/toaster"
import useAuth from "@/hooks/useAuth"

interface MilestoneCardProps {
  milestone: CaseMilestone
  caseId: string
  onEdit: () => void
  onUpdate: () => void
}

const MilestoneCard = ({ milestone, caseId, onEdit, onUpdate }: MilestoneCardProps) => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  
  const cardBg = "white"
  const borderColor = "gray.200"
  const hoverBg = "gray.50"

  const statusInfo = getMilestoneStatusInfo(milestone.status)
  const typeInfo = getMilestoneTypeInfo(milestone.milestone_type)
  const canEdit = user?.id === milestone.created_by || user?.role === 'admin'

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: () => CaseProgressService.deleteCaseMilestone(caseId, milestone.id),
    onSuccess: () => {
      toaster.create({
        title: "Milestone deleted",
        description: "Milestone has been removed successfully.",
        status: "success",
      })
      queryClient.invalidateQueries({ queryKey: ["case-milestones", caseId] })
      onUpdate()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to delete milestone",
        status: "error",
      })
    },
  })

  // Status update mutations
  const updateStatusMutation = useMutation({
    mutationFn: (status: string) => 
      CaseProgressService.updateCaseMilestone(caseId, milestone.id, { status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["case-milestones", caseId] })
      onUpdate()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update milestone status",
        status: "error",
      })
    },
  })

  const handleDelete = () => {
    const confirmMessage = `Are you sure you want to delete the milestone "${milestone.title}"?`
    
    if (window.confirm(confirmMessage)) {
      deleteMutation.mutate()
    }
  }

  const handleStatusChange = (newStatus: string) => {
    updateStatusMutation.mutate(newStatus)
  }

  const isOverdue = milestone.target_date && 
    milestone.status !== "completed" && 
    new Date(milestone.target_date) < new Date()

  return (
    <Box
      bg={cardBg}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="lg"
      p={4}
      transition="all 0.2s"
      _hover={{ 
        shadow: "md", 
        bg: hoverBg,
        transform: "translateY(-1px)" 
      }}
      borderLeftWidth="4px"
      borderLeftColor={`${statusInfo.color}.500`}
    >
      <HStack spacing={4} align="start">
        {/* Status Icon */}
        <Box
          fontSize="2xl"
          color={`${statusInfo.color}.500`}
          minW="40px"
          textAlign="center"
          mt={1}
        >
          {statusInfo.icon}
        </Box>

        {/* Milestone Info */}
        <VStack align="start" spacing={3} flex={1}>
          {/* Title and Type */}
          <VStack align="start" spacing={1}>
            <HStack spacing={2} align="center" wrap="wrap">
              <Text
                fontWeight="bold"
                fontSize="md"
                noOfLines={1}
                flex={1}
              >
                {milestone.title}
              </Text>
              {milestone.is_required && (
                <Badge colorScheme="red" variant="subtle" fontSize="xs">
                  Required
                </Badge>
              )}
              {isOverdue && (
                <Badge colorScheme="red" variant="solid" fontSize="xs">
                  Overdue
                </Badge>
              )}
            </HStack>

            <HStack spacing={2}>
              <Badge
                colorScheme={typeInfo.color}
                variant="subtle"
                fontSize="xs"
              >
                {typeInfo.icon} {typeInfo.label}
              </Badge>
              <Badge
                colorScheme={statusInfo.color}
                variant="outline"
                fontSize="xs"
              >
                {statusInfo.label}
              </Badge>
            </HStack>
          </VStack>

          {/* Description */}
          {milestone.description && (
            <Text fontSize="sm" color="gray.700" noOfLines={2}>
              {milestone.description}
            </Text>
          )}

          {/* Progress */}
          {milestone.progress_percentage > 0 && (
            <Box w="full">
              <HStack justify="space-between" mb={1}>
                <Text fontSize="xs" color="gray.600">Progress</Text>
                <Text fontSize="xs" color="gray.600">
                  {formatProgressPercentage(milestone.progress_percentage)}
                </Text>
              </HStack>
              <Progress 
                value={milestone.progress_percentage} 
                colorScheme={getProgressColor(milestone.progress_percentage)}
                size="sm"
              />
            </Box>
          )}

          {/* Dates and Assignment */}
          <VStack align="start" spacing={1} fontSize="sm" color="gray.600">
            {milestone.target_date && (
              <HStack spacing={1}>
                <FiCalendar size={12} />
                <Text>
                  Target: {new Date(milestone.target_date).toLocaleDateString()}
                </Text>
              </HStack>
            )}
            
            {milestone.completion_date && (
              <HStack spacing={1}>
                <FiCheck size={12} />
                <Text>
                  Completed: {new Date(milestone.completion_date).toLocaleDateString()}
                </Text>
              </HStack>
            )}

            {milestone.assigned_user && (
              <HStack spacing={1}>
                <FiUser size={12} />
                <Text>
                  Assigned to: {milestone.assigned_user.full_name || milestone.assigned_user.email}
                </Text>
              </HStack>
            )}

            <HStack spacing={1}>
              <FiClock size={12} />
              <Text>
                Created: {new Date(milestone.created_at).toLocaleDateString()}
              </Text>
            </HStack>
          </VStack>

          {/* Notes */}
          {milestone.notes && (
            <Text fontSize="sm" color="gray.600" fontStyle="italic">
              Note: {milestone.notes}
            </Text>
          )}

          {/* Quick Actions */}
          {milestone.status !== "completed" && (
            <HStack spacing={2}>
              {milestone.status === "not_started" && (
                <Button
                  size="xs"
                  leftIcon={<FiPlay />}
                  colorScheme="blue"
                  variant="outline"
                  onClick={() => handleStatusChange("in_progress")}
                  isLoading={updateStatusMutation.isPending}
                >
                  Start
                </Button>
              )}
              
              {milestone.status === "in_progress" && (
                <Button
                  size="xs"
                  leftIcon={<FiCheck />}
                  colorScheme="green"
                  variant="outline"
                  onClick={() => handleStatusChange("completed")}
                  isLoading={updateStatusMutation.isPending}
                >
                  Complete
                </Button>
              )}
            </HStack>
          )}
        </VStack>

        {/* Actions */}
        {canEdit && (
          <HStack spacing={1}>
            <Tooltip label="Edit milestone">
              <IconButton
                variant="ghost"
                size="sm"
                icon={<FiEdit />}
                onClick={onEdit}
              />
            </Tooltip>
            <Tooltip label="Delete milestone">
              <IconButton
                variant="ghost"
                size="sm"
                icon={<FiTrash2 />}
                onClick={handleDelete}
                isLoading={deleteMutation.isPending}
                colorScheme="red"
              />
            </Tooltip>
          </HStack>
        )}
      </HStack>
    </Box>
  )
}

export default MilestoneCard
