import {
  Box,
  VStack,
  H<PERSON>tack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  Progress,





  CircularProgress,
  CircularProgressLabel,
  Tooltip,
  SimpleGrid,
  Alert,
  AlertIcon,
} from "@chakra-ui/react"
import {
  FiTarget,
  FiFlag,
  FiClock,
  FiCheckCircle,
  FiAlertTriangle,
  FiCalendar,
} from "react-icons/fi"

import { ProgressAnalytics } from "@/client/case-analytics"

interface ProgressAnalyticsCardProps {
  analytics: ProgressAnalytics
}

const ProgressAnalyticsCard = ({ analytics }: ProgressAnalyticsCardProps) => {
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "green"
    if (percentage >= 60) return "blue"
    if (percentage >= 40) return "yellow"
    if (percentage >= 20) return "orange"
    return "red"
  }

  const getTrackingStatus = () => {
    if (analytics.on_track) {
      return {
        status: "success" as const,
        label: "On Track",
        icon: <FiCheckCircle />,
        color: "green"
      }
    } else {
      return {
        status: "warning" as const,
        label: "Needs Attention",
        icon: <FiAlertTriangle />,
        color: "orange"
      }
    }
  }

  const trackingInfo = getTrackingStatus()

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiTarget />
          <Heading size="sm">Progress Analytics</Heading>
          <Badge 
            colorScheme={trackingInfo.color} 
            variant="subtle"
            fontSize="xs"
          >
            {trackingInfo.label}
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Progress Overview */}
          <Box textAlign="center">
            <CircularProgress 
              value={analytics.progress_percentage} 
              color={`${getProgressColor(analytics.progress_percentage)}.400`}
              size="80px"
              thickness="8px"
            >
              <CircularProgressLabel fontSize="sm" fontWeight="bold">
                {analytics.progress_percentage.toFixed(0)}%
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="sm" color="gray.600" mt={2}>
              Overall Progress
            </Text>
            <Text fontSize="xs" color="gray.500">
              {analytics.completed_milestones} of {analytics.total_milestones} milestones completed
            </Text>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Milestone Statistics */}
          <SimpleGrid columns={2} spacing={4}>
            <Box textAlign="center">
              <Box textAlign="center">Milestones</Text>
              <Box textAlign="center">
                {analytics.total_milestones}
              </Text>
              <Box textAlign="center">
                Total planned
              </Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">Completed</Text>
              <Box textAlign="center">
                {analytics.completed_milestones}
              </Text>
              <Box textAlign="center">
                {analytics.milestone_completion_rate.toFixed(1)}% done
              </Text>
            </Box>
          </SimpleGrid>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Progress Breakdown */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Progress Breakdown
            </Text>
            
            <VStack spacing={3} align="stretch">
              {/* Completed Milestones */}
              <Box>
                <HStack justify="space-between" mb={1}>
                  <Tooltip label="Milestones successfully completed">
                    <HStack spacing={2}>
                      <FiCheckCircle size={14} color="green" />
                      <Text fontSize="sm">Completed:</Text>
                    </HStack>
                  </Tooltip>
                  <Text fontSize="sm" fontWeight="bold" color="green.600">
                    {analytics.completed_milestones}
                  </Text>
                </HStack>
                <Progress 
                  value={analytics.milestone_completion_rate} 
                  colorScheme="green" 
                  size="sm" 
                  borderRadius="full"
                />
              </Box>

              {/* Overdue Milestones */}
              {analytics.overdue_milestones > 0 && (
                <Box>
                  <HStack justify="space-between" mb={1}>
                    <Tooltip label="Milestones past their target date">
                      <HStack spacing={2}>
                        <FiAlertTriangle size={14} color="red" />
                        <Text fontSize="sm">Overdue:</Text>
                      </HStack>
                    </Tooltip>
                    <Text fontSize="sm" fontWeight="bold" color="red.600">
                      {analytics.overdue_milestones}
                    </Text>
                  </HStack>
                  <Progress 
                    value={(analytics.overdue_milestones / analytics.total_milestones) * 100} 
                    colorScheme="red" 
                    size="sm" 
                    borderRadius="full"
                  />
                </Box>
              )}

              {/* Remaining Milestones */}
              <Box>
                <HStack justify="space-between" mb={1}>
                  <Tooltip label="Milestones still in progress or not started">
                    <HStack spacing={2}>
                      <FiFlag size={14} color="blue" />
                      <Text fontSize="sm">Remaining:</Text>
                    </HStack>
                  </Tooltip>
                  <Text fontSize="sm" fontWeight="bold" color="blue.600">
                    {analytics.total_milestones - analytics.completed_milestones}
                  </Text>
                </HStack>
                <Progress 
                  value={100 - analytics.milestone_completion_rate} 
                  colorScheme="blue" 
                  size="sm" 
                  borderRadius="full"
                />
              </Box>
            </VStack>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Deadline Analytics */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Deadline Management
            </Text>
            
            <VStack spacing={2} align="stretch">
              <HStack justify="space-between">
                <Tooltip label="Total deadlines set for this case">
                  <HStack spacing={2}>
                    <FiCalendar size={14} color="purple" />
                    <Text fontSize="sm">Total Deadlines:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="purple.600">
                  {analytics.total_deadlines}
                </Text>
              </HStack>

              <HStack justify="space-between">
                <Tooltip label="Deadlines coming up in the next 30 days">
                  <HStack spacing={2}>
                    <FiClock size={14} color="orange" />
                    <Text fontSize="sm">Upcoming:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="orange.600">
                  {analytics.upcoming_deadlines}
                </Text>
              </HStack>
            </VStack>
          </Box>

          {/* Progress Status Alert */}
          <Alert status={trackingInfo.status} borderRadius="md" py={2}>
            <AlertIcon />
            <Box>
              <Text fontSize="sm" fontWeight="medium">
                Progress Status: {trackingInfo.label}
              </Text>
              <Text fontSize="xs">
                {analytics.on_track 
                  ? "Case is progressing well with no major delays"
                  : `${analytics.overdue_milestones} overdue milestones and ${analytics.upcoming_deadlines} upcoming deadlines need attention`
                }
              </Text>
            </Box>
          </Alert>

          {/* Progress Insights */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Progress Insights:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {analytics.progress_percentage >= 80 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Case is nearing completion
                </Text>
              )}
              
              {analytics.progress_percentage < 25 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Case is in early stages
                </Text>
              )}
              
              {analytics.overdue_milestones === 0 && analytics.total_milestones > 0 && (
                <Text fontSize="xs" color="green.600">
                  ✓ No overdue milestones
                </Text>
              )}
              
              {analytics.overdue_milestones > 0 && (
                <Text fontSize="xs" color="red.600">
                  ⚠ {analytics.overdue_milestones} milestone(s) overdue
                </Text>
              )}
              
              {analytics.upcoming_deadlines > 3 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Many upcoming deadlines ({analytics.upcoming_deadlines})
                </Text>
              )}
              
              {analytics.upcoming_deadlines === 0 && analytics.total_deadlines > 0 && (
                <Text fontSize="xs" color="blue.600">
                  ✓ No immediate deadlines
                </Text>
              )}
              
              {analytics.milestone_completion_rate > 50 && analytics.overdue_milestones === 0 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Good progress momentum
                </Text>
              )}
              
              {analytics.total_milestones === 0 && (
                <Text fontSize="xs" color="yellow.600">
                  ⚠ No milestones set - consider adding some
                </Text>
              )}
            </VStack>
          </Box>

          {/* Quick Stats */}
          <HStack justify="space-around" bg="blue.50" p={2} borderRadius="md">
            <Box textAlign="center">
              <Box textAlign="center">
                {analytics.milestone_completion_rate.toFixed(0)}%
              </Text>
              <Box textAlign="center">Completion</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center"> 0 ? "red.600" : "green.600"}>
                {analytics.overdue_milestones}
              </Text>
              <Box textAlign="center">Overdue</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {analytics.upcoming_deadlines}
              </Text>
              <Box textAlign="center">Upcoming</Text>
            </Box>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default ProgressAnalyticsCard
