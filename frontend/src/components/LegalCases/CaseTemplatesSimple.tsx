import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>lex,
  <PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ner,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Input,
  Select,

} from "@chakra-ui/react"
import {
  FiPlus,
  FiSearch,
  FiFilter,
  FiLayers,
  FiFileText,
  FiGitBranch,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"

import { 
  CaseTemplatesService, 
  CASE_TYPE_CONFIG 
} from "@/client/case-templates"
import useCustomToast from "@/hooks/useCustomToast"

const CaseTemplatesSimple = () => {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCaseType, setSelectedCaseType] = useState<string>("")
  const [showPublicOnly, setShowPublicOnly] = useState(false)

  const toast = useCustomToast()

  // Fetch case templates
  const { 
    data: caseTemplates, 
    isLoading: isLoadingCaseTemplates, 
    error: caseTemplatesError 
  } = useQuery({
    queryKey: ["case-templates", searchQuery, selectedCaseType, showPublicOnly],
    queryFn: () => CaseTemplatesService.getCaseTemplates({
      search: searchQuery || undefined,
      case_type: selectedCaseType || undefined,
      is_public: showPublicOnly ? true : undefined,
      limit: 50
    }),
    staleTime: 5 * 60 * 1000,
  })

  // Fetch document templates
  const { 
    data: documentTemplates, 
    isLoading: isLoadingDocumentTemplates, 
    error: documentTemplatesError 
  } = useQuery({
    queryKey: ["document-templates", searchQuery, selectedCaseType, showPublicOnly],
    queryFn: () => CaseTemplatesService.getDocumentTemplates({
      search: searchQuery || undefined,
      case_type: selectedCaseType || undefined,
      is_public: showPublicOnly ? true : undefined,
      limit: 50
    }),
    staleTime: 5 * 60 * 1000,
  })

  // Fetch workflow templates
  const { 
    data: workflowTemplates, 
    isLoading: isLoadingWorkflowTemplates, 
    error: workflowTemplatesError 
  } = useQuery({
    queryKey: ["workflow-templates", searchQuery, selectedCaseType, showPublicOnly],
    queryFn: () => CaseTemplatesService.getWorkflowTemplates({
      search: searchQuery || undefined,
      case_type: selectedCaseType || undefined,
      is_public: showPublicOnly ? true : undefined,
      limit: 50
    }),
    staleTime: 5 * 60 * 1000,
  })

  const handleClearFilters = () => {
    setSearchQuery("")
    setSelectedCaseType("")
    setShowPublicOnly(false)
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={3}>
            <FiLayers size={24} />
            <Heading size="lg">Templates & Automation</Heading>
            <Badge colorScheme="blue" variant="subtle">
              Case Management
            </Badge>
          </HStack>
          <Text color="gray.600">
            Manage case templates, document templates, and workflow automation
          </Text>
        </VStack>
        <Spacer />
        
        {/* Action buttons */}
        <HStack spacing={2}>
          <Button
            leftIcon={<FiFilter />}
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
          >
            Clear Filters
          </Button>
        </HStack>
      </Flex>

      {/* Search and Filters */}
      <Card mb={6}>
        <CardBody>
          <VStack spacing={4} align="stretch">
            <HStack spacing={4}>
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                flex={1}
              />
              
              <Select
                placeholder="All case types"
                value={selectedCaseType}
                onChange={(e) => setSelectedCaseType(e.target.value)}
                width="200px"
              >
                {Object.entries(CASE_TYPE_CONFIG).map(([key, config]) => (
                  <option key={key} value={key}>
                    {config.icon} {config.label}
                  </option>
                ))}
              </Select>
              
              <Button
                variant={showPublicOnly ? "solid" : "outline"}
                colorScheme="blue"
                size="sm"
                onClick={() => setShowPublicOnly(!showPublicOnly)}
              >
                Public Only
              </Button>
            </HStack>
          </VStack>
        </CardBody>
      </Card>

      {/* Case Templates Section */}
      <VStack spacing={6} align="stretch">
        <HStack spacing={4}>
          <Badge colorScheme="blue" variant="solid" px={3} py={1}>
            <HStack spacing={2}>
              <FiLayers />
              <Text>Case Templates ({caseTemplates?.count || 0})</Text>
            </HStack>
          </Badge>
          <Badge colorScheme="green" variant="outline" px={3} py={1}>
            <HStack spacing={2}>
              <FiFileText />
              <Text>Document Templates ({documentTemplates?.count || 0})</Text>
            </HStack>
          </Badge>
          <Badge colorScheme="purple" variant="outline" px={3} py={1}>
            <HStack spacing={2}>
              <FiGitBranch />
              <Text>Workflow Templates ({workflowTemplates?.count || 0})</Text>
            </HStack>
          </Badge>
        </HStack>
        <VStack spacing={4} align="stretch">
          <HStack justify="space-between">
            <Text fontSize="lg" fontWeight="medium">
              Case Templates
            </Text>
            <Button
              leftIcon={<FiPlus />}
              colorScheme="blue"
              onClick={() => toast.info("Create template feature coming soon!")}
            >
              Create Case Template
            </Button>
          </HStack>

              {isLoadingCaseTemplates ? (
                <Box textAlign="center" py={8}>
                  <Spinner size="lg" />
                  <Text mt={2} color="gray.600">Loading case templates...</Text>
                </Box>
              ) : caseTemplatesError ? (
                <Alert status="error">
                  <Text>Failed to load case templates</Text>
                </Alert>
              ) : (
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                  {caseTemplates?.data.map((template) => (
                    <Card key={template.id} variant="outline">
                      <CardHeader>
                        <Text fontSize="lg" fontWeight="bold">
                          {template.name}
                        </Text>
                        {template.is_public && (
                          <Badge colorScheme="green" size="sm">
                            Public
                          </Badge>
                        )}
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={2} align="stretch">
                          {template.description && (
                            <Text fontSize="sm" color="gray.600">
                              {template.description}
                            </Text>
                          )}
                          <Text fontSize="xs" color="gray.500">
                            Created: {new Date(template.created_at).toLocaleDateString()}
                          </Text>
                        </VStack>
                      </CardBody>
                    </Card>
                  ))}
                </SimpleGrid>
              )}

          {caseTemplates?.data.length === 0 && (
            <Box textAlign="center" py={8}>
              <Text color="gray.500">No case templates found</Text>
              <Button
                mt={2}
                leftIcon={<FiPlus />}
                colorScheme="blue"
                variant="outline"
                onClick={() => toast.info("Create template feature coming soon!")}
              >
                Create your first template
              </Button>
            </Box>
          )}
        </VStack>
      </VStack>
    </Box>
  )
}

export default CaseTemplatesSimple
