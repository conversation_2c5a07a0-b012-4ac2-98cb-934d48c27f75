import {
  Box,
  VStack,
  HStack,
  Text,
  Progress,
  Badge,

  SimpleGrid,
} from "@chakra-ui/react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>lock,
  FiTrendingUp,
  FiCalendar,
} from "react-icons/fi"

import { 
  CaseProgressSummary,
  formatProgressPercentage,
  getProgressColor,
  getMilestoneStatusInfo,
  getDeadlineTypeInfo,
  formatDeadlineUrgency 
} from "@/client/case-progress"

interface ProgressVisualizationProps {
  progressSummary: CaseProgressSummary
  caseId: string
}

const ProgressVisualization = ({ progressSummary }: ProgressVisualizationProps) => {
  const cardBg = "gray.50"
  const borderColor = "gray.200"

  return (
    <VStack spacing={6} align="stretch">
      {/* Header */}
      <HStack spacing={2}>
        <FiTrendingUp />
        <Text fontSize="lg" fontWeight="bold">
          Progress Overview
        </Text>
      </HStack>

      {/* Overall Progress Bar */}
      <Box>
        <HStack justify="space-between" mb={2}>
          <Text fontSize="sm" fontWeight="medium">
            Case Completion
          </Text>
          <Text fontSize="sm" color="gray.600">
            {formatProgressPercentage(progressSummary.progress_percentage)}
          </Text>
        </HStack>
        <Progress 
          value={progressSummary.progress_percentage} 
          colorScheme={getProgressColor(progressSummary.progress_percentage)}
          size="lg"
          borderRadius="md"
        />
        <HStack justify="space-between" mt={2} fontSize="xs" color="gray.500">
          <Text>
            {progressSummary.completed_milestones} of {progressSummary.total_milestones} milestones completed
          </Text>
          <Text>
            {progressSummary.total_milestones - progressSummary.completed_milestones} remaining
          </Text>
        </HStack>
      </Box>

      {/* Key Metrics Grid */}
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
        <Box
          p={4}
          bg={cardBg}
          borderRadius="md"
          border="1px solid"
          borderColor={borderColor}
          textAlign="center"
        >
          <VStack spacing={2}>
            <FiFlag size={20} color={getProgressColor(progressSummary.progress_percentage)} />
            <Text fontSize="2xl" fontWeight="bold">
              {progressSummary.total_milestones}
            </Text>
            <Text fontSize="sm" color="gray.600">
              Total Milestones
            </Text>
          </VStack>
        </Box>

        <Box
          p={4}
          bg={cardBg}
          borderRadius="md"
          border="1px solid"
          borderColor={borderColor}
          textAlign="center"
        >
          <VStack spacing={2}>
            <FiCalendar size={20} color={progressSummary.upcoming_deadlines > 0 ? "#f56500" : "#38a169"} />
            <Text fontSize="2xl" fontWeight="bold">
              {progressSummary.upcoming_deadlines}
            </Text>
            <Text fontSize="sm" color="gray.600">
              Upcoming Deadlines
            </Text>
          </VStack>
        </Box>

        <Box
          p={4}
          bg={cardBg}
          borderRadius="md"
          border="1px solid"
          borderColor={borderColor}
          textAlign="center"
        >
          <VStack spacing={2}>
            <FiFlag size={20} color={progressSummary.critical_deadlines > 0 ? "#e53e3e" : "#38a169"} />
            <Text fontSize="2xl" fontWeight="bold">
              {progressSummary.critical_deadlines}
            </Text>
            <Text fontSize="sm" color="gray.600">
              Critical Deadlines
            </Text>
          </VStack>
        </Box>

        <Box
          p={4}
          bg={cardBg}
          borderRadius="md"
          border="1px solid"
          borderColor={borderColor}
          textAlign="center"
        >
          <VStack spacing={2}>
            <FiClock size={20} color={progressSummary.overdue_milestones > 0 ? "#e53e3e" : "#38a169"} />
            <Text fontSize="2xl" fontWeight="bold">
              {progressSummary.overdue_milestones}
            </Text>
            <Text fontSize="sm" color="gray.600">
              Overdue Items
            </Text>
          </VStack>
        </Box>
      </SimpleGrid>

      {/* Next Actions */}
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
        {/* Next Milestone */}
        {progressSummary.next_milestone && (
          <Box
            p={4}
            bg={cardBg}
            borderRadius="md"
            border="1px solid"
            borderColor={borderColor}
          >
            <VStack align="start" spacing={3}>
              <HStack spacing={2}>
                <FiFlag />
                <Text fontSize="md" fontWeight="bold">
                  Next Milestone
                </Text>
              </HStack>
              
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={1}>
                  {progressSummary.next_milestone.title}
                </Text>
                <HStack spacing={2} mb={2}>
                  <Badge
                    variant="subtle"
                    colorScheme={getMilestoneStatusInfo(progressSummary.next_milestone.status).color}
                    fontSize="xs"
                  >
                    {getMilestoneStatusInfo(progressSummary.next_milestone.status).label}
                  </Badge>
                  {progressSummary.next_milestone.target_date && (
                    <Text fontSize="xs" color="gray.600">
                      Due: {new Date(progressSummary.next_milestone.target_date).toLocaleDateString()}
                    </Text>
                  )}
                </HStack>
                {progressSummary.next_milestone.progress_percentage > 0 && (
                  <Progress 
                    value={progressSummary.next_milestone.progress_percentage} 
                    size="sm"
                    colorScheme={getProgressColor(progressSummary.next_milestone.progress_percentage)}
                  />
                )}
              </Box>
            </VStack>
          </Box>
        )}

        {/* Next Deadline */}
        {progressSummary.next_deadline && (
          <Box
            p={4}
            bg={cardBg}
            borderRadius="md"
            border="1px solid"
            borderColor={borderColor}
          >
            <VStack align="start" spacing={3}>
              <HStack spacing={2}>
                <FiClock />
                <Text fontSize="md" fontWeight="bold">
                  Next Deadline
                </Text>
              </HStack>
              
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={1}>
                  {progressSummary.next_deadline.title}
                </Text>
                <HStack spacing={2} mb={2}>
                  <Badge
                    variant="subtle"
                    colorScheme={getDeadlineTypeInfo(progressSummary.next_deadline.deadline_type).color}
                    fontSize="xs"
                  >
                    {getDeadlineTypeInfo(progressSummary.next_deadline.deadline_type).label}
                  </Badge>
                  {progressSummary.next_deadline.is_critical && (
                    <Badge variant="solid" colorScheme="red" fontSize="xs">
                      Critical
                    </Badge>
                  )}
                </HStack>
                <HStack spacing={2}>
                  <Text fontSize="xs" color="gray.600">
                    {new Date(progressSummary.next_deadline.deadline_date).toLocaleDateString()}
                  </Text>
                  <Badge
                    variant="subtle"
                    colorScheme={formatDeadlineUrgency(progressSummary.next_deadline.deadline_date).color}
                    fontSize="xs"
                  >
                    {formatDeadlineUrgency(progressSummary.next_deadline.deadline_date).label}
                  </Badge>
                </HStack>
              </Box>
            </VStack>
          </Box>
        )}

        {/* No next items message */}
        {!progressSummary.next_milestone && !progressSummary.next_deadline && (
          <Box
            p={4}
            bg={cardBg}
            borderRadius="md"
            border="1px solid"
            borderColor={borderColor}
            textAlign="center"
            gridColumn={{ md: "1 / -1" }}
          >
            <Text fontSize="sm" color="gray.600">
              🎉 All milestones completed and no upcoming deadlines!
            </Text>
          </Box>
        )}
      </SimpleGrid>

      {/* Progress Insights */}
      <Box
        p={4}
        bg="blue.50"
        borderRadius="md"
        border="1px solid"
        borderColor="blue.200"
      >
        <VStack align="start" spacing={2}>
          <Text fontSize="sm" fontWeight="bold" color="blue.700">
            📊 Progress Insights
          </Text>
          <VStack align="start" spacing={1} fontSize="xs" color="blue.600">
            {progressSummary.progress_percentage === 100 ? (
              <Text>✅ Case is fully complete! All milestones have been achieved.</Text>
            ) : progressSummary.progress_percentage >= 75 ? (
              <Text>🎯 Case is nearing completion. Focus on remaining milestones.</Text>
            ) : progressSummary.progress_percentage >= 50 ? (
              <Text>📈 Case is progressing well. Keep up the momentum.</Text>
            ) : progressSummary.progress_percentage >= 25 ? (
              <Text>🚀 Case is in early stages. Consider setting more milestones.</Text>
            ) : (
              <Text>📋 Case is just getting started. Add milestones to track progress.</Text>
            )}
            
            {progressSummary.overdue_milestones > 0 && (
              <Text>⚠️ {progressSummary.overdue_milestones} milestone(s) are overdue and need attention.</Text>
            )}
            
            {progressSummary.critical_deadlines > 0 && (
              <Text>🚨 {progressSummary.critical_deadlines} critical deadline(s) require immediate action.</Text>
            )}
            
            {progressSummary.upcoming_deadlines > 0 && progressSummary.critical_deadlines === 0 && (
              <Text>📅 {progressSummary.upcoming_deadlines} deadline(s) coming up in the next 30 days.</Text>
            )}
          </VStack>
        </VStack>
      </Box>
    </VStack>
  )
}

export default ProgressVisualization
