import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  Progress,






  Tooltip,
  SimpleGrid,
  Alert,
  AlertIcon,
} from "@chakra-ui/react"
import {
  FiBarChart3,
  FiTrendingUp,
  FiTrendingDown,
  FiMinus,
  FiTarget,
  FiActivity,
  FiClock,
} from "react-icons/fi"

import { ComparativeAnalytics, formatDuration } from "@/client/case-analytics"

interface ComparativeAnalyticsCardProps {
  analytics: ComparativeAnalytics
}

const ComparativeAnalyticsCard = ({ analytics }: ComparativeAnalyticsCardProps) => {
  const getComparisonColor = (comparison: string) => {
    switch (comparison) {
      case "above_average":
        return "green"
      case "below_average":
        return "orange"
      default:
        return "gray"
    }
  }

  const getComparisonIcon = (comparison: string) => {
    switch (comparison) {
      case "above_average":
        return <FiTrendingUp />
      case "below_average":
        return <FiTrendingDown />
      default:
        return <FiMinus />
    }
  }

  const getComparisonLabel = (comparison: string) => {
    switch (comparison) {
      case "above_average":
        return "Above Average"
      case "below_average":
        return "Below Average"
      default:
        return "Average"
    }
  }

  const getPercentileColor = (percentile: number) => {
    if (percentile >= 80) return "green"
    if (percentile >= 60) return "blue"
    if (percentile >= 40) return "yellow"
    if (percentile >= 20) return "orange"
    return "red"
  }

  const getPercentileLabel = (percentile: number) => {
    if (percentile >= 90) return "Top 10%"
    if (percentile >= 75) return "Top 25%"
    if (percentile >= 50) return "Above Median"
    if (percentile >= 25) return "Below Median"
    return "Bottom 25%"
  }

  // Check if we have valid comparison data
  if (!analytics.comparison_metrics) {
    return (
      <Card>
        <CardHeader pb={2}>
          <HStack spacing={2}>
            <FiBarChart3 />
            <Heading size="sm">Comparative Analytics</Heading>
          </HStack>
        </CardHeader>
        
        <CardBody pt={2}>
          <Alert status="info" borderRadius="md">
            <AlertIcon />
            <Box>
              <Text fontSize="sm" fontWeight="medium">
                No Comparison Data Available
              </Text>
              <Text fontSize="xs">
                Not enough similar cases found for meaningful comparison.
              </Text>
            </Box>
          </Alert>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiBarChart3 />
          <Heading size="sm">Comparative Analytics</Heading>
          <Badge variant="subtle" colorScheme="blue" fontSize="xs">
            vs {analytics.similar_cases_count} similar cases
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Comparison Overview */}
          <Alert status="info" borderRadius="md" py={2}>
            <AlertIcon />
            <Box>
              <Text fontSize="sm" fontWeight="medium">
                Compared to {analytics.similar_cases_count} Similar Cases
              </Text>
              <Text fontSize="xs">
                Analysis based on cases with similar type, lawyer, or priority
              </Text>
            </Box>
          </Alert>

          {/* Key Metrics Comparison */}
          <SimpleGrid columns={2} spacing={4}>
            <Box textAlign="center">
              <Box textAlign="center">Duration Ranking</Text>
              <Box textAlign="center">
                {analytics.percentile_ranking.duration}%
              </Text>
              <Box textAlign="center">
                {getPercentileLabel(analytics.percentile_ranking.duration)}
              </Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">Activity Ranking</Text>
              <Box textAlign="center">
                {analytics.percentile_ranking.activity}%
              </Text>
              <Box textAlign="center">
                {getPercentileLabel(analytics.percentile_ranking.activity)}
              </Text>
            </Box>
          </SimpleGrid>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Detailed Comparisons */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Performance vs Average
            </Text>
            
            <VStack spacing={3} align="stretch">
              {/* Duration Comparison */}
              <Box>
                <HStack justify="space-between" mb={2}>
                  <Tooltip label="Case duration compared to similar cases">
                    <HStack spacing={2}>
                      <FiClock size={14} color="blue" />
                      <Text fontSize="sm">Duration:</Text>
                    </HStack>
                  </Tooltip>
                  <Badge 
                    colorScheme={getComparisonColor(analytics.performance_vs_average.duration_comparison)}
                    variant="subtle"
                    fontSize="xs"
                  >
                    {getComparisonIcon(analytics.performance_vs_average.duration_comparison)}
                    <Text ml={1}>
                      {getComparisonLabel(analytics.performance_vs_average.duration_comparison)}
                    </Text>
                  </Badge>
                </HStack>
                
                <VStack spacing={1} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="xs" color="gray.600">Current Age:</Text>
                    <Text fontSize="xs" fontWeight="medium">
                      {formatDuration(analytics.comparison_metrics.current_case_age_days)}
                    </Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text fontSize="xs" color="gray.600">Average Duration:</Text>
                    <Text fontSize="xs" fontWeight="medium">
                      {formatDuration(analytics.comparison_metrics.average_duration_days)}
                    </Text>
                  </HStack>
                </VStack>
                
                <Progress 
                  value={analytics.percentile_ranking.duration} 
                  colorScheme={getPercentileColor(analytics.percentile_ranking.duration)} 
                  size="sm" 
                  borderRadius="full"
                  mt={2}
                />
              </Box>

              {/* Activity Comparison */}
              <Box>
                <HStack justify="space-between" mb={2}>
                  <Tooltip label="Activity level compared to similar cases">
                    <HStack spacing={2}>
                      <FiActivity size={14} color="green" />
                      <Text fontSize="sm">Activity Level:</Text>
                    </HStack>
                  </Tooltip>
                  <Badge 
                    colorScheme={getComparisonColor(analytics.performance_vs_average.activity_comparison)}
                    variant="subtle"
                    fontSize="xs"
                  >
                    {getComparisonIcon(analytics.performance_vs_average.activity_comparison)}
                    <Text ml={1}>
                      {getComparisonLabel(analytics.performance_vs_average.activity_comparison)}
                    </Text>
                  </Badge>
                </HStack>
                
                <VStack spacing={1} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="xs" color="gray.600">Current Activities:</Text>
                    <Text fontSize="xs" fontWeight="medium">
                      {analytics.comparison_metrics.current_activities}
                    </Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text fontSize="xs" color="gray.600">Average Activities:</Text>
                    <Text fontSize="xs" fontWeight="medium">
                      {analytics.comparison_metrics.average_activities.toFixed(1)}
                    </Text>
                  </HStack>
                </VStack>
                
                <Progress 
                  value={analytics.percentile_ranking.activity} 
                  colorScheme={getPercentileColor(analytics.percentile_ranking.activity)} 
                  size="sm" 
                  borderRadius="full"
                  mt={2}
                />
              </Box>
            </VStack>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Percentile Rankings */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Percentile Rankings
            </Text>
            
            <SimpleGrid columns={2} spacing={4}>
              <Box textAlign="center" p={3} bg="blue.50" borderRadius="md">
                <Text fontSize="xs" color="gray.600" mb={1}>Duration</Text>
                <Text fontSize="lg" fontWeight="bold" color={`${getPercentileColor(analytics.percentile_ranking.duration)}.600`}>
                  {analytics.percentile_ranking.duration}%
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {getPercentileLabel(analytics.percentile_ranking.duration)}
                </Text>
              </Box>
              
              <Box textAlign="center" p={3} bg="green.50" borderRadius="md">
                <Text fontSize="xs" color="gray.600" mb={1}>Activity</Text>
                <Text fontSize="lg" fontWeight="bold" color={`${getPercentileColor(analytics.percentile_ranking.activity)}.600`}>
                  {analytics.percentile_ranking.activity}%
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {getPercentileLabel(analytics.percentile_ranking.activity)}
                </Text>
              </Box>
            </SimpleGrid>
          </Box>

          {/* Comparative Insights */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Comparative Insights:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {analytics.percentile_ranking.duration >= 75 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Case duration is longer than most similar cases
                </Text>
              )}
              
              {analytics.percentile_ranking.duration <= 25 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Case is progressing faster than most similar cases
                </Text>
              )}
              
              {analytics.percentile_ranking.activity >= 75 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Higher activity level than most similar cases
                </Text>
              )}
              
              {analytics.percentile_ranking.activity <= 25 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Lower activity level than most similar cases
                </Text>
              )}
              
              {analytics.performance_vs_average.duration_comparison === "above_average" && 
               analytics.performance_vs_average.activity_comparison === "above_average" && (
                <Text fontSize="xs" color="blue.600">
                  📊 High engagement but longer duration - thorough approach
                </Text>
              )}
              
              {analytics.performance_vs_average.duration_comparison === "below_average" && 
               analytics.performance_vs_average.activity_comparison === "above_average" && (
                <Text fontSize="xs" color="green.600">
                  ⚡ Efficient case handling with high activity
                </Text>
              )}
              
              {analytics.similar_cases_count < 5 && (
                <Text fontSize="xs" color="yellow.600">
                  ⚠ Limited comparison data - results may vary
                </Text>
              )}
              
              {analytics.similar_cases_count >= 20 && (
                <Text fontSize="xs" color="blue.600">
                  📈 Strong comparison dataset for reliable insights
                </Text>
              )}
            </VStack>
          </Box>

          {/* Benchmark Summary */}
          <HStack justify="space-around" bg="purple.50" p={2} borderRadius="md">
            <Box textAlign="center">
              <Box textAlign="center">
                {analytics.similar_cases_count}
              </Text>
              <Box textAlign="center">Similar Cases</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {((analytics.percentile_ranking.duration + analytics.percentile_ranking.activity) / 2).toFixed(0)}%
              </Text>
              <Box textAlign="center">Avg Ranking</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {analytics.performance_vs_average.duration_comparison === "above_average" && 
                 analytics.performance_vs_average.activity_comparison === "above_average" ? "A" :
                 analytics.performance_vs_average.duration_comparison === "below_average" && 
                 analytics.performance_vs_average.activity_comparison === "above_average" ? "A+" :
                 analytics.performance_vs_average.duration_comparison === "above_average" && 
                 analytics.performance_vs_average.activity_comparison === "below_average" ? "B" : "C"}
              </Text>
              <Box textAlign="center">Grade</Text>
            </Box>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default ComparativeAnalyticsCard
