import {
  <PERSON>,
  <PERSON><PERSON>tack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Badge,
  IconButton,
  Tooltip,
  useColorModeValue,
  Link,
} from "@chakra-ui/react"
import {
  FiDownload,
  <PERSON>Eye,
  Fi<PERSON>ser,
  <PERSON><PERSON>lock,
  FiFolder,
  FiLock,
  FiShare2,
} from "react-icons/fi"

import { 
  LinkedDocument, 
  getLinkTypeInfo,
  formatLinkDate 
} from "@/client/document-case-links"
import { formatFileSize, getCategoryInfo } from "@/client/case-documents"

interface LinkedDocumentCardProps {
  document: LinkedDocument
  showLinkType?: boolean
}

const LinkedDocumentCard = ({ document, showLinkType = true }: LinkedDocumentCardProps) => {
  const cardBg = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const hoverBg = useColorModeValue("gray.50", "gray.700")

  const linkTypeInfo = getLinkTypeInfo(document.link.link_type)
  const categoryInfo = getCategoryInfo(document.category)

  const handleDownload = () => {
    // Create download link
    const downloadUrl = `/api/v1/legal-cases/${document.case_id}/documents/${document.id}/download`
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = document.original_filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handlePreview = () => {
    // Open document in new tab for preview
    const previewUrl = `/api/v1/legal-cases/${document.case_id}/documents/${document.id}/download`
    window.open(previewUrl, '_blank')
  }

  return (
    <Box
      bg={cardBg}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="lg"
      p={4}
      transition="all 0.2s"
      _hover={{ 
        shadow: "md", 
        bg: hoverBg,
        transform: "translateY(-1px)" 
      }}
    >
      <HStack spacing={4} align="start">
        {/* Document Icon */}
        <Box
          fontSize="2xl"
          color={`${categoryInfo.color}.500`}
          minW="40px"
          textAlign="center"
        >
          {categoryInfo.icon}
        </Box>

        {/* Document Info */}
        <VStack align="start" spacing={2} flex={1}>
          {/* Document Name and Actions */}
          <HStack spacing={2} align="center" w="full">
            <Text
              fontWeight="medium"
              fontSize="md"
              noOfLines={1}
              flex={1}
              title={document.original_filename}
            >
              {document.original_filename}
            </Text>
            
            <HStack spacing={1}>
              <Tooltip label="Preview document">
                <IconButton
                  variant="ghost"
                  size="sm"
                  icon={<FiEye />}
                  onClick={handlePreview}
                  aria-label="Preview document"
                />
              </Tooltip>
              <Tooltip label="Download document">
                <IconButton
                  variant="ghost"
                  size="sm"
                  icon={<FiDownload />}
                  onClick={handleDownload}
                  aria-label="Download document"
                />
              </Tooltip>
            </HStack>
          </HStack>

          {/* Document Details */}
          <HStack spacing={4} fontSize="sm" color="gray.600" wrap="wrap">
            <Text>{formatFileSize(document.file_size)}</Text>
            <Text>{document.content_type}</Text>
            
            <Badge
              colorScheme={categoryInfo.color}
              variant="subtle"
              fontSize="xs"
            >
              {categoryInfo.label}
            </Badge>

            {showLinkType && (
              <Badge
                colorScheme={linkTypeInfo.color}
                variant="outline"
                fontSize="xs"
              >
                {linkTypeInfo.icon} {linkTypeInfo.label}
              </Badge>
            )}

            {document.is_confidential && (
              <Badge colorScheme="red" variant="subtle" fontSize="xs">
                <FiLock size={10} style={{ marginRight: '2px' }} />
                Confidential
              </Badge>
            )}

            {document.is_shared_with_client && (
              <Badge colorScheme="green" variant="subtle" fontSize="xs">
                <FiShare2 size={10} style={{ marginRight: '2px' }} />
                Shared
              </Badge>
            )}
          </HStack>

          {/* Description */}
          {document.description && (
            <Text fontSize="sm" color="gray.700" noOfLines={2}>
              {document.description}
            </Text>
          )}

          {/* Link Notes */}
          {document.link.notes && (
            <Text fontSize="sm" color="blue.700" fontStyle="italic" noOfLines={2}>
              Link note: "{document.link.notes}"
            </Text>
          )}

          {/* Tags */}
          {document.tags && document.tags.length > 0 && (
            <HStack spacing={1} wrap="wrap">
              {document.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" fontSize="xs">
                  {tag}
                </Badge>
              ))}
              {document.tags.length > 3 && (
                <Badge variant="outline" fontSize="xs" color="gray.500">
                  +{document.tags.length - 3} more
                </Badge>
              )}
            </HStack>
          )}

          {/* Metadata */}
          <HStack spacing={4} fontSize="xs" color="gray.500">
            {document.uploader && (
              <HStack spacing={1}>
                <FiUser size={12} />
                <Text>
                  Uploaded by {document.uploader.full_name || document.uploader.email}
                </Text>
              </HStack>
            )}
            <HStack spacing={1}>
              <FiClock size={12} />
              <Text>
                Linked {formatLinkDate(document.link.linked_at)}
              </Text>
            </HStack>
            {document.folder_id && (
              <HStack spacing={1}>
                <FiFolder size={12} />
                <Text>In folder</Text>
              </HStack>
            )}
          </HStack>
        </VStack>
      </HStack>
    </Box>
  )
}

export default LinkedDocumentCard
