import { toaster } from "@/components/ui/toaster"

// Hook pour utiliser facilement les toasts de changement de statut
export const useStatusChangeToast = () => {
  const showStatusChangeToast = (
    caseTitle: string,
    oldStatus: string,
    newStatus: string,
    isSuccess: boolean
  ) => {
    toaster.create({
      title: isSuccess ? 'Status Updated Successfully' : 'Status Update Failed',
      description: `${caseTitle}: ${oldStatus} → ${newStatus}`,
      type: isSuccess ? 'success' : 'error',
      duration: isSuccess ? 3000 : 5000,
    })
  }

  const showBulkStatusChangeToast = (
    oldStatus: string,
    newStatus: string,
    successCount: number,
    failedCount: number
  ) => {
    if (successCount > 0) {
      toaster.create({
        title: 'Bulk Status Update Successful',
        description: `${successCount} case${successCount !== 1 ? 's' : ''} updated: ${oldStatus} → ${newStatus}`,
        type: 'success',
        duration: 3000,
      })
    }

    if (failedCount > 0) {
      toaster.create({
        title: 'Bulk Status Update Failed',
        description: `${failedCount} case${failedCount !== 1 ? 's' : ''} failed to update`,
        type: 'error',
        duration: 5000,
      })
    }
  }

  return {
    showStatusChangeToast,
    showBulkStatusChangeToast
  }
}
