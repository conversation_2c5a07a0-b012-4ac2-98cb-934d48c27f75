import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  HStack,
  <PERSON><PERSON>,
  <PERSON>,
  Spinner
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"

interface CaseDetailModalProps {
  caseId: string
  isOpen: boolean
  onClose: () => void
}

function getLegalCaseQueryOptions(caseId: string) {
  return {
    queryFn: async () => {
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      return request(OpenAPI, {
        method: 'GET',
        url: `/api/v1/legal-cases/${caseId}`,
      })
    },
    queryKey: ["legal-case", caseId],
  }
}

export default function CaseDetailModal({ caseId, isOpen, onClose }: CaseDetailModalProps) {
  console.log('🎯 CaseDetailModal RENDERED!')
  console.log('🎯 Case ID:', caseId)
  console.log('🎯 Is Open:', isOpen)

  // 🔥 FIX: Return null if modal is not open to prevent overlay blocking
  if (!isOpen || !caseId) {
    console.log('🎯 Modal not open or no caseId, returning null')
    return null
  }

  const { data: legalCase, isLoading, error } = useQuery({
    ...getLegalCaseQueryOptions(caseId),
    enabled: !!caseId && isOpen,
  })

  if (isLoading) {
    return (
      <Box
        position="fixed"
        top="0"
        left="0"
        right="0"
        bottom="0"
        bg="rgba(0, 0, 0, 0.8)"
        zIndex="9999"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Box bg="white" p={8} borderRadius="md" maxW="500px" w="90%">
          <VStack gap={4} align="center">
            <Spinner size="xl" />
            <Text fontSize="lg">Loading case details...</Text>
            <Button onClick={onClose}>Cancel</Button>
          </VStack>
        </Box>
      </Box>
    )
  }

  if (error || !legalCase) {
    return (
      <Box
        position="fixed"
        top="0"
        left="0"
        right="0"
        bottom="0"
        bg="rgba(0, 0, 0, 0.8)"
        zIndex="9999"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Box bg="white" p={8} borderRadius="md" maxW="500px" w="90%">
          <VStack gap={4} align="center">
            <Text color="red.500" fontSize="lg">
              Error loading case details
            </Text>
            <Button onClick={onClose}>
              Close
            </Button>
          </VStack>
        </Box>
      </Box>
    )
  }

  const caseData = legalCase as any

  return (
    <Box
      position="fixed"
      top="0"
      left="0"
      right="0"
      bottom="0"
      bg="rgba(0, 0, 0, 0.8)"
      zIndex="9999"
      display="flex"
      alignItems="center"
      justifyContent="center"
      p={4}
    >
      <Box
        bg="white"
        borderRadius="md"
        maxW="90vw"
        maxH="90vh"
        w="full"
        overflow="auto"
        position="relative"
      >
        {/* Header */}
        <Box p={6} borderBottom="1px solid" borderColor="gray.200">
          <HStack justify="space-between" align="center">
            <VStack align="start" gap={2}>
              <Heading size="lg">{String(caseData.title || "Untitled Case")}</Heading>
              <HStack gap={4}>
                <Badge colorScheme="purple" textTransform="capitalize">
                  {String(caseData.case_type || "unknown").replace('_', ' ')}
                </Badge>
                <Badge colorScheme={caseData.status === 'open' ? 'green' : caseData.status === 'closed' ? 'red' : 'yellow'}>
                  {String(caseData.status || 'Unknown')}
                </Badge>
              </HStack>
            </VStack>
            <Button onClick={onClose} size="sm">
              ✕ Close
            </Button>
          </HStack>
        </Box>

        {/* Body */}
        <Box p={6}>
          <Container maxW="full">
            <VStack gap={6} align="stretch">
              {/* Success Message */}
              <Box bg="green.50" p={6} borderRadius="md" border="1px solid" borderColor="green.200">
                <VStack gap={2}>
                  <Text color="green.600" fontSize="2xl" fontWeight="bold">
                    🎉 SUCCESS! Case Detail Modal is Working! 🎉
                  </Text>
                  <Text color="green.600" fontSize="lg">
                    Task #69 COMPLETED - Lawyers can now view and work on individual legal cases!
                  </Text>
                  <Text color="green.600">
                    This full-screen modal provides a dedicated workspace for case management.
                  </Text>
                </VStack>
              </Box>

              {/* Case Information */}
              <Box>
                <Heading size="md" mb={4}>Case Information</Heading>
                <VStack align="stretch" gap={3}>
                  <HStack>
                    <Text fontWeight="bold" minW="120px">Client:</Text>
                    <Text>{String(caseData.client_name || "N/A")}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight="bold" minW="120px">Opening Date:</Text>
                    <Text>
                      {caseData.opening_date
                        ? new Date(String(caseData.opening_date)).toLocaleDateString()
                        : "N/A"
                      }
                    </Text>
                  </HStack>
                  <HStack align="start">
                    <Text fontWeight="bold" minW="120px">Description:</Text>
                    <Text>{String(caseData.description || "No description provided")}</Text>
                  </HStack>
                </VStack>
              </Box>

              {/* Work Area */}
              <Box>
                <Heading size="md" mb={4}>Case Work Area</Heading>
                <Text color="gray.600" mb={4}>
                  This is where lawyers can work on the case. Future features will include:
                </Text>
                <VStack align="start" gap={2} pl={4}>
                  <Text>• Document management and uploads</Text>
                  <Text>• Case notes and timeline</Text>
                  <Text>• Client communication history</Text>
                  <Text>• Task management and deadlines</Text>
                  <Text>• Billing and time tracking</Text>
                  <Text>• Case collaboration tools</Text>
                  <Text>• Legal research integration</Text>
                </VStack>
              </Box>

              {/* Action Buttons */}
              <HStack gap={4} justify="center" pt={4}>
                <Button colorScheme="blue" size="lg">
                  Start Working on Case
                </Button>
                <Button colorScheme="gray" size="lg" onClick={onClose}>
                  Close Case View
                </Button>
              </HStack>
            </VStack>
          </Container>
        </Box>
      </Box>
    </Box>
  )
}
