import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  Avatar,
  AvatarGroup,
  Progress,





  Tooltip,
  SimpleGrid,
  CircularProgress,
  CircularProgressLabel,
} from "@chakra-ui/react"
import {
  FiUsers,
  FiMessageSquare,
  FiActivity,
  FiStar,
  FiTrendingUp,
  FiUser,
} from "react-icons/fi"

import { CollaborationAnalytics } from "@/client/case-analytics"

interface CollaborationAnalyticsCardProps {
  analytics: CollaborationAnalytics
}

const CollaborationAnalyticsCard = ({ analytics }: CollaborationAnalyticsCardProps) => {
  const getCollaborationScoreColor = (score: number) => {
    if (score >= 80) return "green"
    if (score >= 60) return "blue"
    if (score >= 40) return "yellow"
    if (score >= 20) return "orange"
    return "red"
  }

  const getCollaborationLevel = (score: number) => {
    if (score >= 80) return "Excellent"
    if (score >= 60) return "Good"
    if (score >= 40) return "Moderate"
    if (score >= 20) return "Low"
    return "Minimal"
  }

  const getRoleColor = (role: string) => {
    const colors: Record<string, string> = {
      admin: "purple",
      lawyer: "blue",
      assistant: "green",
      client: "orange",
    }
    return colors[role] || "gray"
  }

  const getRoleIcon = (role: string) => {
    const icons: Record<string, string> = {
      admin: "👑",
      lawyer: "⚖️",
      assistant: "🤝",
      client: "👤",
    }
    return icons[role] || "👤"
  }

  // Sort team members by activity count
  const sortedMembers = analytics.team_members.sort((a, b) => {
    const aActivity = analytics.user_interactions[a.user_id]?.activity_count || 0
    const bActivity = analytics.user_interactions[b.user_id]?.activity_count || 0
    return bActivity - aActivity
  })

  // Sort notes collaboration
  const sortedNotesCollaboration = Object.entries(analytics.notes_collaboration)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)

  const totalActivities = Object.values(analytics.user_interactions)
    .reduce((sum, user) => sum + user.activity_count, 0)

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiUsers />
          <Heading size="sm">Team Collaboration</Heading>
          <Badge 
            colorScheme={getCollaborationScoreColor(analytics.collaboration_score)} 
            variant="subtle"
            fontSize="xs"
          >
            {getCollaborationLevel(analytics.collaboration_score)}
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Collaboration Score */}
          <Box textAlign="center">
            <CircularProgress 
              value={Math.min(100, analytics.collaboration_score)} 
              color={`${getCollaborationScoreColor(analytics.collaboration_score)}.400`}
              size="80px"
              thickness="8px"
            >
              <CircularProgressLabel fontSize="sm" fontWeight="bold">
                {Math.round(analytics.collaboration_score)}
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="sm" color="gray.600" mt={2}>
              Collaboration Score
            </Text>
            <Text fontSize="xs" color="gray.500">
              {getCollaborationLevel(analytics.collaboration_score)} team engagement
            </Text>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Team Overview */}
          <SimpleGrid columns={2} spacing={4}>
            <Box textAlign="center">
              <Box textAlign="center">Team Size</Text>
              <Box textAlign="center">
                {analytics.team_size}
              </Text>
              <Box textAlign="center">
                Active members
              </Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">Total Activity</Text>
              <Box textAlign="center">
                {totalActivities}
              </Text>
              <Box textAlign="center">
                Team interactions
              </Text>
            </Box>
          </SimpleGrid>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Team Members */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Team Members
            </Text>
            
            {sortedMembers.length > 0 ? (
              <VStack spacing={2} align="stretch">
                {sortedMembers.slice(0, 5).map((member) => {
                  const userActivity = analytics.user_interactions[member.user_id]
                  const activityCount = userActivity?.activity_count || 0
                  const activityPercentage = totalActivities > 0 
                    ? (activityCount / totalActivities) * 100 
                    : 0
                  
                  return (
                    <HStack key={member.user_id} justify="space-between">
                      <HStack spacing={2} flex={1}>
                        <Avatar size="xs" name={member.name} />
                        <VStack align="start" spacing={0} flex={1}>
                          <HStack spacing={2}>
                            <Text fontSize="xs" fontWeight="medium" noOfLines={1}>
                              {member.name}
                            </Text>
                            <Badge 
                              size="xs" 
                              colorScheme={getRoleColor(member.role)}
                              variant="subtle"
                            >
                              {getRoleIcon(member.role)} {member.role}
                            </Badge>
                          </HStack>
                          <Text fontSize="xs" color="gray.500">
                            {activityCount} activities ({activityPercentage.toFixed(1)}%)
                          </Text>
                        </VStack>
                      </HStack>
                      <Progress 
                        value={activityPercentage} 
                        colorScheme={getRoleColor(member.role)} 
                        size="sm" 
                        width="60px"
                        borderRadius="full"
                      />
                    </HStack>
                  )
                })}
                
                {sortedMembers.length > 5 && (
                  <Text fontSize="xs" color="gray.500" textAlign="center">
                    +{sortedMembers.length - 5} more team members
                  </Text>
                )}
              </VStack>
            ) : (
              <Text fontSize="sm" color="gray.500" textAlign="center">
                No team activity data available
              </Text>
            )}
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Notes Collaboration */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Notes & Communication
            </Text>
            
            {sortedNotesCollaboration.length > 0 ? (
              <VStack spacing={2} align="stretch">
                {sortedNotesCollaboration.map(([userName, noteCount]) => {
                  const totalNotes = Object.values(analytics.notes_collaboration)
                    .reduce((sum, count) => sum + count, 0)
                  const percentage = totalNotes > 0 ? (noteCount / totalNotes) * 100 : 0
                  
                  return (
                    <HStack key={userName} justify="space-between">
                      <HStack spacing={2} flex={1}>
                        <FiMessageSquare size={12} color="purple" />
                        <Text fontSize="xs" color="gray.600" noOfLines={1}>
                          {userName}
                        </Text>
                      </HStack>
                      <HStack spacing={2}>
                        <Text fontSize="xs" color="gray.500">
                          {noteCount}
                        </Text>
                        <Text fontSize="xs" fontWeight="medium">
                          {percentage.toFixed(1)}%
                        </Text>
                      </HStack>
                    </HStack>
                  )
                })}
              </VStack>
            ) : (
              <Text fontSize="sm" color="gray.500" textAlign="center">
                No notes collaboration data
              </Text>
            )}
          </Box>

          {/* Team Avatars */}
          <Box textAlign="center">
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={2}>
              Active Team
            </Text>
            <AvatarGroup size="sm" max={6}>
              {sortedMembers.map((member) => (
                <Tooltip key={member.user_id} label={`${member.name} (${member.role})`}>
                  <Avatar name={member.name} />
                </Tooltip>
              ))}
            </AvatarGroup>
          </Box>

          {/* Collaboration Insights */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Collaboration Insights:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {analytics.team_size === 1 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Single person working on case
                </Text>
              )}
              
              {analytics.team_size > 3 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Good team size for collaboration
                </Text>
              )}
              
              {analytics.collaboration_score >= 80 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Excellent team collaboration
                </Text>
              )}
              
              {analytics.collaboration_score < 40 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Low collaboration - consider team engagement
                </Text>
              )}
              
              {Object.keys(analytics.notes_collaboration).length > 2 && (
                <Text fontSize="xs" color="purple.600">
                  💬 Active communication through notes
                </Text>
              )}
              
              {totalActivities > 50 && (
                <Text fontSize="xs" color="blue.600">
                  📈 High team activity level
                </Text>
              )}
              
              {sortedMembers.some(m => m.role === "client") && (
                <Text fontSize="xs" color="teal.600">
                  👤 Client actively involved in case
                </Text>
              )}
            </VStack>
          </Box>

          {/* Quick Stats */}
          <HStack justify="space-around" bg="purple.50" p={2} borderRadius="md">
            <Box textAlign="center">
              <Box textAlign="center">
                {analytics.team_size}
              </Text>
              <Box textAlign="center">Members</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {(totalActivities / Math.max(1, analytics.team_size)).toFixed(0)}
              </Text>
              <Box textAlign="center">Avg Activity</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {Object.values(analytics.notes_collaboration).reduce((sum, count) => sum + count, 0)}
              </Text>
              <Box textAlign="center">Notes</Text>
            </Box>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default CollaborationAnalyticsCard
