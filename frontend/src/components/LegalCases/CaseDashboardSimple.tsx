import {
  Box,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Button,
  Flex,
  Spacer,
  Badge,
  IconButton,
  Tooltip,
  Alert,

  <PERSON>ner,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Progress,

} from "@chakra-ui/react"
import {
  FiRefreshCw,
  FiDownload,
  FiBarChart,
  FiTrendingUp,
  FiClock,
  FiUsers,
  FiFileText,
  FiTarget,
  FiActivity,
  FiSettings,
  FiHeart,
  FiDollarSign,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"

import { CaseAnalyticsService, getHealthStatusInfo } from "@/client/case-analytics"

interface CaseDashboardSimpleProps {
  caseId: string
  caseTitle?: string
}

const CaseDashboardSimple = ({ 
  caseId, 
  caseTitle
}: CaseDashboardSimpleProps) => {
  const [periodDays, setPeriodDays] = useState(30)

  // Fetch case analytics
  const { 
    data: analytics, 
    isLoading, 
    error, 
    refetch,
    isFetching 
  } = useQuery({
    queryKey: ["case-analytics", caseId, periodDays],
    queryFn: () => CaseAnalyticsService.getCaseAnalytics(caseId, {
      period_days: periodDays,
      include_comparisons: true
    }),
    enabled: !!caseId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })

  if (error) {
    return (
      <Alert status="error" borderRadius="lg">
        <Box>
          <Text fontWeight="bold">❌ Failed to load case analytics</Text>
          <Text fontSize="sm">
            {error instanceof Error ? error.message : "An unexpected error occurred"}
          </Text>
        </Box>
        <Spacer />
        <Button size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  if (isLoading) {
    return (
      <Box textAlign="center" py={12}>
        <Spinner size="xl" color="blue.500" thickness="4px" />
        <Text mt={4} fontSize="lg" color="gray.600">
          Loading case analytics...
        </Text>
        <Text fontSize="sm" color="gray.500">
          Analyzing case data and generating insights
        </Text>
      </Box>
    )
  }

  if (!analytics) {
    return (
      <Alert status="info" borderRadius="lg">
        <Box>
          <Text fontWeight="bold">ℹ️ No analytics data available</Text>
          <Text fontSize="sm">
            Analytics data could not be generated for this case.
          </Text>
        </Box>
      </Alert>
    )
  }

  const healthInfo = getHealthStatusInfo(analytics.health_indicators.health_status)

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={3}>
            <FiBarChart size={24} />
            <Heading size="lg">Case Analytics Dashboard</Heading>
            <Badge 
              colorScheme={healthInfo.color} 
              variant="subtle" 
              fontSize="sm"
              px={2}
              py={1}
            >
              {healthInfo.icon} {healthInfo.label}
            </Badge>
          </HStack>
          <Text color="gray.600">
            {caseTitle || analytics.case_overview.title} • 
            Analysis for last {periodDays} days • 
            Generated {new Date(analytics.generated_at).toLocaleDateString()}
          </Text>
        </VStack>
        <Spacer />
        
        {/* Controls */}
        <HStack spacing={2}>
          {/* Period selector */}
          <HStack spacing={1}>
            {[7, 30, 90, 365].map((days) => (
              <Button
                key={days}
                size="sm"
                variant={periodDays === days ? "solid" : "ghost"}
                colorScheme={periodDays === days ? "blue" : "gray"}
                onClick={() => setPeriodDays(days)}
              >
                {days}d
              </Button>
            ))}
          </HStack>
          
          <Tooltip label="Refresh analytics">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isFetching}
            />
          </Tooltip>
        </HStack>
      </Flex>

      {/* Key Metrics Grid */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
        {/* Case Overview */}
        <Card>
          <CardHeader pb={2}>
            <HStack spacing={2}>
              <FiTarget />
              <Heading size="sm">Case Overview</Heading>
            </HStack>
          </CardHeader>
          <CardBody pt={2}>
            <VStack spacing={3} align="stretch">
              <Box textAlign="center">
                <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                  {analytics.case_overview.case_age_days}
                </Text>
                <Text fontSize="sm" color="gray.600">Days Active</Text>
              </Box>
              
              <Box height="1px" bg="gray.200" my={2} />
              
              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Activities:</Text>
                  <Text fontSize="sm" fontWeight="bold" color="blue.600">
                    {analytics.case_overview.totals.activities}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Documents:</Text>
                  <Text fontSize="sm" fontWeight="bold" color="green.600">
                    {analytics.case_overview.totals.documents}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Notes:</Text>
                  <Text fontSize="sm" fontWeight="bold" color="purple.600">
                    {analytics.case_overview.totals.notes}
                  </Text>
                </HStack>
              </VStack>
            </VStack>
          </CardBody>
        </Card>

        {/* Performance Score */}
        <Card>
          <CardHeader pb={2}>
            <HStack spacing={2}>
              <FiTrendingUp />
              <Heading size="sm">Performance</Heading>
            </HStack>
          </CardHeader>
          <CardBody pt={2}>
            <VStack spacing={3} align="stretch">
              <Box textAlign="center">
                <Text fontSize="2xl" fontWeight="bold" color="purple.600">
                  {analytics.performance_metrics.performance_score}
                </Text>
                <Text fontSize="sm" color="gray.600">Performance Score</Text>
                <Progress
                  value={analytics.performance_metrics.performance_score}
                  colorScheme="purple"
                  size="sm"
                  borderRadius="full"
                  mt={2}
                />
              </Box>
              
              <Box height="1px" bg="gray.200" my={2} />
              
              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Efficiency:</Text>
                  <Text fontSize="sm" fontWeight="bold" textTransform="capitalize">
                    {analytics.performance_metrics.efficiency_rating}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Activity Rate:</Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {analytics.performance_metrics.activity_frequency.toFixed(1)}/day
                  </Text>
                </HStack>
              </VStack>
            </VStack>
          </CardBody>
        </Card>

        {/* Health Score */}
        <Card>
          <CardHeader pb={2}>
            <HStack spacing={2}>
              <FiHeart />
              <Heading size="sm">Case Health</Heading>
            </HStack>
          </CardHeader>
          <CardBody pt={2}>
            <VStack spacing={3} align="stretch">
              <Box textAlign="center">
                <Text fontSize="2xl" fontWeight="bold" color={`${healthInfo.color}.600`}>
                  {analytics.health_indicators.health_score}
                </Text>
                <Text fontSize="sm" color="gray.600">Health Score</Text>
                <Progress
                  value={analytics.health_indicators.health_score}
                  colorScheme={healthInfo.color}
                  size="sm"
                  borderRadius="full"
                  mt={2}
                />
              </Box>
              
              <Box height="1px" bg="gray.200" my={2} />
              
              <Alert status={analytics.health_indicators.health_score >= 60 ? "success" : "warning"} size="sm" py={1}>
                <Text fontSize="xs">{healthInfo.icon} {healthInfo.label}</Text>
              </Alert>
            </VStack>
          </CardBody>
        </Card>

        {/* Progress */}
        <Card>
          <CardHeader pb={2}>
            <HStack spacing={2}>
              <FiTarget />
              <Heading size="sm">Progress</Heading>
            </HStack>
          </CardHeader>
          <CardBody pt={2}>
            <VStack spacing={3} align="stretch">
              <Box textAlign="center">
                <Text fontSize="2xl" fontWeight="bold" color="green.600">
                  {analytics.progress_analytics.progress_percentage.toFixed(0)}%
                </Text>
                <Text fontSize="sm" color="gray.600">Overall Progress</Text>
                <Progress
                  value={analytics.progress_analytics.progress_percentage}
                  colorScheme="green"
                  size="sm"
                  borderRadius="full"
                  mt={2}
                />
              </Box>
              
              <Box height="1px" bg="gray.200" my={2} />
              
              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Milestones:</Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {analytics.progress_analytics.completed_milestones}/{analytics.progress_analytics.total_milestones}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Overdue:</Text>
                  <Text fontSize="sm" fontWeight="bold" color="red.600">
                    {analytics.progress_analytics.overdue_milestones}
                  </Text>
                </HStack>
              </VStack>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Analytics Summary */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* Activity Summary */}
        <Card>
          <CardHeader>
            <HStack spacing={2}>
              <FiActivity />
              <Heading size="md">Activity Analytics</Heading>
            </HStack>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="sm" fontWeight="medium">Total Activities:</Text>
                <Text fontSize="lg" fontWeight="bold" color="blue.600">
                  {analytics.activity_analytics.total_activities}
                </Text>
              </HStack>
              
              <Box>
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm">Activity Velocity:</Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {analytics.activity_analytics.activity_velocity.toFixed(2)}/day
                  </Text>
                </HStack>
                <Progress 
                  value={Math.min(100, analytics.activity_analytics.activity_velocity * 50)} 
                  colorScheme="blue" 
                  size="sm" 
                  borderRadius="full"
                />
              </Box>
              
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={2}>Team Members:</Text>
                <Text fontSize="lg" fontWeight="bold" color="purple.600">
                  {analytics.collaboration_analytics.team_size}
                </Text>
              </Box>
            </VStack>
          </CardBody>
        </Card>

        {/* Document Summary */}
        <Card>
          <CardHeader>
            <HStack spacing={2}>
              <FiFileText />
              <Heading size="md">Document Analytics</Heading>
            </HStack>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="sm" fontWeight="medium">Total Documents:</Text>
                <Text fontSize="lg" fontWeight="bold" color="green.600">
                  {analytics.document_analytics.total_documents}
                </Text>
              </HStack>
              
              <Box>
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm">Upload Rate:</Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {analytics.document_analytics.upload_velocity.toFixed(2)}/day
                  </Text>
                </HStack>
                <Progress 
                  value={Math.min(100, analytics.document_analytics.upload_velocity * 100)} 
                  colorScheme="green" 
                  size="sm" 
                  borderRadius="full"
                />
              </Box>
              
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={2}>Storage Used:</Text>
                <Text fontSize="lg" fontWeight="bold" color="orange.600">
                  {analytics.document_analytics.total_size_mb.toFixed(1)} MB
                </Text>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>
    </Box>
  )
}

export default CaseDashboardSimple
