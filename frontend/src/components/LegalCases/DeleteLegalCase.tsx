import {
  <PERSON><PERSON>,
  <PERSON>alogActionTrigger,
  DialogBody,
  DialogClose<PERSON>rigger,
  DialogContent,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
  Text,
} from "@chakra-ui/react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FiTrash } from "react-icons/fi"

import { LegalCasesService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import { MenuItem } from "../ui/menu"

interface DeleteLegalCaseProps {
  id: string
}

const DeleteLegalCase = ({ id }: DeleteLegalCaseProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()

  const mutation = useMutation({
    mutationFn: () => LegalCasesService.deleteLegalCase({ legalCaseId: id }),
    onSuccess: () => {
      showSuccessToast("Legal case deleted successfully.")
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
    },
  })

  return (
    <DialogRoot open={isOpen} onOpenChange={({ open }) => setIsOpen(open)}>
      <DialogTrigger asChild>
        <MenuItem
          value="delete"
          color="red.500"
          _hover={{
            bg: "red.50",
          }}
        >
          <FiTrash />
          Delete Legal Case
        </MenuItem>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Legal Case</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Text>
            Are you sure you want to delete this legal case? This action cannot be undone.
          </Text>
        </DialogBody>
        <DialogFooter>
          <DialogActionTrigger asChild>
            <Button variant="outline">Cancel</Button>
          </DialogActionTrigger>
          <Button
            colorScheme="red"
            onClick={() => mutation.mutate()}
            loading={mutation.isPending}
          >
            Delete
          </Button>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default DeleteLegalCase
