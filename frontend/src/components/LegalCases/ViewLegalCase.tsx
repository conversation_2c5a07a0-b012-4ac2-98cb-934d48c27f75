import {
  But<PERSON>,
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ge,
  SimpleGrid,
  Box,
} from "@chakra-ui/react"
import { useState } from "react"
import { FaEye } from "react-icons/fa"

import { type LegalCasePublic } from "@/client"
import {
  <PERSON>alogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import CaseStatusBadge from "./CaseStatusBadge"

interface ViewLegalCaseProps {
  legalCase: LegalCasePublic
}

const ViewLegalCase = ({ legalCase }: ViewLegalCaseProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <DialogRoot
      size={{ base: "md", md: "lg" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsO<PERSON>(open)}
    >
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <FaEye fontSize="16px" />
          View Details
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Legal Case Details</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <VStack gap={6} align="stretch">
            {/* Case Header */}
            <Box>
              <Text fontSize="xl" fontWeight="bold" mb={2}>
                {String(legalCase.title || 'Untitled Case')}
              </Text>
              <Text color="gray.600" fontSize="lg" mb={3}>
                Client: {String(legalCase.client_name || 'Unknown Client')}
              </Text>
              <HStack gap={3}>
                <CaseStatusBadge status={legalCase.status || 'open'} />
                <Badge
                  colorScheme={
                    legalCase.priority === 'urgent' ? 'red' :
                    legalCase.priority === 'high' ? 'orange' :
                    legalCase.priority === 'medium' ? 'blue' : 'gray'
                  }
                  textTransform="capitalize"
                >
                  {String(legalCase.priority || 'medium')}
                </Badge>
              </HStack>
            </Box>

            {/* Description */}
            {legalCase.description && (
              <Box>
                <Text fontSize="sm" color="gray.600" fontWeight="medium" mb={2}>
                  Description
                </Text>
                <Text color="gray.700" fontSize="sm" p={3} bg="gray.50" borderRadius="md">
                  {String(legalCase.description || 'No description provided')}
                </Text>
              </Box>
            )}

            {/* Case Information Grid */}
            <Box>
              <Text fontSize="md" fontWeight="medium" mb={3}>
                Case Information
              </Text>
              <SimpleGrid columns={{ base: 1, md: 2 }} gap={4}>
                <VStack align="start" gap={2}>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    Case Type
                  </Text>
                  <Badge colorScheme="purple" textTransform="capitalize">
                    {String(legalCase.case_type || 'general').replace('_', ' ')}
                  </Badge>
                </VStack>

                <VStack align="start" gap={2}>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    Opening Date
                  </Text>
                  <Text fontSize="sm">
                    {formatDate(legalCase.opening_date)}
                  </Text>
                </VStack>

                <VStack align="start" gap={2}>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    Status
                  </Text>
                  <CaseStatusBadge status={legalCase.status || 'open'} />
                </VStack>

                <VStack align="start" gap={2}>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    Priority
                  </Text>
                  <Badge
                    colorScheme={
                      legalCase.priority === 'urgent' ? 'red' :
                      legalCase.priority === 'high' ? 'orange' :
                      legalCase.priority === 'medium' ? 'blue' : 'gray'
                    }
                    textTransform="capitalize"
                  >
                    {String(legalCase.priority || 'medium')}
                  </Badge>
                </VStack>
              </SimpleGrid>
            </Box>

            {/* Case ID */}
            <Box>
              <Text fontSize="sm" color="gray.600" fontWeight="medium" mb={1}>
                Case ID
              </Text>
              <Text fontSize="sm" fontFamily="mono" color="gray.700">
                {String(legalCase.id || 'Unknown ID')}
              </Text>
            </Box>
          </VStack>
        </DialogBody>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default ViewLegalCase
