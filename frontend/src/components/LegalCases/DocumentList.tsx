import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Badge,
  IconButton,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
} from "@chakra-ui/react"
import {
  FiDownload,
  FiEdit,
  FiTrash2,
  FiMoreVertical,
  FiShare2,
  FiLock,
} from "react-icons/fi"
import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { formatDistanceToNow } from "date-fns"

import { 
  CaseDocument, 
  CaseDocumentsService,
  getFileTypeInfo,
  getCategoryInfo,
  formatFileSize 
} from "@/client/case-documents"
import { toaster } from "@/components/ui/toaster"
import DocumentEditModal from "./DocumentEditModal"

interface DocumentListProps {
  documents: CaseDocument[]
  caseId: string
  onUpdated: () => void
  onDeleted: () => void
}

const DocumentList = ({ documents, caseId, onUpdated, onDeleted }: DocumentListProps) => {
  const [selectedDocument, setSelectedDocument] = useState<CaseDocument | null>(null)
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (documentId: string) => CaseDocumentsService.deleteCaseDocument(caseId, documentId),
    onSuccess: () => {
      toaster.create({
        title: "Document deleted",
        description: "The document has been deleted successfully.",
        status: "success",
      })
      onDeleted()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to delete document",
        status: "error",
      })
    },
  })

  const handleDownload = async (document: CaseDocument) => {
    try {
      const blob = await CaseDocumentsService.downloadCaseDocument(caseId, document.id)
      const url = window.URL.createObjectURL(blob)
      const link = window.document.createElement("a")
      link.href = url
      link.download = document.original_filename
      window.document.body.appendChild(link)
      link.click()
      window.document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toaster.create({
        title: "Download started",
        description: `Downloading ${document.original_filename}`,
        status: "success",
      })
    } catch (error: any) {
      toaster.create({
        title: "Download failed",
        description: error.response?.data?.detail || "Failed to download document",
        status: "error",
      })
    }
  }

  const handleEdit = (document: CaseDocument) => {
    setSelectedDocument(document)
    onEditOpen()
  }

  const handleDelete = (document: CaseDocument) => {
    if (window.confirm(`Are you sure you want to delete "${document.original_filename}"?`)) {
      deleteMutation.mutate(document.id)
    }
  }

  const handleEditSuccess = () => {
    onEditClose()
    setSelectedDocument(null)
    onUpdated()
  }

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch {
      return "Unknown time"
    }
  }

  return (
    <>
      <Table variant="simple" size="sm">
        <Thead>
          <Tr>
            <Th>Document</Th>
            <Th>Category</Th>
            <Th>Size</Th>
            <Th>Uploaded By</Th>
            <Th>Date</Th>
            <Th>Status</Th>
            <Th>Actions</Th>
          </Tr>
        </Thead>
        <Tbody>
          {documents.map((document) => {
            const fileTypeInfo = getFileTypeInfo(document.content_type)
            const categoryInfo = getCategoryInfo(document.category)

            return (
              <Tr key={document.id} _hover={{ bg: "gray.50" }}>
                {/* Document name and type */}
                <Td>
                  <HStack spacing={3}>
                    <Text fontSize="lg">{fileTypeInfo.icon}</Text>
                    <div>
                      <Text fontWeight="medium" fontSize="sm" noOfLines={1}>
                        {document.original_filename}
                      </Text>
                      <Badge colorScheme={fileTypeInfo.color} variant="subtle" fontSize="xs">
                        {fileTypeInfo.label}
                      </Badge>
                    </div>
                  </HStack>
                </Td>

                {/* Category */}
                <Td>
                  <Badge colorScheme={categoryInfo.color} variant="outline" fontSize="xs">
                    {categoryInfo.icon} {categoryInfo.label}
                  </Badge>
                </Td>

                {/* File size */}
                <Td>
                  <Text fontSize="sm">{formatFileSize(document.file_size)}</Text>
                </Td>

                {/* Uploader */}
                <Td>
                  <Text fontSize="sm" noOfLines={1}>
                    {document.uploader?.full_name || "Unknown"}
                  </Text>
                </Td>

                {/* Upload date */}
                <Td>
                  <Text fontSize="sm" color="gray.600">
                    {formatDate(document.uploaded_at)}
                  </Text>
                </Td>

                {/* Status indicators */}
                <Td>
                  <HStack spacing={2}>
                    {document.is_confidential && (
                      <Tooltip label="Confidential">
                        <Badge colorScheme="red" variant="subtle" fontSize="xs">
                          <FiLock size={10} style={{ marginRight: "4px" }} />
                          Confidential
                        </Badge>
                      </Tooltip>
                    )}
                    {document.is_shared_with_client && (
                      <Tooltip label="Shared with client">
                        <Badge colorScheme="green" variant="subtle" fontSize="xs">
                          <FiShare2 size={10} style={{ marginRight: "4px" }} />
                          Shared
                        </Badge>
                      </Tooltip>
                    )}
                    {document.tags && document.tags.length > 0 && (
                      <Tooltip label={`Tags: ${document.tags.join(", ")}`}>
                        <Badge variant="subtle" colorScheme="blue" fontSize="xs">
                          {document.tags.length} tag{document.tags.length !== 1 ? 's' : ''}
                        </Badge>
                      </Tooltip>
                    )}
                  </HStack>
                </Td>

                {/* Actions */}
                <Td>
                  <HStack spacing={1}>
                    {document.can_download && (
                      <Tooltip label="Download">
                        <IconButton
                          variant="ghost"
                          size="sm"
                          icon={<FiDownload />}
                          onClick={() => handleDownload(document)}
                        />
                      </Tooltip>
                    )}

                    {(document.can_edit || document.can_delete) && (
                      <Menu>
                        <MenuButton
                          as={IconButton}
                          variant="ghost"
                          size="sm"
                          icon={<FiMoreVertical />}
                        />
                        <MenuList>
                          {document.can_edit && (
                            <MenuItem
                              icon={<FiEdit />}
                              onClick={() => handleEdit(document)}
                            >
                              Edit
                            </MenuItem>
                          )}
                          {document.can_delete && (
                            <MenuItem
                              icon={<FiTrash2 />}
                              onClick={() => handleDelete(document)}
                              color="red.500"
                            >
                              Delete
                            </MenuItem>
                          )}
                        </MenuList>
                      </Menu>
                    )}
                  </HStack>
                </Td>
              </Tr>
            )
          })}
        </Tbody>
      </Table>

      {/* Edit Modal */}
      {selectedDocument && (
        <DocumentEditModal
          isOpen={isEditOpen}
          onClose={onEditClose}
          caseId={caseId}
          document={selectedDocument}
          onSuccess={handleEditSuccess}
        />
      )}
    </>
  )
}

export default DocumentList
