import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Select,
  Alert,
  AlertIcon,
  Box,
  Badge,
  Spinner,

} from "@chakra-ui/react"
import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseTemplate,
  CaseTemplatesService,
  getCaseTypeInfo 
} from "@/client/case-templates"
import { LegalCasesService } from "@/client"
import useCustomToast from "@/hooks/useCustomToast"

interface ApplyCaseTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  template: CaseTemplate
  onApply?: (templateId: string, caseId: string) => void
}

const ApplyCaseTemplateModal = ({ 
  isOpen, 
  onClose, 
  template,
  onApply 
}: ApplyCaseTemplateModalProps) => {
  const [selectedCaseId, setSelectedCaseId] = useState("")

  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const caseTypeInfo = getCaseTypeInfo(template.case_type)

  // Fetch available cases
  const { 
    data: casesData, 
    isLoading: isLoadingCases, 
    error: casesError 
  } = useQuery({
    queryKey: ["legal-cases", "for-template"],
    queryFn: () => LegalCasesService.readLegalCases({
      limit: 100,
      case_type: template.case_type // Filter by same case type
    }),
    enabled: isOpen,
  })

  // Apply template mutation
  const applyTemplateMutation = useMutation({
    mutationFn: ({ templateId, caseId }: { templateId: string; caseId: string }) =>
      CaseTemplatesService.applyCaseTemplate(templateId, caseId),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
      queryClient.invalidateQueries({ queryKey: ["case-milestones"] })
      queryClient.invalidateQueries({ queryKey: ["case-deadlines"] })
      
      toast({
        title: "Template applied successfully",
        description: `Applied ${result.applied_items.length} items to the case`,
        status: "success",
        duration: 5000,
      })
      
      onApply?.(template.id, selectedCaseId)
      handleClose()
    },
    onError: (error: any) => {
      toast({
        title: "Error applying template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleClose = () => {
    setSelectedCaseId("")
    onClose()
  }

  const handleApply = () => {
    if (!selectedCaseId) {
      toast({
        title: "No case selected",
        description: "Please select a case to apply the template to",
        status: "warning",
        duration: 3000,
      })
      return
    }

    applyTemplateMutation.mutate({
      templateId: template.id,
      caseId: selectedCaseId
    })
  }

  const getTemplateStats = () => {
    const data = template.template_data
    const milestones = data.milestones?.length || 0
    const deadlines = data.deadlines?.length || 0
    const documents = data.documents?.length || 0
    
    return { milestones, deadlines, documents }
  }

  const stats = getTemplateStats()
  const selectedCase = casesData?.data.find(c => c.id === selectedCaseId)

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Apply Case Template</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Template Info */}
            <Box p={4} bg="blue.50" borderRadius="md">
              <VStack spacing={3} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="lg" fontWeight="bold">
                    {template.name}
                  </Text>
                  <Badge colorScheme={caseTypeInfo.color} variant="subtle">
                    {caseTypeInfo.icon} {caseTypeInfo.label}
                  </Badge>
                </HStack>
                
                {template.description && (
                  <Text fontSize="sm" color="gray.600">
                    {template.description}
                  </Text>
                )}
                
                <HStack justify="space-around" bg="white" p={3} borderRadius="md">
                  <VStack spacing={1}>
                    <Text fontSize="lg" fontWeight="bold" color="blue.600">
                      {stats.milestones}
                    </Text>
                    <Text fontSize="xs" color="gray.600">Milestones</Text>
                  </VStack>
                  
                  <VStack spacing={1}>
                    <Text fontSize="lg" fontWeight="bold" color="orange.600">
                      {stats.deadlines}
                    </Text>
                    <Text fontSize="xs" color="gray.600">Deadlines</Text>
                  </VStack>
                  
                  <VStack spacing={1}>
                    <Text fontSize="lg" fontWeight="bold" color="green.600">
                      {stats.documents}
                    </Text>
                    <Text fontSize="xs" color="gray.600">Documents</Text>
                  </VStack>
                </HStack>
              </VStack>
            </Box>

            {/* Case Selection */}
            <VStack spacing={3} align="stretch">
              <Text fontSize="md" fontWeight="medium">
                Select Case to Apply Template
              </Text>
              
              {isLoadingCases ? (
                <Box textAlign="center" py={4}>
                  <Spinner size="md" />
                  <Text mt={2} fontSize="sm" color="gray.600">
                    Loading available cases...
                  </Text>
                </Box>
              ) : casesError ? (
                <Alert status="error">
                  <AlertIcon />
                  <Text>Failed to load cases</Text>
                </Alert>
              ) : (
                <Select
                  placeholder="Choose a case..."
                  value={selectedCaseId}
                  onChange={(e) => setSelectedCaseId(e.target.value)}
                >
                  {casesData?.data.map((case_) => (
                    <option key={case_.id} value={case_.id}>
                      {case_.title} - {case_.client_name}
                    </option>
                  ))}
                </Select>
              )}
              
              {casesData?.data.length === 0 && (
                <Alert status="info">
                  <AlertIcon />
                  <Text>
                    No {caseTypeInfo.label.toLowerCase()} cases found. 
                    Create a case first or change the template case type.
                  </Text>
                </Alert>
              )}
            </VStack>

            {/* Selected Case Info */}
            {selectedCase && (
              <Box p={4} bg="gray.50" borderRadius="md">
                <VStack spacing={2} align="stretch">
                  <Text fontSize="sm" fontWeight="medium" color="gray.700">
                    Selected Case:
                  </Text>
                  <Text fontSize="md" fontWeight="bold">
                    {selectedCase.title}
                  </Text>
                  <HStack justify="space-between" fontSize="sm" color="gray.600">
                    <Text>Client: {selectedCase.client_name}</Text>
                    <Text>Status: {selectedCase.status}</Text>
                  </HStack>
                  {selectedCase.lawyer && (
                    <Text fontSize="sm" color="gray.600">
                      Lawyer: {selectedCase.lawyer.full_name}
                    </Text>
                  )}
                </VStack>
              </Box>
            )}

            {/* Warning */}
            <Alert status="warning">
              <AlertIcon />
              <VStack align="start" spacing={1}>
                <Text fontSize="sm" fontWeight="medium">
                  This will add template items to the selected case:
                </Text>
                <Text fontSize="sm">
                  • {stats.milestones} milestones will be created
                </Text>
                <Text fontSize="sm">
                  • {stats.deadlines} deadlines will be created
                </Text>
                <Text fontSize="sm">
                  • Existing case data will not be modified
                </Text>
              </VStack>
            </Alert>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleApply}
            isLoading={applyTemplateMutation.isPending}
            isDisabled={!selectedCaseId || casesData?.data.length === 0}
          >
            Apply Template
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default ApplyCaseTemplateModal
