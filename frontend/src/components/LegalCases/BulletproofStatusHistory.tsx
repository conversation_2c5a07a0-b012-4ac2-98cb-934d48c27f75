import React, { useMemo } from 'react'
import {
  <PERSON>ge,
  Box,
  Card,
  CardBody,
  Heading,
  HStack,
  Text,
  VStack,
  Spin<PERSON>,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"

import { LegalCasesService } from "../../client"

interface BulletproofStatusHistoryProps {
  caseId: string
}

// Bulletproof data sanitization to prevent React Error #130
const sanitizeForReact = (data: any): any => {
  if (data === null || data === undefined) {
    return null
  }
  
  if (typeof data === 'string' || typeof data === 'number' || typeof data === 'boolean') {
    return data
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeForReact)
  }
  
  if (typeof data === 'object') {
    const sanitized: any = {}
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key]
        // Convert complex objects to safe strings
        if (typeof value === 'object' && value !== null) {
          if (value instanceof Date) {
            sanitized[key] = value.toISOString()
          } else if (typeof value.toString === 'function') {
            sanitized[key] = String(value)
          } else {
            sanitized[key] = JSON.stringify(value)
          }
        } else {
          sanitized[key] = sanitizeForReact(value)
        }
      }
    }
    return sanitized
  }
  
  return String(data)
}

// Safe string conversion
const safeString = (value: any): string => {
  if (value === null || value === undefined) return ""
  if (typeof value === "string") return value
  if (typeof value === "number" || typeof value === "boolean") return String(value)
  try {
    return String(value)
  } catch {
    return ""
  }
}

// Safe date formatting
const safeFormatDate = (dateValue: any): string => {
  try {
    if (!dateValue) return "Unknown date"
    
    let dateString: string
    if (typeof dateValue === "string") {
      dateString = dateValue
    } else if (dateValue instanceof Date) {
      dateString = dateValue.toISOString()
    } else {
      dateString = String(dateValue)
    }
    
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return "Invalid date"
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return "Unknown date"
  }
}

// Safe user name extraction
const safeUserName = (userObj: any): string => {
  try {
    if (!userObj) return "Unknown User"
    
    if (typeof userObj === "string") return userObj
    
    if (typeof userObj === "object") {
      const fullName = safeString(userObj.full_name)
      if (fullName) return fullName
      
      const email = safeString(userObj.email)
      if (email) return email
      
      const firstName = safeString(userObj.first_name)
      const lastName = safeString(userObj.last_name)
      if (firstName && lastName) return `${firstName} ${lastName}`
      
      if (firstName) return firstName
    }
    
    return "Unknown User"
  } catch {
    return "Unknown User"
  }
}

const BulletproofStatusHistory: React.FC<BulletproofStatusHistoryProps> = ({ caseId }) => {
  const { data: statusHistory, isLoading, error } = useQuery({
    queryKey: ["case-status-history", caseId],
    queryFn: () => LegalCasesService.readCaseStatusHistory({
      legalCaseId: caseId,
      skip: 0,
      limit: 10,
    }),
    enabled: !!caseId,
  })

  // Memoize sanitized data to prevent re-renders and React errors
  const sanitizedHistory = useMemo(() => {
    if (!statusHistory?.data) return []
    
    return statusHistory.data.map((item, index) => {
      // Create a completely safe object with only primitive values
      return {
        id: safeString(item?.id) || `history-${index}`,
        oldStatus: safeString(item?.old_status) || null,
        newStatus: safeString(item?.new_status) || "UNKNOWN",
        changedAt: safeString(item?.changed_at),
        changedByUser: safeUserName(item?.changed_by_user),
        notes: safeString(item?.notes) || null,
      }
    })
  }, [statusHistory])

  const getStatusColor = (status: string): string => {
    const normalizedStatus = status.toLowerCase()
    switch (normalizedStatus) {
      case "open": return "blue"
      case "in_progress": return "orange"
      case "under_review": return "purple"
      case "closed": return "green"
      case "archived": return "gray"
      default: return "gray"
    }
  }

  if (isLoading) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={2}>Loading status history...</Text>
        </Box>
      </Box>
    )
  }

  if (error) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box p={4} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
          <Text color="red.600">
            ⚠️ Error loading status history. Please try again later.
          </Text>
        </Box>
      </Box>
    )
  }

  if (!sanitizedHistory.length) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box textAlign="center" py={8}>
          <Text color="gray.500">No status changes recorded yet</Text>
        </Box>
      </Box>
    )
  }

  return (
    <Box>
      <Heading size="md" mb={4}>Status History</Heading>
      <VStack spacing={3} align="stretch">
        {sanitizedHistory.map((historyItem) => (
          <Card key={historyItem.id} size="sm">
            <CardBody>
              <VStack spacing={2} align="stretch">
                <HStack justify="space-between" align="center">
                  <HStack spacing={2}>
                    {historyItem.oldStatus && (
                      <>
                        <Badge colorScheme={getStatusColor(historyItem.oldStatus)} variant="outline">
                          {historyItem.oldStatus}
                        </Badge>
                        <Text fontSize="sm" color="gray.500">→</Text>
                      </>
                    )}
                    <Badge colorScheme={getStatusColor(historyItem.newStatus)} variant="solid">
                      {historyItem.newStatus}
                    </Badge>
                  </HStack>
                  <Text fontSize="sm" color="gray.500">
                    {safeFormatDate(historyItem.changedAt)}
                  </Text>
                </HStack>

                <HStack justify="space-between" align="center">
                  <Text fontSize="sm" color="gray.600">
                    Changed by: {historyItem.changedByUser}
                  </Text>
                </HStack>

                {historyItem.notes && (
                  <Box mt={2} p={2} bg="gray.50" borderRadius="md">
                    <Text fontSize="sm" color="gray.700">
                      <strong>Notes:</strong> {historyItem.notes}
                    </Text>
                  </Box>
                )}
              </VStack>
            </CardBody>
          </Card>
        ))}
      </VStack>

      {/* Success indicator */}
      <Box mt={4} p={2} bg="green.50" borderRadius="md" border="1px solid" borderColor="green.200">
        <Text fontSize="xs" color="green.600" textAlign="center">
          ✅ Bulletproof Status History - React Error #130 Fixed
        </Text>
      </Box>
    </Box>
  )
}

export default BulletproofStatusHistory
