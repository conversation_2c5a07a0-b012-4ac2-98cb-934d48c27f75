import {
  Modal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Switch,
  Badge,
  Text,
  Box,

  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
} from "@chakra-ui/react"
import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseTemplatesService, 
  DocumentTemplateCreate,
  CASE_TYPE_CONFIG,
  DOCUMENT_TYPE_CONFIG,
  extractPlaceholders,
  validateTemplateContent
} from "@/client/case-templates"
import useCustomToast from "@/hooks/useCustomToast"

interface CreateDocumentTemplateModalProps {
  isOpen: boolean
  onClose: () => void
}

const CreateDocumentTemplateModal = ({ isOpen, onClose }: CreateDocumentTemplateModalProps) => {
  const [formData, setFormData] = useState<DocumentTemplateCreate>({
    name: "",
    description: "",
    case_type: undefined,
    document_type: "contract",
    template_content: "",
    placeholders: [],
    is_active: true,
    is_public: false,
    tags: []
  })
  
  const [tagInput, setTagInput] = useState("")
  const [contentValidation, setContentValidation] = useState({ isValid: true, errors: [] })

  const toast = useCustomToast()
  const queryClient = useQueryClient()

  const createTemplateMutation = useMutation({
    mutationFn: CaseTemplatesService.createDocumentTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["document-templates"] })
      toast({
        title: "Template created",
        description: "Document template has been created successfully",
        status: "success",
        duration: 3000,
      })
      handleClose()
    },
    onError: (error: any) => {
      toast({
        title: "Error creating template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      case_type: undefined,
      document_type: "contract",
      template_content: "",
      placeholders: [],
      is_active: true,
      is_public: false,
      tags: []
    })
    setTagInput("")
    setContentValidation({ isValid: true, errors: [] })
    onClose()
  }

  const handleContentChange = (content: string) => {
    setFormData({ ...formData, template_content: content })
    
    // Extract placeholders and validate content
    const placeholders = extractPlaceholders(content)
    const validation = validateTemplateContent(content)
    
    setFormData(prev => ({ ...prev, placeholders }))
    setContentValidation(validation)
  }

  const handleSubmit = () => {
    if (!contentValidation.isValid) {
      toast({
        title: "Invalid template content",
        description: "Please fix the template content errors before saving",
        status: "error",
        duration: 5000,
      })
      return
    }
    
    createTemplateMutation.mutate(formData)
  }

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, tagInput.trim()]
      })
      setTagInput("")
    }
  }

  const removeTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    })
  }

  const insertPlaceholder = (placeholder: string) => {
    const textarea = document.getElementById('template-content') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const content = formData.template_content
      const newContent = content.substring(0, start) + `{{${placeholder}}}` + content.substring(end)
      handleContentChange(newContent)
      
      // Set cursor position after the inserted placeholder
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + placeholder.length + 4, start + placeholder.length + 4)
      }, 0)
    }
  }

  const commonPlaceholders = [
    "client_name", "client_address", "client_phone", "client_email",
    "lawyer_name", "lawyer_firm", "lawyer_address", "lawyer_phone",
    "case_number", "case_title", "case_type", "date",
    "court_name", "judge_name", "opposing_party", "amount"
  ]

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="6xl">
      <ModalOverlay />
      <ModalContent maxH="90vh" overflowY="auto">
        <ModalHeader>Create Document Template</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <Tabs>
            <TabList>
              <Tab>Basic Info</Tab>
              <Tab>Content</Tab>
              <Tab>Placeholders</Tab>
              <Tab>Settings</Tab>
            </TabList>

            <TabPanels>
              {/* Basic Info */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel>Template Name</FormLabel>
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter template name"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel>Description</FormLabel>
                    <Textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Describe what this template is for"
                      rows={3}
                    />
                  </FormControl>

                  <HStack spacing={4}>
                    <FormControl isRequired>
                      <FormLabel>Document Type</FormLabel>
                      <Select
                        value={formData.document_type}
                        onChange={(e) => setFormData({ ...formData, document_type: e.target.value })}
                      >
                        {Object.entries(DOCUMENT_TYPE_CONFIG).map(([key, config]) => (
                          <option key={key} value={key}>
                            {config.icon} {config.label}
                          </option>
                        ))}
                      </Select>
                    </FormControl>

                    <FormControl>
                      <FormLabel>Case Type (Optional)</FormLabel>
                      <Select
                        value={formData.case_type || ""}
                        onChange={(e) => setFormData({ ...formData, case_type: e.target.value || undefined })}
                      >
                        <option value="">Universal (All case types)</option>
                        {Object.entries(CASE_TYPE_CONFIG).map(([key, config]) => (
                          <option key={key} value={key}>
                            {config.icon} {config.label}
                          </option>
                        ))}
                      </Select>
                    </FormControl>
                  </HStack>

                  <FormControl>
                    <FormLabel>Tags</FormLabel>
                    <HStack>
                      <Input
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      />
                      <Button onClick={addTag} size="sm">Add</Button>
                    </HStack>
                    <HStack mt={2} flexWrap="wrap">
                      {formData.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="solid"
                          colorScheme="green"
                          cursor="pointer"
                          onClick={() => removeTag(tag)}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </HStack>
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Content */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel>Template Content</FormLabel>
                    <Text fontSize="sm" color="gray.600" mb={2}>
                      Use placeholders like {`{{client_name}}`} to insert dynamic content
                    </Text>
                    <Textarea
                      id="template-content"
                      value={formData.template_content}
                      onChange={(e) => handleContentChange(e.target.value)}
                      placeholder="Enter your document template content here..."
                      rows={15}
                      fontFamily="monospace"
                      fontSize="sm"
                    />
                  </FormControl>

                  {!contentValidation.isValid && (
                    <Alert status="error">
                      <AlertIcon />
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="medium">Template content has errors:</Text>
                        {contentValidation.errors.map((error, index) => (
                          <Text key={index} fontSize="sm">• {error}</Text>
                        ))}
                      </VStack>
                    </Alert>
                  )}

                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={2}>Quick Insert:</Text>
                    <HStack spacing={2} flexWrap="wrap">
                      {commonPlaceholders.map((placeholder) => (
                        <Button
                          key={placeholder}
                          size="xs"
                          variant="outline"
                          onClick={() => insertPlaceholder(placeholder)}
                        >
                          {`{{${placeholder}}}`}
                        </Button>
                      ))}
                    </HStack>
                  </Box>
                </VStack>
              </TabPanel>

              {/* Placeholders */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Text fontSize="lg" fontWeight="medium">Detected Placeholders</Text>
                  
                  {formData.placeholders.length > 0 ? (
                    <VStack spacing={2} align="stretch">
                      {formData.placeholders.map((placeholder, index) => (
                        <HStack key={index} p={3} bg="gray.50" borderRadius="md">
                          <Badge colorScheme="blue" variant="solid">
                            {`{{${placeholder}}}`}
                          </Badge>
                          <Text fontSize="sm" color="gray.600">
                            {placeholder.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Text>
                        </HStack>
                      ))}
                    </VStack>
                  ) : (
                    <Box textAlign="center" py={8} color="gray.500">
                      <Text>No placeholders detected in the template content</Text>
                      <Text fontSize="sm" mt={1}>
                        Add placeholders like {`{{client_name}}`} to make your template dynamic
                      </Text>
                    </Box>
                  )}

                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={2}>Placeholder Guidelines:</Text>
                    <VStack align="start" spacing={1} fontSize="sm" color="gray.600">
                      <Text>• Use double curly braces: {`{{placeholder_name}}`}</Text>
                      <Text>• Use lowercase with underscores: {`{{client_name}}`}</Text>
                      <Text>• Be descriptive: {`{{contract_start_date}}`} instead of {`{{date}}`}</Text>
                      <Text>• Avoid spaces and special characters</Text>
                    </VStack>
                  </Box>
                </VStack>
              </TabPanel>

              {/* Settings */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="medium">Active Template</Text>
                        <Text fontSize="sm" color="gray.600">
                          Active templates can be used to generate documents
                        </Text>
                      </VStack>
                      <Switch
                        isChecked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                      />
                    </HStack>
                  </FormControl>

                  <FormControl>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="medium">Public Template</Text>
                        <Text fontSize="sm" color="gray.600">
                          Public templates can be used by other users
                        </Text>
                      </VStack>
                      <Switch
                        isChecked={formData.is_public}
                        onChange={(e) => setFormData({ ...formData, is_public: e.target.checked })}
                      />
                    </HStack>
                  </FormControl>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            colorScheme="green"
            onClick={handleSubmit}
            isLoading={createTemplateMutation.isPending}
            isDisabled={!formData.name.trim() || !formData.template_content.trim() || !contentValidation.isValid}
          >
            Create Template
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default CreateDocumentTemplateModal
