import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  Select,
  FormControl,
  FormLabel,
  Switch,
  Textarea,
  Input,
  Box,
  Progress,
  Alert,
  AlertIcon,
  Badge,
  Flex,
  IconButton,
} from "@chakra-ui/react"
import { FiUpload, FiX, FiFile } from "react-icons/fi"
import { useState, useRef, useCallback } from "react"
import { useMutation } from "@tanstack/react-query"
import { useDropzone } from "react-dropzone"

import { 
  CaseDocumentsService, 
  CaseDocumentCreate, 
  DOCUMENT_CATEGORY_CONFIG,
  getFileTypeInfo,
  formatFileSize 
} from "@/client/case-documents"
import useAuth from "@/hooks/useAuth"
import { toaster } from "@/components/ui/toaster"

interface DocumentUploadModalProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  defaultFolderId?: string | null
  onSuccess: () => void
}

const DocumentUploadModal = ({
  isOpen,
  onClose,
  caseId,
  defaultFolderId,
  onSuccess
}: DocumentUploadModalProps) => {
  const { user } = useAuth()
  
  // Form state
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(defaultFolderId || null)
  const [category, setCategory] = useState<string>("other")
  const [description, setDescription] = useState("")
  const [tags, setTags] = useState("")
  const [isConfidential, setIsConfidential] = useState(false)
  const [isSharedWithClient, setIsSharedWithClient] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Allowed file types
  const allowedTypes = [
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "image/jpeg",
    "image/png",
    "image/gif",
    "text/plain",
  ]

  const maxFileSize = 50 * 1024 * 1024 // 50MB

  // Dropzone configuration
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const validFiles = acceptedFiles.filter(file => {
      if (!allowedTypes.includes(file.type)) {
        toaster.create({
          title: "Invalid file type",
          description: `${file.name} is not a supported file type`,
          status: "error",
        })
        return false
      }
      if (file.size > maxFileSize) {
        toaster.create({
          title: "File too large",
          description: `${file.name} exceeds the 50MB limit`,
          status: "error",
        })
        return false
      }
      return true
    })
    
    setSelectedFiles(prev => [...prev, ...validFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'text/plain': ['.txt'],
    },
    multiple: true,
  })

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const metadata: CaseDocumentCreate = {
        folder_id: selectedFolderId || undefined,
        category: category as any,
        description: description || undefined,
        tags: tags || undefined,
        is_confidential: isConfidential,
        is_shared_with_client: isSharedWithClient,
      }
      return CaseDocumentsService.uploadCaseDocument(caseId, file, metadata)
    },
    onSuccess: () => {
      toaster.create({
        title: "Document uploaded",
        description: "Your document has been uploaded successfully.",
        status: "success",
      })
    },
    onError: (error: any) => {
      toaster.create({
        title: "Upload failed",
        description: error.response?.data?.detail || "Failed to upload document",
        status: "error",
      })
    },
  })

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      toaster.create({
        title: "No files selected",
        description: "Please select at least one file to upload",
        status: "error",
      })
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i]
        await uploadMutation.mutateAsync(file)
        setUploadProgress(((i + 1) / selectedFiles.length) * 100)
      }
      
      onSuccess()
      handleReset()
    } catch (error) {
      // Error handling is done in the mutation
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const handleReset = () => {
    setSelectedFiles([])
    setSelectedFolderId(defaultFolderId || null)
    setCategory("other")
    setDescription("")
    setTags("")
    setIsConfidential(false)
    setIsSharedWithClient(false)
    setUploadProgress(0)
    setIsUploading(false)
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleClose = () => {
    if (!isUploading) {
      handleReset()
      onClose()
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl">
      <ModalOverlay />
      <ModalContent maxH="90vh">
        <ModalHeader>Upload Documents</ModalHeader>
        <ModalCloseButton isDisabled={isUploading} />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* File Drop Zone */}
            <Box
              {...getRootProps()}
              border="2px dashed"
              borderColor={isDragActive ? "blue.400" : "gray.300"}
              borderRadius="lg"
              p={8}
              textAlign="center"
              cursor="pointer"
              bg={isDragActive ? "blue.50" : "gray.50"}
              transition="all 0.2s"
              _hover={{ borderColor: "blue.400", bg: "blue.50" }}
            >
              <input {...getInputProps()} />
              <VStack spacing={3}>
                <FiUpload size={32} color={isDragActive ? "#3182ce" : "#718096"} />
                <Text fontSize="lg" fontWeight="medium">
                  {isDragActive ? "Drop files here" : "Drag & drop files here"}
                </Text>
                <Text fontSize="sm" color="gray.600">
                  or click to browse files
                </Text>
                <Text fontSize="xs" color="gray.500">
                  Supported: PDF, DOCX, JPG, PNG, GIF, TXT (max 50MB each)
                </Text>
              </VStack>
            </Box>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <Box>
                <Text fontWeight="medium" mb={2}>
                  Selected Files ({selectedFiles.length})
                </Text>
                <VStack spacing={2} align="stretch">
                  {selectedFiles.map((file, index) => {
                    const fileTypeInfo = getFileTypeInfo(file.type)
                    return (
                      <Flex
                        key={index}
                        align="center"
                        p={3}
                        bg="white"
                        border="1px solid"
                        borderColor="gray.200"
                        borderRadius="md"
                      >
                        <Text mr={2}>{fileTypeInfo.icon}</Text>
                        <VStack align="start" spacing={0} flex={1}>
                          <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                            {file.name}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            {formatFileSize(file.size)} • {fileTypeInfo.label}
                          </Text>
                        </VStack>
                        <IconButton
                          variant="ghost"
                          size="sm"
                          icon={<FiX />}
                          onClick={() => removeFile(index)}
                          isDisabled={isUploading}
                        />
                      </Flex>
                    )
                  })}
                </VStack>
              </Box>
            )}

            {/* Upload Progress */}
            {isUploading && (
              <Box>
                <Text fontSize="sm" mb={2}>
                  Uploading... {Math.round(uploadProgress)}%
                </Text>
                <Progress value={uploadProgress} colorScheme="blue" />
              </Box>
            )}

            {/* Document Metadata */}
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel fontSize="sm">Category</FormLabel>
                <Select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  size="sm"
                  isDisabled={isUploading}
                >
                  {Object.entries(DOCUMENT_CATEGORY_CONFIG).map(([key, config]) => (
                    <option key={key} value={key}>
                      {config.icon} {config.label}
                    </option>
                  ))}
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Description (Optional)</FormLabel>
                <Textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Brief description of the document..."
                  size="sm"
                  rows={3}
                  isDisabled={isUploading}
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Tags (Optional)</FormLabel>
                <Input
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  placeholder="tag1, tag2, tag3"
                  size="sm"
                  isDisabled={isUploading}
                />
              </FormControl>

              <HStack spacing={6}>
                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="confidential" mb="0" fontSize="sm">
                    Confidential
                  </FormLabel>
                  <Switch
                    id="confidential"
                    isChecked={isConfidential}
                    onChange={(e) => setIsConfidential(e.target.checked)}
                    size="sm"
                    isDisabled={isUploading}
                  />
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="shared" mb="0" fontSize="sm">
                    Share with client
                  </FormLabel>
                  <Switch
                    id="shared"
                    isChecked={isSharedWithClient}
                    onChange={(e) => setIsSharedWithClient(e.target.checked)}
                    size="sm"
                    isDisabled={isUploading}
                  />
                </FormControl>
              </HStack>
            </VStack>

            {/* Help text */}
            <Alert status="info" size="sm">
              <AlertIcon />
              <Text fontSize="xs">
                Documents will be associated with this case and accessible based on your permissions.
              </Text>
            </Alert>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button 
              variant="ghost" 
              onClick={handleClose}
              isDisabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleUpload}
              isLoading={isUploading}
              loadingText="Uploading..."
              leftIcon={<FiUpload />}
              isDisabled={selectedFiles.length === 0}
            >
              Upload {selectedFiles.length > 0 && `(${selectedFiles.length})`}
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default DocumentUploadModal
