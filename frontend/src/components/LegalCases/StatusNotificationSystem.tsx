import { useEffect, useCallback } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { toaster } from "@/components/ui/toaster"
import { LegalCasesService } from "@/client"

interface StatusNotificationSystemProps {
  userId?: string
  enabled?: boolean
  pollInterval?: number
}

interface StatusChangeNotification {
  id: string
  caseId: string
  caseTitle: string
  oldStatus: string
  newStatus: string
  changedBy: string
  changedAt: string
  isRead: boolean
}

const StatusNotificationSystem = ({
  userId,
  enabled = true,
  pollInterval = 30000 // 30 seconds
}: StatusNotificationSystemProps) => {
  
  const queryClient = useQueryClient()

  // Fetch recent status changes for notifications
  const { data: recentChanges } = useQuery({
    queryKey: ["status-notifications", userId],
    queryFn: async () => {
      if (!enabled || !userId) return []
      
      // Get recent cases that the user has access to
      const casesResponse = await LegalCasesService.readLegalCases({
        skip: 0,
        limit: 100,
      })
      
      // For each case, get recent status history
      const notifications: StatusChangeNotification[] = []
      
      for (const case_ of casesResponse.data) {
        try {
          const historyResponse = await LegalCasesService.readCaseStatusHistory({
            legalCaseId: case_.id,
            skip: 0,
            limit: 5, // Only recent changes
          })
          
          // Convert to notifications
          historyResponse.data.forEach(history => {
            const changeDate = new Date(history.changed_at)
            const now = new Date()
            const hoursSinceChange = (now.getTime() - changeDate.getTime()) / (1000 * 60 * 60)
            
            // Only notify about changes in the last 24 hours
            if (hoursSinceChange <= 24) {
              notifications.push({
                id: `${case_.id}-${history.id}`,
                caseId: case_.id,
                caseTitle: case_.title,
                oldStatus: history.old_status || "",
                newStatus: history.new_status,
                changedBy: history.changed_by_user?.full_name || "Unknown",
                changedAt: history.changed_at,
                isRead: false // In a real app, this would be stored in user preferences
              })
            }
          })
        } catch (error) {
          console.warn(`Failed to fetch status history for case ${case_.id}:`, error)
        }
      }
      
      return notifications.sort((a, b) => 
        new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime()
      )
    },
    enabled: enabled && !!userId,
    refetchInterval: pollInterval,
    staleTime: pollInterval / 2,
  })

  // Show toast notifications for new status changes
  const showStatusChangeNotification = useCallback((notification: StatusChangeNotification) => {
    const statusLabels: Record<string, string> = {
      open: "Open",
      in_progress: "In Progress", 
      under_review: "Under Review",
      closed: "Closed",
      archived: "Archived"
    }

    const oldStatusLabel = statusLabels[notification.oldStatus] || notification.oldStatus
    const newStatusLabel = statusLabels[notification.newStatus] || notification.newStatus

    toaster.create({
      title: "Case Status Updated",
      description: `${notification.caseTitle}: ${oldStatusLabel} → ${newStatusLabel}`,
      type: "info",
      duration: 5000,
      action: {
        label: "View Case",
        onClick: () => {
          // Navigate to case details
          window.location.href = `/legal-cases/${notification.caseId}`
        }
      }
    })
  }, [])

  // Track previous notifications to detect new ones
  useEffect(() => {
    if (!recentChanges || recentChanges.length === 0) return

    // In a real app, we would store seen notifications in localStorage or user preferences
    const seenNotifications = JSON.parse(
      localStorage.getItem(`seen-notifications-${userId}`) || "[]"
    ) as string[]

    const newNotifications = recentChanges.filter(
      notification => !seenNotifications.includes(notification.id)
    )

    // Show toast for new notifications
    newNotifications.forEach(notification => {
      showStatusChangeNotification(notification)
    })

    // Update seen notifications
    if (newNotifications.length > 0) {
      const updatedSeenNotifications = [
        ...seenNotifications,
        ...newNotifications.map(n => n.id)
      ]
      localStorage.setItem(
        `seen-notifications-${userId}`,
        JSON.stringify(updatedSeenNotifications.slice(-100)) // Keep only last 100
      )
    }
  }, [recentChanges, userId, showStatusChangeNotification])

  // Provide a method to manually trigger notification check
  const checkForUpdates = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ["status-notifications", userId] })
  }, [queryClient, userId])

  // Auto-refresh when window gains focus
  useEffect(() => {
    if (!enabled) return

    const handleFocus = () => {
      checkForUpdates()
    }

    window.addEventListener("focus", handleFocus)
    return () => window.removeEventListener("focus", handleFocus)
  }, [enabled, checkForUpdates])

  // This component doesn't render anything visible
  return null
}

export default StatusNotificationSystem

// Hook for easy integration
export const useStatusNotifications = (options?: {
  enabled?: boolean
  pollInterval?: number
}) => {
  // In a real app, get current user from auth context
  const userId = "current-user-id" // This should come from auth context
  
  return {
    StatusNotificationSystem: () => (
      <StatusNotificationSystem
        userId={userId}
        enabled={options?.enabled}
        pollInterval={options?.pollInterval}
      />
    ),
    checkForUpdates: () => {
      // Trigger manual check
    }
  }
}
