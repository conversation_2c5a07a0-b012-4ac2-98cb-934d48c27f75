import {
  Modal,
  Modal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Box,
  SimpleGrid,
  useColorModeValue,
} from "@chakra-ui/react"
import { FiSave, FiFolderPlus } from "react-icons/fi"
import { useState } from "react"
import { useMutation } from "@tanstack/react-query"

import { 
  CaseFoldersService, 
  CaseFolderCreate,
  DEFAULT_FOLDER_COLORS 
} from "@/client/case-folders"
import { toaster } from "@/components/ui/toaster"

interface FolderCreateModalProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  parentFolderId?: string
  onSuccess: () => void
}

const FolderCreateModal = ({ 
  isOpen, 
  onClose, 
  caseId, 
  parentFolderId,
  onSuccess 
}: FolderCreateModalProps) => {
  // Form state
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [selectedColor, setSelectedColor] = useState(DEFAULT_FOLDER_COLORS[0])

  const selectedBg = useColorModeValue("gray.100", "gray.600")

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: CaseFolderCreate) => CaseFoldersService.createCaseFolder(caseId, data),
    onSuccess: () => {
      toaster.create({
        title: "Folder created",
        description: "Your folder has been created successfully.",
        status: "success",
      })
      onSuccess()
      handleReset()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create folder",
        status: "error",
      })
    },
  })

  const handleSubmit = () => {
    if (!name.trim()) {
      toaster.create({
        title: "Error",
        description: "Folder name cannot be empty",
        status: "error",
      })
      return
    }

    const folderData: CaseFolderCreate = {
      name: name.trim(),
      description: description.trim() || undefined,
      parent_folder_id: parentFolderId,
      color: selectedColor,
    }

    createMutation.mutate(folderData)
  }

  const handleReset = () => {
    setName("")
    setDescription("")
    setSelectedColor(DEFAULT_FOLDER_COLORS[0])
  }

  const handleClose = () => {
    if (!createMutation.isPending) {
      handleReset()
      onClose()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handleSubmit()
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={2}>
            <FiFolderPlus />
            <Text>Create New Folder</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton isDisabled={createMutation.isPending} />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* Folder Name */}
            <FormControl isRequired>
              <FormLabel fontSize="sm">Folder Name</FormLabel>
              <Input
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter folder name..."
                onKeyDown={handleKeyPress}
                maxLength={255}
              />
            </FormControl>

            {/* Description */}
            <FormControl>
              <FormLabel fontSize="sm">Description (Optional)</FormLabel>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Brief description of the folder..."
                rows={3}
                maxLength={1000}
              />
            </FormControl>

            {/* Color Selection */}
            <FormControl>
              <FormLabel fontSize="sm">Folder Color</FormLabel>
              <SimpleGrid columns={6} spacing={2}>
                {DEFAULT_FOLDER_COLORS.map((color) => (
                  <Box
                    key={color}
                    w="40px"
                    h="40px"
                    bg={color}
                    borderRadius="md"
                    cursor="pointer"
                    border="2px solid"
                    borderColor={selectedColor === color ? "blue.500" : "transparent"}
                    _hover={{ 
                      transform: "scale(1.1)",
                      shadow: "md" 
                    }}
                    transition="all 0.2s"
                    onClick={() => setSelectedColor(color)}
                  />
                ))}
              </SimpleGrid>
              <Text fontSize="xs" color="gray.500" mt={2}>
                Selected color: {selectedColor}
              </Text>
            </FormControl>

            {/* Preview */}
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" fontWeight="medium" mb={2}>Preview:</Text>
              <HStack spacing={3}>
                <Box
                  fontSize="2xl"
                  color={selectedColor}
                >
                  📁
                </Box>
                <VStack align="start" spacing={0}>
                  <Text fontWeight="medium" fontSize="sm">
                    {name || "Folder Name"}
                  </Text>
                  {description && (
                    <Text fontSize="xs" color="gray.600" noOfLines={1}>
                      {description}
                    </Text>
                  )}
                </VStack>
              </HStack>
            </Box>

            {/* Parent folder info */}
            {parentFolderId && (
              <Box p={3} bg="blue.50" borderRadius="md">
                <Text fontSize="sm" color="blue.700">
                  📁 This folder will be created inside the current folder
                </Text>
              </Box>
            )}

            {/* Help text */}
            <Text fontSize="xs" color="gray.500">
              Tip: Use Cmd/Ctrl + Enter to create quickly
            </Text>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button 
              variant="ghost" 
              onClick={handleClose}
              isDisabled={createMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={createMutation.isPending}
              leftIcon={<FiSave />}
              isDisabled={!name.trim()}
            >
              Create Folder
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default FolderCreateModal
