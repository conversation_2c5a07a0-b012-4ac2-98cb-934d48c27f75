import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON><PERSON>er,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  FormControl,
  FormLabel,
  Select,
  Textarea,
  Box,
  Badge,
} from "@chakra-ui/react"
import { FiEdit, FiSave } from "react-icons/fi"
import { useState, useEffect } from "react"
import { useMutation } from "@tanstack/react-query"

import { 
  DocumentCaseLink,
  DocumentCaseLinksService, 
  DocumentCaseLinkUpdate,
  LINK_TYPE_CONFIG,
  getLinkTypeInfo 
} from "@/client/document-case-links"
import { toaster } from "@/components/ui/toaster"

interface EditLinkModalProps {
  isOpen: boolean
  onClose: () => void
  documentId: string
  link: DocumentCaseLink
  onSuccess: () => void
}

const EditLinkModal = ({ 
  isOpen, 
  onClose, 
  documentId,
  link,
  onSuccess 
}: EditLinkModalProps) => {
  // Form state
  const [linkType, setLinkType] = useState(link.link_type)
  const [notes, setNotes] = useState(link.notes || "")

  // Reset form when link changes
  useEffect(() => {
    setLinkType(link.link_type)
    setNotes(link.notes || "")
  }, [link])

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: DocumentCaseLinkUpdate) => 
      DocumentCaseLinksService.updateDocumentCaseLink(documentId, link.id, data),
    onSuccess: () => {
      toaster.create({
        title: "Link updated",
        description: "Document link has been updated successfully.",
        status: "success",
      })
      onSuccess()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update link",
        status: "error",
      })
    },
  })

  const handleSubmit = () => {
    const updateData: DocumentCaseLinkUpdate = {
      link_type: linkType,
      notes: notes.trim() || undefined,
    }

    updateMutation.mutate(updateData)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handleSubmit()
    }
  }

  const linkTypeInfo = getLinkTypeInfo(linkType)
  const hasChanges = linkType !== link.link_type || notes !== (link.notes || "")

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={2}>
            <FiEdit />
            <Text>Edit Document Link</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* Current Case Info */}
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" fontWeight="medium" mb={2}>
                Linked Case:
              </Text>
              <VStack align="start" spacing={1}>
                <Text fontSize="sm" fontWeight="medium">
                  {link.case?.title || "Unknown Case"}
                </Text>
                <HStack spacing={2}>
                  <Text fontSize="xs" color="gray.600">
                    #{link.case?.case_number || "Unknown"}
                  </Text>
                  {link.case?.status && (
                    <Badge
                      colorScheme={
                        link.case.status === 'active' ? 'green' :
                        link.case.status === 'closed' ? 'gray' :
                        link.case.status === 'pending' ? 'yellow' : 'blue'
                      }
                      variant="subtle"
                      fontSize="xs"
                    >
                      {link.case.status}
                    </Badge>
                  )}
                  {link.case?.priority && (
                    <Badge
                      colorScheme={
                        link.case.priority === 'high' ? 'red' :
                        link.case.priority === 'medium' ? 'orange' : 'green'
                      }
                      variant="outline"
                      fontSize="xs"
                    >
                      {link.case.priority} priority
                    </Badge>
                  )}
                </HStack>
              </VStack>
            </Box>

            {/* Link Type */}
            <FormControl>
              <FormLabel fontSize="sm">Link Type</FormLabel>
              <Select
                value={linkType}
                onChange={(e) => setLinkType(e.target.value)}
                onKeyDown={handleKeyPress}
              >
                {Object.entries(LINK_TYPE_CONFIG).map(([type, config]) => (
                  <option key={type} value={type}>
                    {config.icon} {config.label}
                  </option>
                ))}
              </Select>
              <Text fontSize="xs" color="gray.500" mt={1}>
                {linkTypeInfo.description}
              </Text>
            </FormControl>

            {/* Notes */}
            <FormControl>
              <FormLabel fontSize="sm">Notes</FormLabel>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add any additional context or notes about this link..."
                rows={3}
                maxLength={1000}
                onKeyDown={handleKeyPress}
              />
              <Text fontSize="xs" color="gray.500" mt={1}>
                {notes.length}/1000 characters
              </Text>
            </FormControl>

            {/* Preview */}
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" fontWeight="medium" mb={2}>Preview:</Text>
              <HStack spacing={3}>
                <Box
                  fontSize="xl"
                  color={`${linkTypeInfo.color}.500`}
                >
                  {linkTypeInfo.icon}
                </Box>
                <VStack align="start" spacing={0}>
                  <Text fontSize="sm" fontWeight="medium">
                    {link.case?.title || "Unknown Case"}
                  </Text>
                  <Text fontSize="xs" color="gray.600">
                    {linkTypeInfo.label} • #{link.case?.case_number || "Unknown"}
                  </Text>
                  {notes && (
                    <Text fontSize="xs" color="gray.600" fontStyle="italic">
                      "{notes}"
                    </Text>
                  )}
                </VStack>
              </HStack>
            </Box>

            {/* Link Metadata */}
            <Box p={3} bg="blue.50" borderRadius="md">
              <Text fontSize="sm" fontWeight="medium" mb={2">
                Link Information:
              </Text>
              <VStack align="start" spacing={1} fontSize="xs" color="gray.600">
                <Text>
                  Created by: {link.linked_by_user?.full_name || link.linked_by_user?.email || "Unknown"}
                </Text>
                <Text>
                  Created: {new Date(link.linked_at).toLocaleDateString()}
                </Text>
                <Text>
                  Link ID: {link.id}
                </Text>
              </VStack>
            </Box>

            {/* Changes indicator */}
            {hasChanges && (
              <Box p={2} bg="orange.50" borderRadius="md" border="1px solid" borderColor="orange.200">
                <Text fontSize="xs" color="orange.700">
                  ⚠️ You have unsaved changes
                </Text>
              </Box>
            )}

            {/* Help text */}
            <Text fontSize="xs" color="gray.500">
              Tip: Use Cmd/Ctrl + Enter to save quickly
            </Text>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={updateMutation.isPending}
              leftIcon={<FiSave />}
              isDisabled={!hasChanges}
            >
              Save Changes
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default EditLinkModal
