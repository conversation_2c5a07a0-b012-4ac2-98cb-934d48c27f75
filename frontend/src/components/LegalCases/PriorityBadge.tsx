import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ck, Icon } from "@chakra-ui/react"
import {
  FiAlertTriangle,
  FiArrowUp,
  FiMinus,
  FiArrowDown
} from "react-icons/fi"

interface PriorityBadgeProps {
  priority: string
  size?: "sm" | "md" | "lg"
  showIcon?: boolean
  animated?: boolean
}

const PriorityBadge = ({
  priority,
  size = "sm",
  showIcon = true,
  animated = false
}: PriorityBadgeProps) => {

  const getPriorityConfig = (priority: string) => {
    const priorityStr = String(priority || 'medium').toLowerCase()
    switch (priorityStr) {
      case 'urgent':
        return {
          color: 'red',
          icon: FiAlertTriangle,
          label: 'Urgent',
          description: 'Requires immediate attention'
        }
      case 'high':
        return {
          color: 'orange',
          icon: FiArrowUp,
          label: 'High',
          description: 'High priority case'
        }
      case 'medium':
        return {
          color: 'yellow',
          icon: FiMinus,
          label: 'Medium',
          description: 'Normal priority case'
        }
      case 'low':
        return {
          color: 'gray',
          icon: FiArrowDown,
          label: 'Low',
          description: 'Low priority case'
        }
      default:
        return {
          color: 'gray',
          icon: FiMinus,
          label: priorityStr ? priorityStr.charAt(0).toUpperCase() + priorityStr.slice(1) : 'Medium',
          description: 'Unknown priority'
        }
    }
  }

  const priorityConfig = getPriorityConfig(priority)
  const IconComponent = priorityConfig.icon

  return (
    <Badge
      colorScheme={priorityConfig.color}
      size={size}
      textTransform="none"
      title={priorityConfig.description}
      cursor="help"
      transition={animated ? "all 0.2s ease-in-out" : "none"}
      _hover={animated ? {
        transform: "scale(1.05)",
        shadow: "md"
      } : {}}
    >
      <HStack spacing={1} align="center">
        {showIcon && (
          <Icon
            as={IconComponent}
            boxSize={size === "sm" ? "12px" : size === "md" ? "14px" : "16px"}
          />
        )}
        <span>{priorityConfig.label}</span>
      </HStack>
    </Badge>
  )
}

export default PriorityBadge
