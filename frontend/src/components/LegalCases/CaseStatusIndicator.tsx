import { HStack, Box, Text } from "@chakra-ui/react"
import CaseStatusBadge from "./CaseStatusBadge"

interface CaseStatusIndicatorProps {
  status: string
  size?: "sm" | "md" | "lg"
  layout?: "vertical" | "horizontal"
  showProgress?: boolean
  showTooltip?: boolean
  animated?: boolean
  className?: string
}

const CaseStatusIndicator = ({
  status,
  size = "md",
  layout = "horizontal",
  showProgress = true,
  showTooltip = false,
  animated = true,
  className = ""
}: CaseStatusIndicatorProps) => {

  // Ensure status is always a string
  const statusStr = String(status || 'open')

  // Simple progress indicator with colors
  const getProgressConfig = (status: string) => {
    const normalizedStatus = String(status || '').toLowerCase()
    switch (normalizedStatus) {
      case 'open':
        return { percentage: 10, color: 'blue.500' }
      case 'in_progress':
        return { percentage: 50, color: 'orange.500' }
      case 'under_review':
        return { percentage: 80, color: 'purple.500' }
      case 'closed':
        return { percentage: 100, color: 'green.500' }
      case 'archived':
        return { percentage: 100, color: 'gray.500' }
      default:
        return { percentage: 0, color: 'gray.300' }
    }
  }

  const progressConfig = getProgressConfig(statusStr)

  return (
    <Box className={className}>
      <HStack spacing={3} align="center">
        <CaseStatusBadge
          status={statusStr}
          size={size}
          showIcon={true}
          animated={animated}
        />
        {showProgress && (
          <Box flex={1} minW="60px">
            <Box
              w="100%"
              h="4px"
              bg="gray.200"
              borderRadius="full"
              overflow="hidden"
            >
              <Box
                w={`${String(progressConfig.percentage)}%`}
                h="100%"
                bg={progressConfig.color}
                borderRadius="full"
                transition={animated ? "width 0.3s ease" : "none"}
              />
            </Box>
          </Box>
        )}
      </HStack>
    </Box>
  )
}

export default CaseStatusIndicator
