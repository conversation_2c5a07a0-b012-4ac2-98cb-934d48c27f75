import { useState } from "react"
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Button, 
  Icon,
  useDisclosure,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  PopoverArrow,
  Badge
} from "@chakra-ui/react"
import { 
  FiCircle, 
  FiPlay, 
  FiEye, 
  FiCheck, 
  FiArchive,
  FiChevronDown,
  FiArrowRight
} from "react-icons/fi"
import CaseStatusBadge from "./CaseStatusBadge"

interface StatusOption {
  value: string
  label: string
  description: string
  icon: any
  color: string
}

interface StatusSelectorProps {
  currentStatus: string
  validTransitions: string[]
  onStatusChange: (newStatus: string) => void
  disabled?: boolean
  size?: "sm" | "md" | "lg"
}

const STATUS_OPTIONS: StatusOption[] = [
  {
    value: 'open',
    label: 'Open',
    description: 'Case is open and ready for work',
    icon: FiCircle,
    color: 'blue'
  },
  {
    value: 'in_progress',
    label: 'In Progress',
    description: 'Case is actively being worked on',
    icon: FiPlay,
    color: 'orange'
  },
  {
    value: 'under_review',
    label: 'Under Review',
    description: 'Case is being reviewed',
    icon: FiEye,
    color: 'purple'
  },
  {
    value: 'closed',
    label: 'Closed',
    description: 'Case has been completed',
    icon: FiCheck,
    color: 'green'
  },
  {
    value: 'archived',
    label: 'Archived',
    description: 'Case has been archived',
    icon: FiArchive,
    color: 'gray'
  }
]

export default function StatusSelector({
  currentStatus,
  validTransitions,
  onStatusChange,
  disabled = false,
  size = "md"
}: StatusSelectorProps) {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [selectedStatus, setSelectedStatus] = useState(currentStatus)

  const availableTransitions = STATUS_OPTIONS.filter(option => 
    validTransitions.includes(option.value) && option.value !== currentStatus
  )

  const handleStatusSelect = (newStatus: string) => {
    setSelectedStatus(newStatus)
    onStatusChange(newStatus)
    onClose()
  }

  const currentStatusConfig = STATUS_OPTIONS.find(option => option.value === currentStatus)

  if (availableTransitions.length === 0) {
    return (
      <CaseStatusBadge 
        status={currentStatus} 
        size={size} 
        showIcon={true}
        animated={false}
      />
    )
  }

  return (
    <Popover isOpen={isOpen} onOpen={onOpen} onClose={onClose} placement="bottom-start">
      <PopoverTrigger>
        <Button
          variant="ghost"
          size={size}
          disabled={disabled}
          rightIcon={<Icon as={FiChevronDown} />}
          _hover={{ bg: "gray.50" }}
          _active={{ bg: "gray.100" }}
        >
          <CaseStatusBadge 
            status={currentStatus} 
            size={size} 
            showIcon={true}
            animated={false}
          />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent width="300px">
        <PopoverArrow />
        <PopoverBody p={4}>
          <VStack align="stretch" spacing={3}>
            <Box>
              <Text fontSize="sm" fontWeight="medium" color="gray.600" mb={2}>
                Current Status
              </Text>
              <HStack>
                <CaseStatusBadge 
                  status={currentStatus} 
                  size="md" 
                  showIcon={true}
                  animated={false}
                />
                <Text fontSize="sm" color="gray.500">
                  {currentStatusConfig?.description}
                </Text>
              </HStack>
            </Box>

            {availableTransitions.length > 0 && (
              <Box>
                <Text fontSize="sm" fontWeight="medium" color="gray.600" mb={2}>
                  Available Transitions
                </Text>
                <VStack align="stretch" spacing={2}>
                  {availableTransitions.map((option) => (
                    <Button
                      key={option.value}
                      variant="ghost"
                      size="sm"
                      justifyContent="flex-start"
                      onClick={() => handleStatusSelect(option.value)}
                      _hover={{ bg: `${option.color}.50` }}
                      leftIcon={<Icon as={FiArrowRight} color={`${option.color}.500`} />}
                    >
                      <HStack flex={1} justify="space-between">
                        <VStack align="start" spacing={0}>
                          <HStack>
                            <Icon as={option.icon} color={`${option.color}.500`} />
                            <Text fontWeight="medium">{option.label}</Text>
                          </HStack>
                          <Text fontSize="xs" color="gray.500" textAlign="left">
                            {option.description}
                          </Text>
                        </VStack>
                      </HStack>
                    </Button>
                  ))}
                </VStack>
              </Box>
            )}
          </VStack>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  )
}
