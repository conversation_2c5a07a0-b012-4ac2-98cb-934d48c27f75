import {
  But<PERSON>,
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  Badge,
} from "@chakra-ui/react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FaExchangeAlt } from "react-icons/fa"

import { LegalCasesService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface StatusChangeModalProps {
  caseId: string
  currentStatus: string
  onStatusChange?: (newStatus: string) => void
}

const STATUS_OPTIONS = [
  { value: 'open', label: 'Open', color: 'blue' },
  { value: 'in_progress', label: 'In Progress', color: 'orange' },
  { value: 'under_review', label: 'Under Review', color: 'purple' },
  { value: 'closed', label: 'Closed', color: 'green' },
  { value: 'archived', label: 'Archived', color: 'gray' },
]

const StatusChangeModal = ({ caseId, currentStatus, onStatusChange }: StatusChangeModalProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState(currentStatus)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()

  const mutation = useMutation({
    mutationFn: (newStatus: string) =>
      LegalCasesService.updateCaseStatus({
        legalCaseId: caseId,
        status: newStatus as any
      }),
    onSuccess: () => {
      showSuccessToast("Case status updated successfully.")
      setIsOpen(false)
      onStatusChange?.(selectedStatus)
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
      queryClient.invalidateQueries({ queryKey: ["legal-case", caseId] })
      queryClient.invalidateQueries({ queryKey: ["legal-case", caseId, "status-history"] })
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
  })

  const handleStatusChange = () => {
    if (selectedStatus !== currentStatus) {
      mutation.mutate(selectedStatus)
    } else {
      setIsOpen(false)
    }
  }

  const getStatusColor = (status: string) => {
    const statusOption = STATUS_OPTIONS.find(option => option.value === status)
    return statusOption?.color || 'gray'
  }

  const getStatusLabel = (status: string) => {
    const statusOption = STATUS_OPTIONS.find(option => option.value === status)
    return statusOption?.label || status
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsOpen(open)}
    >
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" colorScheme="blue">
          <FaExchangeAlt fontSize="14px" />
          Change Status
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change Case Status</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <VStack gap={4} align="stretch">
            <Text mb={2}>
              Select the new status for this case:
            </Text>

            <VStack gap={3} align="stretch">
              {STATUS_OPTIONS.map((option) => (
                <HStack
                  key={option.value}
                  p={3}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={selectedStatus === option.value ? `${option.color}.300` : "gray.200"}
                  bg={selectedStatus === option.value ? `${option.color}.50` : "white"}
                  cursor="pointer"
                  onClick={() => setSelectedStatus(option.value)}
                  _hover={{ bg: `${option.color}.50` }}
                  justify="space-between"
                >
                  <HStack>
                    <Badge colorScheme={option.color}>
                      {option.label}
                    </Badge>
                    {option.value === currentStatus && (
                      <Text fontSize="sm" color="gray.600">
                        (Current)
                      </Text>
                    )}
                  </HStack>
                  {selectedStatus === option.value && (
                    <Text fontSize="sm" color={`${option.color}.600`} fontWeight="medium">
                      ✓ Selected
                    </Text>
                  )}
                </HStack>
              ))}
            </VStack>

            {selectedStatus !== currentStatus && (
              <Text fontSize="sm" color="gray.600" mt={2}>
                Status will change from{" "}
                <Badge colorScheme={getStatusColor(currentStatus)}>
                  {getStatusLabel(currentStatus)}
                </Badge>
                {" "}to{" "}
                <Badge colorScheme={getStatusColor(selectedStatus)}>
                  {getStatusLabel(selectedStatus)}
                </Badge>
              </Text>
            )}
          </VStack>
        </DialogBody>
        <DialogFooter>
          <HStack gap={2}>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={mutation.isPending}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleStatusChange}
              loading={mutation.isPending}
              disabled={selectedStatus === currentStatus}
            >
              {selectedStatus === currentStatus ? "No Change" : "Update Status"}
            </Button>
          </HStack>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default StatusChangeModal
