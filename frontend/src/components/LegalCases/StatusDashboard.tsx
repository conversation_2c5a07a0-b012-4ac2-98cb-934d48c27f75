import {
  Box,
  SimpleGrid,
  VStack,
  HStack,
  Text,
  Progress,
  Circle,




  useColorModeValue
} from "@chakra-ui/react"
import { FiTrendingUp, FiTrendingDown, FiMinus } from "react-icons/fi"
import CaseStatusBadge from "./CaseStatusBadge"

interface StatusStats {
  open: number
  in_progress: number
  under_review: number
  closed: number
  archived: number
}

interface StatusDashboardProps {
  stats: StatusStats
  totalCases: number
  showTrends?: boolean
  compact?: boolean
  className?: string
}

const StatusDashboard = ({
  stats,
  totalCases,
  showTrends = true,
  compact = false,
  className = ""
}: StatusDashboardProps) => {
  
  const cardBg = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const textColor = useColorModeValue("gray.600", "gray.300")

  // Calculate percentages
  const getPercentage = (count: number) => {
    return totalCases > 0 ? Math.round((count / totalCases) * 100) : 0
  }

  // Status configuration with colors and trends (mock data)
  const statusConfig = [
    {
      key: "open" as keyof StatusStats,
      label: "Open",
      count: stats.open,
      percentage: getPercentage(stats.open),
      color: "blue",
      trend: { direction: "up", value: 5 }, // Mock trend data
      description: "Cases ready for work"
    },
    {
      key: "in_progress" as keyof StatusStats,
      label: "In Progress",
      count: stats.in_progress,
      percentage: getPercentage(stats.in_progress),
      color: "orange",
      trend: { direction: "up", value: 12 },
      description: "Cases being worked on"
    },
    {
      key: "under_review" as keyof StatusStats,
      label: "Under Review",
      count: stats.under_review,
      percentage: getPercentage(stats.under_review),
      color: "purple",
      trend: { direction: "down", value: 3 },
      description: "Cases under review"
    },
    {
      key: "closed" as keyof StatusStats,
      label: "Closed",
      count: stats.closed,
      percentage: getPercentage(stats.closed),
      color: "green",
      trend: { direction: "up", value: 8 },
      description: "Completed cases"
    },
    {
      key: "archived" as keyof StatusStats,
      label: "Archived",
      count: stats.archived,
      percentage: getPercentage(stats.archived),
      color: "gray",
      trend: { direction: "neutral", value: 0 },
      description: "Archived cases"
    }
  ]

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case "up":
        return FiTrendingUp
      case "down":
        return FiTrendingDown
      default:
        return FiMinus
    }
  }

  const getTrendColor = (direction: string) => {
    switch (direction) {
      case "up":
        return "green.500"
      case "down":
        return "red.500"
      default:
        return "gray.500"
    }
  }

  if (compact) {
    return (
      <Box className={className} bg={cardBg} p={4} borderRadius="lg" border="1px" borderColor={borderColor}>
        <VStack spacing={3}>
          <Text fontWeight="bold" fontSize="md">Case Status Overview</Text>
          
          <SimpleGrid columns={5} spacing={2} w="full">
            {statusConfig.map((status) => (
              <VStack key={status.key} spacing={1}>
                <CaseStatusBadge status={status.key} size="sm" showIcon={true} />
                <Text fontSize="lg" fontWeight="bold">{status.count}</Text>
                <Text fontSize="xs" color={textColor}>{status.percentage}%</Text>
              </VStack>
            ))}
          </SimpleGrid>
          
          <Text fontSize="sm" color={textColor}>
            Total: {totalCases} cases
          </Text>
        </VStack>
      </Box>
    )
  }

  return (
    <Box className={className}>
      <VStack spacing={6}>
        {/* Header */}
        <Box textAlign="center">
          <Text fontSize="2xl" fontWeight="bold" mb={2}>
            Case Status Dashboard
          </Text>
          <Text color={textColor}>
            Overview of all {totalCases} legal cases
          </Text>
        </Box>

        {/* Status Cards */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 5 }} spacing={4} w="full">
          {statusConfig.map((status) => {
            const TrendIcon = getTrendIcon(status.trend.direction)
            
            return (
              <Box
                key={status.key}
                bg={cardBg}
                p={6}
                borderRadius="lg"
                border="1px"
                borderColor={borderColor}
                shadow="sm"
                _hover={{ shadow: "md" }}
                transition="all 0.2s"
              >
                <VStack spacing={4} align="stretch">
                  {/* Header */}
                  <HStack justify="space-between" align="center">
                    <CaseStatusBadge 
                      status={status.key} 
                      size="sm" 
                      showIcon={true}
                      animated={false}
                    />
                    
                    {showTrends && status.trend.value > 0 && (
                      <HStack spacing={1}>
                        <TrendIcon 
                          size="14px" 
                          color={getTrendColor(status.trend.direction)}
                        />
                        <Text 
                          fontSize="xs" 
                          color={getTrendColor(status.trend.direction)}
                          fontWeight="medium"
                        >
                          {status.trend.value}%
                        </Text>
                      </HStack>
                    )}
                  </HStack>

                  {/* Stats */}
                  <Box textAlign="center">
                    <Box textAlign="center">
                      {status.count}
                    </Text>
                    <Box textAlign="center">
                      {status.description}
                    </Text>
                    <Box textAlign="center">
                      {status.percentage}% of total cases
                    </Text>
                  </Box>

                  {/* Progress Bar */}
                  <Progress
                    value={status.percentage}
                    colorScheme={status.color}
                    size="sm"
                    borderRadius="full"
                    bg="gray.100"
                  />
                </VStack>
              </Box>
            )
          })}
        </SimpleGrid>

        {/* Summary */}
        <Box 
          bg={cardBg} 
          p={6} 
          borderRadius="lg" 
          border="1px" 
          borderColor={borderColor}
          w="full"
        >
          <VStack spacing={4}>
            <Text fontWeight="bold" fontSize="lg">
              Workflow Distribution
            </Text>
            
            <HStack spacing={8} justify="center" wrap="wrap">
              <VStack spacing={1}>
                <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                  {stats.open + stats.in_progress}
                </Text>
                <Text fontSize="sm" color={textColor}>Active Cases</Text>
              </VStack>
              
              <VStack spacing={1}>
                <Text fontSize="2xl" fontWeight="bold" color="purple.500">
                  {stats.under_review}
                </Text>
                <Text fontSize="sm" color={textColor}>Under Review</Text>
              </VStack>
              
              <VStack spacing={1}>
                <Text fontSize="2xl" fontWeight="bold" color="green.500">
                  {stats.closed}
                </Text>
                <Text fontSize="sm" color={textColor}>Completed</Text>
              </VStack>
              
              <VStack spacing={1}>
                <Text fontSize="2xl" fontWeight="bold" color="gray.500">
                  {getPercentage(stats.closed)}%
                </Text>
                <Text fontSize="sm" color={textColor}>Completion Rate</Text>
              </VStack>
            </HStack>
          </VStack>
        </Box>
      </VStack>
    </Box>
  )
}

export default StatusDashboard
