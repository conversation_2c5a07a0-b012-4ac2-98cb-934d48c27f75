/**
=========================================================
* EquiNova Material Bulk Status Update Modal
=========================================================

* Material-UI implementation of bulk status update modal
* Replaces Chakra UI modal with Material Design

=========================================================
*/

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Typography,
  Box,
  Alert,
  Chip,
  Stack,
  CircularProgress,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  Update as UpdateIcon,
} from '@mui/icons-material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useStatusChangeToast } from './StatusChangeToast';

interface MaterialBulkStatusUpdateModalProps {
  open: boolean;
  onClose: () => void;
  selectedCaseIds: string[];
  onSuccess: () => void;
}

const STATUS_OPTIONS = [
  { value: 'open', label: 'Open', color: 'info' },
  { value: 'in_progress', label: 'In Progress', color: 'warning' },
  { value: 'under_review', label: 'Under Review', color: 'secondary' },
  { value: 'closed', label: 'Closed', color: 'success' },
  { value: 'archived', label: 'Archived', color: 'default' },
] as const;

const MaterialBulkStatusUpdateModal: React.FC<MaterialBulkStatusUpdateModalProps> = ({
  open,
  onClose,
  selectedCaseIds,
  onSuccess,
}) => {
  const [newStatus, setNewStatus] = useState('');
  const [notes, setNotes] = useState('');

  const queryClient = useQueryClient();
  const { showBulkStatusChangeToast } = useStatusChangeToast();

  const bulkUpdateMutation = useMutation({
    mutationFn: async (data: { case_ids: string[], new_status: string, notes?: string }) => {
      console.log('🚀 Starting bulk update with data:', data);
      
      const { request } = await import('@/client/core/request');
      const { OpenAPI } = await import('@/client/core/OpenAPI');

      try {
        const result = await request(OpenAPI, {
          method: 'PATCH',
          url: '/api/v1/legal-cases/bulk/status',
          body: data,
        });
        return result;
      } catch (error) {
        console.error('Bulk update failed:', error);
        throw error;
      }
    },
    onSuccess: (result: any) => {
      // Invalidate and refetch legal cases
      queryClient.invalidateQueries({ queryKey: ['legal-cases'] });

      // Show toast notifications
      if (result.success_count > 0 || result.failed_count > 0) {
        showBulkStatusChangeToast(
          'previous', // We don't have the old status here, could be improved
          newStatus,
          result.success_count,
          result.failed_count
        );
      }

      // Close modal and clear selection
      onSuccess();
      handleClose();
    },
    onError: (error: any) => {
      console.error('Bulk update failed:', error);
      // Error handling will be shown in the UI
    },
  });

  const handleClose = () => {
    setNewStatus('');
    setNotes('');
    onClose();
  };

  const handleSubmit = () => {
    if (!newStatus || selectedCaseIds.length === 0) return;

    bulkUpdateMutation.mutate({
      case_ids: selectedCaseIds,
      new_status: newStatus,
      notes: notes || undefined,
    });
  };

  const selectedStatusOption = STATUS_OPTIONS.find(option => option.value === newStatus);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', pb: 1 }}>
        <Box display="flex" alignItems="center" gap={1}>
          <UpdateIcon color="primary" />
          <Typography variant="h6" fontWeight="bold">
            Bulk Status Update
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        <Stack spacing={3}>
          {/* Selected Cases Info */}
          <Alert severity="info" sx={{ borderRadius: 2 }}>
            <Typography variant="body2" fontWeight="medium" gutterBottom>
              Selected Cases: {selectedCaseIds.length}
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={0.5} mt={1}>
              {selectedCaseIds.slice(0, 5).map((id) => (
                <Chip
                  key={id}
                  label={`Case ${id.slice(-6)}`}
                  size="small"
                  variant="outlined"
                />
              ))}
              {selectedCaseIds.length > 5 && (
                <Chip
                  label={`+${selectedCaseIds.length - 5} more`}
                  size="small"
                  variant="outlined"
                  color="primary"
                />
              )}
            </Box>
          </Alert>

          {/* Status Selection */}
          <FormControl fullWidth required>
            <InputLabel>New Status</InputLabel>
            <Select
              value={newStatus}
              label="New Status"
              onChange={(e) => setNewStatus(e.target.value)}
              disabled={bulkUpdateMutation.isPending}
            >
              {STATUS_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Chip
                      label={option.label}
                      color={option.color as any}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Notes */}
          <TextField
            label="Notes (Optional)"
            multiline
            rows={3}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about this status change..."
            disabled={bulkUpdateMutation.isPending}
            fullWidth
          />

          {/* Preview */}
          {newStatus && (
            <Box
              sx={{
                p: 2,
                backgroundColor: 'grey.50',
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'grey.200',
              }}
            >
              <Typography variant="subtitle2" gutterBottom>
                Preview:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedCaseIds.length} case(s) will be updated to{' '}
                {selectedStatusOption && (
                  <Chip
                    label={selectedStatusOption.label}
                    color={selectedStatusOption.color as any}
                    size="small"
                    sx={{ mx: 0.5 }}
                  />
                )}
                status.
              </Typography>
            </Box>
          )}

          {/* Error Display */}
          {bulkUpdateMutation.isError && (
            <Alert severity="error" sx={{ borderRadius: 2 }}>
              <Typography variant="body2">
                Failed to update cases: {bulkUpdateMutation.error?.message || 'Unknown error'}
              </Typography>
            </Alert>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 2 }}>
        <Button
          onClick={handleClose}
          disabled={bulkUpdateMutation.isPending}
          color="inherit"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={!newStatus || selectedCaseIds.length === 0 || bulkUpdateMutation.isPending}
          startIcon={
            bulkUpdateMutation.isPending ? (
              <CircularProgress size={16} />
            ) : (
              <UpdateIcon />
            )
          }
        >
          {bulkUpdateMutation.isPending ? 'Updating...' : 'Update Status'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MaterialBulkStatusUpdateModal;
