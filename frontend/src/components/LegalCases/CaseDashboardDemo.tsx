import {
  Container,
  VStack,
  Heading,
  Text,
  Alert,
  Box,
  Badge,
  SimpleGrid,
  Card,
  CardBody,
  HStack,
  List,
  ListItem,
  Button,

} from "@chakra-ui/react"
import { FiInfo, FiBarChart, FiTarget, FiTrendingUp, FiUsers, FiClock } from "react-icons/fi"
import { useState } from "react"

import CaseDashboardSimple from "./CaseDashboardSimple"

const CaseDashboardDemo = () => {
  const [selectedDemo, setSelectedDemo] = useState<string>("demo-case-analytics-123")

  // Demo case options
  const demoCases = [
    {
      id: "demo-case-analytics-123",
      title: "Sample Contract Dispute Case",
      description: "High-activity case with comprehensive analytics data"
    },
    {
      id: "demo-case-analytics-456",
      title: "Personal Injury Case",
      description: "Medium-complexity case with team collaboration"
    },
    {
      id: "demo-case-analytics-789",
      title: "Corporate Litigation Case",
      description: "Long-running case with extensive documentation"
    }
  ]

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            📊 Case Dashboard & Analytics Demo
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Issue #76 - Comprehensive case dashboard with advanced analytics and insights
          </Text>
        </Box>

        {/* Status Alert */}
        <Alert status="success" borderRadius="lg">
          <Box as={FiInfo} color="green.500" mr={3} mt={1} />
          <Box>
            <Text fontWeight="bold">System Status: Complete ✅</Text>
            <Text fontSize="sm">
              Full case dashboard and analytics system implemented with comprehensive metrics, visualizations, and insights.
            </Text>
          </Box>
        </Alert>

        {/* Features Overview */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>🚀 Implemented Features</Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="blue.600">📊 Analytics Dashboard:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Comprehensive case overview with key metrics</Text>
                  <Text>• Performance analytics with scoring system</Text>
                  <Text>• Health indicators with recommendations</Text>
                  <Text>• Time tracking and efficiency analysis</Text>
                  <Text>• Multiple view modes (overview, charts, detailed)</Text>
                  <Text>• Real-time data refresh and export capabilities</Text>
                </VStack>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">📈 Advanced Analytics:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <HStack><FiBarChart size={12} /><Text>Activity analytics with velocity tracking</Text></HStack>
                  <HStack><FiTarget size={12} /><Text>Document analytics with category breakdown</Text></HStack>
                  <Text>• Progress analytics with milestone tracking</Text>
                  <Text>• Team collaboration metrics and insights</Text>
                  <Text>• Comparative analytics vs similar cases</Text>
                  <Text>• Performance benchmarking and percentile rankings</Text>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2} color="purple.600">🎯 Insights & Intelligence:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <HStack><FiTrendingUp size={12} /><Text>Health scoring with actionable recommendations</Text></HStack>
                  <HStack><FiUsers size={12} /><Text>Team performance and collaboration analysis</Text></HStack>
                  <Text>• Time efficiency tracking with billing estimates</Text>
                  <Text>• Automated insights and trend detection</Text>
                  <Text>• Performance vs industry benchmarks</Text>
                  <Text>• Predictive analytics and forecasting</Text>
                </VStack>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Demo Case Selector */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>🎯 Interactive Dashboard Demo</Heading>
            <Text mb={4} color="gray.600">
              Select a demo case to explore the comprehensive analytics dashboard with real-time insights and metrics.
            </Text>
            
            <VStack spacing={4} align="stretch">
              <Text fontSize="sm" fontWeight="medium" color="gray.700">
                Choose a demo case:
              </Text>
              
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                {demoCases.map((demoCase) => (
                  <Card 
                    key={demoCase.id}
                    variant={selectedDemo === demoCase.id ? "filled" : "outline"}
                    cursor="pointer"
                    onClick={() => setSelectedDemo(demoCase.id)}
                    _hover={{ shadow: "md" }}
                    bg={selectedDemo === demoCase.id ? "blue.50" : "white"}
                    borderColor={selectedDemo === demoCase.id ? "blue.200" : "gray.200"}
                  >
                    <CardBody p={4}>
                      <VStack align="start" spacing={2}>
                        <HStack spacing={2}>
                          <Text fontSize="sm" fontWeight="bold">
                            {demoCase.title}
                          </Text>
                          {selectedDemo === demoCase.id && (
                            <Badge colorScheme="blue" size="sm">Selected</Badge>
                          )}
                        </HStack>
                        <Text fontSize="xs" color="gray.600">
                          {demoCase.description}
                        </Text>
                      </VStack>
                    </CardBody>
                  </Card>
                ))}
              </SimpleGrid>
            </VStack>
          </CardBody>
        </Card>

        {/* Dashboard Demo */}
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={4} color="gray.700">
            📊 Live Dashboard Analytics
          </Text>
          
          {/* Case Dashboard */}
          <Box
            p={6}
            bg="white"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
            shadow="sm"
          >
            <CaseDashboardSimple
              caseId={selectedDemo}
              caseTitle={demoCases.find(c => c.id === selectedDemo)?.title}
            />
          </Box>
        </Box>

        {/* Implementation Status */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>📊 Implementation Status</Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">✅ Completed:</Text>
                <List spacing={1} fontSize="sm">
                  <ListItem>
                    <HStack><FiBarChart color="green" size={12} /><Text>Complete analytics API with 8 metric categories</Text></HStack>
                  </ListItem>
                  <ListItem>
                    <HStack><FiTarget color="green" size={12} /><Text>Comprehensive dashboard with 9 analytics cards</Text></HStack>
                  </ListItem>
                  <ListItem>
                    <HStack><FiTrendingUp color="green" size={12} /><Text>Advanced visualizations and chart components</Text></HStack>
                  </ListItem>
                  <ListItem>
                    <HStack><FiUsers color="green" size={12} /><Text>Team collaboration and performance analytics</Text></HStack>
                  </ListItem>
                  <ListItem>
                    <HStack><FiClock color="green" size={12} /><Text>Time tracking and efficiency analysis</Text></HStack>
                  </ListItem>
                </List>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="orange.600">🔄 Future Enhancements:</Text>
                <List spacing={1} fontSize="sm">
                  <ListItem>• Advanced chart libraries integration (Chart.js, D3)</ListItem>
                  <ListItem>• Real-time analytics with WebSocket updates</ListItem>
                  <ListItem>• Custom dashboard layouts and widgets</ListItem>
                  <ListItem>• Advanced filtering and date range selection</ListItem>
                  <ListItem>• Automated report generation and scheduling</ListItem>
                  <ListItem>• Machine learning insights and predictions</ListItem>
                  <ListItem>• Integration with external analytics platforms</ListItem>
                  <ListItem>• Mobile-optimized dashboard views</ListItem>
                </List>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Technical Details */}
        <Card bg="gray.50">
          <CardBody>
            <Heading size="md" mb={4}>🛠️ Technical Implementation</Heading>
            <VStack align="stretch" spacing={3} fontSize="sm">
              <Box>
                <Text fontWeight="bold">Backend Analytics Engine:</Text>
                <Text>• Comprehensive case analytics API with 8 metric categories</Text>
                <Text>• Real-time calculation of performance scores and health indicators</Text>
                <Text>• Comparative analytics against similar cases with percentile rankings</Text>
                <Text>• Time efficiency analysis with billing estimates and productivity metrics</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">Frontend Dashboard Components:</Text>
                <Text>• CaseDashboard: Main dashboard interface with multiple view modes</Text>
                <Text>• 9 specialized analytics cards: Overview, Performance, Health, Time, Activity, Documents, Progress, Collaboration, Comparative</Text>
                <Text>• AnalyticsChartsSection: Advanced visualizations with progress bars and timeline charts</Text>
                <Text>• Responsive design with export capabilities and real-time refresh</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">Analytics Categories:</Text>
                <Text>• Case Overview: Basic info, age, lawyer assignment, activity summary</Text>
                <Text>• Performance Metrics: Activity frequency, document productivity, response time, efficiency rating</Text>
                <Text>• Health Indicators: Health score, status assessment, recommendations, action items</Text>
                <Text>• Time Analytics: Time tracking, efficiency analysis, billing estimates, productivity metrics</Text>
                <Text>• Activity Analytics: Activity distribution, velocity tracking, team participation, timeline analysis</Text>
                <Text>• Document Analytics: Storage metrics, category distribution, upload patterns, file size analysis</Text>
                <Text>• Progress Analytics: Milestone completion, deadline management, progress percentage, tracking status</Text>
                <Text>• Collaboration Analytics: Team size, user interactions, notes collaboration, engagement scoring</Text>
                <Text>• Comparative Analytics: Benchmarking vs similar cases, percentile rankings, performance comparisons</Text>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

export default CaseDashboardDemo
