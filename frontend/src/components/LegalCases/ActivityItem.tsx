import {
  Box,
  VStack,
  H<PERSON>tack,
  Text,
  Circle,
  Flex,
  Badge,
  Collapse,
  IconButton,
  Tooltip,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react"
import {
  FiChevronDown,
  FiChevronUp,
  <PERSON><PERSON>lock,
  FiUser,
} from "react-icons/fi"

import { 
  CaseActivity,
  getActivityTypeInfo,
  formatActivityDate,
  getActivityMetadataDisplay 
} from "@/client/case-activities"

interface ActivityItemProps {
  activity: CaseActivity
  isLast?: boolean
}

const ActivityItem = ({ activity, isLast = false }: ActivityItemProps) => {
  const { isOpen: showDetails, onToggle: toggleDetails } = useDisclosure()
  
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const bgColor = useColorModeValue("white", "gray.800")
  const hoverBg = useColorModeValue("gray.50", "gray.700")

  const activityInfo = getActivityTypeInfo(activity.activity_type)
  const metadataDetails = getActivityMetadataDisplay(activity)
  const hasDetails = metadataDetails.length > 0 || activity.activity_metadata

  return (
    <Box position="relative">
      <HStack spacing={4} align="start">
        {/* Timeline Icon */}
        <Box position="relative" zIndex={1}>
          <Circle
            size="10"
            bg={`${activityInfo.color}.100`}
            color={`${activityInfo.color}.600`}
            borderWidth="2px"
            borderColor={`${activityInfo.color}.200`}
            fontSize="lg"
          >
            {activityInfo.icon}
          </Circle>

          {/* Timeline Line */}
          {!isLast && (
            <Box
              position="absolute"
              left="50%"
              top="10"
              width="2px"
              height="8"
              bg={borderColor}
              transform="translateX(-50%)"
            />
          )}
        </Box>

        {/* Activity Content */}
        <Box
          flex={1}
          bg={bgColor}
          borderRadius="lg"
          border="1px solid"
          borderColor={borderColor}
          p={4}
          _hover={{ bg: hoverBg }}
          transition="all 0.2s"
        >
          <Flex justify="space-between" align="start" mb={2}>
            <VStack align="start" spacing={1} flex={1}>
              <HStack spacing={2}>
                <Text fontSize="sm" fontWeight="medium">
                  {activity.description}
                </Text>
                <Badge
                  colorScheme={activityInfo.color}
                  variant="subtle"
                  fontSize="xs"
                >
                  {activityInfo.label}
                </Badge>
              </HStack>

              <HStack spacing={3} fontSize="xs" color="gray.500">
                {activity.user && (
                  <HStack spacing={1}>
                    <FiUser size={12} />
                    <Text>
                      {activity.user.full_name || activity.user.email}
                    </Text>
                  </HStack>
                )}
                <HStack spacing={1}>
                  <FiClock size={12} />
                  <Text>{formatActivityDate(activity.created_at)}</Text>
                </HStack>
              </HStack>
            </VStack>

            {/* Details Toggle */}
            {hasDetails && (
              <Tooltip label={showDetails ? "Hide details" : "Show details"}>
                <IconButton
                  variant="ghost"
                  size="xs"
                  icon={showDetails ? <FiChevronUp /> : <FiChevronDown />}
                  onClick={toggleDetails}
                />
              </Tooltip>
            )}
          </Flex>

          {/* Expandable Details */}
          {hasDetails && (
            <Collapse in={showDetails}>
              <Box
                mt={3}
                pt={3}
                borderTop="1px solid"
                borderColor={borderColor}
              >
                <VStack align="stretch" spacing={2}>
                  {metadataDetails.length > 0 && (
                    <Box>
                      <Text fontSize="xs" fontWeight="medium" color="gray.600" mb={2}>
                        Details:
                      </Text>
                      <VStack align="stretch" spacing={1}>
                        {metadataDetails.map((detail, index) => (
                          <Text key={index} fontSize="xs" color="gray.600">
                            • {detail}
                          </Text>
                        ))}
                      </VStack>
                    </Box>
                  )}

                  {/* Raw metadata for debugging (only in development) */}
                  {process.env.NODE_ENV === 'development' && activity.activity_metadata && (
                    <Box>
                      <Text fontSize="xs" fontWeight="medium" color="gray.600" mb={2}>
                        Raw Metadata:
                      </Text>
                      <Box
                        bg="gray.100"
                        borderRadius="md"
                        p={2}
                        fontSize="xs"
                        fontFamily="mono"
                        color="gray.700"
                        maxH="100px"
                        overflowY="auto"
                      >
                        <pre>{JSON.stringify(activity.activity_metadata, null, 2)}</pre>
                      </Box>
                    </Box>
                  )}
                </VStack>
              </Box>
            </Collapse>
          )}
        </Box>
      </HStack>
    </Box>
  )
}

export default ActivityItem
