import {
  Box,
  VStack,
  <PERSON><PERSON><PERSON>ck,
  Text,
  Button,
  Flex,
  Spacer,
  Spinner,
  <PERSON>ert,
  Badge,
  IconButton,
  Tooltip,
  Collapse,
  useColorModeValue,
} from "@chakra-ui/react"
import {
  <PERSON><PERSON><PERSON>,
  FiFilter,
  FiRefreshCw,
  FiCalendar,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"

import {
  CaseActivitiesService,
  CaseActivity,
  groupActivitiesByDate,
  getActivityStats
} from "@/client/case-activities"
import { useDebounce } from "@/hooks/useDebounce"
import ActivityItem from "./ActivityItem"
import ActivityFilters from "./ActivityFilters"
import ActivityStats from "./ActivityStats"

interface CaseTimelineProps {
  caseId: string
}

const CaseTimeline = ({ caseId }: CaseTimelineProps) => {
  // State for filters and search
  const [search, setSearch] = useState("")
  const [selectedActivityType, setSelectedActivityType] = useState<string>("")
  const [startDate, setStartDate] = useState<string>("")
  const [endDate, setEndDate] = useState<string>("")
  const [showFilters, setShowFilters] = useState(false)
  const [showStats, setShowStats] = useState(false)

  const debouncedSearch = useDebounce(search, 300)

  const borderColor = useColorModeValue("gray.200", "gray.600")

  // Fetch activities
  const {
    data: activitiesData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["case-activities", caseId, {
      search: debouncedSearch,
      activity_type: selectedActivityType || undefined,
      start_date: startDate || undefined,
      end_date: endDate || undefined,
    }],
    queryFn: () => CaseActivitiesService.getCaseActivities(caseId, {
      search: debouncedSearch || undefined,
      activity_type: selectedActivityType || undefined,
      start_date: startDate || undefined,
      end_date: endDate || undefined,
      limit: 100, // Get more activities for timeline
    }),
    enabled: !!caseId,
  })

  const activities = activitiesData?.data || []
  const totalCount = activitiesData?.count || 0
  const groupedActivities = groupActivitiesByDate(activities)
  const activityStats = getActivityStats(activities)

  const handleClearFilters = () => {
    setSearch("")
    setSelectedActivityType("")
    setStartDate("")
    setEndDate("")
  }

  const hasActiveFilters = search || selectedActivityType || startDate || endDate

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load case timeline. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={2}>
            <FiClock />
            <Text fontSize="xl" fontWeight="bold">
              Case Timeline
            </Text>
            <Badge variant="subtle" colorScheme="blue">
              {totalCount} activities
            </Badge>
          </HStack>
          <Text fontSize="sm" color="gray.600">
            Complete history of case activities and changes
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Activity statistics">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiCalendar />}
              onClick={() => setShowStats(!showStats)}
              colorScheme={showStats ? "blue" : "gray"}
            />
          </Tooltip>
          <Tooltip label="Filter activities">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiFilter />}
              onClick={() => setShowFilters(!showFilters)}
              colorScheme={showFilters ? "blue" : "gray"}
            />
          </Tooltip>
          <Tooltip label="Refresh timeline">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
        </HStack>
      </Flex>

      {/* Statistics Panel */}
      <Collapse in={showStats}>
        <Box mb={6}>
          <ActivityStats stats={activityStats} />
        </Box>
      </Collapse>

      {/* Filters Panel */}
      <Collapse in={showFilters}>
        <Box mb={6}>
          <ActivityFilters
            search={search}
            onSearchChange={setSearch}
            selectedActivityType={selectedActivityType}
            onActivityTypeChange={setSelectedActivityType}
            startDate={startDate}
            onStartDateChange={setStartDate}
            endDate={endDate}
            onEndDateChange={setEndDate}
            onClearFilters={handleClearFilters}
            hasActiveFilters={hasActiveFilters}
          />
        </Box>
      </Collapse>

      {/* Timeline Content */}
      {isLoading ? (
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={4} color="gray.600">Loading timeline...</Text>
        </Box>
      ) : activities.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            {hasActiveFilters
              ? "No activities match your filters"
              : "No activities recorded yet"
            }
          </Text>
          {hasActiveFilters && (
            <Button onClick={handleClearFilters} variant="outline">
              Clear Filters
            </Button>
          )}
        </Box>
      ) : (
        <VStack spacing={6} align="stretch">
          {Object.entries(groupedActivities)
            .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
            .map(([dateKey, dayActivities]) => (
              <Box key={dateKey}>
                {/* Date Header */}
                <HStack spacing={3} mb={4}>
                  <Text fontSize="sm" fontWeight="bold" color="gray.600">
                    {formatDateHeader(dateKey)}
                  </Text>
                  <Box flex={1} height="1px" bg={borderColor} />
                  <Badge variant="subtle" fontSize="xs">
                    {dayActivities.length} activities
                  </Badge>
                </HStack>

                {/* Activities for this date */}
                <VStack spacing={3} align="stretch" pl={4}>
                  {dayActivities.map((activity, index) => (
                    <ActivityItem
                      key={activity.id}
                      activity={activity}
                      isLast={index === dayActivities.length - 1}
                    />
                  ))}
                </VStack>
              </Box>
            ))}
        </VStack>
      )}
    </Box>
  )
}

const formatDateHeader = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return "Today"
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday"
    } else {
      return date.toLocaleDateString(undefined, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
  } catch {
    return dateString
  }
}

export default CaseTimeline
