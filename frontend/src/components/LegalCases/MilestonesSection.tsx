import {
  <PERSON>,
  <PERSON>Stack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>lex,
  Spacer,
  useDisclosure,
  Spinner,
  Alert,
  Badge,
  IconButton,
  Tooltip,
  Select,

} from "@chakra-ui/react"
import {
  <PERSON>Flag,
  FiPlus,
  FiRefreshCw,
  FiFilter,
  <PERSON>Layers,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseProgressService, 
  CaseMilestone,
  sortMilestonesByOrder,
  MILESTONE_STATUS_CONFIG,
  MILESTONE_TYPE_CONFIG 
} from "@/client/case-progress"
import { toaster } from "@/components/ui/toaster"
import useAuth from "@/hooks/useAuth"
import MilestoneCard from "./MilestoneCard"
import CreateMilestoneModal from "./CreateMilestoneModal"
import EditMilestoneModal from "./EditMilestoneModal"
import MilestoneTemplateModal from "./MilestoneTemplateModal"

interface MilestonesSectionProps {
  caseId: string
  caseType?: string
  onProgressUpdate?: () => void
}

const MilestonesSection = ({ 
  caseId, 
  caseType,
  onProgressUpdate 
}: MilestonesSectionProps) => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  
  const [selectedMilestone, setSelectedMilestone] = useState<CaseMilestone | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>("")
  const [typeFilter, setTypeFilter] = useState<string>("")
  const [includeCompleted, setIncludeCompleted] = useState(true)
  
  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure()
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()
  const { isOpen: isTemplateOpen, onOpen: onTemplateOpen, onClose: onTemplateClose } = useDisclosure()

  const borderColor = "gray.200"

  // Fetch milestones
  const { 
    data: milestonesData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ["case-milestones", caseId, { 
      status: statusFilter || undefined,
      milestone_type: typeFilter || undefined,
      include_completed: includeCompleted 
    }],
    queryFn: () => CaseProgressService.getCaseMilestones(caseId, {
      status: statusFilter || undefined,
      milestone_type: typeFilter || undefined,
      include_completed: includeCompleted,
      limit: 100
    }),
    enabled: !!caseId,
  })

  const milestones = milestonesData?.data || []
  const totalCount = milestonesData?.count || 0
  const sortedMilestones = sortMilestonesByOrder(milestones)

  // Create from template mutation
  const createFromTemplateMutation = useMutation({
    mutationFn: () => CaseProgressService.createMilestonesFromTemplate(caseId, caseType),
    onSuccess: (data) => {
      toaster.create({
        title: "Milestones created",
        description: `${data.count} milestones created from template.`,
        status: "success",
      })
      queryClient.invalidateQueries({ queryKey: ["case-milestones", caseId] })
      onProgressUpdate?.()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create milestones from template",
        status: "error",
      })
    },
  })

  const handleCreateSuccess = () => {
    onCreateClose()
    queryClient.invalidateQueries({ queryKey: ["case-milestones", caseId] })
    onProgressUpdate?.()
  }

  const handleEditSuccess = () => {
    onEditClose()
    setSelectedMilestone(null)
    queryClient.invalidateQueries({ queryKey: ["case-milestones", caseId] })
    onProgressUpdate?.()
  }

  const handleEdit = (milestone: CaseMilestone) => {
    setSelectedMilestone(milestone)
    onEditOpen()
  }

  const handleMilestoneUpdate = () => {
    queryClient.invalidateQueries({ queryKey: ["case-milestones", caseId] })
    onProgressUpdate?.()
  }

  const handleCreateFromTemplate = () => {
    createFromTemplateMutation.mutate()
  }

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load milestones. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={2}>
            <FiFlag />
            <Text fontSize="lg" fontWeight="bold">
              Milestones
            </Text>
            <Badge variant="subtle" colorScheme="blue">
              {totalCount} total
            </Badge>
          </HStack>
          <Text fontSize="sm" color="gray.600">
            Track key milestones and progress markers for this case
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Refresh milestones">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
          {caseType && (
            <Button
              leftIcon={<FiLayers />}
              size="sm"
              variant="outline"
              onClick={handleCreateFromTemplate}
              isLoading={createFromTemplateMutation.isPending}
            >
              From Template
            </Button>
          )}
          <Button
            leftIcon={<FiPlus />}
            colorScheme="blue"
            size="sm"
            onClick={onCreateOpen}
          >
            Add Milestone
          </Button>
        </HStack>
      </Flex>

      {/* Filters */}
      <HStack spacing={4} mb={6}>
        <HStack spacing={2}>
          <FiFilter />
          <Text fontSize="sm" fontWeight="medium">Filters:</Text>
        </HStack>
        
        <Select
          placeholder="All statuses"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          size="sm"
          maxW="150px"
        >
          {Object.entries(MILESTONE_STATUS_CONFIG).map(([status, config]) => (
            <option key={status} value={status}>
              {config.label}
            </option>
          ))}
        </Select>

        <Select
          placeholder="All types"
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value)}
          size="sm"
          maxW="150px"
        >
          {Object.entries(MILESTONE_TYPE_CONFIG).map(([type, config]) => (
            <option key={type} value={type}>
              {config.label}
            </option>
          ))}
        </Select>

        <Button
          size="sm"
          variant={includeCompleted ? "solid" : "outline"}
          onClick={() => setIncludeCompleted(!includeCompleted)}
        >
          {includeCompleted ? "Hide" : "Show"} Completed
        </Button>
      </HStack>

      {/* Content */}
      {isLoading ? (
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={4} color="gray.600">Loading milestones...</Text>
        </Box>
      ) : sortedMilestones.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            {statusFilter || typeFilter || !includeCompleted 
              ? "No milestones match your filters"
              : "No milestones found"
            }
          </Text>
          <Text fontSize="sm" color="gray.600" mb={6}>
            {statusFilter || typeFilter || !includeCompleted
              ? "Try adjusting your filters or create a new milestone."
              : "Create milestones to track important progress markers for this case."
            }
          </Text>
          <HStack spacing={3} justify="center">
            <Button
              leftIcon={<FiPlus />}
              colorScheme="blue"
              onClick={onCreateOpen}
            >
              Create Milestone
            </Button>
            {caseType && (
              <Button
                leftIcon={<FiLayers />}
                variant="outline"
                onClick={handleCreateFromTemplate}
                isLoading={createFromTemplateMutation.isPending}
              >
                Use Template
              </Button>
            )}
          </HStack>
        </Box>
      ) : (
        <VStack spacing={3} align="stretch">
          {sortedMilestones.map((milestone) => (
            <MilestoneCard
              key={milestone.id}
              milestone={milestone}
              caseId={caseId}
              onEdit={() => handleEdit(milestone)}
              onUpdate={handleMilestoneUpdate}
            />
          ))}
        </VStack>
      )}

      {/* Create Milestone Modal */}
      <CreateMilestoneModal
        isOpen={isCreateOpen}
        onClose={onCreateClose}
        caseId={caseId}
        caseType={caseType}
        onSuccess={handleCreateSuccess}
      />

      {/* Edit Milestone Modal */}
      {selectedMilestone && (
        <EditMilestoneModal
          isOpen={isEditOpen}
          onClose={onEditClose}
          caseId={caseId}
          milestone={selectedMilestone}
          onSuccess={handleEditSuccess}
        />
      )}

      {/* Template Modal */}
      <MilestoneTemplateModal
        isOpen={isTemplateOpen}
        onClose={onTemplateClose}
        caseType={caseType}
      />
    </Box>
  )
}

export default MilestonesSection
