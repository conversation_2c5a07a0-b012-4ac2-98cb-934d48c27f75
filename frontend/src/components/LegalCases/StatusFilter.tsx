import {
  <PERSON>,
  <PERSON><PERSON>tack,
  V<PERSON>tack,
  <PERSON>,
  Button,
  Badge,
  Flex,
  useColorModeValue
} from "@chakra-ui/react"
import { useState } from "react"
import CaseStatusBadge from "./CaseStatusBadge"

interface StatusFilterProps {
  selectedStatuses: string[]
  onStatusChange: (statuses: string[]) => void
  statusCounts?: Record<string, number>
  showCounts?: boolean
  layout?: "horizontal" | "vertical"
  size?: "sm" | "md" | "lg"
  className?: string
}

const STATUS_OPTIONS = [
  { value: "open", label: "Open", description: "Cases that are open and ready for work" },
  { value: "in_progress", label: "In Progress", description: "Cases actively being worked on" },
  { value: "under_review", label: "Under Review", description: "Cases being reviewed" },
  { value: "closed", label: "Closed", description: "Completed cases" },
  { value: "archived", label: "Archived", description: "Archived cases" }
]

const StatusFilter = ({
  selectedStatuses,
  onStatusChange,
  statusCounts = {},
  showCounts = true,
  layout = "horizontal",
  size = "md",
  className = ""
}: StatusFilterProps) => {
  
  const bgColor = useColorModeValue("gray.50", "gray.700")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  
  const handleStatusToggle = (status: string) => {
    if (selectedStatuses.includes(status)) {
      // Remove status from selection
      onStatusChange(selectedStatuses.filter(s => s !== status))
    } else {
      // Add status to selection
      onStatusChange([...selectedStatuses, status])
    }
  }

  const handleSelectAll = () => {
    if (selectedStatuses.length === STATUS_OPTIONS.length) {
      // Deselect all
      onStatusChange([])
    } else {
      // Select all
      onStatusChange(STATUS_OPTIONS.map(option => option.value))
    }
  }

  const handleSelectNone = () => {
    onStatusChange([])
  }

  const isSelected = (status: string) => selectedStatuses.includes(status)
  const isAllSelected = selectedStatuses.length === STATUS_OPTIONS.length
  const isNoneSelected = selectedStatuses.length === 0

  const Container = layout === "vertical" ? VStack : HStack

  return (
    <Box className={className} bg={bgColor} p={4} borderRadius="lg" border="1px" borderColor={borderColor}>
      <VStack align="stretch" spacing={4}>
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Text fontWeight="bold" fontSize={size === "sm" ? "md" : "lg"}>
            Filter by Status
          </Text>
          
          <HStack spacing={2}>
            <Button
              size="xs"
              variant={isAllSelected ? "solid" : "outline"}
              colorScheme="blue"
              onClick={handleSelectAll}
            >
              {isAllSelected ? "Deselect All" : "Select All"}
            </Button>
            
            {!isNoneSelected && (
              <Button
                size="xs"
                variant="outline"
                onClick={handleSelectNone}
              >
                Clear
              </Button>
            )}
          </HStack>
        </HStack>

        {/* Status Options */}
        <Container spacing={3} align="stretch" wrap={layout === "horizontal" ? "wrap" : undefined}>
          {STATUS_OPTIONS.map((option) => {
            const count = statusCounts[option.value] || 0
            const selected = isSelected(option.value)
            
            return (
              <Button
                key={option.value}
                variant={selected ? "solid" : "outline"}
                colorScheme={selected ? "blue" : "gray"}
                size={size}
                onClick={() => handleStatusToggle(option.value)}
                justifyContent="space-between"
                minW={layout === "horizontal" ? "auto" : "200px"}
                h="auto"
                p={3}
                title={option.description}
              >
                <HStack spacing={2} flex={1}>
                  <CaseStatusBadge 
                    status={option.value} 
                    size="sm" 
                    showIcon={true}
                    animated={false}
                  />
                  
                  {showCounts && (
                    <Badge
                      colorScheme={selected ? "white" : "gray"}
                      variant={selected ? "solid" : "subtle"}
                      borderRadius="full"
                      px={2}
                      py={1}
                      fontSize="xs"
                    >
                      {count}
                    </Badge>
                  )}
                </HStack>
              </Button>
            )
          })}
        </Container>

        {/* Summary */}
        {selectedStatuses.length > 0 && (
          <Box>
            <Text fontSize="sm" color="gray.600">
              {selectedStatuses.length === STATUS_OPTIONS.length 
                ? "Showing all statuses"
                : `Showing ${selectedStatuses.length} of ${STATUS_OPTIONS.length} statuses`
              }
            </Text>
            
            {selectedStatuses.length < STATUS_OPTIONS.length && (
              <HStack spacing={1} mt={2} wrap="wrap">
                <Text fontSize="xs" color="gray.500">Active filters:</Text>
                {selectedStatuses.map((status) => (
                  <CaseStatusBadge 
                    key={status}
                    status={status} 
                    size="sm" 
                    showIcon={false}
                    animated={false}
                  />
                ))}
              </HStack>
            )}
          </Box>
        )}
      </VStack>
    </Box>
  )
}

export default StatusFilter
