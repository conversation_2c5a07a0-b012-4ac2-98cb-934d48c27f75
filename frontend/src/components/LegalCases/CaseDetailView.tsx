import {
  Box,
  <PERSON>ton,
  Flex,
  <PERSON>ing,
  HStack,
  SimpleGrid,
  Text,
  VStack,
  Badge,
  Separator,
} from "@chakra-ui/react"
import { FaArrowLeft, FaShare } from "react-icons/fa"
import { useState } from "react"

import type { LegalCaseDetail } from "@/client"
import useAuth from "@/hooks/useAuth"
import CaseStatusBadge from "./CaseStatusBadge"
import CaseTimeline from "./CaseTimeline"
import StatusChangeModal from "./StatusChangeModal"
import EditLegalCase from "./EditLegalCase"
import DeleteLegalCase from "./DeleteLegalCase"
import Breadcrumb from "../Common/Breadcrumb"
import CaseNotesSection from "./CaseNotesSection"
import FileOrganizationView from "./FileOrganizationView"
import CaseLinkedDocuments from "./CaseLinkedDocuments"

interface CaseDetailViewProps {
  caseDetail: LegalCaseDetail
  onBack: () => void
}

const CaseDetailView = ({ caseDetail, onBack }: CaseDetailViewProps) => {
  const { user } = useAuth()
  const [currentStatus, setCurrentStatus] = useState(caseDetail.status || 'open')

  // Determine user permissions
  const canEdit = user?.is_superuser ||
    (user?.role === 'lawyer' && caseDetail.lawyer_id === user.id)

  const canDelete = user?.is_superuser ||
    (user?.role === 'lawyer' && caseDetail.lawyer_id === user.id)

  const handleStatusChange = (newStatus: string) => {
    setCurrentStatus(newStatus as any)
  }

  return (
    <VStack gap={6} align="stretch">
      {/* Breadcrumb Navigation */}
      <Breadcrumb />

      {/* Back Button */}
      <HStack>
        <Button
          variant="ghost"
          onClick={onBack}
          size="sm"
        >
          <FaArrowLeft />
          Back to Cases
        </Button>
      </HStack>

      {/* Case Header */}
      <Box
        p={6}
        bg="white"
        borderRadius="lg"
        borderWidth="1px"
        borderColor="gray.200"
        shadow="sm"
      >
        <Flex justify="space-between" align="start" mb={4}>
          <VStack align="start" gap={2}>
            <Heading size="lg">{caseDetail.title}</Heading>
            <Text color="gray.600" fontSize="lg">
              Client: {caseDetail.client_name}
            </Text>
            <HStack gap={3}>
              <CaseStatusBadge status={currentStatus} />
              <Badge
                colorScheme={
                  caseDetail.priority === 'urgent' ? 'red' :
                  caseDetail.priority === 'high' ? 'orange' :
                  caseDetail.priority === 'medium' ? 'blue' : 'gray'
                }
                textTransform="capitalize"
              >
                {caseDetail.priority}
              </Badge>
            </HStack>
          </VStack>

          <HStack gap={2} flexWrap="wrap">
            {/* Status Change Button */}
            {canEdit && (
              <StatusChangeModal
                caseId={caseDetail.id}
                currentStatus={currentStatus}
                onStatusChange={handleStatusChange}
              />
            )}

            {/* Edit Button */}
            {canEdit && (
              <EditLegalCase legalCase={caseDetail} />
            )}

            {/* Share Button */}
            <Button
              size="sm"
              variant="outline"
              colorScheme="gray"
            >
              <FaShare />
              Share
            </Button>
          </HStack>
        </Flex>

        {caseDetail.description && (
          <Box>
            <Text color="gray.700">{caseDetail.description}</Text>
          </Box>
        )}
      </Box>

      {/* Main Content Grid */}
      <SimpleGrid columns={{ base: 1, lg: 3 }} gap={6}>
        {/* Left Column - Case Information */}
        <VStack gap={6} align="stretch" gridColumn={{ base: "1", lg: "1 / 3" }}>
          {/* Case Details Card */}
          <Box
            p={6}
            bg="white"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
            shadow="sm"
          >
            <Heading size="md" mb={4}>
              Case Information
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} gap={4}>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Case Type
                </Text>
                <Badge colorScheme="purple" textTransform="capitalize">
                  {String(caseDetail.case_type || 'unknown').replace('_', ' ')}
                </Badge>
              </VStack>

              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Opening Date
                </Text>
                <Text fontSize="sm">
                  {caseDetail.opening_date ? new Date(String(caseDetail.opening_date)).toLocaleDateString() : 'N/A'}
                </Text>
              </VStack>

              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Status
                </Text>
                <CaseStatusBadge status={currentStatus} />
              </VStack>

              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Priority
                </Text>
                <Badge
                  colorScheme={
                    caseDetail.priority === 'urgent' ? 'red' :
                    caseDetail.priority === 'high' ? 'orange' :
                    caseDetail.priority === 'medium' ? 'blue' : 'gray'
                  }
                  textTransform="capitalize"
                >
                  {String(caseDetail.priority || 'medium')}
                </Badge>
              </VStack>
            </SimpleGrid>
          </Box>

          {/* Case Timeline */}
          <Box
            p={6}
            bg="white"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
            shadow="sm"
          >
            <Heading size="md" mb={4}>
              Recent Activity
            </Heading>
            <CaseTimeline activities={(caseDetail.recent_activities || []).map(activity => ({
              id: String(activity?.id || ''),
              activity_type: String(activity?.activity_type || ''),
              description: String(activity?.description || ''),
              created_at: String(activity?.created_at || ''),
              user: activity?.user ? {
                full_name: String(activity.user.full_name || ''),
                email: String(activity.user.email || '')
              } : undefined
            }))} />
          </Box>
        </VStack>

        {/* Right Column - Actions and Status */}
        <VStack gap={6} align="stretch">
          {/* Quick Actions */}
          <Box
            p={4}
            bg="white"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
            shadow="sm"
          >
            <Heading size="sm" mb={4}>
              Quick Actions
            </Heading>
            <VStack gap={2} align="stretch">
              {/* Status Change */}
              {canEdit && (
                <StatusChangeModal
                  caseId={caseDetail.id}
                  currentStatus={currentStatus}
                  onStatusChange={handleStatusChange}
                />
              )}

              {/* Edit Case */}
              {canEdit && (
                <EditLegalCase legalCase={caseDetail} />
              )}

              {/* Share Case */}
              <Button
                size="sm"
                variant="outline"
                colorScheme="gray"
              >
                <FaShare />
                Share Case
              </Button>

              {/* Delete Case */}
              {canDelete && (
                <DeleteLegalCase id={caseDetail.id} />
              )}
            </VStack>
          </Box>

          {/* Case Status */}
          <Box
            p={4}
            bg="white"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
            shadow="sm"
          >
            <Heading size="sm" mb={3}>
              Case Status
            </Heading>
            <VStack gap={3} align="stretch">
              <Flex justify="space-between" align="center">
                <Text fontSize="sm" color="gray.600">
                  Current Status
                </Text>
                <CaseStatusBadge status={currentStatus} />
              </Flex>

              <Flex justify="space-between" align="center">
                <Text fontSize="sm" color="gray.600">
                  Priority
                </Text>
                <Badge
                  colorScheme={
                    caseDetail.priority === 'urgent' ? 'red' :
                    caseDetail.priority === 'high' ? 'orange' :
                    caseDetail.priority === 'medium' ? 'blue' : 'gray'
                  }
                  textTransform="capitalize"
                >
                  {String(caseDetail.priority || 'medium')}
                </Badge>
              </Flex>

              <Separator />

              <Flex justify="space-between" align="center">
                <Text fontSize="sm" color="gray.600">
                  Case Type
                </Text>
                <Badge colorScheme="purple" textTransform="capitalize">
                  {String(caseDetail.case_type || 'unknown').replace('_', ' ')}
                </Badge>
              </Flex>

              <Flex justify="space-between" align="center">
                <Text fontSize="sm" color="gray.600">
                  Opening Date
                </Text>
                <Text fontSize="sm">
                  {caseDetail.opening_date ? new Date(String(caseDetail.opening_date)).toLocaleDateString() : 'N/A'}
                </Text>
              </Flex>
            </VStack>
          </Box>

          {/* Assigned Lawyer */}
          {caseDetail.lawyer && (
            <Box
              p={4}
              bg="white"
              borderRadius="lg"
              borderWidth="1px"
              borderColor="gray.200"
              shadow="sm"
            >
              <Heading size="sm" mb={3}>
                Assigned Lawyer
              </Heading>
              <VStack gap={2} align="start">
                <Text fontWeight="medium">
                  {String(caseDetail.lawyer.full_name || caseDetail.lawyer.email || 'Unknown')}
                </Text>
                <Text fontSize="sm" color="gray.600">
                  {String(caseDetail.lawyer.email || 'No email')}
                </Text>
                <Badge colorScheme="green" textTransform="capitalize">
                  {String(caseDetail.lawyer.role || 'lawyer')}
                </Badge>
              </VStack>
            </Box>
          )}
        </VStack>
      </SimpleGrid>

      {/* Notes Section */}
      <Box
        p={6}
        bg="white"
        borderRadius="lg"
        borderWidth="1px"
        borderColor="gray.200"
        shadow="sm"
      >
        <CaseNotesSection caseId={caseDetail.id} />
      </Box>

      {/* File Organization Section */}
      <Box
        p={6}
        bg="white"
        borderRadius="lg"
        borderWidth="1px"
        borderColor="gray.200"
        shadow="sm"
      >
        <FileOrganizationView caseId={caseDetail.id} />
      </Box>

      {/* Timeline Section */}
      <Box
        p={6}
        bg="white"
        borderRadius="lg"
        borderWidth="1px"
        borderColor="gray.200"
        shadow="sm"
      >
        <CaseTimeline caseId={caseDetail.id} />
      </Box>

      {/* Linked Documents Section */}
      <Box
        p={6}
        bg="white"
        borderRadius="lg"
        borderWidth="1px"
        borderColor="gray.200"
        shadow="sm"
      >
        <CaseLinkedDocuments caseId={caseDetail.id} />
      </Box>
    </VStack>
  )
}

export default CaseDetailView
