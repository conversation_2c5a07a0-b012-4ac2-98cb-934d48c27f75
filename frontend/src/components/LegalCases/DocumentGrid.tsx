import {
  SimpleGrid,
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  IconButton,
  Tooltip,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
} from "@chakra-ui/react"
import {
  FiDownload,
  FiEdit,
  FiTrash2,
  FiMoreVertical,
  FiEye,
  FiShare2,
  FiLock,
} from "react-icons/fi"
import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { formatDistanceToNow } from "date-fns"

import { 
  CaseDocument, 
  CaseDocumentsService,
  getFileTypeInfo,
  getCategoryInfo,
  formatFileSize 
} from "@/client/case-documents"
import { toaster } from "@/components/ui/toaster"
import DocumentEditModal from "./DocumentEditModal"

interface DocumentGridProps {
  documents: CaseDocument[]
  caseId: string
  onUpdated: () => void
  onDeleted: () => void
}

const DocumentGrid = ({ documents, caseId, onUpdated, onDeleted }: DocumentGridProps) => {
  const queryClient = useQueryClient()
  const [selectedDocument, setSelectedDocument] = useState<CaseDocument | null>(null)
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()

  const cardBg = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const hoverBg = useColorModeValue("gray.50", "gray.700")

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (documentId: string) => CaseDocumentsService.deleteCaseDocument(caseId, documentId),
    onSuccess: () => {
      toaster.create({
        title: "Document deleted",
        description: "The document has been deleted successfully.",
        status: "success",
      })
      onDeleted()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to delete document",
        status: "error",
      })
    },
  })

  const handleDownload = async (document: CaseDocument) => {
    try {
      const blob = await CaseDocumentsService.downloadCaseDocument(caseId, document.id)
      const url = window.URL.createObjectURL(blob)
      const link = window.document.createElement("a")
      link.href = url
      link.download = document.original_filename
      window.document.body.appendChild(link)
      link.click()
      window.document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toaster.create({
        title: "Download started",
        description: `Downloading ${document.original_filename}`,
        status: "success",
      })
    } catch (error: any) {
      toaster.create({
        title: "Download failed",
        description: error.response?.data?.detail || "Failed to download document",
        status: "error",
      })
    }
  }

  const handleEdit = (document: CaseDocument) => {
    setSelectedDocument(document)
    onEditOpen()
  }

  const handleDelete = (document: CaseDocument) => {
    if (window.confirm(`Are you sure you want to delete "${document.original_filename}"?`)) {
      deleteMutation.mutate(document.id)
    }
  }

  const handleEditSuccess = () => {
    onEditClose()
    setSelectedDocument(null)
    onUpdated()
  }

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch {
      return "Unknown time"
    }
  }

  return (
    <>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3, xl: 4 }} spacing={4}>
        {documents.map((document) => {
          const fileTypeInfo = getFileTypeInfo(document.content_type)
          const categoryInfo = getCategoryInfo(document.category)

          return (
            <Box
              key={document.id}
              bg={cardBg}
              border="1px solid"
              borderColor={borderColor}
              borderRadius="lg"
              p={4}
              cursor="pointer"
              transition="all 0.2s"
              _hover={{ 
                shadow: "md", 
                bg: hoverBg,
                transform: "translateY(-2px)" 
              }}
              position="relative"
            >
              {/* Document indicators */}
              <HStack position="absolute" top={2} right={2} spacing={1}>
                {document.is_confidential && (
                  <Tooltip label="Confidential">
                    <Box color="red.500">
                      <FiLock size={12} />
                    </Box>
                  </Tooltip>
                )}
                {document.is_shared_with_client && (
                  <Tooltip label="Shared with client">
                    <Box color="green.500">
                      <FiShare2 size={12} />
                    </Box>
                  </Tooltip>
                )}
              </HStack>

              <VStack align="stretch" spacing={3}>
                {/* File icon and type */}
                <VStack spacing={2}>
                  <Text fontSize="3xl">{fileTypeInfo.icon}</Text>
                  <Badge colorScheme={fileTypeInfo.color} variant="subtle" fontSize="xs">
                    {fileTypeInfo.label}
                  </Badge>
                </VStack>

                {/* File name */}
                <Text
                  fontWeight="medium"
                  fontSize="sm"
                  textAlign="center"
                  noOfLines={2}
                  title={document.original_filename}
                >
                  {document.original_filename}
                </Text>

                {/* Category and size */}
                <VStack spacing={1}>
                  <Badge
                    colorScheme={categoryInfo.color}
                    variant="outline"
                    fontSize="xs"
                  >
                    {categoryInfo.icon} {categoryInfo.label}
                  </Badge>
                  <Text fontSize="xs" color="gray.500">
                    {formatFileSize(document.file_size)}
                  </Text>
                </VStack>

                {/* Upload info */}
                <VStack spacing={1}>
                  <Text fontSize="xs" color="gray.500" textAlign="center">
                    {document.uploader?.full_name || "Unknown"}
                  </Text>
                  <Text fontSize="xs" color="gray.500" textAlign="center">
                    {formatDate(document.uploaded_at)}
                  </Text>
                </VStack>

                {/* Tags */}
                {document.tags && document.tags.length > 0 && (
                  <HStack spacing={1} justify="center" flexWrap="wrap">
                    {document.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="subtle" colorScheme="blue" fontSize="xs">
                        #{tag}
                      </Badge>
                    ))}
                    {document.tags.length > 2 && (
                      <Badge variant="subtle" colorScheme="gray" fontSize="xs">
                        +{document.tags.length - 2}
                      </Badge>
                    )}
                  </HStack>
                )}

                {/* Actions */}
                <HStack justify="center" spacing={2}>
                  {document.can_download && (
                    <Tooltip label="Download">
                      <IconButton
                        variant="ghost"
                        size="sm"
                        icon={<FiDownload />}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDownload(document)
                        }}
                      />
                    </Tooltip>
                  )}

                  {(document.can_edit || document.can_delete) && (
                    <Menu>
                      <MenuButton
                        as={IconButton}
                        variant="ghost"
                        size="sm"
                        icon={<FiMoreVertical />}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <MenuList>
                        {document.can_edit && (
                          <MenuItem
                            icon={<FiEdit />}
                            onClick={() => handleEdit(document)}
                          >
                            Edit
                          </MenuItem>
                        )}
                        {document.can_delete && (
                          <MenuItem
                            icon={<FiTrash2 />}
                            onClick={() => handleDelete(document)}
                            color="red.500"
                          >
                            Delete
                          </MenuItem>
                        )}
                      </MenuList>
                    </Menu>
                  )}
                </HStack>
              </VStack>
            </Box>
          )
        })}
      </SimpleGrid>

      {/* Edit Modal */}
      {selectedDocument && (
        <DocumentEditModal
          isOpen={isEditOpen}
          onClose={onEditClose}
          caseId={caseId}
          document={selectedDocument}
          onSuccess={handleEditSuccess}
        />
      )}
    </>
  )
}

export default DocumentGrid
