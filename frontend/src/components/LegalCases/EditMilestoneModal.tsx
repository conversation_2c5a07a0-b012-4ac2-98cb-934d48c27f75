import {
  VSta<PERSON>,
  H<PERSON><PERSON><PERSON>,
  <PERSON>,
  But<PERSON>,
  Box,
} from "@chakra-ui/react"
import { FiEdit } from "react-icons/fi"

import { CaseMilestone } from "@/client/case-progress"

interface EditMilestoneModalProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  milestone: CaseMilestone
  onSuccess: () => void
}

const EditMilestoneModal = ({ 
  isOpen, 
  onClose, 
  caseId,
  milestone,
  onSuccess 
}: EditMilestoneModalProps) => {
  if (!isOpen) return null

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg="rgba(0,0,0,0.5)"
      zIndex={1000}
      display="flex"
      alignItems="center"
      justifyContent="center"
      onClick={onClose}
    >
      <Box
        bg="white"
        borderRadius="lg"
        p={6}
        maxW="md"
        w="full"
        mx={4}
        onClick={(e) => e.stopPropagation()}
      >
        <VStack spacing={4} align="stretch">
          <HStack spacing={2}>
            <FiEdit />
            <Text fontSize="lg" fontWeight="bold">Edit Milestone</Text>
          </HStack>

          <Text>Edit milestone functionality coming soon...</Text>
          <Text fontSize="sm" color="gray.600">
            Milestone: {milestone.title}
          </Text>

          <HStack spacing={3} justify="flex-end">
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={onSuccess}>
              Save Changes
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Box>
  )
}

export default EditMilestoneModal
