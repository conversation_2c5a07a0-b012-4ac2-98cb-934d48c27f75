import {
  Con<PERSON><PERSON>,
  VS<PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ing,
  HStack,
  <PERSON>ge,
  Box,
  SimpleGrid,
  Spinner,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import BulletproofStatusHistory from "./BulletproofStatusHistory"

interface SimpleCaseDetailPageProps {
  caseId: string
  onBack: () => void
}

function getLegalCaseQueryOptions(caseId: string) {
  return {
    queryFn: async () => {
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      return request(OpenAPI, {
        method: 'GET',
        url: `/api/v1/legal-cases/${caseId}`,
      })
    },
    queryKey: ["legal-case", caseId],
  }
}

export default function SimpleCaseDetailPage({ caseId, onBack }: SimpleCaseDetailPageProps) {
  const { data: legalCase, isLoading, error } = useQuery({
    ...getLegalCaseQueryOptions(caseId),
    enabled: !!caseId,
  })

  if (isLoading) {
    return (
      <Container maxW="full" py={8}>
        <VStack gap={4} align="center" justify="center" minH="400px">
          <Spinner size="xl" />
          <Text>Loading case details...</Text>
        </VStack>
      </Container>
    )
  }

  if (error || !legalCase) {
    return (
      <Container maxW="full" py={8}>
        <VStack gap={4} align="center" justify="center" minH="400px">
          <Text color="red.500" fontSize="lg">
            Error loading case details
          </Text>
          <Button onClick={onBack}>
            Back to Legal Cases
          </Button>
        </VStack>
      </Container>
    )
  }

  // Ultra-safe string conversion to prevent React error #130
  const safeText = (value: any): string => {
    if (value === null || value === undefined) return "N/A"
    if (typeof value === "string") return value
    if (typeof value === "number") return value.toString()
    if (typeof value === "boolean") return value ? "Yes" : "No"
    return "N/A"
  }

  // Extract only the essential data as strings
  const title = safeText(legalCase.title)
  const clientName = safeText(legalCase.client_name)
  const caseType = safeText(legalCase.case_type)
  const openingDate = safeText(legalCase.opening_date)
  const priority = safeText(legalCase.priority)
  const status = safeText(legalCase.status)
  const description = safeText(legalCase.description)

  // Status color mapping
  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case "open": return "blue"
      case "in_progress": return "orange"
      case "under_review": return "purple"
      case "closed": return "green"
      case "archived": return "gray"
      default: return "gray"
    }
  }

  return (
    <Container maxW="full" py={8}>
      <VStack gap={6} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Heading size="lg">{title}</Heading>
          <Button onClick={onBack} variant="outline">
            ← Back to Cases
          </Button>
        </HStack>

        {/* Case Information */}
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          <Box p={6} bg="white" borderRadius="lg" borderWidth="1px" borderColor="gray.200" shadow="sm">
            <Text fontWeight="bold" mb={4} fontSize="lg">Case Information</Text>
            <VStack align="stretch" spacing={3}>
              <HStack justify="space-between">
                <Text fontWeight="medium">Client:</Text>
                <Text>{clientName}</Text>
              </HStack>
              <HStack justify="space-between">
                <Text fontWeight="medium">Type:</Text>
                <Text>{caseType}</Text>
              </HStack>
              <HStack justify="space-between">
                <Text fontWeight="medium">Opening Date:</Text>
                <Text>{openingDate}</Text>
              </HStack>
              <HStack justify="space-between">
                <Text fontWeight="medium">Priority:</Text>
                <Badge colorScheme={priority === "high" ? "red" : priority === "medium" ? "yellow" : "green"}>
                  {priority}
                </Badge>
              </HStack>
            </VStack>
          </Box>

          <Box p={6} bg="white" borderRadius="lg" borderWidth="1px" borderColor="gray.200" shadow="sm">
            <Text fontWeight="bold" mb={4} fontSize="lg">Status</Text>
            <VStack align="stretch" spacing={4}>
              <HStack justify="space-between">
                <Text fontWeight="medium">Current Status:</Text>
                <Badge colorScheme={getStatusColor(status)} size="lg">
                  {status}
                </Badge>
              </HStack>
              <Text fontSize="sm" color="gray.600">
                Status management features are being enhanced. Full functionality will be available soon.
              </Text>
            </VStack>
          </Box>
        </SimpleGrid>

        {/* Description */}
        {description && description !== "N/A" && (
          <Box p={6} bg="white" borderRadius="lg" borderWidth="1px" borderColor="gray.200" shadow="sm">
            <Text fontWeight="bold" mb={3} fontSize="lg">Description</Text>
            <Text lineHeight="1.6">{description}</Text>
          </Box>
        )}

        {/* Status History - Fixed React Error #130 */}
        <BulletproofStatusHistory caseId={safeText(caseId)} />

        {/* Debug Information */}
        <Box p={4} bg="gray.50" borderRadius="md" borderWidth="1px" borderColor="gray.200">
          <Text fontWeight="bold" mb={2} fontSize="sm" color="gray.600">Debug Info</Text>
          <Text fontSize="xs" color="gray.500">
            Case ID: {safeText(caseId)} | Status: No React Error #130 🎉
          </Text>
        </Box>
      </VStack>
    </Container>
  )
}
