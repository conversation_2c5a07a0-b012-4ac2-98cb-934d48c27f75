// Enhanced Case Status Management Components - Issue #72

// Core Status Components
export { default as CaseStatusBadge } from './CaseStatusBadge'
export { default as CaseProgressBar } from './CaseProgressBar'
export { default as StatusManagement } from './StatusManagement'
export { default as StatusChangeModal } from './StatusChangeModal'
export { default as StatusHistory } from './StatusHistory'
export { default as StatusTimeline } from './StatusTimeline'

// Advanced Status Features
export { default as StatusFilter } from './StatusFilter'
export { default as StatusSelector } from './StatusSelector'
export { default as CustomStatusLabels } from './CustomStatusLabels'
export { default as MobileStatusIndicators } from './MobileStatusIndicators'
export { CompactMobileStatusIndicator, MiniStatusIndicator } from './MobileStatusIndicators'

// Bulk Operations
export { default as BulkStatusUpdateModal } from './BulkStatusUpdateModal'

// Workflow and Automation
export { default as StatusApprovalWorkflow } from './StatusApprovalWorkflow'
export { default as AutomatedStatusTriggers } from './AutomatedStatusTriggers'
export { default as StatusNotificationSystem } from './StatusNotificationSystem'

// Notifications and Toasts
export { default as StatusChangeToast, useStatusChangeToast } from './StatusChangeToast'

// Dashboard Components
export { default as StatusDashboard } from './StatusDashboard'

// Hooks
export { useCustomStatusLabels } from './CustomStatusLabels'
export { useAutomatedStatusTriggers } from './AutomatedStatusTriggers'
export { useStatusNotifications } from './StatusNotificationSystem'

// Existing Components (for completeness)
export { default as ViewLegalCase } from './ViewLegalCase'
export { default as CaseDetailView } from './CaseDetailView'
export { default as CaseDetailPage } from './CaseDetailPage'
export { default as CaseDetailModal } from './CaseDetailModal'
export { default as CaseTimeline } from './CaseTimeline'
