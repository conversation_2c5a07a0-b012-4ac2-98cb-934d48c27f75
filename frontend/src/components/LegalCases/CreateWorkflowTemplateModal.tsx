import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>oot<PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Switch,
  Badge,
  Text,
  Box,

  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  IconButton,
} from "@chakra-ui/react"
import { FiPlus, FiTrash2, FiMove, FiZap } from "react-icons/fi"
import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseTemplatesService, 
  WorkflowTemplateCreate,
  CASE_TYPE_CONFIG 
} from "@/client/case-templates"
import useCustomToast from "@/hooks/useCustomToast"

interface CreateWorkflowTemplateModalProps {
  isOpen: boolean
  onClose: () => void
}

interface WorkflowStep {
  name: string
  description: string
  type: string
  order: number
  required: boolean
  estimated_hours?: number
  dependencies: string[]
}

interface AutomationRule {
  name: string
  trigger: string
  condition: string
  action: string
  enabled: boolean
}

const CreateWorkflowTemplateModal = ({ isOpen, onClose }: CreateWorkflowTemplateModalProps) => {
  const [formData, setFormData] = useState<WorkflowTemplateCreate>({
    name: "",
    description: "",
    case_type: "civil",
    workflow_steps: [],
    automation_rules: [],
    is_active: true,
    is_public: false,
    tags: []
  })
  
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([])
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([])
  const [tagInput, setTagInput] = useState("")

  const toast = useCustomToast()
  const queryClient = useQueryClient()

  const createTemplateMutation = useMutation({
    mutationFn: CaseTemplatesService.createWorkflowTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["workflow-templates"] })
      toast({
        title: "Template created",
        description: "Workflow template has been created successfully",
        status: "success",
        duration: 3000,
      })
      handleClose()
    },
    onError: (error: any) => {
      toast({
        title: "Error creating template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      case_type: "civil",
      workflow_steps: [],
      automation_rules: [],
      is_active: true,
      is_public: false,
      tags: []
    })
    setWorkflowSteps([])
    setAutomationRules([])
    setTagInput("")
    onClose()
  }

  const handleSubmit = () => {
    const templateData = {
      ...formData,
      workflow_steps: workflowSteps,
      automation_rules: automationRules
    }
    
    createTemplateMutation.mutate(templateData)
  }

  const addWorkflowStep = () => {
    setWorkflowSteps([...workflowSteps, {
      name: "",
      description: "",
      type: "task",
      order: workflowSteps.length + 1,
      required: false,
      estimated_hours: 1,
      dependencies: []
    }])
  }

  const updateWorkflowStep = (index: number, field: keyof WorkflowStep, value: any) => {
    const updated = [...workflowSteps]
    updated[index] = { ...updated[index], [field]: value }
    setWorkflowSteps(updated)
  }

  const removeWorkflowStep = (index: number) => {
    setWorkflowSteps(workflowSteps.filter((_, i) => i !== index))
  }

  const moveWorkflowStep = (index: number, direction: 'up' | 'down') => {
    const newIndex = direction === 'up' ? index - 1 : index + 1
    if (newIndex < 0 || newIndex >= workflowSteps.length) return

    const updated = [...workflowSteps]
    const temp = updated[index]
    updated[index] = updated[newIndex]
    updated[newIndex] = temp
    
    // Update order numbers
    updated[index].order = index + 1
    updated[newIndex].order = newIndex + 1
    
    setWorkflowSteps(updated)
  }

  const addAutomationRule = () => {
    setAutomationRules([...automationRules, {
      name: "",
      trigger: "status_change",
      condition: "",
      action: "send_notification",
      enabled: true
    }])
  }

  const updateAutomationRule = (index: number, field: keyof AutomationRule, value: any) => {
    const updated = [...automationRules]
    updated[index] = { ...updated[index], [field]: value }
    setAutomationRules(updated)
  }

  const removeAutomationRule = (index: number) => {
    setAutomationRules(automationRules.filter((_, i) => i !== index))
  }

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, tagInput.trim()]
      })
      setTagInput("")
    }
  }

  const removeTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    })
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="5xl">
      <ModalOverlay />
      <ModalContent maxH="90vh" overflowY="auto">
        <ModalHeader>Create Workflow Template</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <Tabs>
            <TabList>
              <Tab>Basic Info</Tab>
              <Tab>Workflow Steps</Tab>
              <Tab>Automation Rules</Tab>
              <Tab>Settings</Tab>
            </TabList>

            <TabPanels>
              {/* Basic Info */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel>Template Name</FormLabel>
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter template name"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel>Description</FormLabel>
                    <Textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Describe what this workflow template does"
                      rows={3}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Case Type</FormLabel>
                    <Select
                      value={formData.case_type}
                      onChange={(e) => setFormData({ ...formData, case_type: e.target.value })}
                    >
                      {Object.entries(CASE_TYPE_CONFIG).map(([key, config]) => (
                        <option key={key} value={key}>
                          {config.icon} {config.label}
                        </option>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel>Tags</FormLabel>
                    <HStack>
                      <Input
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      />
                      <Button onClick={addTag} size="sm">Add</Button>
                    </HStack>
                    <HStack mt={2} flexWrap="wrap">
                      {formData.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="solid"
                          colorScheme="purple"
                          cursor="pointer"
                          onClick={() => removeTag(tag)}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </HStack>
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* Workflow Steps */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="medium">Workflow Steps</Text>
                    <Button leftIcon={<FiPlus />} onClick={addWorkflowStep} size="sm">
                      Add Step
                    </Button>
                  </HStack>

                  {workflowSteps.map((step, index) => (
                    <Box key={index} p={4} border="1px" borderColor="gray.200" borderRadius="md">
                      <VStack spacing={3} align="stretch">
                        <HStack justify="space-between">
                          <Text fontWeight="medium">Step {index + 1}</Text>
                          <HStack>
                            <IconButton
                              icon={<FiMove />}
                              size="sm"
                              variant="ghost"
                              onClick={() => moveWorkflowStep(index, 'up')}
                              isDisabled={index === 0}
                              title="Move up"
                            />
                            <IconButton
                              icon={<FiMove />}
                              size="sm"
                              variant="ghost"
                              onClick={() => moveWorkflowStep(index, 'down')}
                              isDisabled={index === workflowSteps.length - 1}
                              title="Move down"
                              transform="rotate(180deg)"
                            />
                            <IconButton
                              icon={<FiTrash2 />}
                              size="sm"
                              variant="ghost"
                              colorScheme="red"
                              onClick={() => removeWorkflowStep(index)}
                            />
                          </HStack>
                        </HStack>

                        <FormControl isRequired>
                          <FormLabel size="sm">Step Name</FormLabel>
                          <Input
                            value={step.name}
                            onChange={(e) => updateWorkflowStep(index, 'name', e.target.value)}
                            placeholder="Step name"
                            size="sm"
                          />
                        </FormControl>

                        <FormControl>
                          <FormLabel size="sm">Description</FormLabel>
                          <Textarea
                            value={step.description}
                            onChange={(e) => updateWorkflowStep(index, 'description', e.target.value)}
                            placeholder="Step description"
                            size="sm"
                            rows={2}
                          />
                        </FormControl>

                        <HStack>
                          <FormControl>
                            <FormLabel size="sm">Type</FormLabel>
                            <Select
                              value={step.type}
                              onChange={(e) => updateWorkflowStep(index, 'type', e.target.value)}
                              size="sm"
                            >
                              <option value="task">Task</option>
                              <option value="review">Review</option>
                              <option value="approval">Approval</option>
                              <option value="filing">Filing</option>
                              <option value="communication">Communication</option>
                              <option value="research">Research</option>
                              <option value="meeting">Meeting</option>
                            </Select>
                          </FormControl>

                          <FormControl>
                            <FormLabel size="sm">Estimated Hours</FormLabel>
                            <Input
                              type="number"
                              value={step.estimated_hours || ""}
                              onChange={(e) => updateWorkflowStep(index, 'estimated_hours', parseFloat(e.target.value))}
                              size="sm"
                              min="0"
                              step="0.5"
                            />
                          </FormControl>
                        </HStack>

                        <HStack>
                          <Switch
                            isChecked={step.required}
                            onChange={(e) => updateWorkflowStep(index, 'required', e.target.checked)}
                          />
                          <Text fontSize="sm">Required step</Text>
                        </HStack>
                      </VStack>
                    </Box>
                  ))}

                  {workflowSteps.length === 0 && (
                    <Box textAlign="center" py={8} color="gray.500">
                      <Text>No workflow steps added yet</Text>
                      <Button
                        leftIcon={<FiPlus />}
                        onClick={addWorkflowStep}
                        size="sm"
                        mt={2}
                        variant="outline"
                      >
                        Add your first step
                      </Button>
                    </Box>
                  )}
                </VStack>
              </TabPanel>

              {/* Automation Rules */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="medium">Automation Rules</Text>
                    <Button leftIcon={<FiZap />} onClick={addAutomationRule} size="sm">
                      Add Rule
                    </Button>
                  </HStack>

                  {automationRules.map((rule, index) => (
                    <Box key={index} p={4} border="1px" borderColor="gray.200" borderRadius="md">
                      <VStack spacing={3} align="stretch">
                        <HStack justify="space-between">
                          <Text fontWeight="medium">Rule {index + 1}</Text>
                          <IconButton
                            icon={<FiTrash2 />}
                            size="sm"
                            variant="ghost"
                            colorScheme="red"
                            onClick={() => removeAutomationRule(index)}
                          />
                        </HStack>

                        <FormControl isRequired>
                          <FormLabel size="sm">Rule Name</FormLabel>
                          <Input
                            value={rule.name}
                            onChange={(e) => updateAutomationRule(index, 'name', e.target.value)}
                            placeholder="Rule name"
                            size="sm"
                          />
                        </FormControl>

                        <HStack>
                          <FormControl>
                            <FormLabel size="sm">Trigger</FormLabel>
                            <Select
                              value={rule.trigger}
                              onChange={(e) => updateAutomationRule(index, 'trigger', e.target.value)}
                              size="sm"
                            >
                              <option value="status_change">Status Change</option>
                              <option value="deadline_approaching">Deadline Approaching</option>
                              <option value="milestone_completed">Milestone Completed</option>
                              <option value="document_uploaded">Document Uploaded</option>
                              <option value="note_added">Note Added</option>
                              <option value="time_elapsed">Time Elapsed</option>
                            </Select>
                          </FormControl>

                          <FormControl>
                            <FormLabel size="sm">Action</FormLabel>
                            <Select
                              value={rule.action}
                              onChange={(e) => updateAutomationRule(index, 'action', e.target.value)}
                              size="sm"
                            >
                              <option value="send_notification">Send Notification</option>
                              <option value="create_task">Create Task</option>
                              <option value="update_status">Update Status</option>
                              <option value="assign_user">Assign User</option>
                              <option value="send_email">Send Email</option>
                              <option value="create_deadline">Create Deadline</option>
                            </Select>
                          </FormControl>
                        </HStack>

                        <FormControl>
                          <FormLabel size="sm">Condition</FormLabel>
                          <Input
                            value={rule.condition}
                            onChange={(e) => updateAutomationRule(index, 'condition', e.target.value)}
                            placeholder="e.g., status == 'open' && days_since_created > 7"
                            size="sm"
                          />
                        </FormControl>

                        <HStack>
                          <Switch
                            isChecked={rule.enabled}
                            onChange={(e) => updateAutomationRule(index, 'enabled', e.target.checked)}
                          />
                          <Text fontSize="sm">Rule enabled</Text>
                        </HStack>
                      </VStack>
                    </Box>
                  ))}

                  {automationRules.length === 0 && (
                    <Box textAlign="center" py={8} color="gray.500">
                      <Text>No automation rules added yet</Text>
                      <Button
                        leftIcon={<FiZap />}
                        onClick={addAutomationRule}
                        size="sm"
                        mt={2}
                        variant="outline"
                      >
                        Add your first rule
                      </Button>
                    </Box>
                  )}
                </VStack>
              </TabPanel>

              {/* Settings */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="medium">Active Template</Text>
                        <Text fontSize="sm" color="gray.600">
                          Active templates can be applied to cases
                        </Text>
                      </VStack>
                      <Switch
                        isChecked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                      />
                    </HStack>
                  </FormControl>

                  <FormControl>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="medium">Public Template</Text>
                        <Text fontSize="sm" color="gray.600">
                          Public templates can be used by other users
                        </Text>
                      </VStack>
                      <Switch
                        isChecked={formData.is_public}
                        onChange={(e) => setFormData({ ...formData, is_public: e.target.checked })}
                      />
                    </HStack>
                  </FormControl>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            colorScheme="purple"
            onClick={handleSubmit}
            isLoading={createTemplateMutation.isPending}
            isDisabled={!formData.name.trim()}
          >
            Create Template
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default CreateWorkflowTemplateModal
