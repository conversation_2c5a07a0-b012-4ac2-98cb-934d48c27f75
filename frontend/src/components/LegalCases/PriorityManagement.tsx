import {
  Badge,
  Box,
  Button,
  HStack,
  Select,
  Text,
  VStack,
} from "@chakra-ui/react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"

import { LegalCasesService } from "../../client"
import type { CasePriority, LegalCasePublic } from "../../client"
import useCustomToast from "../../hooks/useCustomToast"

interface PriorityManagementProps {
  legalCase: LegalCasePublic
}

const priorityColors: Record<string, string> = {
  // Uppercase versions
  LOW: "green",
  MEDIUM: "yellow",
  HIGH: "orange",
  URGENT: "red",
  // Lowercase versions (for compatibility)
  low: "green",
  medium: "yellow",
  high: "orange",
  urgent: "red",
}

const priorityLabels: Record<string, string> = {
  // Uppercase versions
  LOW: "Low",
  MEDIUM: "Medium",
  HIGH: "High",
  URGENT: "Urgent",
  // Lowercase versions (for compatibility)
  low: "Low",
  medium: "Medium",
  high: "High",
  urgent: "Urgent",
}

export default function PriorityManagement({ legalCase }: PriorityManagementProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [selectedPriority, setSelectedPriority] = useState<CasePriority | "">("")
  const showToast = useCustomToast()
  const queryClient = useQueryClient()

  // Safety check - ensure legalCase exists
  if (!legalCase) {
    return (
      <Box p={4} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
        <Text color="red.600">Error: Case data not available</Text>
      </Box>
    )
  }

  // Additional safety check for required properties
  if (typeof legalCase !== 'object') {
    return (
      <Box p={4} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
        <Text color="red.600">Error: Invalid case data type: {typeof legalCase}</Text>
      </Box>
    )
  }

  // Get available priority options
  const { data: priorityOptions = [] } = useQuery({
    queryKey: ["legal-cases", "priority-options"],
    queryFn: () => Promise.resolve(["LOW", "MEDIUM", "HIGH", "URGENT"]),
  })

  // Update priority mutation
  const updatePriorityMutation = useMutation({
    mutationFn: async ({ caseId, priority }: { caseId: string; priority: CasePriority }) => {
      // Temporary implementation - will be replaced with actual API call
      console.log('Updating priority:', caseId, priority)
      return Promise.resolve({ id: caseId, priority })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
      queryClient.invalidateQueries({ queryKey: ["legal-case", legalCase.id] })
      showToast("Priority Updated", "Case priority has been updated successfully.", "success")
      setIsEditing(false)
      setSelectedPriority("")
    },
    onError: (error: any) => {
      showToast("Error", error.body?.detail || "Failed to update case priority.", "error")
    },
  })

  const handlePriorityChange = () => {
    if (!selectedPriority) return

    updatePriorityMutation.mutate({
      caseId: String(legalCase.id),
      priority: selectedPriority as CasePriority,
    })
  }

  const currentPriority = legalCase.priority || "MEDIUM"
  const currentPriorityColor = priorityColors[currentPriority] || "gray"
  const currentPriorityLabel = priorityLabels[currentPriority] || currentPriority

  if (isEditing) {
    return (
      <Box border="1px solid" borderColor="gray.200" p={4} borderRadius="md">
        <VStack spacing={4} align="stretch">
          <Text fontWeight="bold">Change Case Priority</Text>

          <Box>
            <Text fontSize="sm" color="gray.600" mb={2}>
              Current Priority: <Badge colorScheme={currentPriorityColor}>{currentPriorityLabel}</Badge>
            </Text>
          </Box>

          <Box>
            <Text fontWeight="medium" mb={2}>New Priority</Text>
            <Select
              placeholder="Select new priority"
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value as CasePriority)}
            >
              {priorityOptions
                .filter((priority) => priority !== currentPriority)
                .map((priority) => (
                  <option key={priority} value={priority}>
                    {priorityLabels[priority] || priority}
                  </option>
                ))}
            </Select>
          </Box>

          <HStack>
            <Button variant="ghost" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button
              colorScheme="orange"
              onClick={handlePriorityChange}
              isLoading={updatePriorityMutation.isPending}
              isDisabled={!selectedPriority}
            >
              Update Priority
            </Button>
          </HStack>
        </VStack>
      </Box>
    )
  }

  return (
    <HStack spacing={3}>
      <Text fontWeight="medium">Priority:</Text>
      <Badge
        colorScheme={currentPriorityColor}
        variant="solid"
        px={3}
        py={1}
        borderRadius="md"
        fontSize="sm"
      >
        {currentPriorityLabel}
      </Badge>
      <Button size="sm" colorScheme="orange" variant="outline" onClick={() => setIsEditing(true)}>
        Change Priority
      </Button>
    </HStack>
  )
}
