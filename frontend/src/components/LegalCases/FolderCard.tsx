import {
  Box,
  HStack,
  VStack,
  Text,
  Badge,
  IconButton,
  Tooltip,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
} from "@chakra-ui/react"
import {
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  FiFolder,
  FiLock,
  FiUsers,
} from "react-icons/fi"
import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { formatDistanceToNow } from "date-fns"

import { 
  CaseFolder, 
  CaseFoldersService,
  getFolderIcon,
  getFolderColor,
  formatFolderSize 
} from "@/client/case-folders"
import { toaster } from "@/components/ui/toaster"
import FolderEditModal from "./FolderEditModal"

interface FolderCardProps {
  folder: CaseFolder
  caseId: string
  onClick: () => void
  onUpdated: () => void
  onDeleted: () => void
}

const FolderCard = ({ folder, caseId, onClick, onUpdated, onDeleted }: FolderCardProps) => {
  const queryClient = useQueryClient()
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()
  
  const [isDeleting, setIsDeleting] = useState(false)

  const cardBg = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const hoverBg = useColorModeValue("gray.50", "gray.700")

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: () => CaseFoldersService.deleteCaseFolder(caseId, folder.id),
    onSuccess: () => {
      toaster.create({
        title: "Folder deleted",
        description: "The folder has been deleted successfully.",
        status: "success",
      })
      onDeleted()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to delete folder",
        status: "error",
      })
    },
    onSettled: () => {
      setIsDeleting(false)
    },
  })

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEditOpen()
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    
    const hasContents = folder.document_count > 0 || folder.subfolder_count > 0
    const message = hasContents 
      ? `"${folder.name}" contains ${folder.document_count} documents and ${folder.subfolder_count} subfolders. Are you sure you want to delete it?`
      : `Are you sure you want to delete "${folder.name}"?`
    
    if (window.confirm(message)) {
      setIsDeleting(true)
      deleteMutation.mutate()
    }
  }

  const handleEditSuccess = () => {
    onEditClose()
    onUpdated()
  }

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch {
      return "Unknown time"
    }
  }

  const folderIcon = getFolderIcon(folder)
  const folderColor = getFolderColor(folder)

  return (
    <>
      <Box
        bg={cardBg}
        border="1px solid"
        borderColor={borderColor}
        borderRadius="lg"
        p={4}
        cursor="pointer"
        transition="all 0.2s"
        _hover={{ 
          shadow: "md", 
          bg: hoverBg,
          transform: "translateY(-1px)" 
        }}
        onClick={onClick}
        position="relative"
      >
        <HStack spacing={4} align="center">
          {/* Folder Icon */}
          <Box
            fontSize="2xl"
            color={folderColor}
            minW="40px"
            textAlign="center"
          >
            {folderIcon}
          </Box>

          {/* Folder Info */}
          <VStack align="start" spacing={1} flex={1}>
            <HStack spacing={2} align="center">
              <Text fontWeight="medium" fontSize="md" noOfLines={1}>
                {folder.name}
              </Text>
              
              {folder.is_system_folder && (
                <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                  System
                </Badge>
              )}
            </HStack>

            {folder.description && (
              <Text fontSize="sm" color="gray.600" noOfLines={2}>
                {folder.description}
              </Text>
            )}

            <HStack spacing={4} fontSize="xs" color="gray.500">
              <Text>
                {folder.document_count} document{folder.document_count !== 1 ? 's' : ''}
              </Text>
              <Text>
                {folder.subfolder_count} folder{folder.subfolder_count !== 1 ? 's' : ''}
              </Text>
              {folder.total_size > 0 && (
                <Text>
                  {formatFolderSize(folder.total_size)}
                </Text>
              )}
            </HStack>
          </VStack>

          {/* Metadata */}
          <VStack align="end" spacing={1}>
            <Text fontSize="xs" color="gray.500">
              {folder.creator?.full_name || "Unknown"}
            </Text>
            <Text fontSize="xs" color="gray.500">
              {formatDate(folder.created_at)}
            </Text>
          </VStack>

          {/* Actions Menu */}
          {(folder.can_edit || folder.can_delete) && (
            <Menu>
              <MenuButton
                as={IconButton}
                variant="ghost"
                size="sm"
                icon={<FiMoreVertical />}
                onClick={(e) => e.stopPropagation()}
                isLoading={isDeleting}
              />
              <MenuList>
                {folder.can_edit && (
                  <MenuItem icon={<FiEdit />} onClick={handleEdit}>
                    Edit Folder
                  </MenuItem>
                )}
                {folder.can_delete && (
                  <MenuItem
                    icon={<FiTrash2 />}
                    onClick={handleDelete}
                    color="red.500"
                  >
                    Delete Folder
                  </MenuItem>
                )}
              </MenuList>
            </Menu>
          )}
        </HStack>
      </Box>

      {/* Edit Modal */}
      <FolderEditModal
        isOpen={isEditOpen}
        onClose={onEditClose}
        caseId={caseId}
        folder={folder}
        onSuccess={handleEditSuccess}
      />
    </>
  )
}

export default FolderCard
