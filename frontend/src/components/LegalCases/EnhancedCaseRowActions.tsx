import {
  <PERSON><PERSON><PERSON><PERSON>,
  I<PERSON>Button,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useBreakpointValue,
  useDisclosure,
} from "@chakra-ui/react"
import { <PERSON><PERSON>ye, FiEdit, FiTrash2, FiMoreVertical } from "react-icons/fi"
import { useState } from "react"

import type { LegalCasePublic } from "@/client"
import { <PERSON>u<PERSON>ontent, MenuRoot, MenuTrigger } from "../ui/menu"
import ViewLegalCase from "./ViewLegalCase"
import EditLegalCase from "./EditLegalCase"
import DeleteLegalCase from "./DeleteLegalCase"
import useAuth from "@/hooks/useAuth"

interface EnhancedCaseRowActionsProps {
  legalCase: LegalCasePublic
  onViewClick?: (caseId: string) => void
  compact?: boolean
}

const EnhancedCaseRowActions = ({ 
  legalCase, 
  onViewClick,
  compact = false 
}: EnhancedCaseRowActionsProps) => {
  const { user } = useAuth()
  const isMobile = useBreakpointValue({ base: true, md: false })
  const [isLoading, setIsLoading] = useState(false)

  // Role-based permissions
  const canEdit = user?.role === "admin" || 
    (user?.role === "lawyer" && legalCase.lawyer_id === user.id) ||
    user?.role === "assistant"
  
  const canDelete = user?.role === "admin" || 
    (user?.role === "lawyer" && legalCase.lawyer_id === user.id)

  const canView = true // Everyone can view (with role-based content filtering)

  const handleViewClick = () => {
    if (onViewClick) {
      onViewClick(legalCase.id)
    }
  }

  // Mobile/compact view - show menu
  if (isMobile || compact) {
    return (
      <MenuRoot>
        <MenuTrigger asChild>
          <Tooltip label="More actions">
            <IconButton
              variant="ghost"
              size="sm"
              color="gray.600"
              _hover={{ color: "blue.600", bg: "blue.50" }}
              isLoading={isLoading}
            >
              <FiMoreVertical />
            </IconButton>
          </Tooltip>
        </MenuTrigger>
        <MenuContent>
          {canView && (
            <Button
              variant="ghost"
              size="sm"
              leftIcon={<FiEye />}
              onClick={handleViewClick}
              w="full"
              justifyContent="flex-start"
            >
              View Details
            </Button>
          )}
          {canEdit && <EditLegalCase legalCase={legalCase} />}
          {canDelete && <DeleteLegalCase id={legalCase.id} />}
        </MenuContent>
      </MenuRoot>
    )
  }

  // Desktop view - show individual buttons
  return (
    <HStack spacing={1} onClick={(e) => e.stopPropagation()}>
      {canView && (
        <Tooltip label="View case details" placement="top">
          <IconButton
            variant="ghost"
            size="sm"
            color="blue.600"
            _hover={{ bg: "blue.50", transform: "scale(1.05)" }}
            transition="all 0.2s"
            onClick={handleViewClick}
            isLoading={isLoading}
          >
            <FiEye />
          </IconButton>
        </Tooltip>
      )}
      
      {canEdit && (
        <Tooltip label="Edit case" placement="top">
          <Box>
            <EditLegalCase legalCase={legalCase} />
          </Box>
        </Tooltip>
      )}
      
      {canDelete && (
        <Tooltip label="Delete case" placement="top">
          <Box>
            <DeleteLegalCase id={legalCase.id} />
          </Box>
        </Tooltip>
      )}
    </HStack>
  )
}

export default EnhancedCaseRowActions
