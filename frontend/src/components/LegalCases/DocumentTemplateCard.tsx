import {
  <PERSON>,
  <PERSON>Stack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  <PERSON>ge,
  IconButton,
  Tooltip,
  Card,
  CardBody,
  CardHeader,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,

  useDisclosure,
} from "@chakra-ui/react"
import {
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  FiCopy,
  FiPlay,
  FiFileText,
  FiEye,
  FiShare2,
  FiDownload,
  FiCode,
} from "react-icons/fi"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  DocumentTemplate, 
  CaseTemplatesService,
  getCaseTypeInfo,
  getDocumentTypeInfo 
} from "@/client/case-templates"
import GenerateDocumentModal from "./GenerateDocumentModal"
import useCustomToast from "@/hooks/useCustomToast"

interface DocumentTemplateCardProps {
  template: DocumentTemplate
  onEdit: (template: DocumentTemplate) => void
  onDelete: (templateId: string) => void
  onGenerate?: (templateId: string, content: string) => void
}

const DocumentTemplateCard = ({ 
  template, 
  onEdit, 
  onDelete, 
  onGenerate 
}: DocumentTemplateCardProps) => {
  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const caseTypeInfo = template.case_type ? getCaseTypeInfo(template.case_type) : null
  const documentTypeInfo = getDocumentTypeInfo(template.document_type)

  const { 
    isOpen: isGenerateModalOpen, 
    onOpen: onGenerateModalOpen, 
    onClose: onGenerateModalClose 
  } = useDisclosure()

  // Duplicate template mutation
  const duplicateTemplateMutation = useMutation({
    mutationFn: async (template: DocumentTemplate) => {
      const duplicateData = {
        name: `${template.name} (Copy)`,
        description: template.description,
        case_type: template.case_type,
        document_type: template.document_type,
        template_content: template.template_content,
        placeholders: template.placeholders,
        is_active: true,
        is_public: false,
        tags: template.tags
      }
      return CaseTemplatesService.createDocumentTemplate(duplicateData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["document-templates"] })
      toast({
        title: "Template duplicated",
        description: "Document template has been duplicated successfully",
        status: "success",
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error duplicating template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleDuplicate = () => {
    duplicateTemplateMutation.mutate(template)
  }

  const handleExport = () => {
    const exportData = {
      name: template.name,
      description: template.description,
      case_type: template.case_type,
      document_type: template.document_type,
      template_content: template.template_content,
      placeholders: template.placeholders,
      tags: template.tags,
      exported_at: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json"
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `document-template-${template.name.toLowerCase().replace(/\s+/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Template exported",
      description: "Document template has been exported successfully",
      status: "success",
      duration: 3000,
    })
  }

  const getContentStats = () => {
    const content = template.template_content
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length
    const placeholderCount = template.placeholders.length
    const characterCount = content.length
    
    return { wordCount, placeholderCount, characterCount }
  }

  const stats = getContentStats()

  return (
    <>
      <Card 
        variant="outline" 
        _hover={{ shadow: "md", borderColor: "green.200" }}
        transition="all 0.2s"
      >
        <CardHeader pb={2}>
          <HStack justify="space-between" align="start">
            <VStack align="start" spacing={1} flex={1}>
              <HStack spacing={2}>
                <Text fontSize="lg" fontWeight="bold" noOfLines={1}>
                  {template.name}
                </Text>
                {template.is_public && (
                  <Badge colorScheme="green" size="sm">
                    Public
                  </Badge>
                )}
              </HStack>
              
              <HStack spacing={2} flexWrap="wrap">
                <Badge 
                  colorScheme={documentTypeInfo.color} 
                  variant="subtle"
                  fontSize="xs"
                >
                  {documentTypeInfo.icon} {documentTypeInfo.label}
                </Badge>
                
                {caseTypeInfo && (
                  <Badge 
                    colorScheme={caseTypeInfo.color} 
                    variant="outline"
                    fontSize="xs"
                  >
                    {caseTypeInfo.icon} {caseTypeInfo.label}
                  </Badge>
                )}
                
                {template.tags.slice(0, 2).map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    colorScheme="gray"
                    fontSize="xs"
                  >
                    {tag}
                  </Badge>
                ))}
                
                {template.tags.length > 2 && (
                  <Badge variant="outline" colorScheme="gray" fontSize="xs">
                    +{template.tags.length - 2}
                  </Badge>
                )}
              </HStack>
            </VStack>
            
            <Menu>
              <MenuButton
                as={IconButton}
                icon={<FiMoreVertical />}
                variant="ghost"
                size="sm"
              />
              <MenuList>
                <MenuItem icon={<FiEye />} onClick={() => {/* TODO: View content */}}>
                  View Content
                </MenuItem>
                <MenuItem icon={<FiEdit />} onClick={() => onEdit(template)}>
                  Edit Template
                </MenuItem>
                <MenuItem icon={<FiCopy />} onClick={handleDuplicate}>
                  Duplicate
                </MenuItem>
                <MenuItem icon={<FiCode />} onClick={() => {/* TODO: View placeholders */}}>
                  View Placeholders
                </MenuItem>
                <MenuItem icon={<FiDownload />} onClick={handleExport}>
                  Export
                </MenuItem>
                <MenuItem icon={<FiShare2 />} onClick={() => {/* TODO: Share */}}>
                  Share
                </MenuItem>
                <Box height="1px" bg="gray.200" my={1} />
                <MenuItem 
                  icon={<FiTrash2 />} 
                  onClick={() => onDelete(template.id)}
                  color="red.600"
                >
                  Delete
                </MenuItem>
              </MenuList>
            </Menu>
          </HStack>
        </CardHeader>
        
        <CardBody pt={2}>
          <VStack spacing={4} align="stretch">
            {/* Description */}
            {template.description && (
              <Text fontSize="sm" color="gray.600" noOfLines={2}>
                {template.description}
              </Text>
            )}
            
            {/* Content Stats */}
            <HStack justify="space-around" bg="gray.50" p={3} borderRadius="md">
              <VStack spacing={1}>
                <Text fontSize="lg" fontWeight="bold" color="blue.600">
                  {stats.wordCount}
                </Text>
                <Text fontSize="xs" color="gray.600">Words</Text>
              </VStack>
              
              <VStack spacing={1}>
                <Text fontSize="lg" fontWeight="bold" color="orange.600">
                  {stats.placeholderCount}
                </Text>
                <Text fontSize="xs" color="gray.600">Placeholders</Text>
              </VStack>
              
              <VStack spacing={1}>
                <Text fontSize="lg" fontWeight="bold" color="green.600">
                  {Math.round(stats.characterCount / 1000)}k
                </Text>
                <Text fontSize="xs" color="gray.600">Characters</Text>
              </VStack>
            </HStack>
            
            {/* Placeholders Preview */}
            {template.placeholders.length > 0 && (
              <Box>
                <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={2}>
                  Placeholders:
                </Text>
                <HStack spacing={1} flexWrap="wrap">
                  {template.placeholders.slice(0, 3).map((placeholder) => (
                    <Badge 
                      key={placeholder} 
                      variant="solid" 
                      colorScheme="blue"
                      fontSize="xs"
                    >
                      {`{{${placeholder}}}`}
                    </Badge>
                  ))}
                  {template.placeholders.length > 3 && (
                    <Badge variant="outline" colorScheme="blue" fontSize="xs">
                      +{template.placeholders.length - 3} more
                    </Badge>
                  )}
                </HStack>
              </Box>
            )}
            
            {/* Creator Info */}
            <HStack justify="space-between" fontSize="xs" color="gray.500">
              <Text>
                By {template.creator?.full_name || "Unknown"}
              </Text>
              <Text>
                {new Date(template.created_at).toLocaleDateString()}
              </Text>
            </HStack>
            
            {/* Action Buttons */}
            <VStack spacing={2} align="stretch">
              <Button
                leftIcon={<FiFileText />}
                colorScheme="green"
                size="sm"
                onClick={onGenerateModalOpen}
                isDisabled={template.placeholders.length === 0}
              >
                Generate Document
              </Button>
              
              <Button
                leftIcon={<FiEye />}
                variant="outline"
                colorScheme="green"
                size="sm"
                onClick={() => {/* TODO: Preview template */}}
              >
                Preview Template
              </Button>
            </VStack>
          </VStack>
        </CardBody>
      </Card>

      {/* Generate Document Modal */}
      <GenerateDocumentModal
        isOpen={isGenerateModalOpen}
        onClose={onGenerateModalClose}
        template={template}
        onGenerate={onGenerate}
      />
    </>
  )
}

export default DocumentTemplateCard
