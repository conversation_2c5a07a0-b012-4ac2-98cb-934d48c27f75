import {
  Box,
  VStack,
  H<PERSON>tack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  Progress,





  Tooltip,
  CircularProgress,
  CircularProgressLabel,
} from "@chakra-ui/react"
import {
  FiClock,
  FiDollarSign,
  FiTrendingUp,
  FiActivity,
  FiCalendar,
  FiZap,
} from "react-icons/fi"

import { 
  TimeAnalytics, 
  getTimeEfficiencyInfo,
  formatHours,
  getActivityTypeLabel 
} from "@/client/case-analytics"

interface TimeAnalyticsCardProps {
  timeAnalytics: TimeAnalytics
}

const TimeAnalyticsCard = ({ timeAnalytics }: TimeAnalyticsCardProps) => {
  const efficiencyInfo = getTimeEfficiencyInfo(timeAnalytics.time_efficiency.efficiency_status)
  
  const getEfficiencyColor = () => {
    switch (timeAnalytics.time_efficiency.efficiency_status) {
      case "well_allocated":
        return "green"
      case "under_allocated":
        return "yellow"
      case "significantly_under_allocated":
        return "orange"
      case "over_allocated":
        return "red"
      default:
        return "gray"
    }
  }

  const getEfficiencyPercentage = () => {
    const ratio = timeAnalytics.time_efficiency.efficiency_ratio
    if (ratio <= 1) return ratio * 100
    return 100 + Math.min(50, (ratio - 1) * 100) // Cap at 150%
  }

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiClock />
          <Heading size="sm">Time Analytics</Heading>
          <Badge 
            colorScheme={efficiencyInfo.color} 
            variant="subtle"
            fontSize="xs"
          >
            {efficiencyInfo.icon} {efficiencyInfo.label}
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Time Efficiency Score */}
          <Box textAlign="center">
            <CircularProgress 
              value={getEfficiencyPercentage()} 
              color={`${getEfficiencyColor()}.400`}
              size="80px"
              thickness="8px"
            >
              <CircularProgressLabel fontSize="xs" fontWeight="bold">
                {(timeAnalytics.time_efficiency.efficiency_ratio * 100).toFixed(0)}%
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="sm" color="gray.600" mt={2}>
              Time Efficiency
            </Text>
            <Text fontSize="xs" color="gray.500">
              {efficiencyInfo.description}
            </Text>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Time Summary */}
          <VStack spacing={3} align="stretch">
            <HStack justify="space-between">
              <Tooltip label="Total estimated hours worked">
                <HStack spacing={2}>
                  <FiClock size={14} color="blue" />
                  <Text fontSize="sm">Total Hours:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="blue.600">
                {formatHours(timeAnalytics.estimated_total_hours)}
              </Text>
            </HStack>

            <HStack justify="space-between">
              <Tooltip label="Average hours per day">
                <HStack spacing={2}>
                  <FiCalendar size={14} color="green" />
                  <Text fontSize="sm">Daily Average:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="green.600">
                {formatHours(timeAnalytics.estimated_daily_hours)}
              </Text>
            </HStack>

            <HStack justify="space-between">
              <Tooltip label="Estimated billable hours">
                <HStack spacing={2}>
                  <FiDollarSign size={14} color="purple" />
                  <Text fontSize="sm">Billable:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="purple.600">
                {formatHours(timeAnalytics.billable_hours_estimate)}
              </Text>
            </HStack>

            <HStack justify="space-between">
              <Tooltip label="Case age in days">
                <HStack spacing={2}>
                  <FiActivity size={14} color="orange" />
                  <Text fontSize="sm">Case Age:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="orange.600">
                {timeAnalytics.case_age_days} days
              </Text>
            </HStack>
          </VStack>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Efficiency Breakdown */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Efficiency Analysis
            </Text>
            
            <VStack spacing={2} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Expected Hours:</Text>
                <Text fontSize="xs" fontWeight="medium">
                  {formatHours(timeAnalytics.time_efficiency.expected_hours)}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Actual Hours:</Text>
                <Text fontSize="xs" fontWeight="medium">
                  {formatHours(timeAnalytics.time_efficiency.actual_hours)}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Variance:</Text>
                <Text 
                  fontSize="xs" 
                  fontWeight="medium"
                  color={timeAnalytics.time_efficiency.variance_hours >= 0 ? "green.600" : "red.600"}
                >
                  {timeAnalytics.time_efficiency.variance_hours >= 0 ? "+" : ""}
                  {formatHours(Math.abs(timeAnalytics.time_efficiency.variance_hours))}
                </Text>
              </HStack>
              
              <Progress 
                value={getEfficiencyPercentage()} 
                colorScheme={getEfficiencyColor()} 
                size="sm" 
                borderRadius="full"
              />
            </VStack>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Activity Time Distribution */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Time by Activity Type
            </Text>
            
            <VStack spacing={2} align="stretch" maxH="120px" overflowY="auto">
              {Object.entries(timeAnalytics.activity_time_distribution)
                .sort(([,a], [,b]) => b.estimated_hours - a.estimated_hours)
                .slice(0, 5)
                .map(([activityType, data]) => (
                <HStack key={activityType} justify="space-between">
                  <Text fontSize="xs" color="gray.600" noOfLines={1}>
                    {getActivityTypeLabel(activityType)}:
                  </Text>
                  <HStack spacing={1}>
                    <Text fontSize="xs" color="gray.500">
                      {data.count}x
                    </Text>
                    <Text fontSize="xs" fontWeight="medium">
                      {formatHours(data.estimated_hours)}
                    </Text>
                  </HStack>
                </HStack>
              ))}
            </VStack>
          </Box>

          {/* Time Insights */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Time Insights:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {timeAnalytics.time_efficiency.efficiency_ratio > 1.2 && (
                <Text fontSize="xs" color="red.600">
                  ⚠ Time allocation exceeds expectations
                </Text>
              )}
              
              {timeAnalytics.time_efficiency.efficiency_ratio >= 0.8 && 
               timeAnalytics.time_efficiency.efficiency_ratio <= 1.2 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Time allocation is optimal
                </Text>
              )}
              
              {timeAnalytics.time_efficiency.efficiency_ratio < 0.5 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Significantly under-allocated
                </Text>
              )}
              
              {timeAnalytics.estimated_daily_hours > 4 && (
                <Text fontSize="xs" color="blue.600">
                  ✓ High daily engagement
                </Text>
              )}
              
              {timeAnalytics.estimated_daily_hours < 1 && (
                <Text fontSize="xs" color="yellow.600">
                  ⚠ Low daily engagement
                </Text>
              )}
              
              {timeAnalytics.billable_hours_estimate > timeAnalytics.estimated_total_hours * 0.9 && (
                <Text fontSize="xs" color="green.600">
                  ✓ High billable ratio
                </Text>
              )}
            </VStack>
          </Box>

          {/* Quick Stats */}
          <HStack justify="space-around" bg="blue.50" p={2} borderRadius="md">
            <Box textAlign="center">
              <Box textAlign="center">
                {(timeAnalytics.estimated_total_hours / Math.max(1, timeAnalytics.case_age_days) * 7).toFixed(1)}
              </Text>
              <Box textAlign="center">Hrs/Week</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {((timeAnalytics.billable_hours_estimate / timeAnalytics.estimated_total_hours) * 100).toFixed(0)}%
              </Text>
              <Box textAlign="center">Billable</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {timeAnalytics.time_efficiency.efficiency_ratio > 1 ? "Over" :
                 timeAnalytics.time_efficiency.efficiency_ratio < 0.8 ? "Under" : "On"}
              </Text>
              <Box textAlign="center">Target</Text>
            </Box>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default TimeAnalyticsCard
