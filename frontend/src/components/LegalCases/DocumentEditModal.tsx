import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  Select,
  FormControl,
  FormLabel,
  Switch,
  Textarea,
  Input,
  Badge,
  Box,
  Flex,
  IconButton,
} from "@chakra-ui/react"
import { FiSave, FiX } from "react-icons/fi"
import { useState, useEffect } from "react"
import { useMutation } from "@tanstack/react-query"

import { 
  CaseDocument,
  CaseDocumentUpdate,
  CaseDocumentsService, 
  DOCUMENT_CATEGORY_CONFIG,
  getFileTypeInfo,
  formatFileSize 
} from "@/client/case-documents"
import { toaster } from "@/components/ui/toaster"

interface DocumentEditModalProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  document: CaseDocument
  onSuccess: () => void
}

const DocumentEditModal = ({ 
  isOpen, 
  onClose, 
  caseId, 
  document,
  onSuccess 
}: DocumentEditModalProps) => {
  // Form state
  const [category, setCategory] = useState<string>(document.category)
  const [description, setDescription] = useState(document.description || "")
  const [tags, setTags] = useState<string[]>(document.tags || [])
  const [newTag, setNewTag] = useState("")
  const [isConfidential, setIsConfidential] = useState(document.is_confidential)
  const [isSharedWithClient, setIsSharedWithClient] = useState(document.is_shared_with_client)

  // Reset form when document changes
  useEffect(() => {
    setCategory(document.category)
    setDescription(document.description || "")
    setTags(document.tags || [])
    setNewTag("")
    setIsConfidential(document.is_confidential)
    setIsSharedWithClient(document.is_shared_with_client)
  }, [document])

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: CaseDocumentUpdate) => 
      CaseDocumentsService.updateCaseDocument(caseId, document.id, data),
    onSuccess: () => {
      toaster.create({
        title: "Document updated",
        description: "Document metadata has been updated successfully.",
        status: "success",
      })
      onSuccess()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Update failed",
        description: error.response?.data?.detail || "Failed to update document",
        status: "error",
      })
    },
  })

  const handleSubmit = () => {
    const updateData: CaseDocumentUpdate = {
      category: category as any,
      description: description || undefined,
      tags: tags.length > 0 ? tags : undefined,
      is_confidential: isConfidential,
      is_shared_with_client: isSharedWithClient,
    }

    updateMutation.mutate(updateData)
  }

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const fileTypeInfo = getFileTypeInfo(document.content_type)

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Edit Document</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* Document Info */}
            <Box p={4} bg="gray.50" borderRadius="md">
              <HStack spacing={3}>
                <Text fontSize="2xl">{fileTypeInfo.icon}</Text>
                <VStack align="start" spacing={1} flex={1}>
                  <Text fontWeight="medium" fontSize="sm" noOfLines={1}>
                    {document.original_filename}
                  </Text>
                  <HStack spacing={2}>
                    <Badge colorScheme={fileTypeInfo.color} variant="subtle" fontSize="xs">
                      {fileTypeInfo.label}
                    </Badge>
                    <Text fontSize="xs" color="gray.500">
                      {formatFileSize(document.file_size)}
                    </Text>
                  </HStack>
                </VStack>
              </HStack>
            </Box>

            {/* Category */}
            <FormControl>
              <FormLabel fontSize="sm">Category</FormLabel>
              <Select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                size="sm"
              >
                {Object.entries(DOCUMENT_CATEGORY_CONFIG).map(([key, config]) => (
                  <option key={key} value={key}>
                    {config.icon} {config.label}
                  </option>
                ))}
              </Select>
            </FormControl>

            {/* Description */}
            <FormControl>
              <FormLabel fontSize="sm">Description</FormLabel>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Brief description of the document..."
                size="sm"
                rows={3}
              />
            </FormControl>

            {/* Tags */}
            <FormControl>
              <FormLabel fontSize="sm">Tags</FormLabel>
              <VStack align="stretch" spacing={2}>
                <HStack>
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag..."
                    size="sm"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault()
                        handleAddTag()
                      }
                    }}
                  />
                  <Button
                    size="sm"
                    onClick={handleAddTag}
                    isDisabled={!newTag.trim() || tags.includes(newTag.trim())}
                  >
                    Add
                  </Button>
                </HStack>
                
                {tags.length > 0 && (
                  <Flex flexWrap="wrap" gap={2}>
                    {tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="subtle"
                        colorScheme="blue"
                        display="flex"
                        alignItems="center"
                        gap={1}
                        pr={1}
                      >
                        #{tag}
                        <IconButton
                          variant="ghost"
                          size="xs"
                          icon={<FiX />}
                          onClick={() => handleRemoveTag(tag)}
                          minW="auto"
                          h="auto"
                          p={0}
                        />
                      </Badge>
                    ))}
                  </Flex>
                )}
              </VStack>
            </FormControl>

            {/* Privacy Settings */}
            <VStack spacing={3} align="stretch">
              <Text fontSize="sm" fontWeight="medium">Privacy Settings</Text>
              
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="confidential" mb="0" fontSize="sm" flex={1}>
                  Mark as confidential
                  <Text fontSize="xs" color="gray.500">
                    Only lawyers and document uploader can access
                  </Text>
                </FormLabel>
                <Switch
                  id="confidential"
                  isChecked={isConfidential}
                  onChange={(e) => setIsConfidential(e.target.checked)}
                  size="sm"
                />
              </FormControl>

              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="shared" mb="0" fontSize="sm" flex={1}>
                  Share with client
                  <Text fontSize="xs" color="gray.500">
                    Client can view and download this document
                  </Text>
                </FormLabel>
                <Switch
                  id="shared"
                  isChecked={isSharedWithClient}
                  onChange={(e) => setIsSharedWithClient(e.target.checked)}
                  size="sm"
                  isDisabled={isConfidential} // Can't share confidential docs
                />
              </FormControl>
            </VStack>

            {/* Warning for confidential + shared */}
            {isConfidential && isSharedWithClient && (
              <Box p={3} bg="orange.50" borderRadius="md" border="1px solid" borderColor="orange.200">
                <Text fontSize="sm" color="orange.700">
                  ⚠️ Confidential documents cannot be shared with clients. 
                  The sharing setting will be disabled.
                </Text>
              </Box>
            )}
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={updateMutation.isPending}
              leftIcon={<FiSave />}
            >
              Save Changes
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default DocumentEditModal
