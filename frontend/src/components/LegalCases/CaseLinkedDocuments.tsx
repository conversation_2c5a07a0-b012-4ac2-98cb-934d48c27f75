import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>lex,
  <PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  IconButton,
  Tooltip,
  Select,
  useColorModeValue,
} from "@chakra-ui/react"
import {
  FiFile,
  FiRefreshCw,
  FiDownload,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ilter,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"

import { 
  DocumentCaseLinksService, 
  LinkedDocument,
  LINK_TYPE_CONFIG,
  getLinkTypeInfo 
} from "@/client/document-case-links"
import { formatFileSize, getCategoryInfo } from "@/client/case-documents"
import LinkedDocumentCard from "./LinkedDocumentCard"

interface CaseLinkedDocumentsProps {
  caseId: string
}

const CaseLinkedDocuments = ({ caseId }: CaseLinkedDocumentsProps) => {
  const [selectedLinkType, setSelectedLinkType] = useState<string>("")

  const borderColor = useColorModeValue("gray.200", "gray.600")

  // Fetch linked documents
  const { 
    data: documentsData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ["case-linked-documents", caseId, { link_type: selectedLinkType || undefined }],
    queryFn: () => DocumentCaseLinksService.getCaseLinkedDocuments(caseId, {
      link_type: selectedLinkType || undefined,
      limit: 100,
    }),
    enabled: !!caseId,
  })

  const documents = documentsData?.data || []
  const totalCount = documentsData?.count || 0

  // Group documents by link type
  const documentsByType = documents.reduce((acc, doc) => {
    const type = doc.link.link_type || "related"
    if (!acc[type]) acc[type] = []
    acc[type].push(doc)
    return acc
  }, {} as Record<string, LinkedDocument[]>)

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load linked documents. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={2}>
            <FiFile />
            <Text fontSize="lg" fontWeight="bold">
              Linked Documents
            </Text>
            <Badge variant="subtle" colorScheme="blue">
              {totalCount} documents
            </Badge>
          </HStack>
          <Text fontSize="sm" color="gray.600">
            Documents linked to this case from other sources
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Select
            placeholder="All link types"
            value={selectedLinkType}
            onChange={(e) => setSelectedLinkType(e.target.value)}
            size="sm"
            maxW="200px"
          >
            {Object.entries(LINK_TYPE_CONFIG).map(([type, config]) => (
              <option key={type} value={type}>
                {config.icon} {config.label}
              </option>
            ))}
          </Select>
          <Tooltip label="Refresh documents">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
        </HStack>
      </Flex>

      {/* Content */}
      {isLoading ? (
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={4} color="gray.600">Loading linked documents...</Text>
        </Box>
      ) : documents.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            {selectedLinkType 
              ? `No documents linked as "${LINK_TYPE_CONFIG[selectedLinkType as keyof typeof LINK_TYPE_CONFIG]?.label || selectedLinkType}"`
              : "No linked documents found"
            }
          </Text>
          <Text fontSize="sm" color="gray.600">
            Documents can be linked to this case from the document management section.
          </Text>
          {selectedLinkType && (
            <Button
              mt={4}
              variant="outline"
              onClick={() => setSelectedLinkType("")}
            >
              Show All Link Types
            </Button>
          )}
        </Box>
      ) : selectedLinkType ? (
        // Show filtered documents
        <VStack spacing={3} align="stretch">
          {documents.map((document) => (
            <LinkedDocumentCard
              key={`${document.id}-${document.link.id}`}
              document={document}
            />
          ))}
        </VStack>
      ) : (
        // Show documents grouped by type
        <VStack spacing={6} align="stretch">
          {Object.entries(documentsByType)
            .sort(([typeA], [typeB]) => {
              // Sort by link type priority
              const priorityOrder = ['evidence', 'contract', 'filing', 'correspondence', 'reference', 'research', 'billing', 'related']
              const indexA = priorityOrder.indexOf(typeA)
              const indexB = priorityOrder.indexOf(typeB)
              return (indexA === -1 ? 999 : indexA) - (indexB === -1 ? 999 : indexB)
            })
            .map(([linkType, typeDocuments]) => {
              const linkTypeInfo = getLinkTypeInfo(linkType)
              
              return (
                <Box key={linkType}>
                  {/* Type Header */}
                  <HStack spacing={3} mb={4}>
                    <HStack spacing={2}>
                      <Box fontSize="lg" color={`${linkTypeInfo.color}.500`}>
                        {linkTypeInfo.icon}
                      </Box>
                      <Text fontSize="md" fontWeight="bold">
                        {linkTypeInfo.label}
                      </Text>
                      <Badge variant="subtle" colorScheme={linkTypeInfo.color}>
                        {typeDocuments.length}
                      </Badge>
                    </HStack>
                    <Box flex={1} height="1px" bg={borderColor} />
                  </HStack>

                  {/* Documents for this type */}
                  <VStack spacing={3} align="stretch" pl={4}>
                    {typeDocuments.map((document) => (
                      <LinkedDocumentCard
                        key={`${document.id}-${document.link.id}`}
                        document={document}
                        showLinkType={false} // Don't show link type since we're already grouped
                      />
                    ))}
                  </VStack>
                </Box>
              )
            })}
        </VStack>
      )}
    </Box>
  )
}

export default CaseLinkedDocuments
