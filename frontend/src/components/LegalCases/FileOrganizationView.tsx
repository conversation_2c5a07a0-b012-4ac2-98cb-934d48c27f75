import {
  <PERSON>,
  <PERSON><PERSON><PERSON>ck,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>bs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  useDisclosure,
  Flex,
  Spacer,
  IconButton,
  Tooltip,
  Badge,
} from "@chakra-ui/react"
import {
  FiFolder,
  FiFile,
  FiGrid,
  FiList,
  FiUpload,
  FiSettings,
  FiRefreshCw,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"

import { CaseFoldersService } from "@/client/case-folders"
import { CaseDocumentsService } from "@/client/case-documents"
import FolderBrowser from "./FolderBrowser"
import CaseDocumentsSection from "./CaseDocumentsSection"
import DocumentUploadModal from "./DocumentUploadModal"

interface FileOrganizationViewProps {
  caseId: string
}

const FileOrganizationView = ({ caseId }: FileOrganizationViewProps) => {
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState(0) // 0: Folders, 1: Documents
  const { isOpen: isUploadOpen, onOpen: onUploadOpen, onClose: onUploadClose } = useDisclosure()

  // Check if case has folders initialized
  const { data: foldersData, refetch: refetchFolders } = useQuery({
    queryKey: ["case-folders", caseId, null], // Root folders
    queryFn: () => CaseFoldersService.getCaseFolders(caseId, { parent_folder_id: undefined }),
    enabled: !!caseId,
  })

  // Get documents count for current folder
  const { data: documentsData, refetch: refetchDocuments } = useQuery({
    queryKey: ["case-documents", caseId, { folder_id: currentFolderId }],
    queryFn: () => CaseDocumentsService.getCaseDocuments(caseId, { 
      folder_id: currentFolderId || undefined,
      limit: 1 // Just to get count
    }),
    enabled: !!caseId,
  })

  const folders = foldersData?.data || []
  const hasInitializedFolders = folders.length > 0
  const documentsCount = documentsData?.count || 0

  const handleFolderSelect = (folderId: string | null) => {
    setCurrentFolderId(folderId)
    // Switch to documents tab when folder is selected
    setActiveTab(1)
  }

  const handleFolderChange = () => {
    refetchFolders()
    refetchDocuments()
  }

  const handleDocumentUploaded = () => {
    refetchDocuments()
    refetchFolders() // Refresh to update folder sizes
    onUploadClose()
  }

  const handleInitializeFolders = async () => {
    try {
      await CaseFoldersService.initializeCaseFolders(caseId)
      refetchFolders()
    } catch (error) {
      console.error("Failed to initialize folders:", error)
    }
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <Text fontSize="xl" fontWeight="bold">
            📁 File Organization
          </Text>
          <Text fontSize="sm" color="gray.600">
            Organize documents in folders for better case management
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Refresh">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => {
                refetchFolders()
                refetchDocuments()
              }}
            />
          </Tooltip>
          <Button
            leftIcon={<FiUpload />}
            colorScheme="blue"
            size="sm"
            onClick={onUploadOpen}
          >
            Upload to {currentFolderId ? "Folder" : "Root"}
          </Button>
        </HStack>
      </Flex>

      {/* Initialize folders if not done */}
      {!hasInitializedFolders && (
        <Box
          p={6}
          bg="blue.50"
          borderRadius="lg"
          border="1px solid"
          borderColor="blue.200"
          mb={6}
        >
          <VStack spacing={4}>
            <Text fontSize="lg" fontWeight="medium" color="blue.700">
              📁 Set up folder organization
            </Text>
            <Text textAlign="center" color="blue.600">
              Create a structured folder system to organize your case documents efficiently.
              We'll create default folders for Evidence, Contracts, Correspondence, and more.
            </Text>
            <Button
              colorScheme="blue"
              onClick={handleInitializeFolders}
              leftIcon={<FiFolder />}
            >
              Initialize Folder Structure
            </Button>
          </VStack>
        </Box>
      )}

      {/* Main Content */}
      {hasInitializedFolders && (
        <Tabs index={activeTab} onChange={setActiveTab}>
          <TabList>
            <Tab>
              <HStack spacing={2}>
                <FiFolder />
                <Text>Folders</Text>
                <Badge variant="subtle" colorScheme="blue">
                  {folders.length}
                </Badge>
              </HStack>
            </Tab>
            <Tab>
              <HStack spacing={2}>
                <FiFile />
                <Text>Documents</Text>
                <Badge variant="subtle" colorScheme="green">
                  {documentsCount}
                </Badge>
              </HStack>
            </Tab>
          </TabList>

          <TabPanels>
            {/* Folders Tab */}
            <TabPanel px={0}>
              <FolderBrowser
                caseId={caseId}
                currentFolderId={currentFolderId}
                onFolderSelect={handleFolderSelect}
                onFolderChange={handleFolderChange}
              />
            </TabPanel>

            {/* Documents Tab */}
            <TabPanel px={0}>
              <Box>
                {/* Current location indicator */}
                <Box mb={4} p={3} bg="gray.50" borderRadius="md">
                  <HStack spacing={2}>
                    <FiFolder />
                    <Text fontSize="sm" fontWeight="medium">
                      Current location:
                    </Text>
                    <Text fontSize="sm" color="blue.600">
                      {currentFolderId ? "Selected Folder" : "Root"}
                    </Text>
                    {currentFolderId && (
                      <Button
                        size="xs"
                        variant="ghost"
                        onClick={() => setCurrentFolderId(null)}
                      >
                        Go to Root
                      </Button>
                    )}
                  </HStack>
                </Box>

                {/* Documents Section with folder filter */}
                <CaseDocumentsSection 
                  caseId={caseId}
                  folderId={currentFolderId}
                />
              </Box>
            </TabPanel>
          </TabPanels>
        </Tabs>
      )}

      {/* Upload Modal */}
      <DocumentUploadModal
        isOpen={isUploadOpen}
        onClose={onUploadClose}
        caseId={caseId}
        defaultFolderId={currentFolderId}
        onSuccess={handleDocumentUploaded}
      />
    </Box>
  )
}

export default FileOrganizationView
