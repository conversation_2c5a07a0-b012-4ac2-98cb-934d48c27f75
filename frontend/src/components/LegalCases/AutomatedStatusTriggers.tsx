import { useEffect, useCallback } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { LegalCasesService } from "@/client"
import { toaster } from "@/components/ui/toaster"

interface AutomatedStatusTriggersProps {
  enabled?: boolean
  checkInterval?: number
}

interface StatusTrigger {
  id: string
  name: string
  description: string
  condition: (case_: any) => boolean
  action: {
    newStatus: string
    reason: string
  }
  enabled: boolean
}

const AutomatedStatusTriggers = ({
  enabled = true,
  checkInterval = 60000 // 1 minute
}: AutomatedStatusTriggersProps) => {
  
  const queryClient = useQueryClient()

  // Define automated status triggers
  const statusTriggers: StatusTrigger[] = [
    {
      id: "overdue-review",
      name: "Overdue Review",
      description: "Move cases from 'under_review' to 'in_progress' if review period exceeds 7 days",
      condition: (case_) => {
        if (case_.status !== "under_review") return false
        
        // Check if case has been under review for more than 7 days
        const lastStatusChange = new Date(case_.updated_at || case_.opening_date)
        const daysSinceChange = (Date.now() - lastStatusChange.getTime()) / (1000 * 60 * 60 * 24)
        
        return daysSinceChange > 7
      },
      action: {
        newStatus: "in_progress",
        reason: "Automatically moved back to in_progress due to extended review period (>7 days)"
      },
      enabled: true
    },
    {
      id: "stale-open-cases",
      name: "Stale Open Cases",
      description: "Move cases from 'open' to 'in_progress' if they've been open for more than 3 days",
      condition: (case_) => {
        if (case_.status !== "open") return false
        
        const openingDate = new Date(case_.opening_date)
        const daysSinceOpening = (Date.now() - openingDate.getTime()) / (1000 * 60 * 60 * 24)
        
        return daysSinceOpening > 3
      },
      action: {
        newStatus: "in_progress",
        reason: "Automatically moved to in_progress - case has been open for more than 3 days"
      },
      enabled: true
    },
    {
      id: "auto-archive-old-closed",
      name: "Auto-Archive Old Closed Cases",
      description: "Move cases from 'closed' to 'archived' if they've been closed for more than 30 days",
      condition: (case_) => {
        if (case_.status !== "closed") return false
        
        // This would need to check the actual date when status was changed to closed
        // For now, using a simplified check based on updated_at
        const lastUpdate = new Date(case_.updated_at || case_.opening_date)
        const daysSinceUpdate = (Date.now() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24)
        
        return daysSinceUpdate > 30
      },
      action: {
        newStatus: "archived",
        reason: "Automatically archived - case has been closed for more than 30 days"
      },
      enabled: false // Disabled by default as archiving is more sensitive
    },
    {
      id: "priority-escalation",
      name: "Priority Escalation",
      description: "Move high priority cases to 'under_review' if in_progress for more than 5 days",
      condition: (case_) => {
        if (case_.status !== "in_progress" || case_.priority !== "high") return false
        
        const lastUpdate = new Date(case_.updated_at || case_.opening_date)
        const daysSinceUpdate = (Date.now() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24)
        
        return daysSinceUpdate > 5
      },
      action: {
        newStatus: "under_review",
        reason: "High priority case automatically escalated to review after 5 days in progress"
      },
      enabled: true
    }
  ]

  // Fetch all cases for trigger evaluation
  const { data: casesData } = useQuery({
    queryKey: ["legal-cases", "automation-check"],
    queryFn: () => LegalCasesService.readLegalCases({
      skip: 0,
      limit: 1000, // Get all cases for automation
    }),
    enabled: enabled,
    refetchInterval: checkInterval,
    staleTime: checkInterval / 2,
  })

  // Mutation for automated status updates
  const automatedUpdateMutation = useMutation({
    mutationFn: async (data: {
      caseId: string
      newStatus: string
      reason: string
      triggerName: string
    }) => {
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      return await request(OpenAPI, {
        method: 'PATCH',
        url: `/api/v1/legal-cases/${data.caseId}/status`,
        body: {
          new_status: data.newStatus,
          notes: `[AUTOMATED] ${data.reason} (Trigger: ${data.triggerName})`
        }
      })
    },
    onSuccess: (_, variables) => {
      // Show notification for automated status change
      toaster.create({
        title: "Automated Status Update",
        description: `Case status automatically updated: ${variables.reason}`,
        type: "info",
        duration: 5000,
      })
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
    },
    onError: (error, variables) => {
      console.error(`Automated status update failed for case ${variables.caseId}:`, error)
      
      // Optionally show error notification
      toaster.create({
        title: "Automated Update Failed",
        description: `Failed to automatically update case status: ${error.message}`,
        type: "error",
        duration: 5000,
      })
    }
  })

  // Process automated triggers
  const processAutomatedTriggers = useCallback(() => {
    if (!casesData?.data || !enabled) return

    const enabledTriggers = statusTriggers.filter(trigger => trigger.enabled)
    
    casesData.data.forEach(case_ => {
      enabledTriggers.forEach(trigger => {
        try {
          if (trigger.condition(case_)) {
            console.log(`Automated trigger "${trigger.name}" activated for case ${case_.id}`)
            
            // Execute the automated status change
            automatedUpdateMutation.mutate({
              caseId: case_.id,
              newStatus: trigger.action.newStatus,
              reason: trigger.action.reason,
              triggerName: trigger.name
            })
          }
        } catch (error) {
          console.error(`Error evaluating trigger "${trigger.name}" for case ${case_.id}:`, error)
        }
      })
    })
  }, [casesData, enabled, automatedUpdateMutation])

  // Run trigger evaluation when cases data changes
  useEffect(() => {
    if (enabled && casesData?.data) {
      // Add a small delay to avoid overwhelming the system
      const timeoutId = setTimeout(processAutomatedTriggers, 1000)
      return () => clearTimeout(timeoutId)
    }
  }, [casesData, enabled, processAutomatedTriggers])

  // Manual trigger execution (for testing or admin use)
  const executeTrigger = useCallback((triggerId: string, caseId: string) => {
    const trigger = statusTriggers.find(t => t.id === triggerId)
    const case_ = casesData?.data.find(c => c.id === caseId)
    
    if (!trigger || !case_) {
      console.error("Trigger or case not found")
      return
    }

    if (trigger.condition(case_)) {
      automatedUpdateMutation.mutate({
        caseId: case_.id,
        newStatus: trigger.action.newStatus,
        reason: `${trigger.action.reason} (Manual execution)`,
        triggerName: trigger.name
      })
    } else {
      toaster.create({
        title: "Trigger Condition Not Met",
        description: `The condition for trigger "${trigger.name}" is not met for this case`,
        type: "warning",
        duration: 3000,
      })
    }
  }, [casesData, automatedUpdateMutation])

  // This component doesn't render anything visible
  return null
}

export default AutomatedStatusTriggers

// Hook for manual trigger management
export const useAutomatedStatusTriggers = () => {
  return {
    AutomatedStatusTriggers,
    // Add methods for managing triggers if needed
    executeTrigger: (triggerId: string, caseId: string) => {
      // Implementation would go here
    },
    getTriggerStatus: () => {
      // Return current trigger status
    }
  }
}
