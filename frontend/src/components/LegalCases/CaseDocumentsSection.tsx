import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  <PERSON>ge,
  Flex,
  Spacer,
  useDisclosure,
  Spinner,
  Alert,
  Input,
  Select,
  IconButton,
  Tooltip,
  SimpleGrid,




} from "@chakra-ui/react"
import { FiPlus, FiSearch, FiFilter, FiRefreshCw, FiUpload, FiGrid, FiList } from "react-icons/fi"
import { useState, useEffect } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"

import { 
  CaseDocumentsService, 
  CaseDocument, 
  DOCUMENT_CATEGORY_CONFIG,
  formatFileSize 
} from "@/client/case-documents"
import useAuth from "@/hooks/useAuth"
import DocumentUploadModal from "./DocumentUploadModal"
import DocumentGrid from "./DocumentGrid"
import DocumentList from "./DocumentList"
import { useDebounce } from "@/hooks/useDebounce"

interface CaseDocumentsSectionProps {
  caseId: string
  folderId?: string | null
}

const CaseDocumentsSection = ({ caseId, folderId }: CaseDocumentsSectionProps) => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const { isOpen: isUploadOpen, onOpen: onUploadOpen, onClose: onUploadClose } = useDisclosure()
  
  // View and filters
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [search, setSearch] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [sortBy, setSortBy] = useState<"uploaded_at" | "filename" | "file_size" | "category">("uploaded_at")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  
  const debouncedSearch = useDebounce(search, 300)

  // Fetch documents
  const {
    data: documentsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["case-documents", caseId, {
      folder_id: folderId,
      search: debouncedSearch,
      category: selectedCategory || undefined,
      sort_by: sortBy,
      sort_order: sortOrder,
    }],
    queryFn: () => CaseDocumentsService.getCaseDocuments(caseId, {
      folder_id: folderId || undefined,
      search: debouncedSearch || undefined,
      category: selectedCategory || undefined,
      sort_by: sortBy,
      sort_order: sortOrder,
      limit: 50,
    }),
    enabled: !!caseId,
  })

  // Fetch document statistics
  const { data: statsData } = useQuery({
    queryKey: ["case-documents-stats", caseId],
    queryFn: () => CaseDocumentsService.getDocumentStats(caseId),
    enabled: !!caseId,
  })

  const documents = documentsData?.data || []
  const totalCount = documentsData?.count || 0
  const stats = statsData

  const handleDocumentUploaded = () => {
    queryClient.invalidateQueries({ queryKey: ["case-documents", caseId] })
    queryClient.invalidateQueries({ queryKey: ["case-documents-stats", caseId] })
    onUploadClose()
  }

  const handleDocumentUpdated = () => {
    queryClient.invalidateQueries({ queryKey: ["case-documents", caseId] })
    queryClient.invalidateQueries({ queryKey: ["case-documents-stats", caseId] })
  }

  const handleDocumentDeleted = () => {
    queryClient.invalidateQueries({ queryKey: ["case-documents", caseId] })
    queryClient.invalidateQueries({ queryKey: ["case-documents-stats", caseId] })
  }

  const clearFilters = () => {
    setSearch("")
    setSelectedCategory("")
    setSortBy("uploaded_at")
    setSortOrder("desc")
  }

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load documents. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <Text fontSize="xl" fontWeight="bold">
            Case Documents
          </Text>
          <Text fontSize="sm" color="gray.600">
            {totalCount} document{totalCount !== 1 ? 's' : ''} total
            {stats && ` • ${formatFileSize(stats.total_size_bytes)}`}
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Refresh documents">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
          <Tooltip label={viewMode === "grid" ? "List view" : "Grid view"}>
            <IconButton
              variant="ghost"
              size="sm"
              icon={viewMode === "grid" ? <FiList /> : <FiGrid />}
              onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
            />
          </Tooltip>
          <Button
            leftIcon={<FiUpload />}
            colorScheme="blue"
            size="sm"
            onClick={onUploadOpen}
          >
            Upload Document
          </Button>
        </HStack>
      </Flex>

      {/* Statistics */}
      {stats && (
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} mb={6}>
          <Box textAlign="center">
            <Box textAlign="center">Total Documents</Text>
            <Box textAlign="center">{stats.total_documents}</Text>
          </Box>
          <Box textAlign="center">
            <Box textAlign="center">Total Size</Text>
            <Box textAlign="center">{stats.total_size_mb} MB</Text>
          </Box>
          <Box textAlign="center">
            <Box textAlign="center">Recent Uploads</Text>
            <Box textAlign="center">{stats.recent_uploads}</Text>
            <Box textAlign="center">Last 7 days</Text>
          </Box>
          <Box textAlign="center">
            <Box textAlign="center">Categories</Text>
            <Box textAlign="center">{Object.keys(stats.categories).length}</Text>
          </Box>
        </SimpleGrid>
      )}

      {/* Filters */}
      <Box mb={6} p={4} bg="gray.50" borderRadius="lg">
        <VStack spacing={4}>
          <HStack w="full" spacing={4}>
            <Box flex={1}>
              <Input
                placeholder="Search documents..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                size="sm"
              />
            </Box>
            <Select
              placeholder="All categories"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              size="sm"
              maxW="200px"
            >
              {Object.entries(DOCUMENT_CATEGORY_CONFIG).map(([key, config]) => (
                <option key={key} value={key}>
                  {config.icon} {config.label}
                </option>
              ))}
            </Select>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              leftIcon={<FiFilter />}
            >
              Clear
            </Button>
          </HStack>
          
          <HStack w="full" justify="space-between">
            <HStack spacing={4}>
              <Text fontSize="sm" color="gray.600">Sort by:</Text>
              <Select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                size="sm"
                maxW="120px"
              >
                <option value="uploaded_at">Date</option>
                <option value="filename">Name</option>
                <option value="file_size">Size</option>
                <option value="category">Category</option>
              </Select>
              <Select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as any)}
                size="sm"
                maxW="100px"
              >
                <option value="desc">Newest</option>
                <option value="asc">Oldest</option>
              </Select>
            </HStack>
            
            {/* Active filters display */}
            <HStack spacing={2}>
              {selectedCategory && (
                <Badge colorScheme="blue" variant="subtle">
                  Category: {DOCUMENT_CATEGORY_CONFIG[selectedCategory as keyof typeof DOCUMENT_CATEGORY_CONFIG]?.label}
                </Badge>
              )}
              {debouncedSearch && (
                <Badge colorScheme="purple" variant="subtle">
                  Search: "{debouncedSearch}"
                </Badge>
              )}
            </HStack>
          </HStack>
        </VStack>
      </Box>

      {/* Documents Display */}
      {isLoading ? (
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={4} color="gray.600">Loading documents...</Text>
        </Box>
      ) : documents.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            {debouncedSearch || selectedCategory 
              ? "No documents match your filters" 
              : "No documents uploaded yet"
            }
          </Text>
          <Button
            leftIcon={<FiUpload />}
            colorScheme="blue"
            onClick={onUploadOpen}
          >
            Upload First Document
          </Button>
        </Box>
      ) : viewMode === "grid" ? (
        <DocumentGrid
          documents={documents}
          caseId={caseId}
          onUpdated={handleDocumentUpdated}
          onDeleted={handleDocumentDeleted}
        />
      ) : (
        <DocumentList
          documents={documents}
          caseId={caseId}
          onUpdated={handleDocumentUpdated}
          onDeleted={handleDocumentDeleted}
        />
      )}

      {/* Upload Modal */}
      <DocumentUploadModal
        isOpen={isUploadOpen}
        onClose={onUploadClose}
        caseId={caseId}
        onSuccess={handleDocumentUploaded}
      />
    </Box>
  )
}

export default CaseDocumentsSection
