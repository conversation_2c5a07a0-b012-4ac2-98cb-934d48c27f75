import {
  VSta<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Box,
} from "@chakra-ui/react"
import { FiFlag, FiSave } from "react-icons/fi"
import { useState } from "react"
import { useMutation, useQuery } from "@tanstack/react-query"

import { 
  CaseProgressService, 
  CaseMilestoneCreate,
  MILESTONE_TYPE_CONFIG,
  getMilestoneTypeInfo 
} from "@/client/case-progress"
import { UsersService } from "@/client/users"
import { toaster } from "@/components/ui/toaster"

interface CreateMilestoneModalProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  caseType?: string
  onSuccess: () => void
}

const CreateMilestoneModal = ({
  isOpen,
  onClose,
  caseId,
  caseType,
  onSuccess
}: CreateMilestoneModalProps) => {
  if (!isOpen) return null

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg="rgba(0,0,0,0.5)"
      zIndex={1000}
      display="flex"
      alignItems="center"
      justifyContent="center"
      onClick={onClose}
    >
      <Box
        bg="white"
        borderRadius="lg"
        p={6}
        maxW="md"
        w="full"
        mx={4}
        onClick={(e) => e.stopPropagation()}
      >
        <VStack spacing={4} align="stretch">
          <HStack spacing={2}>
            <FiFlag />
            <Text fontSize="lg" fontWeight="bold">Create Milestone</Text>
          </HStack>

          <Text>Create milestone functionality coming soon...</Text>

          <HStack spacing={3} justify="flex-end">
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={onSuccess}>
              Create Milestone
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Box>
  )
}

export default CreateMilestoneModal
