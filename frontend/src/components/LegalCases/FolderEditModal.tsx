import {
  Modal,
  <PERSON>dal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Box,
  SimpleGrid,
  Badge,
  useColorModeValue,
} from "@chakra-ui/react"
import { FiSave, FiEdit } from "react-icons/fi"
import { useState, useEffect } from "react"
import { useMutation } from "@tanstack/react-query"

import { 
  CaseFolder,
  CaseFoldersService, 
  CaseFolderUpdate,
  DEFAULT_FOLDER_COLORS,
  getFolderIcon 
} from "@/client/case-folders"
import { toaster } from "@/components/ui/toaster"

interface FolderEditModalProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  folder: CaseFolder
  onSuccess: () => void
}

const FolderEditModal = ({ 
  isOpen, 
  onClose, 
  caseId, 
  folder,
  onSuccess 
}: FolderEditModalProps) => {
  // Form state
  const [name, setName] = useState(folder.name)
  const [description, setDescription] = useState(folder.description || "")
  const [selectedColor, setSelectedColor] = useState(folder.color || DEFAULT_FOLDER_COLORS[0])

  const selectedBg = useColorModeValue("gray.100", "gray.600")

  // Reset form when folder changes
  useEffect(() => {
    setName(folder.name)
    setDescription(folder.description || "")
    setSelectedColor(folder.color || DEFAULT_FOLDER_COLORS[0])
  }, [folder])

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: CaseFolderUpdate) => 
      CaseFoldersService.updateCaseFolder(caseId, folder.id, data),
    onSuccess: () => {
      toaster.create({
        title: "Folder updated",
        description: "Your folder has been updated successfully.",
        status: "success",
      })
      onSuccess()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update folder",
        status: "error",
      })
    },
  })

  const handleSubmit = () => {
    if (!name.trim()) {
      toaster.create({
        title: "Error",
        description: "Folder name cannot be empty",
        status: "error",
      })
      return
    }

    const updateData: CaseFolderUpdate = {
      name: name.trim(),
      description: description.trim() || undefined,
      color: selectedColor,
    }

    updateMutation.mutate(updateData)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handleSubmit()
    }
  }

  const folderIcon = getFolderIcon(folder)

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={2}>
            <FiEdit />
            <Text>Edit Folder</Text>
            {folder.is_system_folder && (
              <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                System
              </Badge>
            )}
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* Current folder info */}
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" fontWeight="medium" mb={2}>Current Folder:</Text>
              <HStack spacing={3}>
                <Box fontSize="2xl" color={folder.color}>
                  {folderIcon}
                </Box>
                <VStack align="start" spacing={0}>
                  <Text fontWeight="medium" fontSize="sm">
                    {folder.name}
                  </Text>
                  <HStack spacing={2} fontSize="xs" color="gray.500">
                    <Text>{folder.document_count} documents</Text>
                    <Text>{folder.subfolder_count} folders</Text>
                  </HStack>
                </VStack>
              </HStack>
            </Box>

            {/* Folder Name */}
            <FormControl isRequired>
              <FormLabel fontSize="sm">Folder Name</FormLabel>
              <Input
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter folder name..."
                onKeyDown={handleKeyPress}
                maxLength={255}
                isDisabled={folder.is_system_folder} // System folders can't be renamed
              />
              {folder.is_system_folder && (
                <Text fontSize="xs" color="gray.500" mt={1}>
                  System folder names cannot be changed
                </Text>
              )}
            </FormControl>

            {/* Description */}
            <FormControl>
              <FormLabel fontSize="sm">Description</FormLabel>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Brief description of the folder..."
                rows={3}
                maxLength={1000}
              />
            </FormControl>

            {/* Color Selection */}
            <FormControl>
              <FormLabel fontSize="sm">Folder Color</FormLabel>
              <SimpleGrid columns={6} spacing={2}>
                {DEFAULT_FOLDER_COLORS.map((color) => (
                  <Box
                    key={color}
                    w="40px"
                    h="40px"
                    bg={color}
                    borderRadius="md"
                    cursor="pointer"
                    border="2px solid"
                    borderColor={selectedColor === color ? "blue.500" : "transparent"}
                    _hover={{ 
                      transform: "scale(1.1)",
                      shadow: "md" 
                    }}
                    transition="all 0.2s"
                    onClick={() => setSelectedColor(color)}
                  />
                ))}
              </SimpleGrid>
              <Text fontSize="xs" color="gray.500" mt={2}>
                Selected color: {selectedColor}
              </Text>
            </FormControl>

            {/* Preview */}
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" fontWeight="medium" mb={2}>Preview:</Text>
              <HStack spacing={3}>
                <Box
                  fontSize="2xl"
                  color={selectedColor}
                >
                  {folderIcon}
                </Box>
                <VStack align="start" spacing={0}>
                  <Text fontWeight="medium" fontSize="sm">
                    {name || "Folder Name"}
                  </Text>
                  {description && (
                    <Text fontSize="xs" color="gray.600" noOfLines={1}>
                      {description}
                    </Text>
                  )}
                </VStack>
              </HStack>
            </Box>

            {/* System folder warning */}
            {folder.is_system_folder && (
              <Box p={3} bg="orange.50" borderRadius="md" border="1px solid" borderColor="orange.200">
                <Text fontSize="sm" color="orange.700">
                  ⚠️ This is a system folder. Some properties cannot be modified.
                </Text>
              </Box>
            )}

            {/* Help text */}
            <Text fontSize="xs" color="gray.500">
              Tip: Use Cmd/Ctrl + Enter to save quickly
            </Text>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={updateMutation.isPending}
              leftIcon={<FiSave />}
              isDisabled={!name.trim()}
            >
              Save Changes
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default FolderEditModal
