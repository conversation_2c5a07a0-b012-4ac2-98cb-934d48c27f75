import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  CircularProgress,
  CircularProgressLabel,
  Alert,
  AlertIcon,
  List,
  ListItem,

  Tooltip,
  Button,
  Collapse,
} from "@chakra-ui/react"
import {
  FiHeart,
  FiAlertTriangle,
  FiCheckCircle,
  FiInfo,
  FiChevronDown,
  FiChevronUp,
} from "react-icons/fi"
import { useState } from "react"

import { 
  HealthIndicators, 
  getHealthStatusInfo,
  getHealthScoreColor 
} from "@/client/case-analytics"

interface HealthIndicatorsCardProps {
  indicators: HealthIndicators
}

const HealthIndicatorsCard = ({ indicators }: HealthIndicatorsCardProps) => {
  const [showRecommendations, setShowRecommendations] = useState(false)
  const [showFactors, setShowFactors] = useState(false)
  
  const healthInfo = getHealthStatusInfo(indicators.health_status)
  const scoreColor = getHealthScoreColor(indicators.health_score)

  const getHealthIcon = () => {
    switch (indicators.health_status) {
      case "excellent":
        return <FiCheckCircle color="green" />
      case "good":
        return <FiCheckCircle color="blue" />
      case "fair":
        return <FiInfo color="yellow" />
      case "poor":
        return <FiAlertTriangle color="orange" />
      case "critical":
        return <FiAlertTriangle color="red" />
      default:
        return <FiHeart />
    }
  }

  const getAlertStatus = () => {
    switch (indicators.health_status) {
      case "excellent":
      case "good":
        return "success"
      case "fair":
        return "warning"
      case "poor":
        return "warning"
      case "critical":
        return "error"
      default:
        return "info"
    }
  }

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiHeart />
          <Heading size="sm">Case Health</Heading>
          <Badge 
            colorScheme={healthInfo.color} 
            variant="solid"
            fontSize="xs"
          >
            {healthInfo.icon} {healthInfo.label}
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Health Score */}
          <Box textAlign="center">
            <CircularProgress 
              value={indicators.health_score} 
              color={`${scoreColor}.400`}
              size="80px"
              thickness="8px"
            >
              <CircularProgressLabel fontSize="sm" fontWeight="bold">
                {indicators.health_score}
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="sm" color="gray.600" mt={2}>
              Health Score
            </Text>
            <Text fontSize="xs" color="gray.500">
              {healthInfo.description}
            </Text>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Health Status Alert */}
          <Alert status={getAlertStatus()} borderRadius="md" py={2}>
            <AlertIcon />
            <Box>
              <Text fontSize="sm" fontWeight="medium">
                Case Status: {healthInfo.label}
              </Text>
              <Text fontSize="xs">
                {healthInfo.description}
              </Text>
            </Box>
          </Alert>

          {/* Health Factors */}
          {indicators.health_factors.length > 0 && (
            <Box>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFactors(!showFactors)}
                rightIcon={showFactors ? <FiChevronUp /> : <FiChevronDown />}
                width="full"
                justifyContent="space-between"
              >
                <HStack spacing={2}>
                  <FiInfo size={14} />
                  <Text>Health Factors ({indicators.health_factors.length})</Text>
                </HStack>
              </Button>
              
              <Collapse in={showFactors}>
                <Box mt={2} p={3} bg="gray.50" borderRadius="md">
                  <List spacing={1}>
                    {indicators.health_factors.map((factor, index) => (
                      <ListItem key={index} fontSize="sm">
                        <HStack spacing={2}>
                          <Box color="orange.500">•</Box>
                          <Text>{factor}</Text>
                        </HStack>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Collapse>
            </Box>
          )}

          {/* Recommendations */}
          {indicators.recommendations.length > 0 && (
            <Box>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowRecommendations(!showRecommendations)}
                rightIcon={showRecommendations ? <FiChevronUp /> : <FiChevronDown />}
                width="full"
                justifyContent="space-between"
              >
                <HStack spacing={2}>
                  <FiCheckCircle size={14} />
                  <Text>Recommendations ({indicators.recommendations.length})</Text>
                </HStack>
              </Button>
              
              <Collapse in={showRecommendations}>
                <Box mt={2} p={3} bg="blue.50" borderRadius="md">
                  <List spacing={2}>
                    {indicators.recommendations.map((recommendation, index) => (
                      <ListItem key={index} fontSize="sm">
                        <HStack spacing={2} align="start">
                          <Box color="blue.500" mt={1}>💡</Box>
                          <Text>{recommendation}</Text>
                        </HStack>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Collapse>
            </Box>
          )}

          {/* Health Breakdown */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Health Breakdown:
            </Text>
            
            <VStack spacing={2} align="stretch">
              {/* Score ranges */}
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Excellent (80-100):</Text>
                <Text fontSize="xs" color={indicators.health_score >= 80 ? "green.600" : "gray.400"}>
                  {indicators.health_score >= 80 ? "✓" : "○"}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Good (60-79):</Text>
                <Text fontSize="xs" color={indicators.health_score >= 60 && indicators.health_score < 80 ? "blue.600" : "gray.400"}>
                  {indicators.health_score >= 60 && indicators.health_score < 80 ? "✓" : "○"}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Fair (40-59):</Text>
                <Text fontSize="xs" color={indicators.health_score >= 40 && indicators.health_score < 60 ? "yellow.600" : "gray.400"}>
                  {indicators.health_score >= 40 && indicators.health_score < 60 ? "✓" : "○"}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Poor (20-39):</Text>
                <Text fontSize="xs" color={indicators.health_score >= 20 && indicators.health_score < 40 ? "orange.600" : "gray.400"}>
                  {indicators.health_score >= 20 && indicators.health_score < 40 ? "✓" : "○"}
                </Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="xs" color="gray.600">Critical (0-19):</Text>
                <Text fontSize="xs" color={indicators.health_score < 20 ? "red.600" : "gray.400"}>
                  {indicators.health_score < 20 ? "✓" : "○"}
                </Text>
              </HStack>
            </VStack>
          </Box>

          {/* Quick Actions */}
          <Box>
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Quick Actions:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {indicators.health_score < 60 && (
                <Button size="xs" colorScheme="orange" variant="outline">
                  Review Case Strategy
                </Button>
              )}
              
              {indicators.health_factors.some(f => f.includes("overdue")) && (
                <Button size="xs" colorScheme="red" variant="outline">
                  Address Overdue Items
                </Button>
              )}
              
              {indicators.health_factors.some(f => f.includes("activity")) && (
                <Button size="xs" colorScheme="blue" variant="outline">
                  Increase Activity
                </Button>
              )}
              
              {indicators.health_score >= 80 && (
                <Button size="xs" colorScheme="green" variant="outline">
                  Maintain Current Pace
                </Button>
              )}
            </VStack>
          </Box>

          {/* Health Trend Indicator */}
          <HStack justify="center" bg={`${healthInfo.color}.50`} p={2} borderRadius="md">
            <Box fontSize="lg">{getHealthIcon()}</Box>
            <VStack spacing={0} align="center">
              <Text fontSize="sm" fontWeight="bold" color={`${healthInfo.color}.700`}>
                {healthInfo.label}
              </Text>
              <Text fontSize="xs" color={`${healthInfo.color}.600`}>
                Score: {indicators.health_score}/100
              </Text>
            </VStack>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default HealthIndicatorsCard
