import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>lex,
  <PERSON>r,
  <PERSON>ge,
  IconButton,
  Tooltip,
  Alert,
  AlertIcon,
  Spinner,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Heading,

} from "@chakra-ui/react"
import {
  FiRefreshCw,
  FiDownload,
  FiBarChart3,
  FiTrendingUp,
  FiClock,
  FiUsers,
  FiFileText,
  FiTarget,
  FiActivity,
  FiSettings,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"

import { CaseAnalyticsService, CaseAnalytics, getHealthStatusInfo } from "@/client/case-analytics"
import CaseOverviewCard from "./CaseOverviewCard"
import PerformanceMetricsCard from "./PerformanceMetricsCard"
import ActivityAnalyticsCard from "./ActivityAnalyticsCard"
import DocumentAnalyticsCard from "./DocumentAnalyticsCard"
import ProgressAnalyticsCard from "./ProgressAnalyticsCard"
import CollaborationAnalyticsCard from "./CollaborationAnalyticsCard"
import TimeAnalyticsCard from "./TimeAnalyticsCard"
import HealthIndicatorsCard from "./HealthIndicatorsCard"
import ComparativeAnalyticsCard from "./ComparativeAnalyticsCard"
import AnalyticsChartsSection from "./AnalyticsChartsSection"

interface CaseDashboardProps {
  caseId: string
  caseTitle?: string
  onExport?: () => void
  onSettings?: () => void
}

const CaseDashboard = ({ 
  caseId, 
  caseTitle,
  onExport,
  onSettings 
}: CaseDashboardProps) => {
  const [periodDays, setPeriodDays] = useState(30)
  const [includeComparisons, setIncludeComparisons] = useState(true)
  const [activeView, setActiveView] = useState<"overview" | "charts" | "detailed">("overview")

  // Fetch case analytics
  const { 
    data: analytics, 
    isLoading, 
    error, 
    refetch,
    isFetching 
  } = useQuery({
    queryKey: ["case-analytics", caseId, periodDays, includeComparisons],
    queryFn: () => CaseAnalyticsService.getCaseAnalytics(caseId, {
      period_days: periodDays,
      include_comparisons: includeComparisons
    }),
    enabled: !!caseId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })

  const handleRefresh = () => {
    refetch()
  }

  const handlePeriodChange = (days: number) => {
    setPeriodDays(days)
  }

  const handleExport = () => {
    if (analytics && onExport) {
      // Create downloadable analytics report
      const reportData = {
        case_title: analytics.case_overview.title,
        generated_at: new Date().toISOString(),
        analytics: analytics
      }
      
      const blob = new Blob([JSON.stringify(reportData, null, 2)], {
        type: "application/json"
      })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `case-analytics-${caseId}-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
    
    onExport?.()
  }

  if (error) {
    return (
      <Alert status="error" borderRadius="lg">
        <AlertIcon />
        <Box>
          <Text fontWeight="bold">Failed to load case analytics</Text>
          <Text fontSize="sm">
            {error instanceof Error ? error.message : "An unexpected error occurred"}
          </Text>
        </Box>
        <Spacer />
        <Button size="sm" onClick={handleRefresh}>
          Retry
        </Button>
      </Alert>
    )
  }

  if (isLoading) {
    return (
      <Box textAlign="center" py={12}>
        <Spinner size="xl" color="blue.500" thickness="4px" />
        <Text mt={4} fontSize="lg" color="gray.600">
          Loading case analytics...
        </Text>
        <Text fontSize="sm" color="gray.500">
          Analyzing case data and generating insights
        </Text>
      </Box>
    )
  }

  if (!analytics) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Box>
          <Text fontWeight="bold">No analytics data available</Text>
          <Text fontSize="sm">
            Analytics data could not be generated for this case.
          </Text>
        </Box>
      </Alert>
    )
  }

  const healthInfo = getHealthStatusInfo(analytics.health_indicators.health_status)

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={3}>
            <FiBarChart3 size={24} />
            <Heading size="lg">Case Analytics Dashboard</Heading>
            <Badge 
              colorScheme={healthInfo.color} 
              variant="subtle" 
              fontSize="sm"
              px={2}
              py={1}
            >
              {healthInfo.icon} {healthInfo.label}
            </Badge>
          </HStack>
          <Text color="gray.600">
            {caseTitle || analytics.case_overview.title} • 
            Analysis for last {periodDays} days • 
            Generated {new Date(analytics.generated_at).toLocaleDateString()}
          </Text>
        </VStack>
        <Spacer />
        
        {/* Controls */}
        <HStack spacing={2}>
          {/* Period selector */}
          <HStack spacing={1}>
            {[7, 30, 90, 365].map((days) => (
              <Button
                key={days}
                size="sm"
                variant={periodDays === days ? "solid" : "ghost"}
                colorScheme={periodDays === days ? "blue" : "gray"}
                onClick={() => handlePeriodChange(days)}
              >
                {days}d
              </Button>
            ))}
          </HStack>
          
          <Divider orientation="vertical" height="24px" />
          
          {/* View selector */}
          <HStack spacing={1}>
            {[
              { key: "overview", label: "Overview", icon: FiActivity },
              { key: "charts", label: "Charts", icon: FiBarChart3 },
              { key: "detailed", label: "Detailed", icon: FiSettings },
            ].map(({ key, label, icon: Icon }) => (
              <Button
                key={key}
                size="sm"
                variant={activeView === key ? "solid" : "ghost"}
                colorScheme={activeView === key ? "blue" : "gray"}
                leftIcon={<Icon />}
                onClick={() => setActiveView(key as any)}
              >
                {label}
              </Button>
            ))}
          </HStack>
          
          <Divider orientation="vertical" height="24px" />
          
          {/* Action buttons */}
          <Tooltip label="Refresh analytics">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={handleRefresh}
              isLoading={isFetching}
            />
          </Tooltip>
          
          <Tooltip label="Export analytics report">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiDownload />}
              onClick={handleExport}
            />
          </Tooltip>
          
          {onSettings && (
            <Tooltip label="Analytics settings">
              <IconButton
                variant="ghost"
                size="sm"
                icon={<FiSettings />}
                onClick={onSettings}
              />
            </Tooltip>
          )}
        </HStack>
      </Flex>

      {/* Content based on active view */}
      {activeView === "overview" && (
        <VStack spacing={6} align="stretch">
          {/* Key metrics row */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
            <CaseOverviewCard overview={analytics.case_overview} />
            <PerformanceMetricsCard metrics={analytics.performance_metrics} />
            <HealthIndicatorsCard indicators={analytics.health_indicators} />
            <TimeAnalyticsCard timeAnalytics={analytics.time_analytics} />
          </SimpleGrid>

          {/* Analytics cards */}
          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
            <ActivityAnalyticsCard analytics={analytics.activity_analytics} />
            <DocumentAnalyticsCard analytics={analytics.document_analytics} />
            <ProgressAnalyticsCard analytics={analytics.progress_analytics} />
            <CollaborationAnalyticsCard analytics={analytics.collaboration_analytics} />
          </SimpleGrid>

          {/* Comparative analytics */}
          {includeComparisons && analytics.comparative_analytics && (
            <ComparativeAnalyticsCard analytics={analytics.comparative_analytics} />
          )}
        </VStack>
      )}

      {activeView === "charts" && (
        <AnalyticsChartsSection analytics={analytics} />
      )}

      {activeView === "detailed" && (
        <VStack spacing={6} align="stretch">
          <Card>
            <CardHeader>
              <Heading size="md">Detailed Analytics Report</Heading>
            </CardHeader>
            <CardBody>
              <Text color="gray.600" mb={4}>
                Comprehensive analytics data for advanced analysis and reporting.
              </Text>
              
              {/* Raw data display */}
              <Box
                bg="gray.50"
                p={4}
                borderRadius="md"
                maxH="400px"
                overflowY="auto"
                fontSize="sm"
                fontFamily="mono"
              >
                <pre>{JSON.stringify(analytics, null, 2)}</pre>
              </Box>
            </CardBody>
          </Card>
        </VStack>
      )}
    </Box>
  )
}

export default CaseDashboard
