import {
  VSta<PERSON>,
  H<PERSON>ta<PERSON>,
  <PERSON>,
  But<PERSON>,
  Box,
} from "@chakra-ui/react"
import { FiClock } from "react-icons/fi"

interface CreateDeadlineModalProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  onSuccess: () => void
}

const CreateDeadlineModal = ({ 
  isOpen, 
  onClose, 
  caseId,
  onSuccess 
}: CreateDeadlineModalProps) => {
  if (!isOpen) return null

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg="rgba(0,0,0,0.5)"
      zIndex={1000}
      display="flex"
      alignItems="center"
      justifyContent="center"
      onClick={onClose}
    >
      <Box
        bg="white"
        borderRadius="lg"
        p={6}
        maxW="md"
        w="full"
        mx={4}
        onClick={(e) => e.stopPropagation()}
      >
        <VStack spacing={4} align="stretch">
          <HStack spacing={2}>
            <FiClock />
            <Text fontSize="lg" fontWeight="bold">Create Deadline</Text>
          </HStack>

          <Text>Create deadline functionality coming soon...</Text>

          <HStack spacing={3} justify="flex-end">
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={onSuccess}>
              Create Deadline
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Box>
  )
}

export default CreateDeadlineModal
