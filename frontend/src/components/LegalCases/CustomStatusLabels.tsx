import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from "@chakra-ui/react"
import {
  FiCircle,
  FiPlay,
  FiEye,
  FiCheck,
  FiArchive,
  FiScale,
  FiShield,
  FiHeart,
  FiBriefcase,
  FiGlobe,
  FiActivity,
  FiHome,
  FiCpu,
  FiUsers
} from "react-icons/fi"

interface CustomStatusLabelsProps {
  status: string
  caseType: string
  size?: "sm" | "md" | "lg"
  showIcon?: boolean
  animated?: boolean
}

const CustomStatusLabels = ({
  status,
  caseType,
  size = "sm",
  showIcon = true,
  animated = false
}: CustomStatusLabelsProps) => {

  // Get case type specific configurations
  const getCaseTypeConfig = (caseType: string) => {
    const typeStr = String(caseType || "").toLowerCase()
    
    const configs: Record<string, { icon: any; color: string; prefix: string }> = {
      civil: { icon: FiScale, color: "blue", prefix: "Civil" },
      criminal: { icon: FiShield, color: "red", prefix: "Criminal" },
      family: { icon: FiHear<PERSON>, color: "pink", prefix: "Family" },
      corporate: { icon: FiBriefcase, color: "purple", prefix: "Corporate" },
      immigration: { icon: FiGlobe, color: "teal", prefix: "Immigration" },
      personal_injury: { icon: FiActivity, color: "orange", prefix: "Personal Injury" },
      real_estate: { icon: FiHome, color: "green", prefix: "Real Estate" },
      intellectual_property: { icon: FiCpu, color: "cyan", prefix: "IP" },
      employment: { icon: FiUsers, color: "yellow", prefix: "Employment" },
      other: { icon: FiCircle, color: "gray", prefix: "General" }
    }
    
    return configs[typeStr] || configs.other
  }

  // Get status specific labels based on case type
  const getCustomStatusLabel = (status: string, caseType: string) => {
    const statusStr = String(status || "").toLowerCase()
    const typeStr = String(caseType || "").toLowerCase()
    
    // Custom labels for different case types
    const customLabels: Record<string, Record<string, { label: string; description: string; icon: any; color: string }>> = {
      criminal: {
        open: { label: "Investigation", description: "Case under investigation", icon: FiEye, color: "blue" },
        in_progress: { label: "Prosecution", description: "Active prosecution phase", icon: FiPlay, color: "orange" },
        under_review: { label: "Court Review", description: "Under court review", icon: FiScale, color: "purple" },
        closed: { label: "Resolved", description: "Case resolved", icon: FiCheck, color: "green" },
        archived: { label: "Archived", description: "Case archived", icon: FiArchive, color: "gray" }
      },
      family: {
        open: { label: "Consultation", description: "Initial consultation phase", icon: FiCircle, color: "blue" },
        in_progress: { label: "Mediation", description: "Mediation in progress", icon: FiHeart, color: "orange" },
        under_review: { label: "Court Pending", description: "Awaiting court decision", icon: FiScale, color: "purple" },
        closed: { label: "Settled", description: "Family matter settled", icon: FiCheck, color: "green" },
        archived: { label: "Archived", description: "Case archived", icon: FiArchive, color: "gray" }
      },
      corporate: {
        open: { label: "Due Diligence", description: "Due diligence phase", icon: FiCircle, color: "blue" },
        in_progress: { label: "Negotiation", description: "Contract negotiation", icon: FiBriefcase, color: "orange" },
        under_review: { label: "Legal Review", description: "Legal document review", icon: FiEye, color: "purple" },
        closed: { label: "Executed", description: "Contract executed", icon: FiCheck, color: "green" },
        archived: { label: "Archived", description: "Case archived", icon: FiArchive, color: "gray" }
      },
      real_estate: {
        open: { label: "Property Review", description: "Property documentation review", icon: FiHome, color: "blue" },
        in_progress: { label: "Transaction", description: "Transaction in progress", icon: FiPlay, color: "orange" },
        under_review: { label: "Title Review", description: "Title and deed review", icon: FiEye, color: "purple" },
        closed: { label: "Transferred", description: "Property transferred", icon: FiCheck, color: "green" },
        archived: { label: "Archived", description: "Case archived", icon: FiArchive, color: "gray" }
      },
      immigration: {
        open: { label: "Application", description: "Application preparation", icon: FiGlobe, color: "blue" },
        in_progress: { label: "Processing", description: "Application processing", icon: FiPlay, color: "orange" },
        under_review: { label: "USCIS Review", description: "Under USCIS review", icon: FiEye, color: "purple" },
        closed: { label: "Approved", description: "Application approved", icon: FiCheck, color: "green" },
        archived: { label: "Archived", description: "Case archived", icon: FiArchive, color: "gray" }
      }
    }

    // Default labels for all case types
    const defaultLabels: Record<string, { label: string; description: string; icon: any; color: string }> = {
      open: { label: "Open", description: "Case opened", icon: FiCircle, color: "blue" },
      in_progress: { label: "In Progress", description: "Work in progress", icon: FiPlay, color: "orange" },
      under_review: { label: "Under Review", description: "Under review", icon: FiEye, color: "purple" },
      closed: { label: "Closed", description: "Case closed", icon: FiCheck, color: "green" },
      archived: { label: "Archived", description: "Case archived", icon: FiArchive, color: "gray" }
    }

    return customLabels[typeStr]?.[statusStr] || defaultLabels[statusStr] || defaultLabels.open
  }

  const caseTypeConfig = getCaseTypeConfig(caseType)
  const statusConfig = getCustomStatusLabel(status, caseType)

  return (
    <HStack spacing={1}>
      {/* Case type indicator */}
      <Tooltip label={`${caseTypeConfig.prefix} Case`} placement="top">
        <Badge
          colorScheme={caseTypeConfig.color}
          variant="outline"
          size={size}
          textTransform="none"
        >
          <HStack spacing={1} align="center">
            {showIcon && (
              <Icon
                as={caseTypeConfig.icon}
                boxSize={size === "sm" ? "10px" : size === "md" ? "12px" : "14px"}
              />
            )}
            <span>{caseTypeConfig.prefix}</span>
          </HStack>
        </Badge>
      </Tooltip>

      {/* Custom status label */}
      <Tooltip label={statusConfig.description} placement="top">
        <Badge
          colorScheme={statusConfig.color}
          variant="solid"
          size={size}
          textTransform="none"
          cursor="help"
          transition={animated ? "all 0.2s ease-in-out" : "none"}
          _hover={animated ? {
            transform: "scale(1.05)",
            shadow: "md"
          } : {}}
        >
          <HStack spacing={1} align="center">
            {showIcon && (
              <Icon
                as={statusConfig.icon}
                boxSize={size === "sm" ? "10px" : size === "md" ? "12px" : "14px"}
              />
            )}
            <span>{statusConfig.label}</span>
          </HStack>
        </Badge>
      </Tooltip>
    </HStack>
  )
}

export default CustomStatusLabels

// Hook to get custom status information
export const useCustomStatusLabels = (status: string, caseType: string) => {
  const getCustomStatusLabel = (status: string, caseType: string) => {
    // Same logic as in component but returned as data
    const statusStr = String(status || "").toLowerCase()
    const typeStr = String(caseType || "").toLowerCase()
    
    // This would contain the same mapping logic
    // Simplified for brevity
    return {
      label: status,
      description: `${caseType} case status: ${status}`,
      color: "blue"
    }
  }

  return getCustomStatusLabel(status, caseType)
}
