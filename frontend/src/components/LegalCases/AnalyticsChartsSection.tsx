import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  SimpleGrid,
  Badge,
  Progress,

} from "@chakra-ui/react"
import {
  FiBarChart3,
  FiPieChart,
  FiTrendingUp,
  FiActivity,
  FiFileText,
  FiUsers,
} from "react-icons/fi"

import { 
  CaseAnalytics, 
  getActivityTypeLabel,
  getDocumentCategoryLabel 
} from "@/client/case-analytics"

interface AnalyticsChartsSectionProps {
  analytics: CaseAnalytics
}

const AnalyticsChartsSection = ({ analytics }: AnalyticsChartsSectionProps) => {
  // Prepare chart data
  const activityChartData = Object.entries(analytics.activity_analytics.activity_by_type)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 8)

  const documentChartData = Object.entries(analytics.document_analytics.category_distribution)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 6)

  const timelineData = analytics.activity_analytics.activity_timeline.slice(-14)

  const teamData = analytics.collaboration_analytics.activity_by_user
    .sort((a, b) => b.activity_count - a.activity_count)
    .slice(0, 6)

  const getActivityTypeColor = (activityType: string) => {
    const colors: Record<string, string> = {
      case_created: "green",
      case_updated: "blue",
      status_changed: "purple",
      document_uploaded: "orange",
      note_added: "teal",
      milestone_created: "pink",
      deadline_created: "red",
      user_assigned: "cyan",
    }
    return colors[activityType] || "gray"
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      evidence: "red",
      contracts: "blue",
      correspondence: "green",
      pleadings: "purple",
      discovery: "orange",
      research: "teal",
      other: "gray"
    }
    return colors[category] || "gray"
  }

  return (
    <VStack spacing={6} align="stretch">
      {/* Activity Distribution Chart */}
      <Card>
        <CardHeader>
          <HStack spacing={2}>
            <FiBarChart3 />
            <Heading size="md">Activity Distribution</Heading>
            <Badge variant="subtle" colorScheme="blue">
              {analytics.activity_analytics.total_activities} total activities
            </Badge>
          </HStack>
        </CardHeader>
        <CardBody>
          <VStack spacing={3} align="stretch">
            {activityChartData.map(([activityType, count]) => {
              const percentage = (count / analytics.activity_analytics.total_activities) * 100
              return (
                <Box key={activityType}>
                  <HStack justify="space-between" mb={2}>
                    <Text fontSize="sm" fontWeight="medium">
                      {getActivityTypeLabel(activityType)}
                    </Text>
                    <HStack spacing={3}>
                      <Text fontSize="sm" color="gray.600">
                        {count} ({percentage.toFixed(1)}%)
                      </Text>
                    </HStack>
                  </HStack>
                  <Progress 
                    value={percentage} 
                    colorScheme={getActivityTypeColor(activityType)} 
                    size="md" 
                    borderRadius="full"
                  />
                </Box>
              )
            })}
          </VStack>
        </CardBody>
      </Card>

      {/* Document Categories Chart */}
      <Card>
        <CardHeader>
          <HStack spacing={2}>
            <FiFileText />
            <Heading size="md">Document Categories</Heading>
            <Badge variant="subtle" colorScheme="green">
              {analytics.document_analytics.total_documents} documents
            </Badge>
          </HStack>
        </CardHeader>
        <CardBody>
          {documentChartData.length > 0 ? (
            <VStack spacing={3} align="stretch">
              {documentChartData.map(([category, count]) => {
                const percentage = (count / analytics.document_analytics.total_documents) * 100
                return (
                  <Box key={category}>
                    <HStack justify="space-between" mb={2}>
                      <Text fontSize="sm" fontWeight="medium">
                        {getDocumentCategoryLabel(category)}
                      </Text>
                      <HStack spacing={3}>
                        <Text fontSize="sm" color="gray.600">
                          {count} ({percentage.toFixed(1)}%)
                        </Text>
                      </HStack>
                    </HStack>
                    <Progress 
                      value={percentage} 
                      colorScheme={getCategoryColor(category)} 
                      size="md" 
                      borderRadius="full"
                    />
                  </Box>
                )
              })}
            </VStack>
          ) : (
            <Text color="gray.500" textAlign="center" py={8}>
              No documents uploaded yet
            </Text>
          )}
        </CardBody>
      </Card>

      {/* Activity Timeline Chart */}
      <Card>
        <CardHeader>
          <HStack spacing={2}>
            <FiTrendingUp />
            <Heading size="md">Activity Timeline</Heading>
            <Badge variant="subtle" colorScheme="purple">
              Last 14 days
            </Badge>
          </HStack>
        </CardHeader>
        <CardBody>
          {timelineData.length > 0 ? (
            <VStack spacing={2} align="stretch">
              {timelineData.map((day) => {
                const maxActivity = Math.max(...timelineData.map(d => d.activity_count))
                const percentage = maxActivity > 0 ? (day.activity_count / maxActivity) * 100 : 0
                
                return (
                  <HStack key={day.date} justify="space-between">
                    <Text fontSize="sm" width="100px">
                      {new Date(day.date).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric',
                        weekday: 'short'
                      })}
                    </Text>
                    <Box flex={1} mx={4}>
                      <Progress 
                        value={percentage} 
                        colorScheme="blue" 
                        size="md" 
                        borderRadius="full"
                      />
                    </Box>
                    <Text fontSize="sm" fontWeight="medium" width="40px" textAlign="right">
                      {day.activity_count}
                    </Text>
                  </HStack>
                )
              })}
            </VStack>
          ) : (
            <Text color="gray.500" textAlign="center" py={8}>
              No activity timeline data available
            </Text>
          )}
        </CardBody>
      </Card>

      {/* Team Activity Chart */}
      <Card>
        <CardHeader>
          <HStack spacing={2}>
            <FiUsers />
            <Heading size="md">Team Activity Distribution</Heading>
            <Badge variant="subtle" colorScheme="purple">
              {analytics.collaboration_analytics.team_size} team members
            </Badge>
          </HStack>
        </CardHeader>
        <CardBody>
          {teamData.length > 0 ? (
            <VStack spacing={3} align="stretch">
              {teamData.map((user) => {
                const totalTeamActivity = analytics.collaboration_analytics.activity_by_user
                  .reduce((sum, u) => sum + u.activity_count, 0)
                const percentage = totalTeamActivity > 0 
                  ? (user.activity_count / totalTeamActivity) * 100 
                  : 0
                
                return (
                  <Box key={user.user_id}>
                    <HStack justify="space-between" mb={2}>
                      <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                        {user.user_name}
                      </Text>
                      <HStack spacing={3}>
                        <Text fontSize="sm" color="gray.600">
                          {user.activity_count} ({percentage.toFixed(1)}%)
                        </Text>
                      </HStack>
                    </HStack>
                    <Progress 
                      value={percentage} 
                      colorScheme="purple" 
                      size="md" 
                      borderRadius="full"
                    />
                  </Box>
                )
              })}
            </VStack>
          ) : (
            <Text color="gray.500" textAlign="center" py={8}>
              No team activity data available
            </Text>
          )}
        </CardBody>
      </Card>

      {/* Progress & Performance Summary */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* Progress Summary */}
        <Card>
          <CardHeader>
            <HStack spacing={2}>
              <FiActivity />
              <Heading size="md">Progress Summary</Heading>
            </HStack>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Box>
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm">Milestone Completion</Text>
                  <Text fontSize="sm" fontWeight="bold" color="green.600">
                    {analytics.progress_analytics.milestone_completion_rate.toFixed(1)}%
                  </Text>
                </HStack>
                <Progress 
                  value={analytics.progress_analytics.milestone_completion_rate} 
                  colorScheme="green" 
                  size="md" 
                  borderRadius="full"
                />
              </Box>

              <Box>
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm">Overall Progress</Text>
                  <Text fontSize="sm" fontWeight="bold" color="blue.600">
                    {analytics.progress_analytics.progress_percentage.toFixed(1)}%
                  </Text>
                </HStack>
                <Progress 
                  value={analytics.progress_analytics.progress_percentage} 
                  colorScheme="blue" 
                  size="md" 
                  borderRadius="full"
                />
              </Box>

              <Box height="1px" bg="gray.200" my={2} />

              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Total Milestones:</Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {analytics.progress_analytics.total_milestones}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Completed:</Text>
                  <Text fontSize="sm" fontWeight="medium" color="green.600">
                    {analytics.progress_analytics.completed_milestones}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Overdue:</Text>
                  <Text fontSize="sm" fontWeight="medium" color="red.600">
                    {analytics.progress_analytics.overdue_milestones}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Upcoming Deadlines:</Text>
                  <Text fontSize="sm" fontWeight="medium" color="orange.600">
                    {analytics.progress_analytics.upcoming_deadlines}
                  </Text>
                </HStack>
              </VStack>
            </VStack>
          </CardBody>
        </Card>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <HStack spacing={2}>
              <FiTrendingUp />
              <Heading size="md">Performance Metrics</Heading>
            </HStack>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Box>
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm">Performance Score</Text>
                  <Text fontSize="sm" fontWeight="bold" color="purple.600">
                    {analytics.performance_metrics.performance_score}/100
                  </Text>
                </HStack>
                <Progress 
                  value={analytics.performance_metrics.performance_score} 
                  colorScheme="purple" 
                  size="md" 
                  borderRadius="full"
                />
              </Box>

              <Box>
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm">Health Score</Text>
                  <Text fontSize="sm" fontWeight="bold" color="teal.600">
                    {analytics.health_indicators.health_score}/100
                  </Text>
                </HStack>
                <Progress 
                  value={analytics.health_indicators.health_score} 
                  colorScheme="teal" 
                  size="md" 
                  borderRadius="full"
                />
              </Box>

              <Box height="1px" bg="gray.200" my={2} />

              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Activity Rate:</Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {analytics.performance_metrics.activity_frequency.toFixed(2)}/day
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Document Rate:</Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {analytics.performance_metrics.document_productivity.toFixed(2)}/day
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Response Time:</Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {analytics.performance_metrics.average_response_time_hours.toFixed(1)}h
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">Efficiency:</Text>
                  <Text fontSize="sm" fontWeight="medium" textTransform="capitalize">
                    {analytics.performance_metrics.efficiency_rating}
                  </Text>
                </HStack>
              </VStack>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>
    </VStack>
  )
}

export default AnalyticsChartsSection
