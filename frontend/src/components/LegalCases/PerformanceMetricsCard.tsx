import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Progress,
  Badge,




  CircularProgress,
  CircularProgressLabel,
  <PERSON>lt<PERSON>,

} from "@chakra-ui/react"
import {
  FiTrendingUp,
  FiActivity,
  FiFileText,
  FiClock,
  FiZap,
  FiTarget,
} from "react-icons/fi"

import { 
  PerformanceMetrics, 
  getEfficiencyRatingInfo, 
  getPerformanceColor,
  formatHours 
} from "@/client/case-analytics"

interface PerformanceMetricsCardProps {
  metrics: PerformanceMetrics
}

const PerformanceMetricsCard = ({ metrics }: PerformanceMetricsCardProps) => {
  const efficiencyInfo = getEfficiencyRatingInfo(metrics.efficiency_rating)
  const performanceColor = getPerformanceColor(metrics.performance_score)

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiTrendingUp />
          <Heading size="sm">Performance Metrics</Heading>
          <Badge 
            colorScheme={efficiencyInfo.color} 
            variant="subtle"
            fontSize="xs"
          >
            {efficiencyInfo.icon} {efficiencyInfo.label}
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Performance Score */}
          <Box textAlign="center">
            <CircularProgress 
              value={metrics.performance_score} 
              color={`${performanceColor}.400`}
              size="80px"
              thickness="8px"
            >
              <CircularProgressLabel fontSize="sm" fontWeight="bold">
                {metrics.performance_score}
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="sm" color="gray.600" mt={2}>
              Performance Score
            </Text>
            <Text fontSize="xs" color="gray.500">
              {efficiencyInfo.description}
            </Text>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Key Metrics */}
          <VStack spacing={3} align="stretch">
            {/* Activity Frequency */}
            <Box>
              <HStack justify="space-between" mb={1}>
                <Tooltip label="Average activities per day">
                  <HStack spacing={2}>
                    <FiActivity size={14} color="blue" />
                    <Text fontSize="sm">Activity Rate:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="blue.600">
                  {metrics.activity_frequency.toFixed(2)}/day
                </Text>
              </HStack>
              <Progress 
                value={Math.min(100, metrics.activity_frequency * 50)} 
                colorScheme="blue" 
                size="sm" 
                borderRadius="full"
              />
            </Box>

            {/* Document Productivity */}
            <Box>
              <HStack justify="space-between" mb={1}>
                <Tooltip label="Documents uploaded per day">
                  <HStack spacing={2}>
                    <FiFileText size={14} color="green" />
                    <Text fontSize="sm">Doc Rate:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="green.600">
                  {metrics.document_productivity.toFixed(2)}/day
                </Text>
              </HStack>
              <Progress 
                value={Math.min(100, metrics.document_productivity * 100)} 
                colorScheme="green" 
                size="sm" 
                borderRadius="full"
              />
            </Box>

            {/* Response Time */}
            <Box>
              <HStack justify="space-between" mb={1}>
                <Tooltip label="Average time between activities">
                  <HStack spacing={2}>
                    <FiClock size={14} color="orange" />
                    <Text fontSize="sm">Response Time:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="orange.600">
                  {formatHours(metrics.average_response_time_hours)}
                </Text>
              </HStack>
              <Progress 
                value={Math.max(0, 100 - (metrics.average_response_time_hours / 24) * 100)} 
                colorScheme="orange" 
                size="sm" 
                borderRadius="full"
              />
              <Text fontSize="xs" color="gray.500" mt={1}>
                Lower is better
              </Text>
            </Box>
          </VStack>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Additional Stats */}
          <VStack spacing={2} align="stretch">
            <HStack justify="space-between">
              <Tooltip label="Number of status changes">
                <HStack spacing={2}>
                  <FiZap size={14} color="purple" />
                  <Text fontSize="sm">Status Changes:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="purple.600">
                {metrics.status_changes}
              </Text>
            </HStack>

            <HStack justify="space-between">
              <Tooltip label="Case age in days">
                <HStack spacing={2}>
                  <FiTarget size={14} color="teal" />
                  <Text fontSize="sm">Case Age:</Text>
                </HStack>
              </Tooltip>
              <Text fontSize="sm" fontWeight="bold" color="teal.600">
                {metrics.case_age_days} days
              </Text>
            </HStack>
          </VStack>

          {/* Performance Insights */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Performance Insights:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {metrics.activity_frequency > 1 && (
                <Text fontSize="xs" color="green.600">
                  ✓ High activity frequency
                </Text>
              )}
              
              {metrics.activity_frequency < 0.5 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Low activity frequency
                </Text>
              )}
              
              {metrics.average_response_time_hours < 24 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Quick response time
                </Text>
              )}
              
              {metrics.average_response_time_hours > 72 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Slow response time
                </Text>
              )}
              
              {metrics.document_productivity > 0.3 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Good document productivity
                </Text>
              )}
              
              {metrics.document_productivity < 0.1 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Low document activity
                </Text>
              )}
              
              {metrics.performance_score >= 80 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Excellent overall performance
                </Text>
              )}
              
              {metrics.performance_score < 50 && (
                <Text fontSize="xs" color="red.600">
                  ⚠ Performance needs improvement
                </Text>
              )}
            </VStack>
          </Box>

          {/* Quick Stats Grid */}
          <HStack justify="space-around" bg="blue.50" p={2} borderRadius="md">
            <Box textAlign="center">
              <Box textAlign="center">
                {(metrics.activity_frequency * 7).toFixed(1)}
              </Text>
              <Box textAlign="center">Weekly Rate</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {metrics.efficiency_rating === "excellent" ? "A+" : 
                 metrics.efficiency_rating === "good" ? "B+" :
                 metrics.efficiency_rating === "average" ? "C" :
                 metrics.efficiency_rating === "below_average" ? "D" : "F"}
              </Text>
              <Box textAlign="center">Grade</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {metrics.performance_score >= 80 ? "🏆" :
                 metrics.performance_score >= 60 ? "🥈" :
                 metrics.performance_score >= 40 ? "🥉" : "📈"}
              </Text>
              <Box textAlign="center">Rank</Text>
            </Box>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default PerformanceMetricsCard
