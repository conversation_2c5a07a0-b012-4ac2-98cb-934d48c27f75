import {
  <PERSON>,
  <PERSON><PERSON><PERSON>ck,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>lex,
  <PERSON>r,
  <PERSON><PERSON>,
  IconButton,
  Tooltip,
  <PERSON>ert,
  <PERSON>ner,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Progress,
  Input,
  Select,
  InputGroup,
  InputLeftElement,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useDisclosure,

} from "@chakra-ui/react"
import {
  FiPlus,
  FiSearch,
  FiFilter,
  FiEdit,
  FiTrash2,
  FiCopy,
  FiPlay,
  FiFileText,
  FiSettings,
  FiTemplate,
  FiGitBranch,
} from "react-icons/fi"
import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseTemplatesService, 
  CaseTemplate, 
  DocumentTemplate, 
  WorkflowTemplate,
  getCaseTypeInfo,
  getDocumentTypeInfo,
  CASE_TYPE_CONFIG 
} from "@/client/case-templates"
import CreateCaseTemplateModal from "./CreateCaseTemplateModal"
import CreateDocumentTemplateModal from "./CreateDocumentTemplateModal"
import CreateWorkflowTemplateModal from "./CreateWorkflowTemplateModal"
import CaseTemplateCard from "./CaseTemplateCard"
import DocumentTemplateCard from "./DocumentTemplateCard"
import WorkflowTemplateCard from "./WorkflowTemplateCard"
import useCustomToast from "@/hooks/useCustomToast"

interface CaseTemplatesManagerProps {
  onTemplateApplied?: (templateId: string, caseId: string) => void
  onCaseCreated?: (caseId: string) => void
}

const CaseTemplatesManager = ({ 
  onTemplateApplied,
  onCaseCreated 
}: CaseTemplatesManagerProps) => {
  const [activeTab, setActiveTab] = useState(0)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCaseType, setSelectedCaseType] = useState<string>("")
  const [selectedDocumentType, setSelectedDocumentType] = useState<string>("")
  const [showPublicOnly, setShowPublicOnly] = useState(false)

  const toast = useCustomToast()
  const queryClient = useQueryClient()

  // Modal controls
  const { 
    isOpen: isCreateCaseTemplateOpen, 
    onOpen: onCreateCaseTemplateOpen, 
    onClose: onCreateCaseTemplateClose 
  } = useDisclosure()
  
  const { 
    isOpen: isCreateDocumentTemplateOpen, 
    onOpen: onCreateDocumentTemplateOpen, 
    onClose: onCreateDocumentTemplateClose 
  } = useDisclosure()
  
  const { 
    isOpen: isCreateWorkflowTemplateOpen, 
    onOpen: onCreateWorkflowTemplateOpen, 
    onClose: onCreateWorkflowTemplateClose 
  } = useDisclosure()

  // Fetch case templates
  const { 
    data: caseTemplates, 
    isLoading: isLoadingCaseTemplates, 
    error: caseTemplatesError 
  } = useQuery({
    queryKey: ["case-templates", searchQuery, selectedCaseType, showPublicOnly],
    queryFn: () => CaseTemplatesService.getCaseTemplates({
      search: searchQuery || undefined,
      case_type: selectedCaseType || undefined,
      is_public: showPublicOnly ? true : undefined,
      limit: 50
    }),
    staleTime: 5 * 60 * 1000,
  })

  // Fetch document templates
  const { 
    data: documentTemplates, 
    isLoading: isLoadingDocumentTemplates, 
    error: documentTemplatesError 
  } = useQuery({
    queryKey: ["document-templates", searchQuery, selectedCaseType, selectedDocumentType, showPublicOnly],
    queryFn: () => CaseTemplatesService.getDocumentTemplates({
      search: searchQuery || undefined,
      case_type: selectedCaseType || undefined,
      document_type: selectedDocumentType || undefined,
      is_public: showPublicOnly ? true : undefined,
      limit: 50
    }),
    staleTime: 5 * 60 * 1000,
  })

  // Fetch workflow templates
  const { 
    data: workflowTemplates, 
    isLoading: isLoadingWorkflowTemplates, 
    error: workflowTemplatesError 
  } = useQuery({
    queryKey: ["workflow-templates", searchQuery, selectedCaseType, showPublicOnly],
    queryFn: () => CaseTemplatesService.getWorkflowTemplates({
      search: searchQuery || undefined,
      case_type: selectedCaseType || undefined,
      is_public: showPublicOnly ? true : undefined,
      limit: 50
    }),
    staleTime: 5 * 60 * 1000,
  })

  // Delete mutations
  const deleteCaseTemplateMutation = useMutation({
    mutationFn: CaseTemplatesService.deleteCaseTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["case-templates"] })
      toast({
        title: "Template deleted",
        description: "Case template has been deleted successfully",
        status: "success",
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const deleteDocumentTemplateMutation = useMutation({
    mutationFn: CaseTemplatesService.deleteDocumentTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["document-templates"] })
      toast({
        title: "Template deleted",
        description: "Document template has been deleted successfully",
        status: "success",
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const deleteWorkflowTemplateMutation = useMutation({
    mutationFn: CaseTemplatesService.deleteWorkflowTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["workflow-templates"] })
      toast({
        title: "Template deleted",
        description: "Workflow template has been deleted successfully",
        status: "success",
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleDeleteCaseTemplate = (templateId: string) => {
    if (window.confirm("Are you sure you want to delete this template?")) {
      deleteCaseTemplateMutation.mutate(templateId)
    }
  }

  const handleDeleteDocumentTemplate = (templateId: string) => {
    if (window.confirm("Are you sure you want to delete this template?")) {
      deleteDocumentTemplateMutation.mutate(templateId)
    }
  }

  const handleDeleteWorkflowTemplate = (templateId: string) => {
    if (window.confirm("Are you sure you want to delete this template?")) {
      deleteWorkflowTemplateMutation.mutate(templateId)
    }
  }

  const handleClearFilters = () => {
    setSearchQuery("")
    setSelectedCaseType("")
    setSelectedDocumentType("")
    setShowPublicOnly(false)
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <HStack spacing={3}>
            <FiTemplate size={24} />
            <Heading size="lg">Templates & Automation</Heading>
            <Badge colorScheme="blue" variant="subtle">
              Case Management
            </Badge>
          </HStack>
          <Text color="gray.600">
            Manage case templates, document templates, and workflow automation
          </Text>
        </VStack>
        <Spacer />
        
        {/* Action buttons */}
        <HStack spacing={2}>
          <Button
            leftIcon={<FiFilter />}
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
          >
            Clear Filters
          </Button>
        </HStack>
      </Flex>

      {/* Search and Filters */}
      <Card mb={6}>
        <CardBody>
          <VStack spacing={4} align="stretch">
            <HStack spacing={4}>
              <InputGroup flex={1}>
                <InputLeftElement>
                  <FiSearch color="gray.400" />
                </InputLeftElement>
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </InputGroup>
              
              <Select
                placeholder="All case types"
                value={selectedCaseType}
                onChange={(e) => setSelectedCaseType(e.target.value)}
                width="200px"
              >
                {Object.entries(CASE_TYPE_CONFIG).map(([key, config]) => (
                  <option key={key} value={key}>
                    {config.icon} {config.label}
                  </option>
                ))}
              </Select>
              
              {activeTab === 1 && (
                <Select
                  placeholder="All document types"
                  value={selectedDocumentType}
                  onChange={(e) => setSelectedDocumentType(e.target.value)}
                  width="200px"
                >
                  <option value="contract">📄 Contract</option>
                  <option value="letter">✉️ Letter</option>
                  <option value="motion">⚖️ Motion</option>
                  <option value="brief">📝 Brief</option>
                  <option value="notice">📢 Notice</option>
                  <option value="agreement">🤝 Agreement</option>
                  <option value="petition">📋 Petition</option>
                  <option value="other">📄 Other</option>
                </Select>
              )}
              
              <Button
                variant={showPublicOnly ? "solid" : "outline"}
                colorScheme="blue"
                size="sm"
                onClick={() => setShowPublicOnly(!showPublicOnly)}
              >
                Public Only
              </Button>
            </HStack>
          </VStack>
        </CardBody>
      </Card>

      {/* Templates Tabs */}
      <Tabs index={activeTab} onChange={setActiveTab}>
        <TabList>
          <Tab>
            <HStack spacing={2}>
              <FiTemplate />
              <Text>Case Templates</Text>
              <Badge size="sm" colorScheme="blue">
                {caseTemplates?.count || 0}
              </Badge>
            </HStack>
          </Tab>
          <Tab>
            <HStack spacing={2}>
              <FiFileText />
              <Text>Document Templates</Text>
              <Badge size="sm" colorScheme="green">
                {documentTemplates?.count || 0}
              </Badge>
            </HStack>
          </Tab>
          <Tab>
            <HStack spacing={2}>
              <FiGitBranch />
              <Text>Workflow Templates</Text>
              <Badge size="sm" colorScheme="purple">
                {workflowTemplates?.count || 0}
              </Badge>
            </HStack>
          </Tab>
        </TabList>

        <TabPanels>
          {/* Case Templates */}
          <TabPanel px={0}>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="lg" fontWeight="medium">
                  Case Templates
                </Text>
                <Button
                  leftIcon={<FiPlus />}
                  colorScheme="blue"
                  onClick={onCreateCaseTemplateOpen}
                >
                  Create Case Template
                </Button>
              </HStack>

              {isLoadingCaseTemplates ? (
                <Box textAlign="center" py={8}>
                  <Spinner size="lg" />
                  <Text mt={2} color="gray.600">Loading case templates...</Text>
                </Box>
              ) : caseTemplatesError ? (
                <Alert status="error">
                  <Text>Failed to load case templates</Text>
                </Alert>
              ) : (
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                  {caseTemplates?.data.map((template) => (
                    <CaseTemplateCard
                      key={template.id}
                      template={template}
                      onEdit={() => {/* TODO: Implement edit */}}
                      onDelete={() => handleDeleteCaseTemplate(template.id)}
                      onApply={onTemplateApplied}
                      onCreateCase={onCaseCreated}
                    />
                  ))}
                </SimpleGrid>
              )}

              {caseTemplates?.data.length === 0 && (
                <Box textAlign="center" py={8}>
                  <Text color="gray.500">No case templates found</Text>
                  <Button
                    mt={2}
                    leftIcon={<FiPlus />}
                    colorScheme="blue"
                    variant="outline"
                    onClick={onCreateCaseTemplateOpen}
                  >
                    Create your first template
                  </Button>
                </Box>
              )}
            </VStack>
          </TabPanel>

          {/* Document Templates */}
          <TabPanel px={0}>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="lg" fontWeight="medium">
                  Document Templates
                </Text>
                <Button
                  leftIcon={<FiPlus />}
                  colorScheme="green"
                  onClick={onCreateDocumentTemplateOpen}
                >
                  Create Document Template
                </Button>
              </HStack>

              {isLoadingDocumentTemplates ? (
                <Box textAlign="center" py={8}>
                  <Spinner size="lg" />
                  <Text mt={2} color="gray.600">Loading document templates...</Text>
                </Box>
              ) : documentTemplatesError ? (
                <Alert status="error">
                  <Text>Failed to load document templates</Text>
                </Alert>
              ) : (
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                  {documentTemplates?.data.map((template) => (
                    <DocumentTemplateCard
                      key={template.id}
                      template={template}
                      onEdit={() => {/* TODO: Implement edit */}}
                      onDelete={() => handleDeleteDocumentTemplate(template.id)}
                      onGenerate={() => {/* TODO: Implement generate */}}
                    />
                  ))}
                </SimpleGrid>
              )}

              {documentTemplates?.data.length === 0 && (
                <Box textAlign="center" py={8}>
                  <Text color="gray.500">No document templates found</Text>
                  <Button
                    mt={2}
                    leftIcon={<FiPlus />}
                    colorScheme="green"
                    variant="outline"
                    onClick={onCreateDocumentTemplateOpen}
                  >
                    Create your first template
                  </Button>
                </Box>
              )}
            </VStack>
          </TabPanel>

          {/* Workflow Templates */}
          <TabPanel px={0}>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Text fontSize="lg" fontWeight="medium">
                  Workflow Templates
                </Text>
                <Button
                  leftIcon={<FiPlus />}
                  colorScheme="purple"
                  onClick={onCreateWorkflowTemplateOpen}
                >
                  Create Workflow Template
                </Button>
              </HStack>

              {isLoadingWorkflowTemplates ? (
                <Box textAlign="center" py={8}>
                  <Spinner size="lg" />
                  <Text mt={2} color="gray.600">Loading workflow templates...</Text>
                </Box>
              ) : workflowTemplatesError ? (
                <Alert status="error">
                  <Text>Failed to load workflow templates</Text>
                </Alert>
              ) : (
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                  {workflowTemplates?.data.map((template) => (
                    <WorkflowTemplateCard
                      key={template.id}
                      template={template}
                      onEdit={() => {/* TODO: Implement edit */}}
                      onDelete={() => handleDeleteWorkflowTemplate(template.id)}
                      onApply={() => {/* TODO: Implement apply */}}
                    />
                  ))}
                </SimpleGrid>
              )}

              {workflowTemplates?.data.length === 0 && (
                <Box textAlign="center" py={8}>
                  <Text color="gray.500">No workflow templates found</Text>
                  <Button
                    mt={2}
                    leftIcon={<FiPlus />}
                    colorScheme="purple"
                    variant="outline"
                    onClick={onCreateWorkflowTemplateOpen}
                  >
                    Create your first template
                  </Button>
                </Box>
              )}
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Modals */}
      <CreateCaseTemplateModal
        isOpen={isCreateCaseTemplateOpen}
        onClose={onCreateCaseTemplateClose}
      />
      
      <CreateDocumentTemplateModal
        isOpen={isCreateDocumentTemplateOpen}
        onClose={onCreateDocumentTemplateClose}
      />
      
      <CreateWorkflowTemplateModal
        isOpen={isCreateWorkflowTemplateOpen}
        onClose={onCreateWorkflowTemplateClose}
      />
    </Box>
  )
}

export default CaseTemplatesManager
