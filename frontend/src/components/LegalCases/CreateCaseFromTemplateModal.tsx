import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Text,
  Box,
  Badge,

} from "@chakra-ui/react"
import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseTemplate,
  CaseTemplatesService,
  getCaseTypeInfo 
} from "@/client/case-templates"
import useCustomToast from "@/hooks/useCustomToast"

interface CreateCaseFromTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  template: CaseTemplate
  onCaseCreated?: (caseId: string) => void
}

const CreateCaseFromTemplateModal = ({ 
  isOpen, 
  onClose, 
  template,
  onCaseCreated 
}: CreateCaseFromTemplateModalProps) => {
  const [formData, setFormData] = useState({
    title: "",
    client_name: "",
    description: ""
  })

  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const caseTypeInfo = getCaseTypeInfo(template.case_type)

  // Create case from template mutation
  const createCaseMutation = useMutation({
    mutationFn: (caseData: Record<string, any>) =>
      CaseTemplatesService.createCaseFromTemplate(template.id, caseData),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
      
      toast({
        title: "Case created successfully",
        description: `Created "${result.case_title}" with template items applied`,
        status: "success",
        duration: 5000,
      })
      
      onCaseCreated?.(result.case_id)
      handleClose()
    },
    onError: (error: any) => {
      toast({
        title: "Error creating case",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleClose = () => {
    setFormData({
      title: "",
      client_name: "",
      description: ""
    })
    onClose()
  }

  const handleSubmit = () => {
    if (!formData.title.trim() || !formData.client_name.trim()) {
      toast({
        title: "Missing required fields",
        description: "Please fill in the case title and client name",
        status: "warning",
        duration: 3000,
      })
      return
    }

    createCaseMutation.mutate(formData)
  }

  const getTemplateStats = () => {
    const data = template.template_data
    const milestones = data.milestones?.length || 0
    const deadlines = data.deadlines?.length || 0
    const documents = data.documents?.length || 0
    
    return { milestones, deadlines, documents }
  }

  const stats = getTemplateStats()

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Create Case from Template</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Template Info */}
            <Box p={4} bg="blue.50" borderRadius="md">
              <VStack spacing={3} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="lg" fontWeight="bold">
                    {template.name}
                  </Text>
                  <Badge colorScheme={caseTypeInfo.color} variant="subtle">
                    {caseTypeInfo.icon} {caseTypeInfo.label}
                  </Badge>
                </HStack>
                
                {template.description && (
                  <Text fontSize="sm" color="gray.600">
                    {template.description}
                  </Text>
                )}
                
                <HStack justify="space-around" bg="white" p={3} borderRadius="md">
                  <VStack spacing={1}>
                    <Text fontSize="lg" fontWeight="bold" color="blue.600">
                      {stats.milestones}
                    </Text>
                    <Text fontSize="xs" color="gray.600">Milestones</Text>
                  </VStack>
                  
                  <VStack spacing={1}>
                    <Text fontSize="lg" fontWeight="bold" color="orange.600">
                      {stats.deadlines}
                    </Text>
                    <Text fontSize="xs" color="gray.600">Deadlines</Text>
                  </VStack>
                  
                  <VStack spacing={1}>
                    <Text fontSize="lg" fontWeight="bold" color="green.600">
                      {stats.documents}
                    </Text>
                    <Text fontSize="xs" color="gray.600">Documents</Text>
                  </VStack>
                </HStack>
              </VStack>
            </Box>

            {/* Case Form */}
            <VStack spacing={4} align="stretch">
              <Text fontSize="md" fontWeight="medium">
                New Case Information
              </Text>
              
              <FormControl isRequired>
                <FormLabel>Case Title</FormLabel>
                <Input
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder={`Enter ${caseTypeInfo.label.toLowerCase()} case title`}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Client Name</FormLabel>
                <Input
                  value={formData.client_name}
                  onChange={(e) => setFormData({ ...formData, client_name: e.target.value })}
                  placeholder="Enter client name"
                />
              </FormControl>

              <FormControl>
                <FormLabel>Description</FormLabel>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter case description (optional)"
                  rows={3}
                />
              </FormControl>
            </VStack>

            {/* Case Type Info */}
            <Box p={4} bg="gray.50" borderRadius="md">
              <VStack spacing={2} align="stretch">
                <Text fontSize="sm" fontWeight="medium" color="gray.700">
                  Case Type: {caseTypeInfo.label}
                </Text>
                <Text fontSize="sm" color="gray.600">
                  {caseTypeInfo.description}
                </Text>
              </VStack>
            </Box>

            {/* What will be created */}
            <Box p={4} bg="green.50" borderRadius="md">
              <VStack spacing={2} align="stretch">
                <Text fontSize="sm" fontWeight="medium" color="green.800">
                  What will be created:
                </Text>
                <VStack align="start" spacing={1} fontSize="sm" color="green.700">
                  <Text>✓ New {caseTypeInfo.label.toLowerCase()} case</Text>
                  {stats.milestones > 0 && (
                    <Text>✓ {stats.milestones} milestone{stats.milestones !== 1 ? 's' : ''}</Text>
                  )}
                  {stats.deadlines > 0 && (
                    <Text>✓ {stats.deadlines} deadline{stats.deadlines !== 1 ? 's' : ''}</Text>
                  )}
                  {stats.documents > 0 && (
                    <Text>✓ {stats.documents} document template{stats.documents !== 1 ? 's' : ''}</Text>
                  )}
                  <Text>✓ Case activity log entry</Text>
                </VStack>
              </VStack>
            </Box>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={createCaseMutation.isPending}
            isDisabled={!formData.title.trim() || !formData.client_name.trim()}
          >
            Create Case
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default CreateCaseFromTemplateModal
