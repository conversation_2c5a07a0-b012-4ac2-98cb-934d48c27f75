/**
=========================================================
* EquiNova Material Priority Badge
=========================================================

* Material-UI implementation of priority badges
* Replaces Chakra UI priority indicators with Material Design

=========================================================
*/

import React from 'react';
import {
  Chip,
  Box,
  Tooltip,
  useTheme,
  keyframes,
} from '@mui/material';
import {
  KeyboardArrowDown as LowIcon,
  Remove as MediumIcon,
  KeyboardArrowUp as HighIcon,
  PriorityHigh as UrgentIcon,
} from '@mui/icons-material';

interface MaterialPriorityBadgeProps {
  priority: string;
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
  animated?: boolean;
  variant?: 'filled' | 'outlined';
}

// Pulse animation for urgent priority
const pulseAnimation = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

const MaterialPriorityBadge: React.FC<MaterialPriorityBadgeProps> = ({
  priority,
  size = 'medium',
  showIcon = true,
  animated = false,
  variant = 'filled',
}) => {
  const theme = useTheme();

  const getPriorityConfig = (priority: string) => {
    const priorityStr = String(priority || '').toLowerCase();
    
    switch (priorityStr) {
      case 'low':
        return {
          color: 'success' as const,
          icon: LowIcon,
          label: 'Low',
          description: 'Low priority case',
          bgColor: theme.palette.success.light,
          textColor: theme.palette.success.contrastText,
          pulse: false,
        };
      case 'medium':
        return {
          color: 'info' as const,
          icon: MediumIcon,
          label: 'Medium',
          description: 'Medium priority case',
          bgColor: theme.palette.info.light,
          textColor: theme.palette.info.contrastText,
          pulse: false,
        };
      case 'high':
        return {
          color: 'warning' as const,
          icon: HighIcon,
          label: 'High',
          description: 'High priority case - requires attention',
          bgColor: theme.palette.warning.light,
          textColor: theme.palette.warning.contrastText,
          pulse: false,
        };
      case 'urgent':
        return {
          color: 'error' as const,
          icon: UrgentIcon,
          label: 'Urgent',
          description: 'Urgent priority case - immediate attention required',
          bgColor: theme.palette.error.light,
          textColor: theme.palette.error.contrastText,
          pulse: true,
        };
      default:
        return {
          color: 'default' as const,
          icon: MediumIcon,
          label: priorityStr ? priorityStr.charAt(0).toUpperCase() + priorityStr.slice(1) : 'Unknown',
          description: 'Unknown priority',
          bgColor: theme.palette.grey[300],
          textColor: theme.palette.text.primary,
          pulse: false,
        };
    }
  };

  const priorityConfig = getPriorityConfig(priority);
  const IconComponent = priorityConfig.icon;

  const chipSize = size === 'small' ? 'small' : 'medium';
  const iconSize = size === 'small' ? 14 : size === 'medium' ? 16 : 20;

  return (
    <Tooltip title={priorityConfig.description} arrow>
      <Box
        sx={{
          display: 'inline-flex',
          animation: animated && priorityConfig.pulse ? `${pulseAnimation} 2s infinite` : 'none',
        }}
      >
        <Chip
          icon={showIcon ? <IconComponent sx={{ fontSize: iconSize }} /> : undefined}
          label={priorityConfig.label}
          color={priorityConfig.color}
          variant={variant}
          size={chipSize}
          sx={{
            fontWeight: 500,
            transition: animated ? 'all 0.2s ease-in-out' : 'none',
            '&:hover': animated ? {
              transform: 'scale(1.05)',
              boxShadow: theme.shadows[4],
            } : {},
            // Custom styling for urgent priority
            ...(priorityConfig.pulse && {
              '&.MuiChip-colorError': {
                backgroundColor: theme.palette.error.main,
                color: theme.palette.error.contrastText,
                '&:hover': {
                  backgroundColor: theme.palette.error.dark,
                },
              },
            }),
          }}
        />
      </Box>
    </Tooltip>
  );
};

export default MaterialPriorityBadge;
