import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Button,
  Badge,
  IconButton,
  Tooltip,
  Card,
  CardBody,
  CardHeader,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,

} from "@chakra-ui/react"
import {
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  FiCopy,
  FiPlay,
  FiGitBranch,
  FiEye,
  FiShare2,
  FiDownload,
  FiSettings,
  FiZap,
} from "react-icons/fi"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  WorkflowTemplate, 
  CaseTemplatesService,
  getCaseTypeInfo 
} from "@/client/case-templates"
import useCustomToast from "@/hooks/useCustomToast"

interface WorkflowTemplateCardProps {
  template: WorkflowTemplate
  onEdit: (template: WorkflowTemplate) => void
  onDelete: (templateId: string) => void
  onApply?: (templateId: string) => void
}

const WorkflowTemplateCard = ({ 
  template, 
  onEdit, 
  onDelete, 
  onApply 
}: WorkflowTemplateCardProps) => {
  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const caseTypeInfo = getCaseTypeInfo(template.case_type)

  // Duplicate template mutation
  const duplicateTemplateMutation = useMutation({
    mutationFn: async (template: WorkflowTemplate) => {
      const duplicateData = {
        name: `${template.name} (Copy)`,
        description: template.description,
        case_type: template.case_type,
        workflow_steps: template.workflow_steps,
        automation_rules: template.automation_rules,
        is_active: true,
        is_public: false,
        tags: template.tags
      }
      return CaseTemplatesService.createWorkflowTemplate(duplicateData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["workflow-templates"] })
      toast({
        title: "Template duplicated",
        description: "Workflow template has been duplicated successfully",
        status: "success",
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error duplicating template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleDuplicate = () => {
    duplicateTemplateMutation.mutate(template)
  }

  const handleExport = () => {
    const exportData = {
      name: template.name,
      description: template.description,
      case_type: template.case_type,
      workflow_steps: template.workflow_steps,
      automation_rules: template.automation_rules,
      tags: template.tags,
      exported_at: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json"
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `workflow-template-${template.name.toLowerCase().replace(/\s+/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Template exported",
      description: "Workflow template has been exported successfully",
      status: "success",
      duration: 3000,
    })
  }

  const getWorkflowStats = () => {
    const steps = template.workflow_steps.length
    const automationRules = template.automation_rules.length
    const complexity = steps + automationRules
    
    return { steps, automationRules, complexity }
  }

  const getComplexityLevel = (complexity: number) => {
    if (complexity <= 3) return { label: "Simple", color: "green" }
    if (complexity <= 7) return { label: "Medium", color: "yellow" }
    return { label: "Complex", color: "red" }
  }

  const stats = getWorkflowStats()
  const complexityInfo = getComplexityLevel(stats.complexity)

  return (
    <Card 
      variant="outline" 
      _hover={{ shadow: "md", borderColor: "purple.200" }}
      transition="all 0.2s"
    >
      <CardHeader pb={2}>
        <HStack justify="space-between" align="start">
          <VStack align="start" spacing={1} flex={1}>
            <HStack spacing={2}>
              <Text fontSize="lg" fontWeight="bold" noOfLines={1}>
                {template.name}
              </Text>
              {template.is_public && (
                <Badge colorScheme="green" size="sm">
                  Public
                </Badge>
              )}
            </HStack>
            
            <HStack spacing={2} flexWrap="wrap">
              <Badge 
                colorScheme={caseTypeInfo.color} 
                variant="subtle"
                fontSize="xs"
              >
                {caseTypeInfo.icon} {caseTypeInfo.label}
              </Badge>
              
              <Badge 
                colorScheme={complexityInfo.color} 
                variant="outline"
                fontSize="xs"
              >
                {complexityInfo.label}
              </Badge>
              
              {template.tags.slice(0, 2).map((tag) => (
                <Badge 
                  key={tag} 
                  variant="outline" 
                  colorScheme="gray"
                  fontSize="xs"
                >
                  {tag}
                </Badge>
              ))}
              
              {template.tags.length > 2 && (
                <Badge variant="outline" colorScheme="gray" fontSize="xs">
                  +{template.tags.length - 2}
                </Badge>
              )}
            </HStack>
          </VStack>
          
          <Menu>
            <MenuButton
              as={IconButton}
              icon={<FiMoreVertical />}
              variant="ghost"
              size="sm"
            />
            <MenuList>
              <MenuItem icon={<FiEye />} onClick={() => {/* TODO: View workflow */}}>
                View Workflow
              </MenuItem>
              <MenuItem icon={<FiEdit />} onClick={() => onEdit(template)}>
                Edit Template
              </MenuItem>
              <MenuItem icon={<FiCopy />} onClick={handleDuplicate}>
                Duplicate
              </MenuItem>
              <MenuItem icon={<FiSettings />} onClick={() => {/* TODO: Configure */}}>
                Configure Rules
              </MenuItem>
              <MenuItem icon={<FiDownload />} onClick={handleExport}>
                Export
              </MenuItem>
              <MenuItem icon={<FiShare2 />} onClick={() => {/* TODO: Share */}}>
                Share
              </MenuItem>
              <Box height="1px" bg="gray.200" my={1} />
              <MenuItem 
                icon={<FiTrash2 />} 
                onClick={() => onDelete(template.id)}
                color="red.600"
              >
                Delete
              </MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Description */}
          {template.description && (
            <Text fontSize="sm" color="gray.600" noOfLines={2}>
              {template.description}
            </Text>
          )}
          
          {/* Workflow Stats */}
          <HStack justify="space-around" bg="gray.50" p={3} borderRadius="md">
            <VStack spacing={1}>
              <Text fontSize="lg" fontWeight="bold" color="blue.600">
                {stats.steps}
              </Text>
              <Text fontSize="xs" color="gray.600">Steps</Text>
            </VStack>
            
            <VStack spacing={1}>
              <Text fontSize="lg" fontWeight="bold" color="orange.600">
                {stats.automationRules}
              </Text>
              <Text fontSize="xs" color="gray.600">Rules</Text>
            </VStack>
            
            <VStack spacing={1}>
              <Text fontSize="lg" fontWeight="bold" color={`${complexityInfo.color}.600`}>
                {complexityInfo.label}
              </Text>
              <Text fontSize="xs" color="gray.600">Complexity</Text>
            </VStack>
          </HStack>
          
          {/* Workflow Steps Preview */}
          {template.workflow_steps.length > 0 && (
            <Box>
              <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={2}>
                Workflow Steps:
              </Text>
              <VStack spacing={1} align="stretch">
                {template.workflow_steps.slice(0, 3).map((step, index) => (
                  <HStack key={index} spacing={2} fontSize="xs">
                    <Badge colorScheme="purple" variant="solid" minW="20px" textAlign="center">
                      {index + 1}
                    </Badge>
                    <Text color="gray.600" noOfLines={1}>
                      {step.name || step.title || `Step ${index + 1}`}
                    </Text>
                  </HStack>
                ))}
                {template.workflow_steps.length > 3 && (
                  <HStack spacing={2} fontSize="xs">
                    <Badge variant="outline" colorScheme="purple" minW="20px" textAlign="center">
                      ...
                    </Badge>
                    <Text color="gray.500">
                      +{template.workflow_steps.length - 3} more steps
                    </Text>
                  </HStack>
                )}
              </VStack>
            </Box>
          )}
          
          {/* Automation Rules Preview */}
          {template.automation_rules.length > 0 && (
            <Box>
              <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={2}>
                Automation Rules:
              </Text>
              <VStack spacing={1} align="stretch">
                {template.automation_rules.slice(0, 2).map((rule, index) => (
                  <HStack key={index} spacing={2} fontSize="xs">
                    <FiZap color="orange" />
                    <Text color="gray.600" noOfLines={1}>
                      {rule.name || rule.trigger || `Rule ${index + 1}`}
                    </Text>
                  </HStack>
                ))}
                {template.automation_rules.length > 2 && (
                  <HStack spacing={2} fontSize="xs">
                    <FiZap color="gray" />
                    <Text color="gray.500">
                      +{template.automation_rules.length - 2} more rules
                    </Text>
                  </HStack>
                )}
              </VStack>
            </Box>
          )}
          
          {/* Creator Info */}
          <HStack justify="space-between" fontSize="xs" color="gray.500">
            <Text>
              By {template.creator?.full_name || "Unknown"}
            </Text>
            <Text>
              {new Date(template.created_at).toLocaleDateString()}
            </Text>
          </HStack>
          
          {/* Action Buttons */}
          <VStack spacing={2} align="stretch">
            <Button
              leftIcon={<FiPlay />}
              colorScheme="purple"
              size="sm"
              onClick={() => onApply?.(template.id)}
              isDisabled={template.workflow_steps.length === 0}
            >
              Apply Workflow
            </Button>
            
            <Button
              leftIcon={<FiGitBranch />}
              variant="outline"
              colorScheme="purple"
              size="sm"
              onClick={() => {/* TODO: Visualize workflow */}}
            >
              Visualize Workflow
            </Button>
          </VStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default WorkflowTemplateCard
