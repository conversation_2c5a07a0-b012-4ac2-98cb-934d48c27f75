import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>ge,
  IconButton,
  Tooltip,
  Card,
  CardBody,
  CardHeader,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,

  useDisclosure,
} from "@chakra-ui/react"
import {
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  FiCopy,
  FiPlay,
  FiPlus,
  FiEye,
  FiShare2,
  FiDownload,
} from "react-icons/fi"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { 
  CaseTemplate, 
  CaseTemplatesService,
  getCaseTypeInfo 
} from "@/client/case-templates"
import ApplyCaseTemplateModal from "./ApplyCaseTemplateModal"
import CreateCaseFromTemplateModal from "./CreateCaseFromTemplateModal"
import useCustomToast from "@/hooks/useCustomToast"

interface CaseTemplateCardProps {
  template: CaseTemplate
  onEdit: (template: CaseTemplate) => void
  onDelete: (templateId: string) => void
  onApply?: (templateId: string, caseId: string) => void
  onCreateCase?: (caseId: string) => void
}

const CaseTemplateCard = ({ 
  template, 
  onEdit, 
  onDelete, 
  onApply,
  onCreateCase 
}: CaseTemplateCardProps) => {
  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const caseTypeInfo = getCaseTypeInfo(template.case_type)

  const { 
    isOpen: isApplyModalOpen, 
    onOpen: onApplyModalOpen, 
    onClose: onApplyModalClose 
  } = useDisclosure()
  
  const { 
    isOpen: isCreateCaseModalOpen, 
    onOpen: onCreateCaseModalOpen, 
    onClose: onCreateCaseModalClose 
  } = useDisclosure()

  // Duplicate template mutation
  const duplicateTemplateMutation = useMutation({
    mutationFn: async (template: CaseTemplate) => {
      const duplicateData = {
        name: `${template.name} (Copy)`,
        description: template.description,
        case_type: template.case_type,
        template_data: template.template_data,
        is_active: true,
        is_public: false,
        tags: template.tags
      }
      return CaseTemplatesService.createCaseTemplate(duplicateData)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["case-templates"] })
      toast({
        title: "Template duplicated",
        description: "Template has been duplicated successfully",
        status: "success",
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error duplicating template",
        description: error.response?.data?.detail || "An error occurred",
        status: "error",
        duration: 5000,
      })
    },
  })

  const handleDuplicate = () => {
    duplicateTemplateMutation.mutate(template)
  }

  const handleExport = () => {
    const exportData = {
      name: template.name,
      description: template.description,
      case_type: template.case_type,
      template_data: template.template_data,
      tags: template.tags,
      exported_at: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json"
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `case-template-${template.name.toLowerCase().replace(/\s+/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Template exported",
      description: "Template has been exported successfully",
      status: "success",
      duration: 3000,
    })
  }

  const getTemplateStats = () => {
    const data = template.template_data
    const milestones = data.milestones?.length || 0
    const deadlines = data.deadlines?.length || 0
    const documents = data.documents?.length || 0
    
    return { milestones, deadlines, documents }
  }

  const stats = getTemplateStats()

  return (
    <>
      <Card 
        variant="outline" 
        _hover={{ shadow: "md", borderColor: "blue.200" }}
        transition="all 0.2s"
      >
        <CardHeader pb={2}>
          <HStack justify="space-between" align="start">
            <VStack align="start" spacing={1} flex={1}>
              <HStack spacing={2}>
                <Text fontSize="lg" fontWeight="bold" noOfLines={1}>
                  {template.name}
                </Text>
                {template.is_public && (
                  <Badge colorScheme="green" size="sm">
                    Public
                  </Badge>
                )}
              </HStack>
              
              <HStack spacing={2}>
                <Badge 
                  colorScheme={caseTypeInfo.color} 
                  variant="subtle"
                  fontSize="xs"
                >
                  {caseTypeInfo.icon} {caseTypeInfo.label}
                </Badge>
                
                {template.tags.map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    colorScheme="gray"
                    fontSize="xs"
                  >
                    {tag}
                  </Badge>
                ))}
              </HStack>
            </VStack>
            
            <Menu>
              <MenuButton
                as={IconButton}
                icon={<FiMoreVertical />}
                variant="ghost"
                size="sm"
              />
              <MenuList>
                <MenuItem icon={<FiEye />} onClick={() => {/* TODO: View details */}}>
                  View Details
                </MenuItem>
                <MenuItem icon={<FiEdit />} onClick={() => onEdit(template)}>
                  Edit Template
                </MenuItem>
                <MenuItem icon={<FiCopy />} onClick={handleDuplicate}>
                  Duplicate
                </MenuItem>
                <MenuItem icon={<FiDownload />} onClick={handleExport}>
                  Export
                </MenuItem>
                <MenuItem icon={<FiShare2 />} onClick={() => {/* TODO: Share */}}>
                  Share
                </MenuItem>
                <Box height="1px" bg="gray.200" my={1} />
                <MenuItem 
                  icon={<FiTrash2 />} 
                  onClick={() => onDelete(template.id)}
                  color="red.600"
                >
                  Delete
                </MenuItem>
              </MenuList>
            </Menu>
          </HStack>
        </CardHeader>
        
        <CardBody pt={2}>
          <VStack spacing={4} align="stretch">
            {/* Description */}
            {template.description && (
              <Text fontSize="sm" color="gray.600" noOfLines={2}>
                {template.description}
              </Text>
            )}
            
            {/* Template Stats */}
            <HStack justify="space-around" bg="gray.50" p={3} borderRadius="md">
              <VStack spacing={1}>
                <Text fontSize="lg" fontWeight="bold" color="blue.600">
                  {stats.milestones}
                </Text>
                <Text fontSize="xs" color="gray.600">Milestones</Text>
              </VStack>
              
              <VStack spacing={1}>
                <Text fontSize="lg" fontWeight="bold" color="orange.600">
                  {stats.deadlines}
                </Text>
                <Text fontSize="xs" color="gray.600">Deadlines</Text>
              </VStack>
              
              <VStack spacing={1}>
                <Text fontSize="lg" fontWeight="bold" color="green.600">
                  {stats.documents}
                </Text>
                <Text fontSize="xs" color="gray.600">Documents</Text>
              </VStack>
            </HStack>
            
            {/* Creator Info */}
            <HStack justify="space-between" fontSize="xs" color="gray.500">
              <Text>
                By {template.creator?.full_name || "Unknown"}
              </Text>
              <Text>
                {new Date(template.created_at).toLocaleDateString()}
              </Text>
            </HStack>
            
            {/* Action Buttons */}
            <VStack spacing={2} align="stretch">
              <Button
                leftIcon={<FiPlus />}
                colorScheme="blue"
                size="sm"
                onClick={onCreateCaseModalOpen}
              >
                Create New Case
              </Button>
              
              <Button
                leftIcon={<FiPlay />}
                variant="outline"
                colorScheme="blue"
                size="sm"
                onClick={onApplyModalOpen}
              >
                Apply to Existing Case
              </Button>
            </VStack>
          </VStack>
        </CardBody>
      </Card>

      {/* Apply Template Modal */}
      <ApplyCaseTemplateModal
        isOpen={isApplyModalOpen}
        onClose={onApplyModalClose}
        template={template}
        onApply={onApply}
      />
      
      {/* Create Case Modal */}
      <CreateCaseFromTemplateModal
        isOpen={isCreateCaseModalOpen}
        onClose={onCreateCaseModalClose}
        template={template}
        onCaseCreated={onCreateCase}
      />
    </>
  )
}

export default CaseTemplateCard
