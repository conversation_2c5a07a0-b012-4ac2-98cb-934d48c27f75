import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  Progress,





  Tooltip,
  SimpleGrid,
} from "@chakra-ui/react"
import {
  FiFileText,
  FiHardDrive,
  FiUpload,
  FiLink,
  FiTrendingUp,
  FiFolder,
} from "react-icons/fi"

import { 
  DocumentAnalytics, 
  formatFileSize,
  getDocumentCategoryLabel 
} from "@/client/case-analytics"

interface DocumentAnalyticsCardProps {
  analytics: DocumentAnalytics
}

const DocumentAnalyticsCard = ({ analytics }: DocumentAnalyticsCardProps) => {
  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      evidence: "red",
      contracts: "blue",
      correspondence: "green",
      pleadings: "purple",
      discovery: "orange",
      research: "teal",
      other: "gray"
    }
    return colors[category] || "gray"
  }

  const getUploadVelocityColor = (velocity: number) => {
    if (velocity >= 1) return "green"
    if (velocity >= 0.5) return "blue"
    if (velocity >= 0.2) return "yellow"
    return "orange"
  }

  const getUploadVelocityLabel = (velocity: number) => {
    if (velocity >= 1) return "High"
    if (velocity >= 0.5) return "Good"
    if (velocity >= 0.2) return "Moderate"
    return "Low"
  }

  // Sort categories by count
  const sortedCategories = Object.entries(analytics.category_distribution)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 6)

  // Get recent upload timeline
  const recentUploads = analytics.upload_timeline.slice(-7)

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiFileText />
          <Heading size="sm">Document Analytics</Heading>
          <Badge 
            colorScheme={getUploadVelocityColor(analytics.upload_velocity)} 
            variant="subtle"
            fontSize="xs"
          >
            {getUploadVelocityLabel(analytics.upload_velocity)} Upload Rate
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Document Summary */}
          <SimpleGrid columns={2} spacing={4}>
            <Box textAlign="center">
              <Box textAlign="center">Total Documents</Text>
              <Box textAlign="center">
                {analytics.total_documents}
              </Text>
              <Box textAlign="center">
                Files uploaded
              </Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">Total Size</Text>
              <Box textAlign="center">
                {formatFileSize(analytics.total_size_bytes)}
              </Text>
              <Box textAlign="center">
                Storage used
              </Text>
            </Box>
          </SimpleGrid>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Storage Analytics */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Storage Analytics
            </Text>
            
            <VStack spacing={2} align="stretch">
              <HStack justify="space-between">
                <Tooltip label="Average file size">
                  <HStack spacing={2}>
                    <FiHardDrive size={14} color="blue" />
                    <Text fontSize="sm">Avg File Size:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="blue.600">
                  {formatFileSize(analytics.average_size_bytes)}
                </Text>
              </HStack>

              <HStack justify="space-between">
                <Tooltip label="Largest file uploaded">
                  <HStack spacing={2}>
                    <FiUpload size={14} color="orange" />
                    <Text fontSize="sm">Largest File:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="orange.600">
                  {formatFileSize(analytics.largest_file_bytes)}
                </Text>
              </HStack>

              <HStack justify="space-between">
                <Tooltip label="Document links to other cases">
                  <HStack spacing={2}>
                    <FiLink size={14} color="purple" />
                    <Text fontSize="sm">Total Links:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="purple.600">
                  {analytics.total_links}
                </Text>
              </HStack>

              <HStack justify="space-between">
                <Tooltip label="Documents uploaded per day">
                  <HStack spacing={2}>
                    <FiTrendingUp size={14} color="green" />
                    <Text fontSize="sm">Upload Rate:</Text>
                  </HStack>
                </Tooltip>
                <Text fontSize="sm" fontWeight="bold" color="green.600">
                  {analytics.upload_velocity.toFixed(2)}/day
                </Text>
              </HStack>
            </VStack>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Category Distribution */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Document Categories
            </Text>
            
            {sortedCategories.length > 0 ? (
              <VStack spacing={2} align="stretch">
                {sortedCategories.map(([category, count]) => {
                  const percentage = analytics.total_documents > 0 
                    ? (count / analytics.total_documents) * 100 
                    : 0
                  
                  return (
                    <Box key={category}>
                      <HStack justify="space-between" mb={1}>
                        <Text fontSize="xs" color="gray.600">
                          {getDocumentCategoryLabel(category)}
                        </Text>
                        <HStack spacing={2}>
                          <Text fontSize="xs" color="gray.500">
                            {count}
                          </Text>
                          <Text fontSize="xs" fontWeight="medium">
                            {percentage.toFixed(1)}%
                          </Text>
                        </HStack>
                      </HStack>
                      <Progress 
                        value={percentage} 
                        colorScheme={getCategoryColor(category)} 
                        size="sm" 
                        borderRadius="full"
                      />
                    </Box>
                  )
                })}
              </VStack>
            ) : (
              <Text fontSize="sm" color="gray.500" textAlign="center">
                No documents uploaded yet
              </Text>
            )}
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Upload Timeline */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Recent Upload Activity
            </Text>
            
            {recentUploads.length > 0 ? (
              <VStack spacing={1} align="stretch">
                {recentUploads.map((day) => {
                  const maxUploads = Math.max(...recentUploads.map(d => d.upload_count))
                  const percentage = maxUploads > 0 ? (day.upload_count / maxUploads) * 100 : 0
                  
                  return (
                    <HStack key={day.date} justify="space-between">
                      <Text fontSize="xs" color="gray.600" width="80px">
                        {new Date(day.date).toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </Text>
                      <Box flex={1} mx={2}>
                        <Progress 
                          value={percentage} 
                          colorScheme="green" 
                          size="sm" 
                          borderRadius="full"
                        />
                      </Box>
                      <Text fontSize="xs" fontWeight="medium" width="30px" textAlign="right">
                        {day.upload_count}
                      </Text>
                    </HStack>
                  )
                })}
              </VStack>
            ) : (
              <Text fontSize="sm" color="gray.500" textAlign="center">
                No recent upload activity
              </Text>
            )}
          </Box>

          {/* Document Insights */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Document Insights:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {analytics.total_documents === 0 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ No documents uploaded yet
                </Text>
              )}
              
              {analytics.upload_velocity >= 1 && (
                <Text fontSize="xs" color="green.600">
                  ✓ High document upload activity
                </Text>
              )}
              
              {analytics.upload_velocity < 0.2 && analytics.total_documents > 0 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Low recent upload activity
                </Text>
              )}
              
              {analytics.total_size_mb > 100 && (
                <Text fontSize="xs" color="blue.600">
                  📊 Substantial document collection ({analytics.total_size_mb.toFixed(1)} MB)
                </Text>
              )}
              
              {analytics.average_size_bytes > 5 * 1024 * 1024 && (
                <Text fontSize="xs" color="purple.600">
                  📄 Large average file size - detailed documents
                </Text>
              )}
              
              {analytics.total_links > 5 && (
                <Text fontSize="xs" color="teal.600">
                  🔗 Well-linked document collection
                </Text>
              )}
              
              {Object.keys(analytics.category_distribution).length > 4 && (
                <Text fontSize="xs" color="indigo.600">
                  📁 Diverse document categories
                </Text>
              )}
            </VStack>
          </Box>

          {/* Quick Stats */}
          <HStack justify="space-around" bg="green.50" p={2} borderRadius="md">
            <Box textAlign="center">
              <Box textAlign="center">
                {(analytics.upload_velocity * 7).toFixed(1)}
              </Text>
              <Box textAlign="center">Weekly Rate</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {analytics.total_documents > 0 
                  ? (analytics.total_size_bytes / analytics.total_documents / 1024).toFixed(0)
                  : 0}
              </Text>
              <Box textAlign="center">KB/Doc</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {Object.keys(analytics.category_distribution).length}
              </Text>
              <Box textAlign="center">Categories</Text>
            </Box>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default DocumentAnalyticsCard
