import {
  Box,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  Text,
  Button,
  Badge,
  Flex,
  Spacer,
  useDisclosure,
  Spinner,
  Alert,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Input,
  Select,
  IconButton,
  Tooltip,
} from "@chakra-ui/react"
import { FiPlus, FiSearch, FiFilter, FiRefreshCw } from "react-icons/fi"
import { useState, useEffect } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"

import { CaseNotesService, CaseNote, NOTE_TYPE_CONFIG, NOTE_CATEGORY_CONFIG } from "@/client/case-notes"
import useAuth from "@/hooks/useAuth"
import NoteCard from "./NoteCard"
import NoteEditor from "./NoteEditor"
import { useDebounce } from "@/hooks/useDebounce"

interface CaseNotesSectionProps {
  caseId: string
}

const CaseNotesSection = ({ caseId }: CaseNotesSectionProps) => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const { isOpen: isEditorOpen, onOpen: onEditorOpen, onClose: onEditorClose } = useDisclosure()
  
  // Filters and search
  const [search, setSearch] = useState("")
  const [selectedType, setSelectedType] = useState<string>("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [sortBy, setSortBy] = useState<"created_at" | "updated_at" | "category">("created_at")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  
  const debouncedSearch = useDebounce(search, 300)

  // Fetch notes
  const { 
    data: notesData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ["case-notes", caseId, {
      search: debouncedSearch,
      note_type: selectedType || undefined,
      category: selectedCategory || undefined,
      sort_by: sortBy,
      sort_order: sortOrder,
    }],
    queryFn: () => CaseNotesService.getCaseNotes(caseId, {
      search: debouncedSearch || undefined,
      note_type: selectedType || undefined,
      category: selectedCategory || undefined,
      sort_by: sortBy,
      sort_order: sortOrder,
      limit: 50,
    }),
    enabled: !!caseId,
  })

  const notes = notesData?.data || []
  const totalCount = notesData?.count || 0

  // Group notes by type for tabs
  const notesByType = notes.reduce((acc, note) => {
    if (!acc[note.note_type]) {
      acc[note.note_type] = []
    }
    acc[note.note_type].push(note)
    return acc
  }, {} as Record<string, CaseNote[]>)

  // Get available note types based on user role
  const getAvailableNoteTypes = () => {
    const types = ["private", "team", "client"]
    if (user?.role === "admin" || user?.role === "lawyer") {
      types.push("admin")
    }
    types.push("task", "communication")
    return types
  }

  const availableTypes = getAvailableNoteTypes()

  const handleNoteCreated = () => {
    queryClient.invalidateQueries({ queryKey: ["case-notes", caseId] })
    onEditorClose()
  }

  const handleNoteUpdated = () => {
    queryClient.invalidateQueries({ queryKey: ["case-notes", caseId] })
  }

  const handleNoteDeleted = () => {
    queryClient.invalidateQueries({ queryKey: ["case-notes", caseId] })
  }

  const clearFilters = () => {
    setSearch("")
    setSelectedType("")
    setSelectedCategory("")
    setSortBy("created_at")
    setSortOrder("desc")
  }

  if (error) {
    return (
      <Alert status="error">
        <Text>Failed to load notes. Please try again.</Text>
        <Button ml={4} size="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex align="center" mb={6}>
        <VStack align="start" spacing={1}>
          <Text fontSize="xl" fontWeight="bold">
            Case Notes & Comments
          </Text>
          <Text fontSize="sm" color="gray.600">
            {totalCount} note{totalCount !== 1 ? 's' : ''} total
          </Text>
        </VStack>
        <Spacer />
        <HStack spacing={2}>
          <Tooltip label="Refresh notes">
            <IconButton
              variant="ghost"
              size="sm"
              icon={<FiRefreshCw />}
              onClick={() => refetch()}
              isLoading={isLoading}
            />
          </Tooltip>
          <Button
            leftIcon={<FiPlus />}
            colorScheme="blue"
            size="sm"
            onClick={onEditorOpen}
          >
            Add Note
          </Button>
        </HStack>
      </Flex>

      {/* Filters */}
      <Box mb={6} p={4} bg="gray.50" borderRadius="lg">
        <VStack spacing={4}>
          <HStack w="full" spacing={4}>
            <Box flex={1}>
              <Input
                placeholder="Search notes..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                leftElement={<FiSearch />}
                size="sm"
              />
            </Box>
            <Select
              placeholder="All types"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              size="sm"
              maxW="150px"
            >
              {availableTypes.map((type) => (
                <option key={type} value={type}>
                  {NOTE_TYPE_CONFIG[type as keyof typeof NOTE_TYPE_CONFIG]?.label || type}
                </option>
              ))}
            </Select>
            <Select
              placeholder="All categories"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              size="sm"
              maxW="150px"
            >
              {Object.entries(NOTE_CATEGORY_CONFIG).map(([key, config]) => (
                <option key={key} value={key}>
                  {config.label}
                </option>
              ))}
            </Select>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              leftIcon={<FiFilter />}
            >
              Clear
            </Button>
          </HStack>
          
          <HStack w="full" justify="space-between">
            <HStack spacing={4}>
              <Text fontSize="sm" color="gray.600">Sort by:</Text>
              <Select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                size="sm"
                maxW="120px"
              >
                <option value="created_at">Created</option>
                <option value="updated_at">Updated</option>
                <option value="category">Category</option>
              </Select>
              <Select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as any)}
                size="sm"
                maxW="100px"
              >
                <option value="desc">Newest</option>
                <option value="asc">Oldest</option>
              </Select>
            </HStack>
            
            {/* Active filters display */}
            <HStack spacing={2}>
              {selectedType && (
                <Badge colorScheme="blue" variant="subtle">
                  Type: {NOTE_TYPE_CONFIG[selectedType as keyof typeof NOTE_TYPE_CONFIG]?.label}
                </Badge>
              )}
              {selectedCategory && (
                <Badge colorScheme="green" variant="subtle">
                  Category: {NOTE_CATEGORY_CONFIG[selectedCategory as keyof typeof NOTE_CATEGORY_CONFIG]?.label}
                </Badge>
              )}
              {debouncedSearch && (
                <Badge colorScheme="purple" variant="subtle">
                  Search: "{debouncedSearch}"
                </Badge>
              )}
            </HStack>
          </HStack>
        </VStack>
      </Box>

      {/* Notes List */}
      {isLoading ? (
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={4} color="gray.600">Loading notes...</Text>
        </Box>
      ) : notes.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text fontSize="lg" color="gray.500" mb={4}>
            {debouncedSearch || selectedType || selectedCategory 
              ? "No notes match your filters" 
              : "No notes yet"
            }
          </Text>
          <Button
            leftIcon={<FiPlus />}
            colorScheme="blue"
            onClick={onEditorOpen}
          >
            Add First Note
          </Button>
        </Box>
      ) : (
        <VStack spacing={4} align="stretch">
          {notes.map((note) => (
            <NoteCard
              key={note.id}
              note={note}
              caseId={caseId}
              onUpdated={handleNoteUpdated}
              onDeleted={handleNoteDeleted}
            />
          ))}
        </VStack>
      )}

      {/* Note Editor Modal */}
      <NoteEditor
        isOpen={isEditorOpen}
        onClose={onEditorClose}
        caseId={caseId}
        onSuccess={handleNoteCreated}
      />
    </Box>
  )
}

export default CaseNotesSection
