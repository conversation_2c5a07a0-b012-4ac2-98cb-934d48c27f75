import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  Progress,





  Avatar,
  AvatarGroup,
  Tooltip,
  SimpleGrid,
} from "@chakra-ui/react"
import {
  FiActivity,
  FiTrendingUp,
  FiUsers,
  FiCalendar,
  FiZap,
  FiTarget,
} from "react-icons/fi"

import { 
  ActivityAnalytics, 
  getActivityTypeLabel 
} from "@/client/case-analytics"

interface ActivityAnalyticsCardProps {
  analytics: ActivityAnalytics
}

const ActivityAnalyticsCard = ({ analytics }: ActivityAnalyticsCardProps) => {
  const getActivityTypeColor = (activityType: string) => {
    const colors: Record<string, string> = {
      case_created: "green",
      case_updated: "blue",
      status_changed: "purple",
      document_uploaded: "orange",
      note_added: "teal",
      milestone_created: "pink",
      deadline_created: "red",
      user_assigned: "cyan",
    }
    return colors[activityType] || "gray"
  }

  const getVelocityColor = (velocity: number) => {
    if (velocity >= 2) return "green"
    if (velocity >= 1) return "blue"
    if (velocity >= 0.5) return "yellow"
    return "orange"
  }

  const getVelocityLabel = (velocity: number) => {
    if (velocity >= 2) return "High"
    if (velocity >= 1) return "Good"
    if (velocity >= 0.5) return "Moderate"
    return "Low"
  }

  // Sort activity types by count
  const sortedActivityTypes = Object.entries(analytics.activity_by_type)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 6)

  // Sort users by activity count
  const sortedUsers = analytics.activity_by_user
    .sort((a, b) => b.activity_count - a.activity_count)
    .slice(0, 5)

  // Get recent timeline data
  const recentTimeline = analytics.activity_timeline.slice(-7)

  return (
    <Card>
      <CardHeader pb={2}>
        <HStack spacing={2}>
          <FiActivity />
          <Heading size="sm">Activity Analytics</Heading>
          <Badge 
            colorScheme={getVelocityColor(analytics.activity_velocity)} 
            variant="subtle"
            fontSize="xs"
          >
            {getVelocityLabel(analytics.activity_velocity)} Velocity
          </Badge>
        </HStack>
      </CardHeader>
      
      <CardBody pt={2}>
        <VStack spacing={4} align="stretch">
          {/* Activity Summary */}
          <SimpleGrid columns={2} spacing={4}>
            <Box textAlign="center">
              <Box textAlign="center">Total Activities</Text>
              <Box textAlign="center">
                {analytics.total_activities}
              </Text>
              <Box textAlign="center">
                All recorded activities
              </Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">Daily Velocity</Text>
              <Box textAlign="center">
                {analytics.activity_velocity.toFixed(1)}
              </Text>
              <Box textAlign="center">
                Activities per day
              </Text>
            </Box>
          </SimpleGrid>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Activity by Type */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Activity Breakdown
            </Text>
            
            <VStack spacing={2} align="stretch">
              {sortedActivityTypes.map(([activityType, count]) => {
                const percentage = (count / analytics.total_activities) * 100
                return (
                  <Box key={activityType}>
                    <HStack justify="space-between" mb={1}>
                      <Text fontSize="xs" color="gray.600">
                        {getActivityTypeLabel(activityType)}
                      </Text>
                      <HStack spacing={2}>
                        <Text fontSize="xs" color="gray.500">
                          {count}
                        </Text>
                        <Text fontSize="xs" fontWeight="medium">
                          {percentage.toFixed(1)}%
                        </Text>
                      </HStack>
                    </HStack>
                    <Progress 
                      value={percentage} 
                      colorScheme={getActivityTypeColor(activityType)} 
                      size="sm" 
                      borderRadius="full"
                    />
                  </Box>
                )
              })}
            </VStack>
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Team Activity */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Team Participation
            </Text>
            
            {sortedUsers.length > 0 ? (
              <VStack spacing={2} align="stretch">
                {sortedUsers.map((user) => {
                  const percentage = (user.activity_count / analytics.total_activities) * 100
                  return (
                    <HStack key={user.user_id} justify="space-between">
                      <HStack spacing={2} flex={1}>
                        <Avatar size="xs" name={user.user_name} />
                        <VStack align="start" spacing={0} flex={1}>
                          <Text fontSize="xs" fontWeight="medium" noOfLines={1}>
                            {user.user_name}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            {user.activity_count} activities ({percentage.toFixed(1)}%)
                          </Text>
                        </VStack>
                      </HStack>
                      <Progress 
                        value={percentage} 
                        colorScheme="blue" 
                        size="sm" 
                        width="60px"
                        borderRadius="full"
                      />
                    </HStack>
                  )
                })}
              </VStack>
            ) : (
              <Text fontSize="sm" color="gray.500" textAlign="center">
                No team activity data available
              </Text>
            )}
          </Box>

          <Box height="1px" bg="gray.200" my={2} />

          {/* Activity Timeline */}
          <Box>
            <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
              Recent Activity Trend
            </Text>
            
            {recentTimeline.length > 0 ? (
              <VStack spacing={1} align="stretch">
                {recentTimeline.map((day) => {
                  const maxActivity = Math.max(...recentTimeline.map(d => d.activity_count))
                  const percentage = maxActivity > 0 ? (day.activity_count / maxActivity) * 100 : 0
                  
                  return (
                    <HStack key={day.date} justify="space-between">
                      <Text fontSize="xs" color="gray.600" width="80px">
                        {new Date(day.date).toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </Text>
                      <Box flex={1} mx={2}>
                        <Progress 
                          value={percentage} 
                          colorScheme="blue" 
                          size="sm" 
                          borderRadius="full"
                        />
                      </Box>
                      <Text fontSize="xs" fontWeight="medium" width="30px" textAlign="right">
                        {day.activity_count}
                      </Text>
                    </HStack>
                  )
                })}
              </VStack>
            ) : (
              <Text fontSize="sm" color="gray.500" textAlign="center">
                No recent activity data
              </Text>
            )}
          </Box>

          {/* Activity Insights */}
          <Box bg="gray.50" p={3} borderRadius="md">
            <Text fontSize="xs" fontWeight="medium" color="gray.700" mb={2}>
              Activity Insights:
            </Text>
            
            <VStack spacing={1} align="stretch">
              {analytics.activity_velocity >= 2 && (
                <Text fontSize="xs" color="green.600">
                  ✓ High activity velocity - case is very active
                </Text>
              )}
              
              {analytics.activity_velocity < 0.5 && (
                <Text fontSize="xs" color="orange.600">
                  ⚠ Low activity velocity - consider increasing engagement
                </Text>
              )}
              
              {analytics.most_active_day && (
                <Text fontSize="xs" color="blue.600">
                  📈 Most active day: {new Date(analytics.most_active_day.date).toLocaleDateString()} 
                  ({analytics.most_active_day.activity_count} activities)
                </Text>
              )}
              
              {analytics.average_daily_activities > 3 && (
                <Text fontSize="xs" color="green.600">
                  ✓ Consistent daily activity pattern
                </Text>
              )}
              
              {sortedUsers.length > 3 && (
                <Text fontSize="xs" color="purple.600">
                  👥 Good team collaboration ({sortedUsers.length} active members)
                </Text>
              )}
              
              {analytics.total_activities > 50 && (
                <Text fontSize="xs" color="blue.600">
                  📊 Rich activity history for analysis
                </Text>
              )}
            </VStack>
          </Box>

          {/* Quick Stats */}
          <HStack justify="space-around" bg="blue.50" p={2} borderRadius="md">
            <Box textAlign="center">
              <Box textAlign="center">
                {(analytics.activity_velocity * 7).toFixed(1)}
              </Text>
              <Box textAlign="center">Weekly Rate</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {sortedUsers.length}
              </Text>
              <Box textAlign="center">Active Users</Text>
            </Box>
            
            <Box textAlign="center">
              <Box textAlign="center">
                {Object.keys(analytics.activity_by_type).length}
              </Text>
              <Box textAlign="center">Activity Types</Text>
            </Box>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

export default ActivityAnalyticsCard
