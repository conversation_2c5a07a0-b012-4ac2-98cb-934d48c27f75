/**
=========================================================
* EquiNova Material Legal Cases Table
=========================================================

* Material-UI DataGrid implementation for legal cases
* Replaces Chakra UI table with enhanced functionality

=========================================================
*/

import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  InputAdornment,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  DataGrid,
  GridColDef,
  GridRowSelectionModel,
  GridToolbar,
  GridActionsCellItem,
  GridRowParams,
} from '@mui/x-data-grid';
import {
  Search as SearchIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { useDebounce } from '@/hooks/useDebounce';

// Import existing services and types
import { LegalCasesService } from '@/client';
import MaterialCaseStatusIndicator from './MaterialCaseStatusIndicator';
import MaterialPriorityBadge from './MaterialPriorityBadge';
import MaterialBulkStatusUpdateModal from './MaterialBulkStatusUpdateModal';
import MaterialAddLegalCase from './MaterialAddLegalCase';
import MaterialEditLegalCase from './MaterialEditLegalCase';

interface MaterialLegalCasesTableProps {
  onCaseClick?: (caseId: string) => void;
}

const PER_PAGE = 10;

const MaterialLegalCasesTable: React.FC<MaterialLegalCasesTableProps> = ({
  onCaseClick,
}) => {
  // State management
  const [page, setPage] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [filters, setFilters] = useState({
    case_type: '',
    status: '',
    priority: '',
    sort_by: 'opening_date',
    sort_order: 'desc' as 'asc' | 'desc',
  });

  const debouncedSearch = useDebounce(search, 300);

  // Data fetching
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["legal-cases", {
      skip: page * PER_PAGE,
      limit: PER_PAGE,
      search: debouncedSearch || undefined,
      caseType: filters.case_type || undefined,
      status: filters.status || undefined,
      priority: filters.priority || undefined,
      sortBy: filters.sort_by || 'opening_date',
      sortOrder: filters.sort_order || 'desc',
    }],
    queryFn: () => LegalCasesService.readLegalCases({
      skip: page * PER_PAGE,
      limit: PER_PAGE,
      search: debouncedSearch || undefined,
      caseType: filters.case_type || undefined,
      status: filters.status || undefined,
      priority: filters.priority || undefined,
      sortBy: filters.sort_by || 'opening_date',
      sortOrder: filters.sort_order || 'desc',
    }),
    placeholderData: (prevData) => prevData,
  });

  // Process data
  const legalCases = (data as any)?.data ?? [];
  const totalCount = (data as any)?.count ?? 0;

  // DataGrid columns configuration
  const columns: GridColDef[] = useMemo(() => [
    {
      field: 'title',
      headerName: 'Case Title',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            color: 'primary.main',
            cursor: 'pointer',
            fontWeight: 500,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
          onClick={() => onCaseClick?.(params.row.id)}
        >
          {params.value || 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'client_name',
      headerName: 'Client Name',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value || 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'case_type',
      headerName: 'Case Type',
      width: 140,
      renderCell: (params) => (
        <Chip
          label={params.value?.replace('_', ' ') || 'N/A'}
          color="secondary"
          variant="outlined"
          size="small"
          sx={{ textTransform: 'capitalize' }}
        />
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 150,
      renderCell: (params) => (
        <MaterialCaseStatusIndicator
          status={params.value || 'open'}
          size="small"
          showProgress={true}
        />
      ),
    },
    {
      field: 'priority',
      headerName: 'Priority',
      width: 120,
      renderCell: (params) => (
        <MaterialPriorityBadge
          priority={params.value || 'medium'}
          size="small"
          showIcon={true}
        />
      ),
    },
    {
      field: 'opening_date',
      headerName: 'Opening Date',
      width: 130,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value
            ? new Date(params.value).toLocaleDateString()
            : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 120,
      getActions: (params: GridRowParams) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="View Case">
              <ViewIcon />
            </Tooltip>
          }
          label="View"
          onClick={() => onCaseClick?.(params.row.id)}
        />,
        <GridActionsCellItem
          icon={
            <MaterialEditLegalCase
              legalCase={params.row}
              variant="icon"
              size="small"
            />
          }
          label="Edit"
          showInMenu
        />,
      ],
    },
  ], [onCaseClick]);

  // Handle bulk operations
  const handleBulkStatusUpdate = () => {
    if (selectedRows.length > 0) {
      setShowBulkModal(true);
    }
  };

  const clearSelection = () => {
    setSelectedRows([]);
  };

  // Loading state
  if (isLoading) {
    return (
      <Box>
        <Paper sx={{ p: 3, mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Loading Legal Cases...
          </Typography>
          <LinearProgress />
        </Paper>
        <Paper sx={{ height: 600 }}>
          <Box sx={{ p: 2 }}>
            {[...Array(5)].map((_, index) => (
              <Box key={index} sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
                <Box sx={{ width: 20, height: 20, bgcolor: 'grey.300', borderRadius: 1 }} />
                <Box sx={{ flex: 1, height: 20, bgcolor: 'grey.200', borderRadius: 1 }} />
                <Box sx={{ width: 80, height: 20, bgcolor: 'grey.200', borderRadius: 1 }} />
                <Box sx={{ width: 100, height: 20, bgcolor: 'grey.200', borderRadius: 1 }} />
              </Box>
            ))}
          </Box>
        </Paper>
      </Box>
    );
  }

  // Error state
  if (isError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading legal cases: {error?.message || 'Unknown error'}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header and Search */}
      <Paper sx={{ p: 3, mb: 2 }}>
        <Stack spacing={3}>
          {/* Title and Stats */}
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5" fontWeight="bold">
              Legal Cases
            </Typography>
            <Box display="flex" alignItems="center" gap={2}>
              <Chip
                label={`${totalCount} Total Cases`}
                color="primary"
                variant="outlined"
              />
              <MaterialAddLegalCase variant="button" size="medium" />
            </Box>
          </Box>

          {/* Search and Filters */}
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center">
            <TextField
              placeholder="Search cases..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
              size="small"
            />

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={filters.status}
                label="Status"
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="open">Open</MenuItem>
                <MenuItem value="in_progress">In Progress</MenuItem>
                <MenuItem value="under_review">Under Review</MenuItem>
                <MenuItem value="closed">Closed</MenuItem>
                <MenuItem value="archived">Archived</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Priority</InputLabel>
              <Select
                value={filters.priority}
                label="Priority"
                onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="low">Low</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="urgent">Urgent</MenuItem>
              </Select>
            </FormControl>
          </Stack>

          {/* Bulk Actions */}
          {selectedRows.length > 0 && (
            <Alert severity="info" sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2">
                {selectedRows.length} case(s) selected
              </Typography>
              <Button
                variant="contained"
                size="small"
                onClick={handleBulkStatusUpdate}
              >
                Update Status
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={clearSelection}
              >
                Clear Selection
              </Button>
            </Alert>
          )}
        </Stack>
      </Paper>

      {/* DataGrid */}
      <Paper sx={{ height: 600 }}>
        <DataGrid
          rows={legalCases}
          columns={columns}
          paginationMode="server"
          rowCount={totalCount}
          page={page}
          pageSize={PER_PAGE}
          onPageChange={setPage}
          checkboxSelection
          rowSelectionModel={selectedRows}
          onRowSelectionModelChange={setSelectedRows}
          disableRowSelectionOnClick
          slots={{
            toolbar: GridToolbar,
          }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 500 },
            },
          }}
          sx={{
            border: 0,
            '& .MuiDataGrid-cell:focus': {
              outline: 'none',
            },
            '& .MuiDataGrid-row:hover': {
              backgroundColor: 'action.hover',
            },
          }}
        />
      </Paper>

      {/* Bulk Status Update Modal */}
      <MaterialBulkStatusUpdateModal
        open={showBulkModal}
        onClose={() => setShowBulkModal(false)}
        selectedCaseIds={selectedRows.map(String)}
        onSuccess={clearSelection}
      />
    </Box>
  );
};

export default MaterialLegalCasesTable;
