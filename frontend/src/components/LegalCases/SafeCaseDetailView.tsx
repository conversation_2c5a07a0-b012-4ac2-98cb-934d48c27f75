import { useMemo } from "react"
import type { LegalCaseDetail } from "@/client"
import CaseDetailView from "./CaseDetailView"

interface SafeCaseDetailViewProps {
  caseDetail: LegalCaseDetail
  onBack: () => void
}

// Utility function to safely convert any value to string
const safeString = (value: any): string => {
  if (value === null || value === undefined) return ''
  if (typeof value === 'string') return value
  if (typeof value === 'number' || typeof value === 'boolean') return String(value)
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value)
    } catch {
      return String(value)
    }
  }
  return String(value)
}

// Utility function to safely convert user object
const safeUser = (user: any) => {
  if (!user) return undefined
  return {
    full_name: safeString(user.full_name || ''),
    email: safeString(user.email || ''),
    role: safeString(user.role || '')
  }
}

// Utility function to safely convert activity object
const safeActivity = (activity: any) => {
  if (!activity) return null
  return {
    id: safeString(activity.id || ''),
    activity_type: safeString(activity.activity_type || ''),
    description: safeString(activity.description || ''),
    created_at: safeString(activity.created_at || ''),
    user: safeUser(activity.user)
  }
}

const SafeCaseDetailView = ({ caseDetail, onBack }: SafeCaseDetailViewProps) => {
  // Create a completely safe version of the case detail
  const safeCaseDetail = useMemo(() => {
    if (!caseDetail) {
      return {
        id: '',
        title: 'Unknown Case',
        client_name: 'Unknown Client',
        case_type: 'other',
        opening_date: '',
        description: '',
        status: 'open',
        priority: 'medium',
        lawyer_id: '',
        lawyer: undefined,
        recent_activities: [],
        status_history: []
      }
    }

    return {
      id: safeString(caseDetail.id),
      title: safeString(caseDetail.title || 'Unknown Case'),
      client_name: safeString(caseDetail.client_name || 'Unknown Client'),
      case_type: safeString(caseDetail.case_type || 'other'),
      opening_date: safeString(caseDetail.opening_date || ''),
      description: safeString(caseDetail.description || ''),
      status: safeString(caseDetail.status || 'open'),
      priority: safeString(caseDetail.priority || 'medium'),
      lawyer_id: safeString(caseDetail.lawyer_id || ''),
      lawyer: caseDetail.lawyer ? {
        id: safeString(caseDetail.lawyer.id || ''),
        full_name: safeString(caseDetail.lawyer.full_name || ''),
        email: safeString(caseDetail.lawyer.email || ''),
        role: safeString(caseDetail.lawyer.role || 'lawyer'),
        is_active: Boolean(caseDetail.lawyer.is_active),
        is_superuser: Boolean(caseDetail.lawyer.is_superuser)
      } : undefined,
      recent_activities: (caseDetail.recent_activities || [])
        .map(safeActivity)
        .filter(Boolean),
      status_history: (caseDetail.status_history || [])
        .map((history: any) => {
          if (!history) return null
          return {
            id: safeString(history.id || ''),
            old_status: safeString(history.old_status || ''),
            new_status: safeString(history.new_status || ''),
            changed_at: safeString(history.changed_at || ''),
            changed_by_user: safeUser(history.changed_by_user),
            notes: safeString(history.notes || '')
          }
        })
        .filter(Boolean)
    }
  }, [caseDetail])

  return <CaseDetailView caseDetail={safeCaseDetail} onBack={onBack} />
}

export default SafeCaseDetailView
