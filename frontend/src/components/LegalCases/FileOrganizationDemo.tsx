import {
  Container,
  VStack,
  Heading,
  Text,
  Alert,
  Box,
  Badge,
  SimpleGrid,
  Card,
  CardBody,
  HStack,
  List,
  ListItem,
  ListIcon,
} from "@chakra-ui/react"
import { FiInfo, FiFolder, FiFile, FiUsers, FiLock, FiSearch } from "react-icons/fi"

import FileOrganizationView from "./FileOrganizationView"
import { SYSTEM_FOLDER_CONFIG } from "@/client/case-folders"

const FileOrganizationDemo = () => {
  // Demo case ID (you can replace with a real case ID for testing)
  const demoCaseId = "demo-case-file-organization-123"

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="xl" color="blue.600" mb={2}>
            📁 Case File Organization System Demo
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Issue #73 - Complete hierarchical file organization for legal cases
          </Text>
        </Box>

        {/* Status Alert */}
        <Alert status="success" borderRadius="lg">
          <Box as={FiInfo} color="green.500" mr={3} mt={1} />
          <Box>
            <Text fontWeight="bold">System Status: Complete ✅</Text>
            <Text fontSize="sm">
              Full hierarchical file organization system implemented with backend API and frontend UI.
            </Text>
          </Box>
        </Alert>

        {/* Features Overview */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>🚀 Implemented Features</Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">✅ Folder System:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  {Object.entries(SYSTEM_FOLDER_CONFIG).slice(0, 3).map(([key, config]) => (
                    <Badge key={key} colorScheme={config.color} variant="subtle">
                      {config.icon} {config.label}
                    </Badge>
                  ))}
                  <Text fontSize="xs" color="gray.500">...and 3 more default folders</Text>
                </VStack>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="blue.600">📋 Organization:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <HStack><FiFolder size={12} /><Text>Hierarchical structure</Text></HStack>
                  <HStack><FiFile size={12} /><Text>Document categorization</Text></HStack>
                  <HStack><FiSearch size={12} /><Text>Folder-based filtering</Text></HStack>
                  <HStack><FiUsers size={12} /><Text>Collaborative organization</Text></HStack>
                  <Text>• Custom folder colors</Text>
                  <Text>• Drag & drop support</Text>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2} color="purple.600">🔧 Management:</Text>
                <VStack align="stretch" spacing={1} fontSize="sm">
                  <Text>• Create/edit/delete folders</Text>
                  <Text>• Move documents between folders</Text>
                  <Text>• Folder statistics and sizes</Text>
                  <Text>• Breadcrumb navigation</Text>
                  <Text>• System folder protection</Text>
                  <Text>• Role-based permissions</Text>
                </VStack>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Demo Section */}
        <Box>
          <Heading size="lg" mb={4}>
            📁 Interactive Demo
          </Heading>
          <Text mb={6} color="gray.600">
            Try organizing documents in folders below. Create custom folders, upload documents, and navigate the hierarchy.
          </Text>
          
          {/* File Organization View */}
          <Box
            p={6}
            bg="white"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="gray.200"
            shadow="sm"
          >
            <FileOrganizationView caseId={demoCaseId} />
          </Box>
        </Box>

        {/* Implementation Status */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>📊 Implementation Status</Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <Box>
                <Text fontWeight="bold" mb={2} color="green.600">✅ Completed:</Text>
                <List spacing={1} fontSize="sm">
                  <ListItem>
                    <ListIcon as={FiFolder} color="green.500" />
                    Backend folder models and API routes
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiFile} color="green.500" />
                    Document-folder relationship system
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiUsers} color="green.500" />
                    Frontend folder browser and navigation
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiLock} color="green.500" />
                    Role-based folder permissions
                  </ListItem>
                  <ListItem>
                    <ListIcon as={FiSearch} color="green.500" />
                    Folder-based document filtering
                  </ListItem>
                </List>
              </Box>
              
              <Box>
                <Text fontWeight="bold" mb={2} color="orange.600">🔄 Future Enhancements:</Text>
                <List spacing={1} fontSize="sm">
                  <ListItem>• Drag & drop folder reorganization</ListItem>
                  <ListItem>• Bulk document operations</ListItem>
                  <ListItem>• Folder templates for case types</ListItem>
                  <ListItem>• Advanced folder search</ListItem>
                  <ListItem>• Folder sharing with external parties</ListItem>
                  <ListItem>• Automated folder organization rules</ListItem>
                  <ListItem>• Folder activity tracking</ListItem>
                  <ListItem>• Mobile folder management</ListItem>
                </List>
              </Box>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Technical Details */}
        <Card bg="gray.50">
          <CardBody>
            <Heading size="md" mb={4}>🛠️ Technical Implementation</Heading>
            <VStack align="stretch" spacing={3} fontSize="sm">
              <Box>
                <Text fontWeight="bold">Backend Architecture:</Text>
                <Text>• CaseFolder model with hierarchical parent-child relationships</Text>
                <Text>• CaseDocument model extended with folder_id foreign key</Text>
                <Text>• RESTful API with CRUD operations for folders</Text>
                <Text>• Recursive folder path calculation and breadcrumb generation</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">Frontend Components:</Text>
                <Text>• FolderBrowser: Hierarchical navigation with breadcrumbs</Text>
                <Text>• FolderCard: Individual folder display with statistics</Text>
                <Text>• FolderCreateModal: Folder creation with color selection</Text>
                <Text>• FileOrganizationView: Integrated folder and document management</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">Data Flow:</Text>
                <Text>• Real-time folder statistics (document count, total size)</Text>
                <Text>• Optimistic UI updates with React Query</Text>
                <Text>• Efficient folder tree traversal and path calculation</Text>
                <Text>• Integrated document upload with folder selection</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">User Experience:</Text>
                <Text>• Intuitive folder navigation with visual breadcrumbs</Text>
                <Text>• Color-coded folders for easy identification</Text>
                <Text>• System folders with protection against deletion</Text>
                <Text>• Seamless integration with document management</Text>
              </Box>
            </VStack>
          </CardBody>
        </Card>

        {/* Default Folder Structure */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>📂 Default Folder Structure</Heading>
            <Text mb={4} fontSize="sm" color="gray.600">
              When initializing a case, the following default folders are automatically created:
            </Text>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={3}>
              {Object.entries(SYSTEM_FOLDER_CONFIG).map(([key, config]) => (
                <Box key={key} p={3} bg="white" borderRadius="md" border="1px solid" borderColor="gray.200">
                  <HStack spacing={3}>
                    <Box fontSize="xl" color={config.color}>
                      {config.icon}
                    </Box>
                    <VStack align="start" spacing={0}>
                      <Text fontWeight="medium" fontSize="sm">
                        {config.label}
                      </Text>
                      <Text fontSize="xs" color="gray.600">
                        {config.description}
                      </Text>
                    </VStack>
                  </HStack>
                </Box>
              ))}
            </SimpleGrid>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

export default FileOrganizationDemo
