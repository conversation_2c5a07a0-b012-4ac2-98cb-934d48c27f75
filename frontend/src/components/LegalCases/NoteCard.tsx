import {
  Box,
  VStack,
  H<PERSON><PERSON><PERSON>,
  Text,
  Badge,
  Button,
  IconButton,
  Flex,
  Spacer,
  useDisclosure,
  Collapse,
  Tooltip,
  Avatar,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useColorModeValue,
} from "@chakra-ui/react"
import {
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  FiMessageCircle,
  FiPin,
  FiClock,
  FiUser,
} from "react-icons/fi"
import { useState } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"

import { CaseNote, CaseNotesService, NOTE_TYPE_CONFIG, NOTE_CATEGORY_CONFIG } from "@/client/case-notes"
import useAuth from "@/hooks/useAuth"
import NoteEditor from "./NoteEditor"
import { toaster } from "@/components/ui/toaster"
import { formatDistanceToNow } from "date-fns"

interface NoteCardProps {
  note: CaseNote
  caseId: string
  onUpdated: () => void
  onDeleted: () => void
  isReply?: boolean
}

const NoteCard = ({ note, caseId, onUpdated, onDeleted, isReply = false }: NoteCardProps) => {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure()
  const { isOpen: isRepliesOpen, onToggle: onToggleReplies } = useDisclosure()
  
  const [isDeleting, setIsDeleting] = useState(false)

  const bgColor = useColorModeValue("white", "gray.800")
  const borderColor = useColorModeValue("gray.200", "gray.600")
  const replyBgColor = useColorModeValue("gray.50", "gray.700")

  // Get type and category configs
  const typeConfig = NOTE_TYPE_CONFIG[note.note_type as keyof typeof NOTE_TYPE_CONFIG]
  const categoryConfig = NOTE_CATEGORY_CONFIG[note.category as keyof typeof NOTE_CATEGORY_CONFIG]

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: () => CaseNotesService.deleteCaseNote(caseId, note.id),
    onSuccess: () => {
      toaster.create({
        title: "Note deleted",
        description: "The note has been deleted successfully.",
        status: "success",
      })
      onDeleted()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to delete note",
        status: "error",
      })
    },
    onSettled: () => {
      setIsDeleting(false)
    },
  })

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this note?")) {
      setIsDeleting(true)
      deleteMutation.mutate()
    }
  }

  const handleEdit = () => {
    onEditOpen()
  }

  const handleEditSuccess = () => {
    onEditClose()
    onUpdated()
  }

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch {
      return "Unknown time"
    }
  }

  return (
    <Box
      bg={isReply ? replyBgColor : bgColor}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="lg"
      p={4}
      ml={isReply ? 8 : 0}
      position="relative"
      _hover={{ shadow: "md" }}
      transition="all 0.2s"
    >
      {/* Pinned indicator */}
      {note.is_pinned && (
        <Box
          position="absolute"
          top={2}
          right={2}
          color="orange.500"
        >
          <FiPin size={16} />
        </Box>
      )}

      <VStack align="stretch" spacing={3}>
        {/* Header */}
        <Flex align="center">
          <HStack spacing={3}>
            <Avatar
              size="sm"
              name={note.author?.full_name || "Unknown"}
              src={undefined} // Add avatar URL when available
            />
            <VStack align="start" spacing={0}>
              <Text fontWeight="medium" fontSize="sm">
                {note.author?.full_name || "Unknown User"}
              </Text>
              <HStack spacing={2}>
                <Text fontSize="xs" color="gray.500">
                  <FiClock size={12} style={{ display: "inline", marginRight: "4px" }} />
                  {formatDate(note.created_at)}
                </Text>
                {note.updated_at !== note.created_at && (
                  <Text fontSize="xs" color="gray.500">
                    (edited {formatDate(note.updated_at)})
                  </Text>
                )}
              </HStack>
            </VStack>
          </HStack>

          <Spacer />

          <HStack spacing={2}>
            {/* Type badge */}
            <Badge
              colorScheme={typeConfig?.color || "gray"}
              variant="subtle"
              fontSize="xs"
            >
              {typeConfig?.icon} {typeConfig?.label || note.note_type}
            </Badge>

            {/* Category badge */}
            <Badge
              colorScheme={categoryConfig?.color || "gray"}
              variant="outline"
              fontSize="xs"
            >
              {categoryConfig?.icon} {categoryConfig?.label || note.category}
            </Badge>

            {/* Actions menu */}
            {(note.can_edit || note.can_delete) && (
              <Menu>
                <MenuButton
                  as={IconButton}
                  variant="ghost"
                  size="sm"
                  icon={<FiMoreVertical />}
                  isLoading={isDeleting}
                />
                <MenuList>
                  {note.can_edit && (
                    <MenuItem icon={<FiEdit />} onClick={handleEdit}>
                      Edit Note
                    </MenuItem>
                  )}
                  {note.can_delete && (
                    <MenuItem
                      icon={<FiTrash2 />}
                      onClick={handleDelete}
                      color="red.500"
                    >
                      Delete Note
                    </MenuItem>
                  )}
                </MenuList>
              </Menu>
            )}
          </HStack>
        </Flex>

        {/* Content */}
        <Box>
          <Text whiteSpace="pre-wrap" lineHeight="1.6">
            {note.content}
          </Text>
        </Box>

        {/* Tags */}
        {note.tags && note.tags.length > 0 && (
          <HStack spacing={2} flexWrap="wrap">
            {note.tags.map((tag, index) => (
              <Badge key={index} variant="subtle" colorScheme="blue" fontSize="xs">
                #{tag}
              </Badge>
            ))}
          </HStack>
        )}

        {/* Footer */}
        {!isReply && (
          <HStack spacing={4} pt={2}>
            {/* Replies button */}
            {note.replies_count > 0 && (
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<FiMessageCircle />}
                onClick={onToggleReplies}
              >
                {note.replies_count} {note.replies_count === 1 ? "reply" : "replies"}
              </Button>
            )}

            {/* Reply button */}
            <Button
              variant="ghost"
              size="sm"
              leftIcon={<FiMessageCircle />}
              onClick={() => {
                // TODO: Implement reply functionality
                console.log("Reply to note:", note.id)
              }}
            >
              Reply
            </Button>
          </HStack>
        )}
      </VStack>

      {/* Replies section */}
      {!isReply && (
        <Collapse in={isRepliesOpen} animateOpacity>
          <Box mt={4}>
            {/* TODO: Load and display replies */}
            <Text fontSize="sm" color="gray.500" textAlign="center" py={4}>
              Replies functionality coming soon...
            </Text>
          </Box>
        </Collapse>
      )}

      {/* Edit Modal */}
      <NoteEditor
        isOpen={isEditOpen}
        onClose={onEditClose}
        caseId={caseId}
        editNote={note}
        onSuccess={handleEditSuccess}
      />
    </Box>
  )
}

export default NoteCard
