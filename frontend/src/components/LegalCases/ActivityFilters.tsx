import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  FormControl,
  FormLabel,
  SimpleGrid,
  Badge,
  useColorModeValue,
} from "@chakra-ui/react"
import {
  FiSearch,
  FiX,
  FiFilter,
} from "react-icons/fi"

import { ACTIVITY_TYPE_CONFIG } from "@/client/case-activities"

interface ActivityFiltersProps {
  search: string
  onSearchChange: (value: string) => void
  selectedActivityType: string
  onActivityTypeChange: (value: string) => void
  startDate: string
  onStartDateChange: (value: string) => void
  endDate: string
  onEndDateChange: (value: string) => void
  onClearFilters: () => void
  hasActiveFilters: boolean
}

const ActivityFilters = ({
  search,
  onSearchChange,
  selectedActivityType,
  onActivityTypeChange,
  startDate,
  onStartDateChange,
  endDate,
  onEndDateChange,
  onClearFilters,
  hasActiveFilters,
}: ActivityFiltersProps) => {
  const bgColor = useColorModeValue("gray.50", "gray.700")
  const borderColor = useColorModeValue("gray.200", "gray.600")

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      border="1px solid"
      borderColor={borderColor}
    >
      <VStack spacing={4} align="stretch">
        {/* Header */}
        <HStack spacing={2} justify="space-between">
          <HStack spacing={2}>
            <FiFilter />
            <Text fontWeight="medium">Filter Activities</Text>
            {hasActiveFilters && (
              <Badge colorScheme="blue" variant="subtle">
                Active
              </Badge>
            )}
          </HStack>
          {hasActiveFilters && (
            <Button
              size="sm"
              variant="ghost"
              leftIcon={<FiX />}
              onClick={onClearFilters}
            >
              Clear All
            </Button>
          )}
        </HStack>

        {/* Filter Controls */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
          {/* Search */}
          <FormControl>
            <FormLabel fontSize="sm">Search</FormLabel>
            <Input
              placeholder="Search in descriptions..."
              value={search}
              onChange={(e) => onSearchChange(e.target.value)}
              leftElement={<FiSearch />}
              size="sm"
            />
          </FormControl>

          {/* Activity Type */}
          <FormControl>
            <FormLabel fontSize="sm">Activity Type</FormLabel>
            <Select
              placeholder="All types"
              value={selectedActivityType}
              onChange={(e) => onActivityTypeChange(e.target.value)}
              size="sm"
            >
              {Object.entries(ACTIVITY_TYPE_CONFIG).map(([type, config]) => (
                <option key={type} value={type}>
                  {config.icon} {config.label}
                </option>
              ))}
            </Select>
          </FormControl>

          {/* Start Date */}
          <FormControl>
            <FormLabel fontSize="sm">From Date</FormLabel>
            <Input
              type="date"
              value={startDate}
              onChange={(e) => onStartDateChange(e.target.value)}
              size="sm"
            />
          </FormControl>

          {/* End Date */}
          <FormControl>
            <FormLabel fontSize="sm">To Date</FormLabel>
            <Input
              type="date"
              value={endDate}
              onChange={(e) => onEndDateChange(e.target.value)}
              size="sm"
            />
          </FormControl>
        </SimpleGrid>

        {/* Quick Filters */}
        <Box>
          <Text fontSize="sm" fontWeight="medium" mb={2}>
            Quick Filters:
          </Text>
          <HStack spacing={2} flexWrap="wrap">
            <Button
              size="xs"
              variant={selectedActivityType === "status_changed" ? "solid" : "outline"}
              colorScheme="purple"
              onClick={() => 
                onActivityTypeChange(
                  selectedActivityType === "status_changed" ? "" : "status_changed"
                )
              }
            >
              🔄 Status Changes
            </Button>
            <Button
              size="xs"
              variant={selectedActivityType === "document_uploaded" ? "solid" : "outline"}
              colorScheme="teal"
              onClick={() => 
                onActivityTypeChange(
                  selectedActivityType === "document_uploaded" ? "" : "document_uploaded"
                )
              }
            >
              📄 Documents
            </Button>
            <Button
              size="xs"
              variant={selectedActivityType === "note_added" ? "solid" : "outline"}
              colorScheme="yellow"
              onClick={() => 
                onActivityTypeChange(
                  selectedActivityType === "note_added" ? "" : "note_added"
                )
              }
            >
              📝 Notes
            </Button>
            <Button
              size="xs"
              variant="outline"
              onClick={() => {
                const today = new Date().toISOString().split('T')[0]
                onStartDateChange(today)
                onEndDateChange(today)
              }}
            >
              📅 Today
            </Button>
            <Button
              size="xs"
              variant="outline"
              onClick={() => {
                const today = new Date()
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
                onStartDateChange(weekAgo.toISOString().split('T')[0])
                onEndDateChange(today.toISOString().split('T')[0])
              }}
            >
              📆 Last Week
            </Button>
          </HStack>
        </Box>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              Active Filters:
            </Text>
            <HStack spacing={2} flexWrap="wrap">
              {search && (
                <Badge colorScheme="blue" variant="subtle">
                  Search: "{search}"
                </Badge>
              )}
              {selectedActivityType && (
                <Badge colorScheme="purple" variant="subtle">
                  Type: {ACTIVITY_TYPE_CONFIG[selectedActivityType as keyof typeof ACTIVITY_TYPE_CONFIG]?.label || selectedActivityType}
                </Badge>
              )}
              {startDate && (
                <Badge colorScheme="green" variant="subtle">
                  From: {new Date(startDate).toLocaleDateString()}
                </Badge>
              )}
              {endDate && (
                <Badge colorScheme="orange" variant="subtle">
                  To: {new Date(endDate).toLocaleDateString()}
                </Badge>
              )}
            </HStack>
          </Box>
        )}
      </VStack>
    </Box>
  )
}

export default ActivityFilters
