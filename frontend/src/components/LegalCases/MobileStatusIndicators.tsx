import { 
  <PERSON>, 
  HStack, 
  VStack, 
  Text, 
  Badge, 
  Progress, 
  Icon,
  useBreakpointValue,
  Flex,
  Circle
} from "@chakra-ui/react"
import {
  FiCircle,
  FiPlay,
  FiEye,
  FiCheck,
  FiArchive,
  FiClock,
  FiTrendingUp
} from "react-icons/fi"
import CaseStatusBadge from "./CaseStatusBadge"
import CaseProgressBar from "./CaseProgressBar"

interface MobileStatusIndicatorsProps {
  status: string
  priority?: string
  caseType?: string
  lastUpdated?: string
  showProgress?: boolean
  showPriority?: boolean
  showLastUpdated?: boolean
  compact?: boolean
}

const MobileStatusIndicators = ({
  status,
  priority = "medium",
  caseType = "other",
  lastUpdated,
  showProgress = true,
  showPriority = true,
  showLastUpdated = true,
  compact = false
}: MobileStatusIndicatorsProps) => {

  // Responsive sizing
  const isMobile = useBreakpointValue({ base: true, md: false })
  const isTablet = useBreakpointValue({ base: false, md: true, lg: false })
  
  const size = compact ? "sm" : isMobile ? "sm" : isTablet ? "md" : "lg"
  const spacing = compact ? 1 : isMobile ? 2 : 3

  // Priority configuration
  const getPriorityConfig = (priority: string) => {
    const priorityStr = String(priority || "").toLowerCase()
    
    const configs: Record<string, { color: string; icon: any; label: string; pulse: boolean }> = {
      low: { color: "green", icon: FiCircle, label: "Low", pulse: false },
      medium: { color: "yellow", icon: FiClock, label: "Medium", pulse: false },
      high: { color: "orange", icon: FiTrendingUp, label: "High", pulse: true },
      urgent: { color: "red", icon: FiTrendingUp, label: "Urgent", pulse: true }
    }
    
    return configs[priorityStr] || configs.medium
  }

  const priorityConfig = getPriorityConfig(priority)

  // Time since last update
  const getTimeSinceUpdate = (lastUpdated?: string) => {
    if (!lastUpdated) return "Unknown"
    
    const updateDate = new Date(lastUpdated)
    const now = new Date()
    const diffMs = now.getTime() - updateDate.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    
    if (diffDays > 0) return `${diffDays}d ago`
    if (diffHours > 0) return `${diffHours}h ago`
    if (diffMinutes > 0) return `${diffMinutes}m ago`
    return "Just now"
  }

  // Mobile-optimized layout
  if (isMobile || compact) {
    return (
      <VStack spacing={spacing} align="stretch" w="100%">
        {/* Top row: Status and Priority */}
        <Flex justify="space-between" align="center" wrap="wrap" gap={2}>
          <CaseStatusBadge 
            status={status} 
            size={size} 
            showIcon={true}
            animated={true}
          />
          
          {showPriority && (
            <Badge
              colorScheme={priorityConfig.color}
              variant="solid"
              size={size}
              animation={priorityConfig.pulse ? "pulse 2s infinite" : "none"}
            >
              <HStack spacing={1} align="center">
                <Icon as={priorityConfig.icon} boxSize="10px" />
                <Text fontSize="xs">{priorityConfig.label}</Text>
              </HStack>
            </Badge>
          )}
        </Flex>

        {/* Progress bar */}
        {showProgress && (
          <Box>
            <CaseProgressBar 
              status={status}
              size="sm"
              showLabel={false}
              showPercentage={true}
              animated={true}
            />
          </Box>
        )}

        {/* Last updated */}
        {showLastUpdated && lastUpdated && (
          <Text fontSize="xs" color="gray.500" textAlign="right">
            Updated {getTimeSinceUpdate(lastUpdated)}
          </Text>
        )}
      </VStack>
    )
  }

  // Tablet/Desktop layout
  return (
    <VStack spacing={spacing} align="stretch" w="100%">
      {/* Header row */}
      <HStack justify="space-between" align="center" wrap="wrap">
        <HStack spacing={3}>
          <CaseStatusBadge 
            status={status} 
            size={size} 
            showIcon={true}
            animated={true}
          />
          
          {showPriority && (
            <Badge
              colorScheme={priorityConfig.color}
              variant="outline"
              size={size}
              animation={priorityConfig.pulse ? "pulse 2s infinite" : "none"}
            >
              <HStack spacing={1} align="center">
                <Icon as={priorityConfig.icon} boxSize="12px" />
                <Text>{priorityConfig.label} Priority</Text>
              </HStack>
            </Badge>
          )}
        </HStack>

        {showLastUpdated && lastUpdated && (
          <Text fontSize="sm" color="gray.500">
            Updated {getTimeSinceUpdate(lastUpdated)}
          </Text>
        )}
      </HStack>

      {/* Progress section */}
      {showProgress && (
        <Box>
          <CaseProgressBar 
            status={status}
            size={size}
            showLabel={true}
            showPercentage={true}
            animated={true}
          />
        </Box>
      )}
    </VStack>
  )
}

export default MobileStatusIndicators

// Compact version for list items
export const CompactMobileStatusIndicator = ({
  status,
  priority = "medium",
  showPulse = true
}: {
  status: string
  priority?: string
  showPulse?: boolean
}) => {
  const priorityConfig = {
    low: "green",
    medium: "yellow", 
    high: "orange",
    urgent: "red"
  }

  const statusConfig = {
    open: "blue",
    in_progress: "orange",
    under_review: "purple", 
    closed: "green",
    archived: "gray"
  }

  const priorityColor = priorityConfig[priority as keyof typeof priorityConfig] || "gray"
  const statusColor = statusConfig[status as keyof typeof statusConfig] || "gray"

  return (
    <HStack spacing={1}>
      {/* Status indicator */}
      <Circle 
        size="8px" 
        bg={`${statusColor}.500`}
        animation={showPulse && priority === "urgent" ? "pulse 2s infinite" : "none"}
      />
      
      {/* Priority indicator */}
      <Circle 
        size="6px" 
        bg={`${priorityColor}.400`}
        opacity={0.7}
      />
    </HStack>
  )
}

// Status indicator for very small spaces (like table cells)
export const MiniStatusIndicator = ({ status }: { status: string }) => {
  const statusConfig = {
    open: { color: "blue", icon: FiCircle },
    in_progress: { color: "orange", icon: FiPlay },
    under_review: { color: "purple", icon: FiEye },
    closed: { color: "green", icon: FiCheck },
    archived: { color: "gray", icon: FiArchive }
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open

  return (
    <Icon 
      as={config.icon}
      color={`${config.color}.500`}
      boxSize="14px"
      title={status}
    />
  )
}
