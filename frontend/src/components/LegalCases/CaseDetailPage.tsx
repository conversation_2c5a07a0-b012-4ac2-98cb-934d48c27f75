import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ing,
  <PERSON>S<PERSON>ck,
  <PERSON><PERSON>,
  Box,
  SimpleGrid,
  Spinner,
} from "@chakra-ui/react"
import { useEffect, useState } from "react"
import { useQuery } from "@tanstack/react-query"
import StatusManagement from "./StatusManagement"
import CaseStatusBadge from "./CaseStatusBadge"
import BulletproofStatusHistory from "./BulletproofStatusHistory"
// import StatusTimeline from "./StatusTimeline"
// import PriorityManagement from "./PriorityManagement"

interface CaseDetailPageProps {
  caseId: string
  onBack: () => void
}

function getLegalCaseQueryOptions(caseId: string) {
  return {
    queryFn: async () => {
      const { request } = await import("@/client/core/request")
      const { OpenAPI } = await import("@/client/core/OpenAPI")

      return request(OpenAPI, {
        method: 'GET',
        url: `/api/v1/legal-cases/${caseId}`,
      })
    },
    queryKey: ["legal-case", caseId],
  }
}

export default function CaseDetailPage({ caseId, onBack }: CaseDetailPageProps) {
  const { data: legalCase, isLoading, error, refetch } = useQuery({
    ...getLegalCaseQueryOptions(caseId),
    enabled: !!caseId,
  })

  if (isLoading) {
    return (
      <Container maxW="full" py={8}>
        <VStack gap={4} align="center" justify="center" minH="400px">
          <Spinner size="xl" />
          <Text>Loading case details...</Text>
        </VStack>
      </Container>
    )
  }

  if (error || !legalCase) {
    return (
      <Container maxW="full" py={8}>
        <VStack gap={4} align="center" justify="center" minH="400px">
          <Text color="red.500" fontSize="lg">
            Error loading case details
          </Text>
          <Button onClick={onBack}>
            Back to Legal Cases
          </Button>
        </VStack>
      </Container>
    )
  }

  // Safely serialize ALL data to prevent React error #130
  const safeString = (value: any): string => {
    if (value === null || value === undefined) return ""
    if (typeof value === "string") return value
    if (typeof value === "number" || typeof value === "boolean") return String(value)
    try {
      return String(value)
    } catch {
      return ""
    }
  }

  const caseData = {
    id: safeString(legalCase?.id),
    title: safeString(legalCase?.title || "Case Details"),
    client_name: safeString(legalCase?.client_name || "Unknown Client"),
    case_type: safeString(legalCase?.case_type || "other"),
    opening_date: safeString(legalCase?.opening_date),
    priority: safeString(legalCase?.priority || "medium"),
    status: safeString(legalCase?.status || "open"),
    description: safeString(legalCase?.description)
  }

  // Create a safe version for StatusManagement that includes all required fields
  const safeLegalCase = {
    id: caseData.id,
    title: caseData.title,
    client_name: caseData.client_name,
    case_type: caseData.case_type,
    opening_date: caseData.opening_date,
    priority: caseData.priority,
    status: caseData.status,
    description: caseData.description,
    lawyer_id: safeString(legalCase?.lawyer_id)
  }

  return (
    <Container maxW="full" py={8}>
      <VStack gap={6} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Heading size="lg">{caseData.title}</Heading>
          <Button onClick={onBack} variant="outline">
            ← Back to Cases
          </Button>
        </HStack>

        {/* Case Information */}
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          <Box>
            <Text fontWeight="bold" mb={2}>Case Information</Text>
            <VStack align="stretch" spacing={2}>
              <Text><strong>Client:</strong> {caseData.client_name || "N/A"}</Text>
              <Text><strong>Type:</strong> {caseData.case_type || "N/A"}</Text>
              <Text><strong>Opening Date:</strong> {caseData.opening_date || "N/A"}</Text>
              <Text><strong>Priority:</strong> {caseData.priority || "N/A"}</Text>
            </VStack>
          </Box>

          <Box>
            <Text fontWeight="bold" mb={4}>Status Management</Text>
            <VStack align="stretch" spacing={4}>
              <CaseStatusBadge
                status={caseData.status}
                size="md"
                showIcon={true}
                animated={true}
              />
              <StatusManagement
                legalCase={safeLegalCase}
                onStatusUpdate={() => refetch()}
              />
            </VStack>
          </Box>
        </SimpleGrid>

        {/* Description */}
        {caseData.description && (
          <Box>
            <Text fontWeight="bold" mb={2}>Description</Text>
            <Text>{caseData.description}</Text>
          </Box>
        )}

        {/* Status History - Fixed React Error #130 */}
        <BulletproofStatusHistory caseId={safeString(caseId)} />
      </VStack>
    </Container>
  )
}
