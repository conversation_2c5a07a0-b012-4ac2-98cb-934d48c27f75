import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  Textarea,
  Select,
  FormControl,
  FormLabel,
  Switch,
  Input,
  Badge,
  Box,
  Flex,
  IconButton,
  Tooltip,
} from "@chakra-ui/react"
import { FiPlus, FiX, FiSave, FiEye } from "react-icons/fi"
import { useState, useEffect } from "react"
import { useMutation } from "@tanstack/react-query"

import { 
  CaseNote, 
  CaseNoteCreate, 
  CaseNoteUpdate, 
  CaseNotesService, 
  NOTE_TYPE_CONFIG, 
  NOTE_CATEGORY_CONFIG 
} from "@/client/case-notes"
import useAuth from "@/hooks/useAuth"
import { toaster } from "@/components/ui/toaster"

interface NoteEditorProps {
  isOpen: boolean
  onClose: () => void
  caseId: string
  editNote?: CaseNote
  parentNoteId?: string
  onSuccess: () => void
}

const NoteEditor = ({ 
  isO<PERSON>, 
  onClose, 
  caseId, 
  editNote, 
  parentNoteId, 
  onSuccess 
}: NoteEditorProps) => {
  const { user } = useAuth()
  
  // Form state
  const [content, setContent] = useState("")
  const [noteType, setNoteType] = useState<string>("private")
  const [category, setCategory] = useState<string>("general")
  const [isPinned, setIsPinned] = useState(false)
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState("")
  const [isPreview, setIsPreview] = useState(false)

  const isEditing = !!editNote
  const isReply = !!parentNoteId

  // Initialize form with edit data
  useEffect(() => {
    if (editNote) {
      setContent(editNote.content)
      setNoteType(editNote.note_type)
      setCategory(editNote.category)
      setIsPinned(editNote.is_pinned)
      setTags(editNote.tags || [])
    } else {
      // Reset form for new note
      setContent("")
      setNoteType(isReply ? "team" : "private")
      setCategory("general")
      setIsPinned(false)
      setTags([])
    }
    setNewTag("")
    setIsPreview(false)
  }, [editNote, isReply, isOpen])

  // Get available note types based on user role
  const getAvailableNoteTypes = () => {
    const types = ["private", "team", "client"]
    if (user?.role === "admin" || user?.role === "lawyer") {
      types.push("admin")
    }
    types.push("task", "communication")
    return types
  }

  const availableTypes = getAvailableNoteTypes()

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: CaseNoteCreate) => CaseNotesService.createCaseNote(caseId, data),
    onSuccess: () => {
      toaster.create({
        title: "Note created",
        description: "Your note has been created successfully.",
        status: "success",
      })
      onSuccess()
      onClose()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create note",
        status: "error",
      })
    },
  })

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: CaseNoteUpdate) => 
      CaseNotesService.updateCaseNote(caseId, editNote!.id, data),
    onSuccess: () => {
      toaster.create({
        title: "Note updated",
        description: "Your note has been updated successfully.",
        status: "success",
      })
      onSuccess()
      onClose()
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update note",
        status: "error",
      })
    },
  })

  const handleSubmit = () => {
    if (!content.trim()) {
      toaster.create({
        title: "Error",
        description: "Note content cannot be empty",
        status: "error",
      })
      return
    }

    const noteData = {
      content: content.trim(),
      note_type: noteType as any,
      category: category as any,
      is_pinned: isPinned,
      tags: tags.length > 0 ? tags : undefined,
      parent_note_id: parentNoteId,
    }

    if (isEditing) {
      updateMutation.mutate(noteData)
    } else {
      createMutation.mutate(noteData)
    }
  }

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handleSubmit()
    }
  }

  const isLoading = createMutation.isPending || updateMutation.isPending

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent maxH="90vh">
        <ModalHeader>
          {isEditing ? "Edit Note" : isReply ? "Reply to Note" : "Add New Note"}
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {/* Note Type and Category */}
            <HStack spacing={4}>
              <FormControl>
                <FormLabel fontSize="sm">Type</FormLabel>
                <Select
                  value={noteType}
                  onChange={(e) => setNoteType(e.target.value)}
                  size="sm"
                >
                  {availableTypes.map((type) => {
                    const config = NOTE_TYPE_CONFIG[type as keyof typeof NOTE_TYPE_CONFIG]
                    return (
                      <option key={type} value={type}>
                        {config?.icon} {config?.label || type}
                      </option>
                    )
                  })}
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">Category</FormLabel>
                <Select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  size="sm"
                >
                  {Object.entries(NOTE_CATEGORY_CONFIG).map(([key, config]) => (
                    <option key={key} value={key}>
                      {config.icon} {config.label}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </HStack>

            {/* Pin toggle */}
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="pin-note" mb="0" fontSize="sm">
                Pin this note
              </FormLabel>
              <Switch
                id="pin-note"
                isChecked={isPinned}
                onChange={(e) => setIsPinned(e.target.checked)}
                size="sm"
              />
            </FormControl>

            {/* Content */}
            <FormControl>
              <Flex justify="space-between" align="center" mb={2}>
                <FormLabel fontSize="sm" mb={0}>Content</FormLabel>
                <Tooltip label={isPreview ? "Edit mode" : "Preview mode"}>
                  <IconButton
                    variant="ghost"
                    size="sm"
                    icon={<FiEye />}
                    onClick={() => setIsPreview(!isPreview)}
                    colorScheme={isPreview ? "blue" : "gray"}
                  />
                </Tooltip>
              </Flex>
              
              {isPreview ? (
                <Box
                  p={3}
                  border="1px solid"
                  borderColor="gray.200"
                  borderRadius="md"
                  minH="120px"
                  bg="gray.50"
                >
                  <Text whiteSpace="pre-wrap" lineHeight="1.6">
                    {content || "Nothing to preview..."}
                  </Text>
                </Box>
              ) : (
                <Textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Write your note here..."
                  rows={6}
                  onKeyDown={handleKeyPress}
                />
              )}
            </FormControl>

            {/* Tags */}
            <FormControl>
              <FormLabel fontSize="sm">Tags</FormLabel>
              <VStack align="stretch" spacing={2}>
                <HStack>
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag..."
                    size="sm"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault()
                        handleAddTag()
                      }
                    }}
                  />
                  <IconButton
                    variant="outline"
                    size="sm"
                    icon={<FiPlus />}
                    onClick={handleAddTag}
                    isDisabled={!newTag.trim()}
                  />
                </HStack>
                
                {tags.length > 0 && (
                  <Flex flexWrap="wrap" gap={2}>
                    {tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="subtle"
                        colorScheme="blue"
                        display="flex"
                        alignItems="center"
                        gap={1}
                      >
                        #{tag}
                        <IconButton
                          variant="ghost"
                          size="xs"
                          icon={<FiX />}
                          onClick={() => handleRemoveTag(tag)}
                          minW="auto"
                          h="auto"
                          p={0}
                        />
                      </Badge>
                    ))}
                  </Flex>
                )}
              </VStack>
            </FormControl>

            {/* Help text */}
            <Text fontSize="xs" color="gray.500">
              Tip: Use Cmd/Ctrl + Enter to save quickly
            </Text>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={isLoading}
              leftIcon={<FiSave />}
            >
              {isEditing ? "Update Note" : "Save Note"}
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default NoteEditor
