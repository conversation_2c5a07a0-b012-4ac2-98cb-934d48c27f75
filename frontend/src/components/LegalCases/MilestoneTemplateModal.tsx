import {
  VSta<PERSON>,
  H<PERSON>ta<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Box,
} from "@chakra-ui/react"
import { FiLayers } from "react-icons/fi"

interface MilestoneTemplateModalProps {
  isOpen: boolean
  onClose: () => void
  caseType?: string
}

const MilestoneTemplateModal = ({ 
  isOpen, 
  onClose, 
  caseType
}: MilestoneTemplateModalProps) => {
  if (!isOpen) return null

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg="rgba(0,0,0,0.5)"
      zIndex={1000}
      display="flex"
      alignItems="center"
      justifyContent="center"
      onClick={onClose}
    >
      <Box
        bg="white"
        borderRadius="lg"
        p={6}
        maxW="md"
        w="full"
        mx={4}
        onClick={(e) => e.stopPropagation()}
      >
        <VStack spacing={4} align="stretch">
          <HStack spacing={2}>
            <FiLayers />
            <Text fontSize="lg" fontWeight="bold">Milestone Templates</Text>
          </HStack>

          <Text>Milestone templates functionality coming soon...</Text>
          {caseType && (
            <Text fontSize="sm" color="gray.600">
              Case Type: {caseType}
            </Text>
          )}

          <HStack spacing={3} justify="flex-end">
            <Button onClick={onClose}>
              Close
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Box>
  )
}

export default MilestoneTemplateModal
