import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON>ck,
  Text,
  Avatar,
  Icon,
  <PERSON>lt<PERSON>,
  Separator
} from "@chakra-ui/react"
import {
  FiCircle,
  FiPlay,
  FiEye,
  FiCheck,
  FiArchive,
  FiClock
} from "react-icons/fi"
import CaseStatusBadge from "./CaseStatusBadge"

interface StatusHistoryItem {
  id: string
  old_status: string
  new_status: string
  changed_by: string
  changed_at: string
  notes?: string
  user_name?: string
  user_avatar?: string
}

interface StatusTimelineProps {
  history: StatusHistoryItem[]
  compact?: boolean
}

const getStatusIcon = (status: string) => {
  const statusStr = String(status || '').toLowerCase()
  switch (statusStr) {
    case 'open':
      return FiCircle
    case 'in_progress':
      return FiPlay
    case 'under_review':
      return FiEye
    case 'closed':
      return FiCheck
    case 'archived':
      return FiArchive
    default:
      return FiCircle
  }
}

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`

  return date.toLocaleDateString()
}

export default function StatusTimeline({ history, compact = false }: StatusTimelineProps) {
  if (!history || history.length === 0) {
    return (
      <Box p={4} textAlign="center" color="gray.500">
        <Icon as={FiClock} mb={2} />
        <Text fontSize="sm">No status changes yet</Text>
      </Box>
    )
  }

  return (
    <VStack align="stretch" spacing={compact ? 2 : 4}>
      {history.map((item, index) => (
        <Box key={item.id} position="relative">
          {/* Timeline line */}
          {index < history.length - 1 && (
            <Box
              position="absolute"
              left="20px"
              top="40px"
              bottom="-16px"
              width="2px"
              bg="gray.200"
              zIndex={0}
            />
          )}

          <HStack align="start" spacing={3}>
            {/* Avatar/Icon */}
            <Box position="relative" zIndex={1}>
              {item.user_avatar ? (
                <Avatar
                  size={compact ? "sm" : "md"}
                  src={item.user_avatar}
                  name={item.user_name || 'User'}
                />
              ) : (
                <Avatar
                  size={compact ? "sm" : "md"}
                  name={item.user_name || 'User'}
                  bg="gray.400"
                />
              )}
            </Box>

            {/* Content */}
            <VStack align="start" spacing={1} flex={1}>
              <HStack wrap="wrap" spacing={2}>
                <Text fontSize={compact ? "sm" : "md"} fontWeight="medium">
                  {item.user_name || 'User'}
                </Text>
                <Text fontSize={compact ? "xs" : "sm"} color="gray.500">
                  changed status from
                </Text>
                <CaseStatusBadge
                  status={item.old_status}
                  size="sm"
                  showIcon={true}
                  animated={false}
                />
                <Text fontSize={compact ? "xs" : "sm"} color="gray.500">
                  to
                </Text>
                <CaseStatusBadge
                  status={item.new_status}
                  size="sm"
                  showIcon={true}
                  animated={false}
                />
              </HStack>

              {item.notes && (
                <Box
                  bg="gray.50"
                  p={2}
                  borderRadius="md"
                  borderLeft="3px solid"
                  borderLeftColor="blue.400"
                  maxW="400px"
                >
                  <Text fontSize="sm" color="gray.700">
                    "{item.notes}"
                  </Text>
                </Box>
              )}

              <Tooltip label={new Date(item.changed_at).toLocaleString()}>
                <Text fontSize="xs" color="gray.400" cursor="help">
                  {formatTimeAgo(item.changed_at)}
                </Text>
              </Tooltip>
            </VStack>
          </HStack>

          {/* Separator between items */}
          {index < history.length - 1 && !compact && (
            <Separator mt={4} />
          )}
        </Box>
      ))}
    </VStack>
  )
}
