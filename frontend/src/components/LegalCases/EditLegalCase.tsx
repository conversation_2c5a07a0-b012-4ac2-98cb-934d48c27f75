import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"

import {
  Button,
  DialogActionTrigger,
  Input,
  VStack,
  Text,
} from "@chakra-ui/react"
import { useState } from "react"
import { FaExchangeAlt } from "react-icons/fa"

import { type LegalCaseUpdate, type LegalCasePublic, LegalCasesService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Field } from "../ui/field"

interface EditLegalCaseProps {
  legalCase: LegalCasePublic
}

const EditLegalCase = ({ legalCase }: EditLegalCaseProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<LegalCaseUpdate>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: legalCase,
  })

  const mutation = useMutation({
    mutationFn: (data: LegalCaseUpdate) =>
      LegalCasesService.updateLegalCase({
        legalCaseId: legalCase.id,
        requestBody: data
      }),
    onSuccess: () => {
      showSuccessToast("Legal case updated successfully.")
      reset()
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["legal-cases"] })
    },
  })

  const onSubmit: SubmitHandler<LegalCaseUpdate> = async (data) => {
    mutation.mutate(data)
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsOpen(open)}
    >
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <FaExchangeAlt fontSize="16px" />
          Edit Legal Case
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Edit Legal Case</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>Update the legal case details below.</Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.title}
                errorText={errors.title?.message}
                label="Case Title"
              >
                <Input
                  id="title"
                  {...register("title", {
                    required: "Case title is required",
                  })}
                  placeholder="Case Title"
                  type="text"
                />
              </Field>

              <Field
                required
                invalid={!!errors.client_name}
                errorText={errors.client_name?.message}
                label="Client Name"
              >
                <Input
                  id="client_name"
                  {...register("client_name", {
                    required: "Client name is required",
                  })}
                  placeholder="Client Name"
                  type="text"
                />
              </Field>
            </VStack>
          </DialogBody>

          <DialogFooter gap={2}>
            <DialogActionTrigger asChild>
              <Button
                variant="subtle"
                colorPalette="gray"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </DialogActionTrigger>
            <Button variant="solid" type="submit" loading={isSubmitting}>
              Save
            </Button>
          </DialogFooter>
          <DialogCloseTrigger />
        </form>
      </DialogContent>
    </DialogRoot>
  )
}

export default EditLegalCase
