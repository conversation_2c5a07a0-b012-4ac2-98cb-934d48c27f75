import {
  <PERSON><PERSON>,
  <PERSON>,
  Card,
  Card<PERSON>ody,
  <PERSON>ing,
  H<PERSON><PERSON>ck,
  Text,
  VStack,
  Spinner,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"

import { LegalCasesService } from "../../client"

interface SafeStatusHistoryProps {
  caseId: string
}

const SafeStatusHistory = ({ caseId }: SafeStatusHistoryProps) => {
  const { data: statusHistory, isLoading, error } = useQuery({
    queryKey: ["case-status-history", caseId],
    queryFn: () => LegalCasesService.readCaseStatusHistory({
      legalCaseId: caseId,
      skip: 0,
      limit: 10,
    }),
    enabled: !!caseId,
  })

  // Ultra-safe string conversion to prevent React error #130
  const safeText = (value: any): string => {
    if (value === null || value === undefined) return "N/A"
    if (typeof value === "string") return value
    if (typeof value === "number") return value.toString()
    if (typeof value === "boolean") return value ? "Yes" : "No"
    try {
      return String(value)
    } catch {
      return "N/A"
    }
  }

  // Safe date formatting
  const safeFormatDate = (dateString: string): string => {
    try {
      if (!dateString || dateString === "N/A") return "Unknown date"
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return "Invalid date"
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    } catch (error) {
      return "Unknown date"
    }
  }

  if (isLoading) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box textAlign="center" py={8}>
          <Spinner size="lg" />
          <Text mt={2}>Loading status history...</Text>
        </Box>
      </Box>
    )
  }

  if (error) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box p={4} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
          <Text color="red.600">
            Failed to load status history
          </Text>
        </Box>
      </Box>
    )
  }

  if (!statusHistory?.data || statusHistory.data.length === 0) {
    return (
      <Box>
        <Heading size="md" mb={4}>Status History</Heading>
        <Box p={4} bg="gray.50" borderRadius="md" border="1px solid" borderColor="gray.200">
          <Text color="gray.600" textAlign="center">
            No status changes recorded yet
          </Text>
        </Box>
      </Box>
    )
  }

  return (
    <Box>
      <Heading size="md" mb={4}>Status History</Heading>
      <VStack spacing={3} align="stretch">
        {statusHistory.data.map((history, index) => {
          // Extract all values safely
          const historyId = safeText(history?.id) || `history-${index}`
          const oldStatus = safeText(history?.old_status) || ""
          const newStatus = safeText(history?.new_status) || "UNKNOWN"
          const changedAt = safeText(history?.changed_at) || ""
          const changedByUser = safeText(history?.changed_by_user?.full_name) || "Unknown User"
          const notes = safeText(history?.notes) || ""

          // Status color mapping
          const getStatusColor = (status: string): string => {
            const statusLower = status.toLowerCase()
            switch (statusLower) {
              case "open": return "blue"
              case "in_progress": return "orange"
              case "under_review": return "purple"
              case "closed": return "green"
              case "archived": return "gray"
              default: return "gray"
            }
          }

          return (
            <Card key={historyId} size="sm">
              <CardBody>
                <VStack spacing={2} align="stretch">
                  <HStack justify="space-between" align="center">
                    <HStack spacing={2}>
                      {oldStatus && (
                        <>
                          <Badge colorScheme={getStatusColor(oldStatus)} variant="outline">
                            {oldStatus}
                          </Badge>
                          <Text fontSize="sm" color="gray.500">→</Text>
                        </>
                      )}
                      <Badge colorScheme={getStatusColor(newStatus)} variant="solid">
                        {newStatus}
                      </Badge>
                    </HStack>
                    <Text fontSize="sm" color="gray.500">
                      {safeFormatDate(changedAt)}
                    </Text>
                  </HStack>

                  <HStack justify="space-between" align="center">
                    <Text fontSize="sm" color="gray.600">
                      Changed by: {changedByUser}
                    </Text>
                  </HStack>

                  {notes && notes !== "N/A" && (
                    <Box mt={2} p={2} bg="gray.50" borderRadius="md">
                      <Text fontSize="sm" color="gray.700">
                        <strong>Notes:</strong> {notes}
                      </Text>
                    </Box>
                  )}
                </VStack>
              </CardBody>
            </Card>
          )
        })}
      </VStack>

      {/* Debug info */}
      <Box mt={4} p={2} bg="green.50" borderRadius="md" border="1px solid" borderColor="green.200">
        <Text fontSize="xs" color="green.600" textAlign="center">
          ✅ Safe Status History - No React Error #130
        </Text>
      </Box>
    </Box>
  )
}

export default SafeStatusHistory
