/**
=========================================================
* TunLeg Simple Theme Configurator
=========================================================

* Simplified theme configurator that works reliably
* Basic dark/light mode and theme variant switching

=========================================================
*/

import React, { useState } from 'react';
import {
  Drawer,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Divider,
  IconButton,
  Paper,
  Stack,
  Chip,
  Button,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Close as CloseIcon,
  Palette as PaletteIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Save as SaveIcon,
  Refresh as ResetIcon,
} from '@mui/icons-material';

import {
  useMaterialUIController,
  setOpenConfigurator,
  setDarkMode,
  setThemeVariant,
} from '../../themes/context';

const SimpleThemeConfigurator: React.FC = () => {
  const { state, dispatch } = useMaterialUIController();
  const { openConfigurator, darkMode, themeVariant } = state;
  
  // Local state for UI feedback
  const [showSuccess, setShowSuccess] = useState(false);

  const handleCloseConfigurator = () => {
    setOpenConfigurator(dispatch, false);
  };

  const handleDarkModeToggle = () => {
    setDarkMode(dispatch, !darkMode);
    setShowSuccess(true);
  };

  const handleThemeVariantChange = (variant: 'default' | 'corporate' | 'modern' | 'classic') => {
    setThemeVariant(dispatch, variant);
    setShowSuccess(true);
  };

  const handleSavePreferences = () => {
    // Save to localStorage
    const preferences = {
      darkMode,
      themeVariant,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem('tunleg-theme-preferences', JSON.stringify(preferences));
    setShowSuccess(true);
  };

  const handleResetToDefaults = () => {
    setDarkMode(dispatch, false);
    setThemeVariant(dispatch, 'default');
    localStorage.removeItem('tunleg-theme-preferences');
    setShowSuccess(true);
  };

  const themeVariants = [
    { value: 'default', label: 'Par Défaut', color: 'primary' },
    { value: 'corporate', label: 'Corporate', color: 'secondary' },
    { value: 'modern', label: 'Moderne', color: 'success' },
    { value: 'classic', label: 'Classique', color: 'warning' },
  ] as const;

  return (
    <>
      <Drawer
        anchor="right"
        open={openConfigurator}
        onClose={handleCloseConfigurator}
        sx={{
          '& .MuiDrawer-paper': {
            width: 360,
            boxSizing: 'border-box',
            p: 0,
          },
        }}
      >
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Header */}
          <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PaletteIcon color="primary" />
                <Typography variant="h6">
                  Configurateur de Thème
                </Typography>
              </Box>
              <IconButton onClick={handleCloseConfigurator} size="small">
                <CloseIcon />
              </IconButton>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Personnalisez l'apparence de TunLeg
            </Typography>
          </Box>

          {/* Content */}
          <Box sx={{ flex: 1, p: 3, overflow: 'auto' }}>
            <Stack spacing={4}>
              {/* Dark Mode Toggle */}
              <Paper elevation={1} sx={{ p: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  🌙 Mode d'Affichage
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={darkMode}
                      onChange={handleDarkModeToggle}
                      color="primary"
                    />
                  }
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {darkMode ? <DarkModeIcon /> : <LightModeIcon />}
                      {darkMode ? 'Mode Sombre' : 'Mode Clair'}
                    </Box>
                  }
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Basculez entre le mode clair et sombre
                </Typography>
              </Paper>

              {/* Theme Variants */}
              <Paper elevation={1} sx={{ p: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  🎨 Variantes de Thème
                </Typography>
                <Stack spacing={2}>
                  {themeVariants.map((variant) => (
                    <Box key={variant.value}>
                      <Chip
                        label={variant.label}
                        color={variant.color}
                        variant={themeVariant === variant.value ? 'filled' : 'outlined'}
                        onClick={() => handleThemeVariantChange(variant.value)}
                        sx={{ 
                          width: '100%', 
                          justifyContent: 'flex-start',
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: 'action.hover',
                          },
                        }}
                      />
                    </Box>
                  ))}
                </Stack>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  Variante actuelle: <strong>{themeVariants.find(v => v.value === themeVariant)?.label}</strong>
                </Typography>
              </Paper>

              {/* Current Status */}
              <Paper elevation={1} sx={{ p: 3, bgcolor: 'background.default' }}>
                <Typography variant="subtitle1" gutterBottom>
                  📊 État Actuel
                </Typography>
                <Stack spacing={1}>
                  <Typography variant="body2">
                    <strong>Mode:</strong> {darkMode ? 'Sombre' : 'Clair'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Variante:</strong> {themeVariants.find(v => v.value === themeVariant)?.label}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Système:</strong> TunLeg Material-UI
                  </Typography>
                </Stack>
              </Paper>
            </Stack>
          </Box>

          {/* Footer Actions */}
          <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
            <Stack spacing={2}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSavePreferences}
                fullWidth
              >
                Sauvegarder les Préférences
              </Button>
              <Button
                variant="outlined"
                startIcon={<ResetIcon />}
                onClick={handleResetToDefaults}
                fullWidth
              >
                Réinitialiser
              </Button>
            </Stack>
          </Box>
        </Box>
      </Drawer>

      {/* Success Snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={3000}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert 
          onClose={() => setShowSuccess(false)} 
          severity="success" 
          sx={{ width: '100%' }}
        >
          Préférences de thème mises à jour !
        </Alert>
      </Snackbar>
    </>
  );
};

export default SimpleThemeConfigurator;
