import React, { useState } from "react"
import {
  <PERSON>,
  Flex,
  Text,
  VStack,
  H<PERSON><PERSON><PERSON>,
  Badge,
  useBreakpointValue,
} from "@chakra-ui/react"
import { Tabs } from "@chakra-ui/react"
import { FiUser, FiSettings, FiShield, FiBell } from "react-icons/fi"

import type { UserPublic } from "@/client"

import { Avatar } from "../ui/avatar"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog"

import { useQueryClient } from "@tanstack/react-query"

interface UserProfileModalProps {
  children: React.ReactNode
}

export const UserProfileModal: React.FC<UserProfileModalProps> = ({
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("profile")
  const queryClient = useQueryClient()
  const currentUser = queryClient.getQueryData<UserPublic>(["currentUser"])


  // Responsive modal size
  const modalSize = useBreakpointValue({ base: "full", md: "xl" }) as "full" | "xl"
  const tabOrientation = useBreakpointValue({ base: "horizontal", md: "vertical" }) as "horizontal" | "vertical"

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'admin': return 'red'
      case 'lawyer': return 'blue'
      case 'assistant': return 'green'
      case 'client': return 'purple'
      default: return 'gray'
    }
  }

  const tabs = [
    { id: "profile", label: "Profile", icon: FiUser },
    { id: "settings", label: "Settings", icon: FiSettings },
    { id: "security", label: "Security", icon: FiShield },
    { id: "notifications", label: "Notifications", icon: FiBell },
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case "profile":
        return (
          <VStack align="start" gap={4} w="full">
            <Box>
              <Text fontSize="sm" color="gray.600" mb={1}>
                Full Name
              </Text>
              <Text fontWeight="medium">
                {currentUser?.full_name || "Not provided"}
              </Text>
            </Box>

            <Box>
              <Text fontSize="sm" color="gray.600" mb={1}>
                Email
              </Text>
              <Text fontWeight="medium">{currentUser?.email}</Text>
            </Box>

            <Box>
              <Text fontSize="sm" color="gray.600" mb={1}>
                Role
              </Text>
              <Badge colorScheme={getRoleColor(currentUser?.role)} size="sm">
                {currentUser?.role?.toUpperCase()}
              </Badge>
            </Box>

            <Box>
              <Text fontSize="sm" color="gray.600" mb={1}>
                Status
              </Text>
              <Badge
                colorScheme={currentUser?.is_active ? "green" : "red"}
                size="sm"
              >
                {currentUser?.is_active ? "Active" : "Inactive"}
              </Badge>
            </Box>
          </VStack>
        )

      case "settings":
        return (
          <VStack align="start" gap={4} w="full">
            <Text color="gray.600">Settings panel coming soon...</Text>
          </VStack>
        )

      case "security":
        return (
          <VStack align="start" gap={4} w="full">
            <Text color="gray.600">Security settings coming soon...</Text>
          </VStack>
        )

      case "notifications":
        return (
          <VStack align="start" gap={4} w="full">
            <Text color="gray.600">Notification preferences coming soon...</Text>
          </VStack>
        )

      default:
        return null
    }
  }

  return (
    <DialogRoot
      open={isOpen}
      onOpenChange={(e) => setIsOpen(e.open)}
      size={modalSize}
    >
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>User Profile</DialogTitle>
          <DialogCloseTrigger />
        </DialogHeader>

        <DialogBody>
          <Flex
            direction={{ base: "column", md: "row" }}
            gap={6}
            h={{ base: "auto", md: "400px" }}
          >
            {/* User Info Header */}
            <VStack
              align="center"
              gap={4}
              minW={{ base: "full", md: "200px" }}
              pb={{ base: 4, md: 0 }}
              borderBottom={{ base: "1px", md: "none" }}
              borderRight={{ base: "none", md: "1px" }}
              borderColor="gray.200"
            >
              <Avatar
                size="xl"
                name={currentUser?.full_name || currentUser?.email}
              />

              <VStack gap={1} textAlign="center">
                <Text fontWeight="bold" fontSize="lg">
                  {currentUser?.full_name || "User"}
                </Text>
                <Text fontSize="sm" color="gray.600">
                  {currentUser?.email}
                </Text>
                <Badge colorScheme={getRoleColor(currentUser?.role)} size="sm">
                  {currentUser?.role?.toUpperCase()}
                </Badge>
              </VStack>
            </VStack>

            {/* Tabs Content */}
            <Box flex="1">
              <Tabs.Root
                value={activeTab}
                onValueChange={(e) => setActiveTab(e.value)}
                orientation={tabOrientation}
                variant="line"
              >
                <Tabs.List>
                  {tabs.map((tab) => (
                    <Tabs.Trigger key={tab.id} value={tab.id}>
                      <HStack gap={2}>
                        <tab.icon size={16} />
                        <Text>{tab.label}</Text>
                      </HStack>
                    </Tabs.Trigger>
                  ))}
                </Tabs.List>

                <Tabs.Content value={activeTab} pt={4}>
                  {renderTabContent()}
                </Tabs.Content>
              </Tabs.Root>
            </Box>
          </Flex>
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  )
}

export default UserProfileModal
