/**
=========================================================
* EquiNova Material Activity Feed
=========================================================

* Material-UI version of activity feed component
* Migrated from Chakra UI to Material Design

=========================================================
*/

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Box,
  Chip,
  Divider,
  Button,
} from '@mui/material';
import {
  Gavel as GavelIcon,
  Person as PersonIcon,
  Upload as UploadIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';

import useAuth from '@/hooks/useAuth';

interface ActivityItem {
  id: string;
  type: 'case_created' | 'case_updated' | 'document_uploaded' | 'user_added' | 'case_closed';
  title: string;
  description: string;
  timestamp: string;
  user: string;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}

const MaterialActivityFeed: React.FC = () => {
  const { user: currentUser } = useAuth();

  // Mock activity data - in real app, this would come from an API
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'case_created',
      title: 'New Legal Case Created',
      description: 'Contract Review for ABC Corporation',
      timestamp: '2 hours ago',
      user: currentUser?.full_name || 'John Doe',
      icon: <GavelIcon />,
      color: 'primary',
    },
    {
      id: '2',
      type: 'document_uploaded',
      title: 'Document Uploaded',
      description: 'Contract_v2.pdf added to case #123',
      timestamp: '4 hours ago',
      user: 'Sarah Wilson',
      icon: <UploadIcon />,
      color: 'secondary',
    },
    {
      id: '3',
      type: 'case_updated',
      title: 'Case Status Updated',
      description: 'Intellectual Property case moved to In Progress',
      timestamp: '6 hours ago',
      user: 'Mike Johnson',
      icon: <EditIcon />,
      color: 'warning',
    },
    {
      id: '4',
      type: 'case_closed',
      title: 'Case Completed',
      description: 'Employment dispute successfully resolved',
      timestamp: '1 day ago',
      user: 'Emily Davis',
      icon: <CheckCircleIcon />,
      color: 'success',
    },
    {
      id: '5',
      type: 'user_added',
      title: 'New Team Member',
      description: 'Alex Thompson joined as Legal Assistant',
      timestamp: '2 days ago',
      user: 'Admin',
      icon: <PersonIcon />,
      color: 'info',
    },
  ];

  const getActivityIcon = (activity: ActivityItem) => {
    return (
      <Avatar
        sx={{
          bgcolor: `${activity.color}.main`,
          width: 40,
          height: 40,
        }}
      >
        {activity.icon}
      </Avatar>
    );
  };

  const formatTimestamp = (timestamp: string) => {
    // In a real app, you'd use a proper date formatting library
    return timestamp;
  };

  return (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" fontWeight="medium">
            Recent Activity
          </Typography>
          <Button
            variant="text"
            size="small"
            endIcon={<ViewIcon />}
            color="primary"
          >
            View All
          </Button>
        </Box>

        <Typography variant="body2" color="text.secondary" mb={3}>
          Latest updates and activities across your legal cases
        </Typography>

        <List disablePadding>
          {activities.map((activity, index) => (
            <React.Fragment key={activity.id}>
              <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                <ListItemAvatar>
                  {getActivityIcon(activity)}
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {activity.title}
                      </Typography>
                      <Chip
                        label={activity.type.replace('_', ' ')}
                        size="small"
                        variant="outlined"
                        color={activity.color}
                        sx={{ 
                          textTransform: 'capitalize',
                          fontSize: '0.7rem',
                          height: 20,
                        }}
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.primary" mb={0.5}>
                        {activity.description}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="caption" color="text.secondary">
                          by {activity.user}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTimestamp(activity.timestamp)}
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
              {index < activities.length - 1 && (
                <Divider variant="inset" component="li" sx={{ ml: 7 }} />
              )}
            </React.Fragment>
          ))}
        </List>

        {activities.length === 0 && (
          <Box textAlign="center" py={4}>
            <Typography variant="body2" color="text.secondary">
              No recent activity to display.
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default MaterialActivityFeed;
