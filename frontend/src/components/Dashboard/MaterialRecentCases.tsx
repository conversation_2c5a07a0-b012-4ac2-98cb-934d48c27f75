/**
=========================================================
* EquiNova Material Recent Cases
=========================================================

* Material-UI version of recent cases component
* Migrated from Chakra UI to Material Design

=========================================================
*/

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Box,
  Button,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Add as AddIcon,
} from '@mui/icons-material';

import { useQuery } from '@tanstack/react-query';
import { LegalCasesService } from '@/client';

const MaterialRecentCases: React.FC = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['recent-legal-cases'],
    queryFn: () => LegalCasesService.readLegalCases({ skip: 0, limit: 5 }),
  });

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'info';
      case 'in_progress':
        return 'warning';
      case 'closed':
        return 'success';
      case 'on_hold':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Legal Cases
          </Typography>
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Legal Cases
          </Typography>
          <Alert severity="error">
            Failed to load recent cases. Please try again later.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const cases = data?.data || [];

  return (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" fontWeight="medium">
            Recent Legal Cases
          </Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            href="/legal-cases"
          >
            View All
          </Button>
        </Box>

        {cases.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Typography variant="body2" color="text.secondary">
              No legal cases found. Create your first case to get started.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              sx={{ mt: 2 }}
              href="/legal-cases"
            >
              Create Case
            </Button>
          </Box>
        ) : (
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Title</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cases.map((legalCase) => (
                  <TableRow key={legalCase.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {legalCase.title}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {legalCase.client_name || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={legalCase.status?.replace('_', ' ') || 'Unknown'}
                        color={getStatusColor(legalCase.status || '') as any}
                        size="small"
                        sx={{ textTransform: 'capitalize' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={legalCase.priority || 'Medium'}
                        color={getPriorityColor(legalCase.priority || '') as any}
                        size="small"
                        variant="outlined"
                        sx={{ textTransform: 'capitalize' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(legalCase.created_at)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Box display="flex" gap={0.5} justifyContent="flex-end">
                        <Button
                          size="small"
                          startIcon={<ViewIcon />}
                          href={`/legal-cases/${legalCase.id}`}
                        >
                          View
                        </Button>
                        <Button
                          size="small"
                          startIcon={<EditIcon />}
                          href={`/legal-cases/${legalCase.id}`}
                        >
                          Edit
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </CardContent>
    </Card>
  );
};

export default MaterialRecentCases;
