import { useQuery } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import {
  Heading,
  VStack,
  Text,
  Badge,
  Button,
  Flex,
  Box,
  Skeleton,
} from "@chakra-ui/react"
import { <PERSON>a<PERSON>ye, FaArrowRight } from "react-icons/fa"

import { LegalCasesService, type LegalCasePublic } from "@/client"

interface RecentCaseItemProps {
  case_: LegalCasePublic
}

const RecentCaseItem = ({ case_ }: RecentCaseItemProps) => {
  const navigate = useNavigate()

  const handleViewCase = () => {
    navigate({ to: "/legal-cases" })
  }

  // Ultra-safe string conversion to prevent React error #130
  const safeString = (value: any): string => {
    if (value === null || value === undefined) return ""
    if (typeof value === "string") return value
    if (typeof value === "number" || typeof value === "boolean") return String(value)
    try {
      return String(value)
    } catch {
      return ""
    }
  }

  const formatDate = (dateString?: string) => {
    try {
      if (!dateString) return 'No date'
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return 'Invalid date'
      return date.toLocaleDateString()
    } catch {
      return 'No date'
    }
  }

  const getCaseTypeColor = (caseType: string) => {
    const colors: Record<string, string> = {
      'civil': 'blue',
      'criminal': 'red',
      'family': 'green',
      'corporate': 'purple',
      'immigration': 'orange',
      'personal_injury': 'yellow',
      'real_estate': 'teal',
      'employment': 'pink',
    }
    return colors[safeString(caseType)] || 'gray'
  }

  // Safely extract case data
  const caseTitle = safeString(case_?.title) || "Untitled Case"
  const clientName = safeString(case_?.client_name) || "Unknown Client"
  const caseType = safeString(case_?.case_type) || "other"
  const openingDate = safeString(case_?.opening_date)
  const caseId = safeString(case_?.id)

  return (
    <Box
      p={4}
      borderWidth="1px"
      borderRadius="md"
      _hover={{ bg: "gray.50", cursor: "pointer" }}
      onClick={handleViewCase}
    >
      <Flex justify="space-between" align="start" mb={2}>
        <VStack align="start" gap={1} flex={1}>
          <Text fontWeight="semibold" fontSize="sm" truncate>
            {caseTitle}
          </Text>
          <Text fontSize="xs" color="gray.600" truncate>
            Client: {clientName}
          </Text>
        </VStack>
        <Badge colorScheme={getCaseTypeColor(caseType)} size="sm">
          {caseType.replace('_', ' ')}
        </Badge>
      </Flex>

      <Flex justify="space-between" align="center">
        <Text fontSize="xs" color="gray.500">
          {formatDate(openingDate)}
        </Text>
        <Button size="xs" variant="ghost" colorScheme="blue">
          <FaEye />
        </Button>
      </Flex>
    </Box>
  )
}

const RecentCases = () => {
  const navigate = useNavigate()

  const { data: recentCases, isLoading } = useQuery({
    queryKey: ["recent-cases-dashboard"],
    queryFn: () => LegalCasesService.readLegalCases({
      limit: 5,
      sortBy: "opening_date",
      sortOrder: "desc"
    }),
  })

  const handleViewAllCases = () => {
    navigate({ to: "/legal-cases" })
  }

  return (
    <Box
      p={6}
      bg="white"
      borderRadius="lg"
      borderWidth="1px"
      borderColor="gray.200"
      shadow="sm"
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">Recent Cases</Heading>
        <Button
          size="sm"
          variant="ghost"
          colorScheme="blue"
          onClick={handleViewAllCases}
        >
          View All <FaArrowRight style={{ marginLeft: '8px' }} />
        </Button>
      </Flex>
        {isLoading ? (
          <VStack gap={3}>
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} height="80px" width="100%" />
            ))}
          </VStack>
        ) : recentCases?.data?.length ? (
          <VStack gap={3} align="stretch">
            {recentCases.data.map((case_, index) => (
              <RecentCaseItem key={String(case_?.id || `case-${index}`)} case_={case_} />
            ))}
          </VStack>
        ) : (
          <Box textAlign="center" py={8}>
            <Text color="gray.500" fontSize="sm">
              No recent cases found
            </Text>
            <Button
              mt={4}
              size="sm"
              colorScheme="blue"
              onClick={() => navigate({ to: "/legal-cases" })}
            >
              Create Your First Case
            </Button>
          </Box>
        )}
    </Box>
  )
}

export default RecentCases
