import { useQuery } from "@tanstack/react-query"
import React from "react"
import {
  Heading,
  VStack,
  Text,
  Box,
  Flex,
  Skeleton,
  Button,
} from "@chakra-ui/react"
import {
  FaGavel,
  <PERSON>a<PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON>,
  FaArrowRight
} from "react-icons/fa"

import { LegalCasesService, UsersService, type LegalCasePublic, type UserPublic } from "@/client"
import useAuth from "@/hooks/useAuth"

interface ActivityItem {
  id: string
  type: 'case_created' | 'case_updated' | 'user_created' | 'user_assigned'
  title: string
  description: string
  timestamp: string
  icon: React.ElementType
  color: string
}

const ActivityItemComponent = ({ item }: { item: ActivityItem }) => {
  const formatTimeAgo = (dateString: string) => {
    try {
      if (!dateString) return "Unknown time"
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return "Invalid date"

      const now = new Date()
      const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

      if (diffInHours < 1) return "Just now"
      if (diffInHours < 24) return `${diffInHours}h ago`
      if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
      return date.toLocaleDateString()
    } catch (error) {
      return "Unknown time"
    }
  }

  return (
    <Flex gap={3} p={3} borderRadius="md" _hover={{ bg: "gray.50" }}>
      <Box
        p={2}
        borderRadius="full"
        bg={`${item.color}.100`}
        color={`${item.color}.600`}
        flexShrink={0}
      >
        <Box fontSize="sm">
          {React.createElement(item.icon)}
        </Box>
      </Box>

      <VStack align="start" gap={1} flex={1}>
        <Text fontSize="sm" fontWeight="medium">
          {String(item.title || "")}
        </Text>
        <Text fontSize="xs" color="gray.600">
          {String(item.description || "")}
        </Text>
        <Text fontSize="xs" color="gray.500">
          {formatTimeAgo(String(item.timestamp || ""))}
        </Text>
      </VStack>
    </Flex>
  )
}

const ActivityFeed = () => {
  const { user } = useAuth()

  // Get recent cases for activity
  const { data: recentCases, isLoading: casesLoading } = useQuery({
    queryKey: ["activity-cases"],
    queryFn: () => LegalCasesService.readLegalCases({
      limit: 10,
      sortBy: "opening_date",
      sortOrder: "desc"
    }),
  })

  // Get recent users for activity (admin only)
  const { data: recentUsers, isLoading: usersLoading } = useQuery({
    queryKey: ["activity-users"],
    queryFn: () => UsersService.readUsers({
      limit: 5,
      skip: 0
    }),
    enabled: user?.is_superuser || false,
  })

  const isLoading = casesLoading || usersLoading

  // Ultra-safe string conversion to prevent React error #130
  const safeString = (value: any): string => {
    if (value === null || value === undefined) return ""
    if (typeof value === "string") return value
    if (typeof value === "number" || typeof value === "boolean") return String(value)
    try {
      return String(value)
    } catch {
      return ""
    }
  }

  // Generate activity items from recent data
  const generateActivityItems = (): ActivityItem[] => {
    const activities: ActivityItem[] = []

    // Add case activities
    if (recentCases?.data) {
      recentCases.data.slice(0, 5).forEach((case_: LegalCasePublic, index) => {
        const caseId = safeString(case_?.id) || `case-${index}`
        const caseTitle = safeString(case_?.title) || "Untitled Case"
        const clientName = safeString(case_?.client_name) || "Unknown Client"
        const openingDate = safeString(case_?.opening_date) || new Date().toISOString()

        activities.push({
          id: `case-${caseId}`,
          type: 'case_created',
          title: 'New case created',
          description: `${caseTitle} for ${clientName}`,
          timestamp: openingDate,
          icon: FaGavel,
          color: 'blue'
        })
      })
    }

    // Add user activities (admin only)
    if (user?.is_superuser && recentUsers?.data) {
      recentUsers.data.slice(0, 3).forEach((user_: UserPublic, index) => {
        const userId = safeString(user_?.id) || `user-${index}`
        const userFullName = safeString(user_?.full_name) || ""
        const userEmail = safeString(user_?.email) || "Unknown Email"
        const userRole = safeString(user_?.role) || "user"
        const displayName = userFullName || userEmail

        activities.push({
          id: `user-${userId}`,
          type: 'user_created',
          title: 'New user registered',
          description: `${displayName} joined as ${userRole}`,
          timestamp: new Date().toISOString(), // Since UserPublic doesn't have created_at, use current time
          icon: FaUser,
          color: 'green'
        })
      })
    }

    // Sort by timestamp (most recent first)
    return activities.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    ).slice(0, 8)
  }

  const activityItems = generateActivityItems()

  return (
    <Box
      p={6}
      bg="white"
      borderRadius="lg"
      borderWidth="1px"
      borderColor="gray.200"
      shadow="sm"
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">Recent Activity</Heading>
        <Button
          size="sm"
          variant="ghost"
          colorScheme="blue"
        >
          View All <FaArrowRight style={{ marginLeft: '8px' }} />
        </Button>
      </Flex>
        {isLoading ? (
          <VStack gap={3}>
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} height="60px" width="100%" />
            ))}
          </VStack>
        ) : activityItems.length > 0 ? (
          <VStack gap={2} align="stretch">
            {activityItems.map((item) => (
              <ActivityItemComponent key={item.id} item={item} />
            ))}
          </VStack>
        ) : (
          <Box textAlign="center" py={8}>
            <Box fontSize="3xl" color="gray.300" mb={4}>
              {React.createElement(FaClock)}
            </Box>
            <Text color="gray.500" fontSize="sm">
              No recent activity
            </Text>
            <Text color="gray.400" fontSize="xs" mt={1}>
              Activity will appear here as you use the system
            </Text>
          </Box>
        )}
    </Box>
  )
}

export default ActivityFeed
