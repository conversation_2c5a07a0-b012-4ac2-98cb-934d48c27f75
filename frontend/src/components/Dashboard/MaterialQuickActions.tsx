/**
=========================================================
* EquiNova Material Quick Actions
=========================================================

* Material-UI version of quick actions component
* Migrated from Chakra UI to Material Design

=========================================================
*/

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Box,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Upload as UploadIcon,
  Search as SearchIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  BarChart as ReportsIcon,
  Gavel as GavelIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';

import useAuth from '@/hooks/useAuth';

interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  roles?: string[];
}

const QuickActionButton: React.FC<QuickActionProps> = ({
  title,
  description,
  icon,
  href,
  color,
}) => {
  return (
    <Button
      variant="outlined"
      fullWidth
      href={href}
      sx={{
        p: 2,
        height: 'auto',
        flexDirection: 'column',
        alignItems: 'flex-start',
        textAlign: 'left',
        borderColor: `${color}.main`,
        '&:hover': {
          borderColor: `${color}.main`,
          backgroundColor: `${color}.50`,
        },
      }}
    >
      <Box display="flex" alignItems="center" gap={1} mb={1} width="100%">
        <Box color={`${color}.main`}>
          {icon}
        </Box>
        <Typography variant="subtitle2" fontWeight="medium" color={`${color}.main`}>
          {title}
        </Typography>
      </Box>
      <Typography variant="body2" color="text.secondary" textAlign="left">
        {description}
      </Typography>
    </Button>
  );
};

const MaterialQuickActions: React.FC = () => {
  const { user: currentUser } = useAuth();

  // Filter actions based on user role
  const getFilteredActions = (actions: QuickActionProps[]) => {
    return actions.filter(action => {
      if (action.roles) {
        return currentUser?.role && action.roles.includes(currentUser.role) || currentUser?.is_superuser;
      }
      return true;
    });
  };

  const primaryActions: QuickActionProps[] = [
    {
      title: 'New Legal Case',
      description: 'Create a new legal case and start managing it',
      icon: <GavelIcon />,
      href: '/legal-cases',
      color: 'primary',
      roles: ['admin', 'lawyer', 'assistant'],
    },
    {
      title: 'Upload Documents',
      description: 'Upload and organize case documents',
      icon: <UploadIcon />,
      href: '/documents',
      color: 'secondary',
    },
    {
      title: 'Search Cases',
      description: 'Find and review existing legal cases',
      icon: <SearchIcon />,
      href: '/search',
      color: 'info',
    },
    {
      title: 'Add Item',
      description: 'Create a new item in the system',
      icon: <AssignmentIcon />,
      href: '/items',
      color: 'success',
    },
  ];

  const adminActions: QuickActionProps[] = [
    {
      title: 'Manage Users',
      description: 'Add, edit, and manage user accounts',
      icon: <PeopleIcon />,
      href: '/admin',
      color: 'warning',
      roles: ['admin'],
    },
    {
      title: 'View Reports',
      description: 'Generate and view system reports',
      icon: <ReportsIcon />,
      href: '/reports',
      color: 'error',
      roles: ['admin', 'lawyer'],
    },
  ];

  const settingsActions: QuickActionProps[] = [
    {
      title: 'User Settings',
      description: 'Manage your profile and preferences',
      icon: <SettingsIcon />,
      href: '/settings',
      color: 'info',
    },
  ];

  const filteredPrimaryActions = getFilteredActions(primaryActions);
  const filteredAdminActions = getFilteredActions(adminActions);
  const filteredSettingsActions = getFilteredActions(settingsActions);

  return (
    <Card elevation={2}>
      <CardContent>
        <Typography variant="h6" fontWeight="medium" gutterBottom>
          Quick Actions
        </Typography>
        <Typography variant="body2" color="text.secondary" mb={3}>
          Common tasks and shortcuts to help you work efficiently
        </Typography>

        {/* Primary Actions */}
        {filteredPrimaryActions.length > 0 && (
          <Box mb={3}>
            <Typography variant="subtitle2" color="text.secondary" mb={2}>
              Main Actions
            </Typography>
            <Grid container spacing={2}>
              {filteredPrimaryActions.map((action, index) => (
                <Grid item xs={12} sm={6} key={index}>
                  <QuickActionButton {...action} />
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Admin Actions */}
        {filteredAdminActions.length > 0 && (
          <>
            <Divider sx={{ my: 2 }} />
            <Box mb={3}>
              <Typography variant="subtitle2" color="text.secondary" mb={2}>
                Administration
              </Typography>
              <Grid container spacing={2}>
                {filteredAdminActions.map((action, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <QuickActionButton {...action} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          </>
        )}

        {/* Settings Actions */}
        {filteredSettingsActions.length > 0 && (
          <>
            <Divider sx={{ my: 2 }} />
            <Box>
              <Typography variant="subtitle2" color="text.secondary" mb={2}>
                Settings
              </Typography>
              <Grid container spacing={2}>
                {filteredSettingsActions.map((action, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <QuickActionButton {...action} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default MaterialQuickActions;
