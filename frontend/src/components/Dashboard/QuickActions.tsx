import React from "react"
import { useNavigate } from "@tanstack/react-router"
import {
  Heading,
  SimpleGrid,
  Button,
  VStack,
  Text,
  Box,
} from "@chakra-ui/react"
import {
  FaGavel,
  FaFileUpload,
  FaUsers,
  FaUserPlus,
  FaCog,
  FaSearch
} from "react-icons/fa"

import useAuth from "@/hooks/useAuth"

interface QuickActionProps {
  title: string
  description: string
  icon: React.ElementType
  color: string
  onClick: () => void
}

const QuickAction = ({ title, description, icon, color, onClick }: QuickActionProps) => (
  <Button
    height="auto"
    p={4}
    variant="outline"
    colorScheme={color}
    onClick={onClick}
    _hover={{
      transform: "translateY(-2px)",
      shadow: "md",
      borderColor: `${color}.400`
    }}
    transition="all 0.2s"
  >
    <VStack gap={2}>
      <Box
        p={3}
        borderRadius="full"
        bg={`${color}.100`}
        color={`${color}.600`}
      >
        <Box fontSize="xl">
          {React.createElement(icon)}
        </Box>
      </Box>
      <VStack gap={1}>
        <Text fontWeight="semibold" fontSize="sm">
          {title}
        </Text>
        <Text fontSize="xs" color="gray.600" textAlign="center">
          {description}
        </Text>
      </VStack>
    </VStack>
  </Button>
)

const QuickActions = () => {
  const navigate = useNavigate()
  const { user } = useAuth()

  const actions = [
    {
      title: "New Case",
      description: "Create a new legal case",
      icon: FaGavel,
      color: "blue",
      onClick: () => navigate({ to: "/legal-cases" }),
      roles: ["admin", "lawyer"]
    },
    {
      title: "Upload Document",
      description: "Upload case documents",
      icon: FaFileUpload,
      color: "green",
      onClick: () => navigate({ to: "/documents" }),
      roles: ["admin", "lawyer", "assistant"]
    },
    {
      title: "Add User",
      description: "Create new user account",
      icon: FaUserPlus,
      color: "purple",
      onClick: () => navigate({ to: "/admin" }),
      roles: ["admin"]
    },
    {
      title: "Manage Users",
      description: "View and manage users",
      icon: FaUsers,
      color: "orange",
      onClick: () => navigate({ to: "/admin" }),
      roles: ["admin"]
    },
    {
      title: "Search Cases",
      description: "Find specific cases",
      icon: FaSearch,
      color: "teal",
      onClick: () => navigate({ to: "/legal-cases" }),
      roles: ["admin", "lawyer", "client", "assistant"]
    },
    {
      title: "Settings",
      description: "Account preferences",
      icon: FaCog,
      color: "gray",
      onClick: () => navigate({ to: "/settings" }),
      roles: ["admin", "lawyer", "client", "assistant"]
    }
  ]

  // Filter actions based on user role
  const userRole = user?.is_superuser ? "admin" : user?.role || "client"
  const availableActions = actions.filter(action =>
    action.roles.includes(userRole)
  )

  return (
    <Box
      p={6}
      bg="white"
      borderRadius="lg"
      borderWidth="1px"
      borderColor="gray.200"
      shadow="sm"
    >
      <Heading size="md" mb={4}>Quick Actions</Heading>

      <SimpleGrid columns={{ base: 2, md: 3 }} gap={4}>
        {availableActions.map((action) => (
          <QuickAction
            key={action.title}
            title={action.title}
            description={action.description}
            icon={action.icon}
            color={action.color}
            onClick={action.onClick}
          />
        ))}
      </SimpleGrid>
    </Box>
  )
}

export default QuickActions
