import React from "react"
import { useQuery } from "@tanstack/react-query"
import {
  Box,
  Flex,
  Heading,
  Text,
  VStack,
  Skeleton,
  SimpleGrid,
} from "@chakra-ui/react"
import { Progress } from "../ui/progress"
import {
  FaGavel,
  FaUsers,
  FaFileAlt,
  FaClock,
  FaChartLine,
  FaUserTie,
  FaUserFriends,
  FaCalendarAlt,
  FaArrowUp,
  FaExclamationTriangle,
  FaCheckCircle
} from "react-icons/fa"

import { LegalCasesService, UsersService } from "@/client"
import useAuth from "@/hooks/useAuth"

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ElementType
  color: string
  isLoading?: boolean
  subtitle?: string
  trend?: {
    value: number
    isPositive: boolean
  }
  progress?: {
    value: number
    max: number
    label?: string
  }
}

const StatCard = ({
  title,
  value,
  icon,
  color,
  isLoading,
  subtitle,
  trend,
  progress
}: StatCardProps) => (
  <Box
    p={6}
    bg="white"
    borderRadius="lg"
    borderWidth="1px"
    borderColor="gray.200"
    shadow="sm"
    _hover={{ shadow: "md" }}
    transition="shadow 0.2s"
  >
    <Flex align="center" justify="space-between" mb={3}>
      <VStack align="start" gap={1} flex={1}>
        <Text fontSize="sm" color="gray.500">
          {title}
        </Text>
        {isLoading ? (
          <Skeleton height="32px" width="60px" />
        ) : (
          <Heading size="lg" color={`${color}.600`}>
            {value}
          </Heading>
        )}
        {subtitle && (
          <Text fontSize="xs" color="gray.400">
            {subtitle}
          </Text>
        )}
      </VStack>
      <Box
        p={3}
        borderRadius="full"
        bg={`${color}.100`}
        color={`${color}.600`}
      >
        <Box fontSize="xl">
          {React.createElement(icon)}
        </Box>
      </Box>
    </Flex>

    {/* Trend indicator */}
    {trend && !isLoading && (
      <Flex align="center" gap={2} mb={2}>
        <Box color={trend.isPositive ? "green.500" : "red.500"}>
          <FaArrowUp style={{
            transform: trend.isPositive ? 'none' : 'rotate(180deg)',
            fontSize: '12px'
          }} />
        </Box>
        <Text
          fontSize="xs"
          color={trend.isPositive ? "green.500" : "red.500"}
          fontWeight="medium"
        >
          {trend.isPositive ? '+' : ''}{trend.value}%
        </Text>
        <Text fontSize="xs" color="gray.500">
          vs last month
        </Text>
      </Flex>
    )}

    {/* Progress bar */}
    {progress && !isLoading && (
      <VStack align="stretch" gap={1}>
        <Flex justify="space-between" align="center">
          <Text fontSize="xs" color="gray.500">
            {progress.label || 'Progress'}
          </Text>
          <Text fontSize="xs" color="gray.600" fontWeight="medium">
            {progress.value}/{progress.max}
          </Text>
        </Flex>
        <Progress
          value={(progress.value / progress.max) * 100}
          colorScheme={color}
          size="sm"
          borderRadius="full"
        />
      </VStack>
    )}
  </Box>
)

const StatisticsCards = () => {
  const { user } = useAuth()

  // Get total legal cases
  const { data: allCases, isLoading: casesLoading } = useQuery({
    queryKey: ["legal-cases-stats"],
    queryFn: () => LegalCasesService.readLegalCases({ limit: 1000 }),
  })

  // Get recent cases (last 30 days)
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

  const { data: recentCases, isLoading: recentCasesLoading } = useQuery({
    queryKey: ["recent-cases-stats"],
    queryFn: () => LegalCasesService.readLegalCases({
      limit: 1000,
      dateFrom: thirtyDaysAgo.toISOString().split('T')[0]
    }),
  })

  // Get previous month cases for trend calculation
  const sixtyDaysAgo = new Date()
  sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60)

  const { data: previousMonthCases, isLoading: previousMonthLoading } = useQuery({
    queryKey: ["previous-month-cases-stats"],
    queryFn: () => LegalCasesService.readLegalCases({
      limit: 1000,
      dateFrom: sixtyDaysAgo.toISOString().split('T')[0],
      dateTo: thirtyDaysAgo.toISOString().split('T')[0]
    }),
  })

  // Get users statistics (only for admins)
  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ["users-stats"],
    queryFn: () => UsersService.readUsers({ limit: 1000 }),
    enabled: user?.is_superuser || false,
  })

  // Get unassigned users (only for admins)
  const { data: unassignedUsers, isLoading: unassignedLoading } = useQuery({
    queryKey: ["unassigned-users-stats"],
    queryFn: () => UsersService.getUnassignedUsers(),
    enabled: user?.is_superuser || false,
  })

  const totalCases = allCases?.count || 0
  const recentCasesCount = recentCases?.count || 0
  const previousMonthCasesCount = previousMonthCases?.count || 0
  const totalUsers = users?.count || 0
  const unassignedCount = unassignedUsers?.count || 0

  // Calculate active cases (all cases are considered active since there's no closing_date field yet)
  const activeCases = allCases?.count || 0

  // Calculate trends
  const casesTrend = previousMonthCasesCount > 0
    ? Math.round(((recentCasesCount - previousMonthCasesCount) / previousMonthCasesCount) * 100)
    : recentCasesCount > 0 ? 100 : 0

  // Ultra-safe string conversion to prevent React error #130
  const safeString = (value: any): string => {
    if (value === null || value === undefined) return ""
    if (typeof value === "string") return value
    if (typeof value === "number" || typeof value === "boolean") return String(value)
    try {
      return String(value)
    } catch {
      return ""
    }
  }

  // Get role-specific statistics
  const getRoleSpecificStats = () => {
    if (user?.is_superuser) {
      // Admin sees system-wide stats - safely filter users
      const lawyerCount = users?.data?.filter(u => safeString(u?.role) === 'lawyer').length || 0
      const clientCount = users?.data?.filter(u => safeString(u?.role) === 'client').length || 0
      const assistantCount = users?.data?.filter(u => safeString(u?.role) === 'assistant').length || 0

      return {
        lawyers: lawyerCount,
        clients: clientCount,
        assistants: assistantCount,
        totalUsers: totalUsers
      }
    } else if (safeString(user?.role) === 'lawyer') {
      // Lawyers see their assigned users and cases
      return {
        myCases: totalCases, // This will be filtered by the API based on lawyer
        myClients: 0, // Would need API endpoint to get lawyer's clients
        recentActivity: recentCasesCount
      }
    }
    return {}
  }

  const roleStats = getRoleSpecificStats()

  const isLoading = casesLoading || recentCasesLoading || previousMonthLoading || usersLoading || unassignedLoading

  // Render different cards based on user role
  const renderAdminCards = () => (
    <>
      <StatCard
        title="Total Cases"
        value={totalCases}
        subtitle="All legal cases in system"
        icon={FaGavel}
        color="blue"
        isLoading={isLoading}
        trend={{
          value: Math.abs(casesTrend),
          isPositive: casesTrend >= 0
        }}
      />

      <StatCard
        title="Active Cases"
        value={activeCases}
        subtitle="Currently open cases"
        icon={FaFileAlt}
        color="green"
        isLoading={isLoading}
        progress={{
          value: activeCases,
          max: totalCases || 1,
          label: "Active vs Total"
        }}
      />

      <StatCard
        title="Total Users"
        value={totalUsers}
        subtitle={`${String(roleStats.lawyers || 0)} lawyers, ${String(roleStats.clients || 0)} clients`}
        icon={FaUsers}
        color="purple"
        isLoading={isLoading}
        progress={{
          value: totalUsers - unassignedCount,
          max: totalUsers || 1,
          label: "Assigned users"
        }}
      />

      <StatCard
        title="System Health"
        value={unassignedCount === 0 ? "Excellent" : "Needs Attention"}
        subtitle={`${String(unassignedCount)} unassigned users`}
        icon={unassignedCount === 0 ? FaCheckCircle : FaExclamationTriangle}
        color={unassignedCount === 0 ? "green" : "red"}
        isLoading={isLoading}
      />

      <StatCard
        title="Lawyers"
        value={String(roleStats.lawyers || 0)}
        subtitle="Active legal professionals"
        icon={FaUserTie}
        color="teal"
        isLoading={isLoading}
      />

      <StatCard
        title="Clients"
        value={String(roleStats.clients || 0)}
        subtitle="Registered clients"
        icon={FaUserFriends}
        color="orange"
        isLoading={isLoading}
      />
    </>
  )

  const renderLawyerCards = () => (
    <>
      <StatCard
        title="My Cases"
        value={totalCases}
        subtitle="Cases assigned to you"
        icon={FaGavel}
        color="blue"
        isLoading={isLoading}
        trend={{
          value: Math.abs(casesTrend),
          isPositive: casesTrend >= 0
        }}
      />

      <StatCard
        title="Recent Activity"
        value={recentCasesCount}
        subtitle="New cases this month"
        icon={FaClock}
        color="green"
        isLoading={isLoading}
      />

      <StatCard
        title="Case Load"
        value={activeCases > 10 ? "High" : activeCases > 5 ? "Medium" : "Light"}
        subtitle={`${activeCases} active cases`}
        icon={FaChartLine}
        color={activeCases > 10 ? "red" : activeCases > 5 ? "orange" : "green"}
        isLoading={isLoading}
        progress={{
          value: activeCases,
          max: 15,
          label: "Workload capacity"
        }}
      />

      <StatCard
        title="This Month"
        value={recentCasesCount}
        subtitle="Cases opened in last 30 days"
        icon={FaCalendarAlt}
        color="purple"
        isLoading={isLoading}
      />
    </>
  )

  const renderClientCards = () => (
    <>
      <StatCard
        title="My Cases"
        value={totalCases}
        subtitle="Your legal matters"
        icon={FaGavel}
        color="blue"
        isLoading={isLoading}
      />

      <StatCard
        title="Recent Updates"
        value={recentCasesCount}
        subtitle="Activity this month"
        icon={FaClock}
        color="green"
        isLoading={isLoading}
      />
    </>
  )

  const renderAssistantCards = () => (
    <>
      <StatCard
        title="Assigned Cases"
        value={totalCases}
        subtitle="Cases you're supporting"
        icon={FaGavel}
        color="blue"
        isLoading={isLoading}
      />

      <StatCard
        title="Recent Activity"
        value={recentCasesCount}
        subtitle="Updates this month"
        icon={FaClock}
        color="green"
        isLoading={isLoading}
      />

      <StatCard
        title="Workload"
        value={activeCases > 8 ? "Busy" : activeCases > 4 ? "Moderate" : "Light"}
        subtitle={`Supporting ${activeCases} cases`}
        icon={FaChartLine}
        color={activeCases > 8 ? "orange" : "green"}
        isLoading={isLoading}
      />
    </>
  )

  return (
    <SimpleGrid columns={{ base: 1, md: 2, lg: user?.is_superuser ? 3 : 2, xl: user?.is_superuser ? 4 : 4 }} gap={6}>
      {user?.is_superuser && renderAdminCards()}
      {user?.role === 'lawyer' && renderLawyerCards()}
      {user?.role === 'client' && renderClientCards()}
      {user?.role === 'assistant' && renderAssistantCards()}
    </SimpleGrid>
  )
}

export default StatisticsCards
