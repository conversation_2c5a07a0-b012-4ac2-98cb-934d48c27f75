/**
=========================================================
* EquiNova Material Statistics Cards
=========================================================

* Material-UI version of dashboard statistics cards
* Migrated from Chakra UI to Material Design

=========================================================
*/

import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Gavel as GavelIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';

import { useQuery } from '@tanstack/react-query';
import { LegalCasesService } from '@/client';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ReactNode;
  color: 'primary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: string;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  subtitle, 
  icon, 
  color, 
  trend 
}) => {
  const colorMap = {
    primary: '#1976d2',
    success: '#2e7d32',
    warning: '#ed6c02',
    error: '#d32f2f',
    info: '#0288d1',
  };

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" gap={2}>
          <Box
            sx={{
              width: 56,
              height: 56,
              borderRadius: 2,
              backgroundColor: `${color}.main`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
            }}
          >
            {icon}
          </Box>
          <Box flex={1}>
            <Typography variant="h4" fontWeight="bold" color={`${color}.main`}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            {trend && (
              <Box display="flex" alignItems="center" gap={0.5} mt={0.5}>
                <Typography 
                  variant="caption" 
                  color={trend.isPositive ? 'success.main' : 'error.main'}
                  fontWeight="medium"
                >
                  {trend.isPositive ? '↗' : '↘'} {trend.value}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  vs last month
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

const MaterialStatisticsCards: React.FC = () => {
  // Fetch legal cases data
  const { data: legalCasesData } = useQuery({
    queryKey: ['legal-cases-stats'],
    queryFn: () => LegalCasesService.readLegalCases({ skip: 0, limit: 1000 }),
  });

  const totalCases = legalCasesData?.count || 0;
  const cases = legalCasesData?.data || [];
  
  // Calculate statistics
  const activeCases = cases.filter(c => c.status === 'open' || c.status === 'in_progress').length;
  const completedCases = cases.filter(c => c.status === 'closed').length;
  const urgentCases = cases.filter(c => c.priority === 'urgent').length;

  const stats = [
    {
      title: 'Active Legal Cases',
      value: activeCases,
      subtitle: 'Currently in progress',
      icon: <GavelIcon />,
      color: 'primary' as const,
      trend: {
        value: '+12%',
        isPositive: true,
      },
    },
    {
      title: 'Total Cases',
      value: totalCases,
      subtitle: 'All time cases',
      icon: <AssignmentIcon />,
      color: 'info' as const,
      trend: {
        value: '+8%',
        isPositive: true,
      },
    },
    {
      title: 'Completed Cases',
      value: completedCases,
      subtitle: 'Successfully closed',
      icon: <CheckCircleIcon />,
      color: 'success' as const,
      trend: {
        value: '+15%',
        isPositive: true,
      },
    },
    {
      title: 'Urgent Cases',
      value: urgentCases,
      subtitle: 'Require immediate attention',
      icon: <TrendingUpIcon />,
      color: urgentCases > 5 ? 'error' : 'warning' as const,
      trend: {
        value: urgentCases > 5 ? '+3%' : '-2%',
        isPositive: urgentCases <= 5,
      },
    },
  ];

  return (
    <Grid container spacing={3}>
      {stats.map((stat, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <StatCard {...stat} />
        </Grid>
      ))}
    </Grid>
  );
};

export default MaterialStatisticsCards;
