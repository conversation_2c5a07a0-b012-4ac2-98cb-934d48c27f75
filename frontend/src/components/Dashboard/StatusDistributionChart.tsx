import { Box, Text, VStack, HStack, Progress, Badge, Spinner } from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { useMemo } from "react"
import { LegalCasesService } from "@/client"

interface StatusDistributionChartProps {
  size?: "sm" | "md" | "lg"
  showPercentages?: boolean
  showCounts?: boolean
  animated?: boolean
}

interface StatusCount {
  status: string
  count: number
  percentage: number
  color: string
  label: string
}

const StatusDistributionChart = ({
  size = "md",
  showPercentages = true,
  showCounts = true,
  animated = true
}: StatusDistributionChartProps) => {

  // Fetch all legal cases to calculate distribution
  const { data: casesData, isLoading, error } = useQuery({
    queryKey: ["legal-cases", "status-distribution"],
    queryFn: () => LegalCasesService.readLegalCases({
      skip: 0,
      limit: 1000, // Get all cases for accurate distribution
    }),
  })

  const statusDistribution = useMemo(() => {
    if (!casesData?.data) return []

    const statusConfig: Record<string, { color: string; label: string }> = {
      open: { color: "blue", label: "Open" },
      in_progress: { color: "orange", label: "In Progress" },
      under_review: { color: "purple", label: "Under Review" },
      closed: { color: "green", label: "Closed" },
      archived: { color: "gray", label: "Archived" }
    }

    // Count cases by status - safely convert all values
    const statusCounts: Record<string, number> = {}
    casesData.data.forEach(case_ => {
      // Safely extract status and convert to string
      const status = String(case_?.status || "unknown").toLowerCase()
      statusCounts[status] = (statusCounts[status] || 0) + 1
    })

    const totalCases = casesData.data.length

    // Convert to array with percentages - ensure all values are safe strings
    const distribution: StatusCount[] = Object.entries(statusCounts).map(([status, count]) => ({
      status: String(status),
      count: Number(count) || 0,
      percentage: totalCases > 0 ? Math.round((count / totalCases) * 100) : 0,
      color: String(statusConfig[status]?.color || "gray"),
      label: String(statusConfig[status]?.label || status)
    }))

    // Sort by count (descending)
    return distribution.sort((a, b) => b.count - a.count)
  }, [casesData])

  const sizeConfig = {
    sm: { height: "6px", fontSize: "xs", spacing: 2 },
    md: { height: "8px", fontSize: "sm", spacing: 3 },
    lg: { height: "12px", fontSize: "md", spacing: 4 }
  }

  const config = sizeConfig[size]

  if (isLoading) {
    return (
      <Box textAlign="center" py={8}>
        <Spinner size="lg" />
        <Text mt={2} fontSize={config.fontSize}>Loading status distribution...</Text>
      </Box>
    )
  }

  if (error) {
    return (
      <Box p={4} bg="red.50" borderRadius="md" border="1px solid" borderColor="red.200">
        <Text color="red.600" fontSize={config.fontSize}>
          Failed to load status distribution
        </Text>
      </Box>
    )
  }

  if (statusDistribution.length === 0) {
    return (
      <Box textAlign="center" py={8}>
        <Text color="gray.500" fontSize={config.fontSize}>
          No cases found
        </Text>
      </Box>
    )
  }

  const totalCases = statusDistribution.reduce((sum, item) => sum + item.count, 0)

  return (
    <VStack spacing={config.spacing} align="stretch">
      <HStack justify="space-between" align="center">
        <Text fontSize={size === "lg" ? "lg" : "md"} fontWeight="bold" color="gray.700">
          Case Status Distribution
        </Text>
        <Badge colorScheme="blue" variant="subtle">
          {totalCases} total cases
        </Badge>
      </HStack>

      <VStack spacing={config.spacing} align="stretch">
        {statusDistribution.map((item) => (
          <Box key={String(item.status)}>
            <HStack justify="space-between" align="center" mb={1}>
              <HStack spacing={2}>
                <Badge colorScheme={String(item.color)} variant="solid" size="sm">
                  {String(item.label)}
                </Badge>
                {showCounts && (
                  <Text fontSize={config.fontSize} color="gray.600">
                    {String(item.count)} cases
                  </Text>
                )}
              </HStack>
              {showPercentages && (
                <Text fontSize={config.fontSize} fontWeight="medium" color="gray.700">
                  {String(item.percentage)}%
                </Text>
              )}
            </HStack>

            <Progress
              value={Number(item.percentage) || 0}
              colorScheme={String(item.color)}
              size={size}
              height={config.height}
              borderRadius="full"
              bg="gray.100"
              transition={animated ? "all 0.3s ease-in-out" : "none"}
              title={`${String(item.label)}: ${String(item.count)} cases (${String(item.percentage)}%)`}
            />
          </Box>
        ))}
      </VStack>

      {/* Summary stats */}
      <Box pt={2} borderTop="1px solid" borderColor="gray.200">
        <HStack justify="space-between" fontSize={config.fontSize} color="gray.600">
          <Text>Most common:</Text>
          <Text fontWeight="medium">
            {String(statusDistribution[0]?.label || "None")} ({String(statusDistribution[0]?.percentage || 0)}%)
          </Text>
        </HStack>
      </Box>
    </VStack>
  )
}

export default StatusDistributionChart
