import React, { useState, useRef } from 'react';
import { Button } from './ui/button';

interface UploadResponse {
  message: string;
  filename: string;
  file_id: string;
  content_type: string;
  size: number;
  uploaded_by: string;
}

interface UploadState {
  status: 'idle' | 'uploading' | 'success' | 'error';
  progress: number;
  message: string;
  response?: UploadResponse;
}

const DocumentUpload: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [uploadState, setUploadState] = useState<UploadState>({
    status: 'idle',
    progress: 0,
    message: '',
  });
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const ALLOWED_TYPES = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const MAX_SIZE = 50 * 1024 * 1024; // 50MB

  const validateFile = (selectedFile: File): string | null => {
    if (!ALLOWED_TYPES.includes(selectedFile.type)) {
      return 'Invalid file type. Only PDF and DOCX files are allowed.';
    }
    if (selectedFile.size > MAX_SIZE) {
      return 'File size exceeds 50MB limit.';
    }
    return null;
  };

  const handleFileSelect = (selectedFile: File) => {
    const error = validateFile(selectedFile);
    if (error) {
      setUploadState({
        status: 'error',
        progress: 0,
        message: error,
      });
      return;
    }

    setFile(selectedFile);
    setUploadState({
      status: 'idle',
      progress: 0,
      message: '',
    });
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      handleFileSelect(event.target.files[0]);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragOver(false);

    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      handleFileSelect(event.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type === 'application/pdf') return '📄';
    if (type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return '📝';
    return '📄';
  };

  const handleUpload = async () => {
    if (!file) {
      setUploadState({
        status: 'error',
        progress: 0,
        message: 'No file selected.',
      });
      return;
    }

    setUploadState({
      status: 'uploading',
      progress: 0,
      message: 'Uploading...',
    });

    const formData = new FormData();
    formData.append('file', file);

    try {
      // Get the token from localStorage or wherever it's stored
      const token = localStorage.getItem('access_token');

      const response = await fetch(`http://localhost:8000/api/v1/documents/upload/`, {
        method: 'POST',
        headers: {
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: formData,
      });

      if (response.ok) {
        const result: UploadResponse = await response.json();
        setUploadState({
          status: 'success',
          progress: 100,
          message: 'File uploaded successfully!',
          response: result,
        });
      } else {
        const errorData = await response.json();
        setUploadState({
          status: 'error',
          progress: 0,
          message: errorData.detail || 'Upload failed.',
        });
      }
    } catch (error) {
      setUploadState({
        status: 'error',
        progress: 0,
        message: 'An error occurred during upload.',
      });
    }
  };

  const resetUpload = () => {
    setFile(null);
    setUploadState({
      status: 'idle',
      progress: 0,
      message: '',
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div style={{ maxWidth: '500px', margin: '0 auto', padding: '24px' }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        <h2 style={{ fontSize: '24px', fontWeight: 'bold', textAlign: 'center', margin: 0 }}>
          Upload Document
        </h2>

        {/* Upload Area */}
        <div
          style={{
            width: '100%',
            height: '200px',
            border: `2px dashed ${isDragOver ? '#3182ce' : '#e2e8f0'}`,
            borderRadius: '8px',
            backgroundColor: isDragOver ? '#ebf8ff' : '#f7fafc',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            transition: 'all 0.2s',
          }}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => fileInputRef.current?.click()}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '12px' }}>
            <div style={{ fontSize: '32px' }}>📤</div>
            <div style={{ textAlign: 'center', color: '#718096' }}>
              Drop your PDF or DOCX file here, or{' '}
              <span style={{ color: '#3182ce', fontWeight: '500' }}>
                click to browse
              </span>
            </div>
            <div style={{ fontSize: '14px', color: '#a0aec0' }}>
              Maximum file size: 50MB
            </div>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf,.docx"
          onChange={handleFileChange}
          style={{ display: 'none' }}
        />

        {/* Selected File Info */}
        {file && (
          <div style={{ width: '100%', padding: '16px', backgroundColor: '#f7fafc', borderRadius: '6px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{ fontSize: '24px' }}>{getFileIcon(file.type)}</div>
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: '500', marginBottom: '4px' }}>
                  {file.name}
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{
                    backgroundColor: '#3182ce',
                    color: 'white',
                    padding: '2px 8px',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}>
                    {file.type === 'application/pdf' ? 'PDF' : 'DOCX'}
                  </span>
                  <span style={{ fontSize: '14px', color: '#718096' }}>
                    {formatFileSize(file.size)}
                  </span>
                </div>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={resetUpload}
                disabled={uploadState.status === 'uploading'}
              >
                ✕
              </Button>
            </div>
          </div>
        )}

        {/* Upload Progress */}
        {uploadState.status === 'uploading' && (
          <div style={{ width: '100%' }}>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: '#e2e8f0',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${uploadState.progress}%`,
                height: '100%',
                backgroundColor: '#3182ce',
                transition: 'width 0.3s ease'
              }} />
            </div>
            <div style={{ marginTop: '8px', textAlign: 'center', fontSize: '14px', color: '#718096' }}>
              {uploadState.message}
            </div>
          </div>
        )}

        {/* Status Messages */}
        {uploadState.status === 'success' && (
          <div style={{
            padding: '16px',
            backgroundColor: '#f0fff4',
            border: '1px solid #9ae6b4',
            borderRadius: '6px',
            color: '#22543d'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>✅ Success!</div>
            <div>{uploadState.message}</div>
            {uploadState.response && (
              <div style={{ fontSize: '14px', marginTop: '4px', color: '#4a5568' }}>
                File ID: {uploadState.response.file_id}
              </div>
            )}
          </div>
        )}

        {uploadState.status === 'error' && (
          <div style={{
            padding: '16px',
            backgroundColor: '#fed7d7',
            border: '1px solid #fc8181',
            borderRadius: '6px',
            color: '#742a2a'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>❌ Error!</div>
            <div>{uploadState.message}</div>
          </div>
        )}

        {/* Upload Button */}
        <div style={{ display: 'flex', gap: '12px' }}>
          <Button
            style={{ flex: 1 }}
            onClick={handleUpload}
            disabled={!file || uploadState.status === 'uploading'}
          >
            {uploadState.status === 'uploading' ? 'Uploading...' : '📤 Upload Document'}
          </Button>

          {uploadState.status === 'success' && (
            <Button variant="outline" onClick={resetUpload}>
              Upload Another
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentUpload;