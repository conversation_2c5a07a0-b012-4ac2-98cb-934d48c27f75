import React, { useState } from 'react'
import {
  Box,
  Container,
  <PERSON>ing,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Switch,
  Select,
  Input,
  Button,
  Spinner,
  Alert,
  Icon,
  Divider,
  useColorModeValue,
  SimpleGrid,
  FormControl,
  FormLabel,
  FormHelperText,
  Checkbox,
  CheckboxGroup,
  Stack,
} from '@chakra-ui/react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  FiBell,
  FiMail,
  FiSmartphone,
  FiMonitor,
  FiSave,
  FiArrowLeft,
} from 'react-icons/fi'

import {
  NotificationsService,
  NotificationPreferencesPublic,
  NotificationType,
  NotificationPriority,
  NotificationChannel,
  NotificationFrequency,
  getNotificationTypeLabel,
  getNotificationChannelLabel,
  getNotificationFrequencyLabel,
} from '@/client/notifications'
import useCustomToast from '@/hooks/useCustomToast'
import { useColorModeValue } from '@/components/ui/color-mode'

const NotificationPreferences: React.FC = () => {
  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const cardBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  // Fetch notification preferences
  const {
    data: preferencesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['notification-preferences'],
    queryFn: () => NotificationsService.getNotificationPreferences(),
    onError: (error: any) => {
      toast.error('Failed to load notification preferences')
      console.error('Preferences error:', error)
    },
  })

  // Update preference mutation
  const updatePreferenceMutation = useMutation({
    mutationFn: ({
      preferenceId,
      preference,
    }: {
      preferenceId: string
      preference: any
    }) =>
      NotificationsService.updateNotificationPreference({
        preferenceId,
        preference,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notification-preferences'] })
      toast.success('Preferences updated successfully')
    },
    onError: () => {
      toast.error('Failed to update preferences')
    },
  })

  const handlePreferenceUpdate = (
    preference: NotificationPreferencesPublic,
    updates: any
  ) => {
    updatePreferenceMutation.mutate({
      preferenceId: preference.id,
      preference: updates,
    })
  }

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case NotificationChannel.IN_APP:
        return FiMonitor
      case NotificationChannel.EMAIL:
        return FiMail
      case NotificationChannel.SMS:
        return FiSmartphone
      case NotificationChannel.PUSH:
        return FiBell
      default:
        return FiBell
    }
  }

  if (isLoading) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack spacing={8}>
          <Heading size="lg">Notification Preferences</Heading>
          <Spinner size="xl" />
          <Text>Loading preferences...</Text>
        </VStack>
      </Container>
    )
  }

  if (error) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack spacing={8}>
          <Heading size="lg">Notification Preferences</Heading>
          <Alert status="error">
            Failed to load notification preferences. Please try again later.
          </Alert>
          <Button onClick={() => refetch()}>Retry</Button>
        </VStack>
      </Container>
    )
  }

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Box>
            <Heading size="lg" mb={2}>
              Notification Preferences
            </Heading>
            <Text color="gray.600">
              Customize how and when you receive notifications
            </Text>
          </Box>
          
          <Button
            variant="outline"
            leftIcon={<FiArrowLeft />}
            onClick={() => {
              window.location.href = '/notifications'
            }}
          >
            Back to Notifications
          </Button>
        </HStack>

        {/* Global Settings */}
        <Card bg={cardBg} borderColor={borderColor} variant="outline">
          <CardHeader>
            <Heading size="md">Global Settings</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={6} align="stretch">
              <Alert status="info">
                <Text fontSize="sm">
                  These settings apply to all notification types. You can override them for specific types below.
                </Text>
              </Alert>
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                <FormControl>
                  <FormLabel>Default Channels</FormLabel>
                  <CheckboxGroup defaultValue={[NotificationChannel.IN_APP]}>
                    <Stack spacing={3}>
                      {Object.entries(NotificationChannel).map(([key, value]) => (
                        <Checkbox key={value} value={value}>
                          <HStack spacing={2}>
                            <Icon as={getChannelIcon(value)} />
                            <Text>{getNotificationChannelLabel(value)}</Text>
                          </HStack>
                        </Checkbox>
                      ))}
                    </Stack>
                  </CheckboxGroup>
                  <FormHelperText>
                    Select your preferred notification channels
                  </FormHelperText>
                </FormControl>

                <FormControl>
                  <FormLabel>Quiet Hours</FormLabel>
                  <VStack spacing={3} align="stretch">
                    <HStack spacing={2}>
                      <Input
                        type="time"
                        placeholder="Start time"
                        size="sm"
                      />
                      <Text fontSize="sm" color="gray.500">to</Text>
                      <Input
                        type="time"
                        placeholder="End time"
                        size="sm"
                      />
                    </HStack>
                  </VStack>
                  <FormHelperText>
                    No notifications during these hours (except urgent)
                  </FormHelperText>
                </FormControl>
              </SimpleGrid>
            </VStack>
          </CardBody>
        </Card>

        {/* Notification Type Preferences */}
        <Card bg={cardBg} borderColor={borderColor} variant="outline">
          <CardHeader>
            <Heading size="md">Notification Type Settings</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={6} align="stretch">
              {preferencesData?.data.map((preference) => (
                <NotificationPreferenceCard
                  key={preference.id}
                  preference={preference}
                  onUpdate={(updates) => handlePreferenceUpdate(preference, updates)}
                  isUpdating={updatePreferenceMutation.isLoading}
                />
              ))}
            </VStack>
          </CardBody>
        </Card>

        {/* Help Section */}
        <Card bg={cardBg} borderColor={borderColor} variant="outline">
          <CardHeader>
            <Heading size="md">Help & Information</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontWeight="medium" mb={2}>Notification Channels:</Text>
                <VStack spacing={2} align="start">
                  <HStack>
                    <Icon as={FiMonitor} color="blue.500" />
                    <Text fontSize="sm"><strong>In-App:</strong> Notifications appear in the application</Text>
                  </HStack>
                  <HStack>
                    <Icon as={FiMail} color="green.500" />
                    <Text fontSize="sm"><strong>Email:</strong> Notifications sent to your email address</Text>
                  </HStack>
                  <HStack>
                    <Icon as={FiSmartphone} color="orange.500" />
                    <Text fontSize="sm"><strong>SMS:</strong> Text messages to your phone (coming soon)</Text>
                  </HStack>
                  <HStack>
                    <Icon as={FiBell} color="purple.500" />
                    <Text fontSize="sm"><strong>Push:</strong> Browser push notifications (coming soon)</Text>
                  </HStack>
                </VStack>
              </Box>
              
              <Divider />
              
              <Box>
                <Text fontWeight="medium" mb={2}>Frequency Options:</Text>
                <VStack spacing={2} align="start">
                  <Text fontSize="sm"><strong>Immediate:</strong> Receive notifications as they happen</Text>
                  <Text fontSize="sm"><strong>Daily Digest:</strong> One summary email per day</Text>
                  <Text fontSize="sm"><strong>Weekly Digest:</strong> One summary email per week</Text>
                  <Text fontSize="sm"><strong>Never:</strong> Disable notifications for this type</Text>
                </VStack>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

interface NotificationPreferenceCardProps {
  preference: NotificationPreferencesPublic
  onUpdate: (updates: any) => void
  isUpdating: boolean
}

const NotificationPreferenceCard: React.FC<NotificationPreferenceCardProps> = ({
  preference,
  onUpdate,
  isUpdating,
}) => {
  const cardBg = useColorModeValue('gray.50', 'gray.700')

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case NotificationChannel.IN_APP:
        return FiMonitor
      case NotificationChannel.EMAIL:
        return FiMail
      case NotificationChannel.SMS:
        return FiSmartphone
      case NotificationChannel.PUSH:
        return FiBell
      default:
        return FiBell
    }
  }

  return (
    <Box p={4} bg={cardBg} borderRadius="md">
      <VStack spacing={4} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={0}>
            <Text fontSize="lg" fontWeight="bold">
              {getNotificationTypeLabel(preference.notification_type)}
            </Text>
            <Text fontSize="sm" color="gray.600">
              Configure how you receive these notifications
            </Text>
          </VStack>
          
          <Switch
            isChecked={preference.is_enabled}
            onChange={(e) => onUpdate({ is_enabled: e.target.checked })}
            colorScheme="blue"
            size="lg"
          />
        </HStack>

        {preference.is_enabled && (
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
            {/* Channels */}
            <FormControl>
              <FormLabel fontSize="sm">Channels</FormLabel>
              <CheckboxGroup
                value={preference.channels}
                onChange={(channels) => onUpdate({ channels })}
              >
                <Stack spacing={2}>
                  {Object.entries(NotificationChannel).map(([key, value]) => (
                    <Checkbox key={value} value={value} size="sm">
                      <HStack spacing={2}>
                        <Icon as={getChannelIcon(value)} boxSize={3} />
                        <Text fontSize="sm">{getNotificationChannelLabel(value)}</Text>
                      </HStack>
                    </Checkbox>
                  ))}
                </Stack>
              </CheckboxGroup>
            </FormControl>

            {/* Frequency */}
            <FormControl>
              <FormLabel fontSize="sm">Frequency</FormLabel>
              <Select
                value={preference.frequency}
                onChange={(e) => onUpdate({ frequency: e.target.value })}
                size="sm"
              >
                {Object.entries(NotificationFrequency).map(([key, value]) => (
                  <option key={value} value={value}>
                    {getNotificationFrequencyLabel(value)}
                  </option>
                ))}
              </Select>
            </FormControl>

            {/* Priority Threshold */}
            <FormControl>
              <FormLabel fontSize="sm">Minimum Priority</FormLabel>
              <Select
                value={preference.priority_threshold}
                onChange={(e) => onUpdate({ priority_threshold: e.target.value })}
                size="sm"
              >
                {Object.entries(NotificationPriority).map(([key, value]) => (
                  <option key={value} value={value}>
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </option>
                ))}
              </Select>
            </FormControl>
          </SimpleGrid>
        )}
      </VStack>
    </Box>
  )
}

export default NotificationPreferences
