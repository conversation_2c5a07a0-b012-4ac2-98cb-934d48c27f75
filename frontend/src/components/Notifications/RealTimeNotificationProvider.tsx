/**
 * Real-time Notification Provider
 * 
 * This component provides real-time notification functionality across the application.
 * It manages WebSocket connections and displays toast notifications for incoming messages.
 */

import React, { createContext, useContext, useEffect, useState } from 'react'
import {
  Box,
  HStack,
  Text,
  Badge,
  Icon,
  useColorModeValue,
} from '@chakra-ui/react'
import {
  FiBell,
  FiAlertTriangle,
  FiInfo,
  FiCheckCircle,
  FiFileText,
  FiMessageSquare,
  FiCalendar,
} from 'react-icons/fi'

import { NotificationPublic, NotificationType } from '@/client/notifications'
import useNotificationWebSocket from '@/hooks/useNotificationWebSocket'
import useCustomToast from '@/hooks/useCustomToast'

interface RealTimeNotificationContextType {
  isConnected: boolean
  unreadCount: number
  lastNotification: NotificationPublic | null
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error'
}

const RealTimeNotificationContext = createContext<RealTimeNotificationContextType>({
  isConnected: false,
  unreadCount: 0,
  lastNotification: null,
  connectionStatus: 'disconnected',
})

export const useRealTimeNotifications = () => {
  const context = useContext(RealTimeNotificationContext)
  if (!context) {
    throw new Error('useRealTimeNotifications must be used within RealTimeNotificationProvider')
  }
  return context
}

interface RealTimeNotificationProviderProps {
  children: React.ReactNode
  enabled?: boolean
}

export const RealTimeNotificationProvider: React.FC<RealTimeNotificationProviderProps> = ({
  children,
  enabled = true,
}) => {
  const toast = useCustomToast()
  const [unreadCount, setUnreadCount] = useState(0)
  const [lastNotification, setLastNotification] = useState<NotificationPublic | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected' | 'error'>('disconnected')

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NotificationType.CASE_UPDATE:
        return FiFileText
      case NotificationType.DOCUMENT_SHARED:
        return FiFileText
      case NotificationType.APPOINTMENT_REMINDER:
        return FiCalendar
      case NotificationType.DEADLINE_APPROACHING:
        return FiAlertTriangle
      case NotificationType.MESSAGE_RECEIVED:
        return FiMessageSquare
      case NotificationType.STATUS_CHANGE:
        return FiInfo
      case NotificationType.MILESTONE_COMPLETED:
        return FiCheckCircle
      case NotificationType.SYSTEM_ANNOUNCEMENT:
        return FiBell
      default:
        return FiBell
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'red'
      case 'high':
        return 'orange'
      case 'medium':
        return 'blue'
      case 'low':
        return 'gray'
      default:
        return 'blue'
    }
  }

  const handleNotificationReceived = (notification: NotificationPublic) => {
    setLastNotification(notification)
    setUnreadCount(prev => prev + 1)

    // Create custom toast with notification styling
    const NotificationToast = () => {
      const bgColor = useColorModeValue('white', 'gray.800')
      const borderColor = useColorModeValue('gray.200', 'gray.600')
      
      return (
        <Box
          bg={bgColor}
          borderColor={borderColor}
          borderWidth="1px"
          borderRadius="md"
          p={4}
          shadow="lg"
          maxW="400px"
          borderLeftWidth="4px"
          borderLeftColor={`${getPriorityColor(notification.priority)}.500`}
        >
          <HStack spacing={3} align="start">
            <Box
              p={2}
              borderRadius="md"
              bg={`${getPriorityColor(notification.priority)}.100`}
              color={`${getPriorityColor(notification.priority)}.600`}
            >
              <Icon as={getNotificationIcon(notification.notification_type)} boxSize={5} />
            </Box>
            
            <Box flex={1}>
              <HStack spacing={2} mb={1}>
                <Text fontSize="sm" fontWeight="bold" noOfLines={1}>
                  {notification.title}
                </Text>
                <Badge
                  colorScheme={getPriorityColor(notification.priority)}
                  variant="solid"
                  fontSize="xs"
                >
                  {notification.priority.toUpperCase()}
                </Badge>
              </HStack>
              
              <Text fontSize="xs" color="gray.600" noOfLines={2}>
                {notification.message}
              </Text>
              
              {notification.case && (
                <Text fontSize="xs" color="gray.500" mt={1}>
                  Case: {notification.case.title}
                </Text>
              )}
            </Box>
          </HStack>
        </Box>
      )
    }

    // Show toast with appropriate duration based on priority
    const duration = notification.priority === 'urgent' ? 10000 : 
                    notification.priority === 'high' ? 7000 : 5000

    toast({
      render: () => <NotificationToast />,
      duration,
      isClosable: true,
      position: 'top-right',
    })

    // Play notification sound for urgent notifications
    if (notification.priority === 'urgent') {
      try {
        const audio = new Audio('/notification-urgent.mp3')
        audio.volume = 0.3
        audio.play().catch(() => {
          // Ignore audio play errors (user interaction required)
        })
      } catch (error) {
        // Ignore audio errors
      }
    }
  }

  const handleNotificationUpdated = (notification: NotificationPublic) => {
    // If notification was marked as read, decrease unread count
    if (notification.is_read && lastNotification?.id === notification.id && !lastNotification.is_read) {
      setUnreadCount(prev => Math.max(0, prev - 1))
    }
    
    setLastNotification(notification)
  }

  const handleError = (error: string) => {
    setConnectionStatus('error')
    console.error('Real-time notification error:', error)
  }

  const { isConnected, connectionAttempts, lastError } = useNotificationWebSocket({
    enabled,
    onNotificationReceived: handleNotificationReceived,
    onNotificationUpdated: handleNotificationUpdated,
    onError: handleError,
  })

  // Update connection status based on WebSocket state
  useEffect(() => {
    if (lastError) {
      setConnectionStatus('error')
    } else if (isConnected) {
      setConnectionStatus('connected')
    } else if (connectionAttempts > 0) {
      setConnectionStatus('connecting')
    } else {
      setConnectionStatus('disconnected')
    }
  }, [isConnected, connectionAttempts, lastError])

  // Reset unread count when user navigates to notifications page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && window.location.pathname.includes('/notifications')) {
        // User is viewing notifications page, reset unread count after a delay
        setTimeout(() => {
          setUnreadCount(0)
        }, 2000)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [])

  const contextValue: RealTimeNotificationContextType = {
    isConnected,
    unreadCount,
    lastNotification,
    connectionStatus,
  }

  return (
    <RealTimeNotificationContext.Provider value={contextValue}>
      {children}
    </RealTimeNotificationContext.Provider>
  )
}

export default RealTimeNotificationProvider
