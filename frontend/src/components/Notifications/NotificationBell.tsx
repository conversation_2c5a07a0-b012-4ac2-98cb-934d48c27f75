/**
 * Notification Bell Component
 * 
 * This component displays a notification bell icon with unread count badge
 * and provides quick access to the notification center.
 */

import React, { useState } from 'react'
import {
  Box,
  IconButton,
  Badge,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverFooter,
  VStack,
  HStack,
  Text,
  Button,
  Divider,
  useColorModeValue,
  Spinner,
  Icon,
} from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import {
  FiBell,
  FiCheck,
  FiSettings,
  FiWifi,
  FiWifiOff,
  FiAlertTriangle,
} from 'react-icons/fi'

import { NotificationsService, NotificationPublic, getNotificationTypeLabel } from '@/client/notifications'
import { useRealTimeNotifications } from './RealTimeNotificationProvider'
import useCustomToast from '@/hooks/useCustomToast'

interface NotificationBellProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'ghost' | 'outline' | 'solid'
}

const NotificationBell: React.FC<NotificationBellProps> = ({
  size = 'md',
  variant = 'ghost',
}) => {
  const toast = useCustomToast()
  const [isOpen, setIsOpen] = useState(false)
  const { isConnected, unreadCount, connectionStatus } = useRealTimeNotifications()
  
  const popoverBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  // Fetch recent notifications for popover
  const { data: recentNotifications, isLoading } = useQuery({
    queryKey: ['notifications', { limit: 5, is_read: false }],
    queryFn: () => NotificationsService.getNotifications({ limit: 5, is_read: false }),
    enabled: isOpen,
  })

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'green'
      case 'connecting':
        return 'yellow'
      case 'error':
        return 'red'
      default:
        return 'gray'
    }
  }

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return FiWifi
      case 'error':
        return FiWifiOff
      default:
        return FiWifi
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = (now.getTime() - date.getTime()) / (1000 * 60)

    if (diffInMinutes < 1) {
      return 'Just now'
    } else if (diffInMinutes < 60) {
      return `${Math.floor(diffInMinutes)}m ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`
    }
  }

  const handleNotificationClick = (notification: NotificationPublic) => {
    setIsOpen(false)
    
    // Navigate to relevant page based on notification type
    if (notification.case_id) {
      window.location.href = `/legal-cases/${notification.case_id}`
    } else {
      window.location.href = '/notifications'
    }
  }

  return (
    <Popover
      isOpen={isOpen}
      onOpen={() => setIsOpen(true)}
      onClose={() => setIsOpen(false)}
      placement="bottom-end"
      closeOnBlur={true}
    >
      <PopoverTrigger>
        <Box position="relative">
          <IconButton
            aria-label="Notifications"
            icon={<FiBell />}
            size={size}
            variant={variant}
            position="relative"
          />
          
          {/* Unread count badge */}
          {unreadCount > 0 && (
            <Badge
              colorScheme="red"
              variant="solid"
              borderRadius="full"
              position="absolute"
              top="-1"
              right="-1"
              fontSize="xs"
              minW="20px"
              h="20px"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
          
          {/* Connection status indicator */}
          <Box
            position="absolute"
            bottom="0"
            right="0"
            w="12px"
            h="12px"
            borderRadius="full"
            bg={`${getConnectionStatusColor()}.500`}
            border="2px solid"
            borderColor={popoverBg}
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            {connectionStatus === 'connecting' && (
              <Spinner size="xs" color="white" />
            )}
          </Box>
        </Box>
      </PopoverTrigger>
      
      <PopoverContent
        bg={popoverBg}
        borderColor={borderColor}
        shadow="xl"
        w="400px"
        maxH="500px"
        overflowY="auto"
      >
        <PopoverHeader borderBottomColor={borderColor}>
          <HStack justify="space-between" align="center">
            <Text fontSize="lg" fontWeight="bold">
              Notifications
            </Text>
            
            <HStack spacing={2}>
              {/* Connection status */}
              <HStack spacing={1}>
                <Icon
                  as={getConnectionStatusIcon()}
                  boxSize={3}
                  color={`${getConnectionStatusColor()}.500`}
                />
                <Text fontSize="xs" color="gray.500">
                  {connectionStatus}
                </Text>
              </HStack>
              
              <IconButton
                aria-label="Notification settings"
                icon={<FiSettings />}
                size="xs"
                variant="ghost"
                onClick={() => {
                  setIsOpen(false)
                  window.location.href = '/notifications/preferences'
                }}
              />
            </HStack>
          </HStack>
        </PopoverHeader>
        
        <PopoverBody p={0}>
          {isLoading ? (
            <Box p={4} textAlign="center">
              <Spinner size="md" />
              <Text fontSize="sm" color="gray.500" mt={2}>
                Loading notifications...
              </Text>
            </Box>
          ) : !recentNotifications?.data.length ? (
            <Box p={4} textAlign="center">
              <Icon as={FiCheck} boxSize={8} color="green.500" mb={2} />
              <Text fontSize="sm" color="gray.600" fontWeight="medium">
                All caught up!
              </Text>
              <Text fontSize="xs" color="gray.500">
                No new notifications
              </Text>
            </Box>
          ) : (
            <VStack spacing={0} align="stretch">
              {recentNotifications.data.map((notification, index) => (
                <Box key={notification.id}>
                  <Box
                    p={3}
                    cursor="pointer"
                    _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <VStack spacing={2} align="stretch">
                      <HStack justify="space-between" align="start">
                        <Text fontSize="sm" fontWeight="medium" noOfLines={2} flex={1}>
                          {notification.title}
                        </Text>
                        <Text fontSize="xs" color="gray.500" flexShrink={0}>
                          {formatDate(notification.created_at)}
                        </Text>
                      </HStack>
                      
                      <Text fontSize="xs" color="gray.600" noOfLines={2}>
                        {notification.message}
                      </Text>
                      
                      <HStack spacing={2}>
                        <Badge
                          size="sm"
                          colorScheme={notification.priority === 'urgent' ? 'red' : 
                                     notification.priority === 'high' ? 'orange' : 'blue'}
                          variant="subtle"
                        >
                          {notification.priority}
                        </Badge>
                        
                        <Badge size="sm" variant="outline">
                          {getNotificationTypeLabel(notification.notification_type)}
                        </Badge>
                      </HStack>
                    </VStack>
                  </Box>
                  
                  {index < recentNotifications.data.length - 1 && (
                    <Divider />
                  )}
                </Box>
              ))}
            </VStack>
          )}
        </PopoverBody>
        
        <PopoverFooter borderTopColor={borderColor}>
          <HStack justify="space-between">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                setIsOpen(false)
                window.location.href = '/notifications'
              }}
            >
              View All
            </Button>
            
            {unreadCount > 0 && (
              <Button
                size="sm"
                colorScheme="blue"
                leftIcon={<FiCheck />}
                onClick={async () => {
                  try {
                    await NotificationsService.markAllNotificationsRead()
                    toast.success('All notifications marked as read')
                    setIsOpen(false)
                  } catch (error) {
                    toast.error('Failed to mark notifications as read')
                  }
                }}
              >
                Mark All Read
              </Button>
            )}
          </HStack>
        </PopoverFooter>
      </PopoverContent>
    </Popover>
  )
}

export default NotificationBell
