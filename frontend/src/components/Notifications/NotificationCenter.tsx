import React, { useState } from 'react'
import {
  Box,
  Container,
  Heading,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  Badge,
  Button,
  Select,
  Spinner,
  Alert,
  Icon,
  SimpleGrid,
} from '@chakra-ui/react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  FiBell,
  FiCheck,
  FiCheckCircle,
  FiAlertTriangle,
  FiInfo,
  FiCalendar,
  FiFileText,
  FiMessageSquare,
  FiSettings,
} from 'react-icons/fi'

import {
  NotificationsService,
  NotificationPublic,
  NotificationType,
  NotificationPriority,
  getNotificationTypeLabel,
  getNotificationPriorityColor,
} from '@/client/notifications'
import useCustomToast from '@/hooks/useCustomToast'
import { useColorModeValue } from '@/components/ui/color-mode'

const NotificationCenter: React.FC = () => {
  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')
  const [typeFilter, setTypeFilter] = useState<NotificationType | 'all'>('all')

  // Color mode values
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  // Fetch notifications
  const {
    data: notifications,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['notifications', filter, typeFilter],
    queryFn: () => NotificationsService.getNotifications({
      skip: 0,
      limit: 50,
      unread_only: filter === 'unread',
      notification_type: typeFilter === 'all' ? undefined : typeFilter,
    }),
  })

  // Mark as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: (notificationId: string) =>
      NotificationsService.markNotificationAsRead({ notificationId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
      toast.success('Notification marked as read')
    },
    onError: () => {
      toast.error('Failed to mark notification as read')
    },
  })

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation({
    mutationFn: () => NotificationsService.markAllNotificationsAsRead(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
      toast.success('All notifications marked as read')
    },
    onError: () => {
      toast.error('Failed to mark all notifications as read')
    },
  })

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'case_update':
        return FiFileText
      case 'deadline_reminder':
        return FiCalendar
      case 'message':
        return FiMessageSquare
      case 'system':
        return FiSettings
      default:
        return FiBell
    }
  }

  const getPriorityColor = (priority: NotificationPriority) => {
    switch (priority) {
      case 'high':
        return 'red'
      case 'medium':
        return 'orange'
      case 'low':
        return 'blue'
      default:
        return 'gray'
    }
  }

  if (isLoading) {
    return (
      <Container maxW="4xl" py={8}>
        <VStack spacing={4}>
          <Spinner size="lg" />
          <Text>Loading notifications...</Text>
        </VStack>
      </Container>
    )
  }

  if (error) {
    return (
      <Container maxW="4xl" py={8}>
        <Alert status="error">
          <Text>Failed to load notifications</Text>
        </Alert>
      </Container>
    )
  }

  return (
    <Container maxW="4xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Heading size="lg">
            <Icon as={FiBell} mr={2} />
            Notifications
          </Heading>
          <Button
            size="sm"
            colorScheme="blue"
            onClick={() => markAllAsReadMutation.mutate()}
            isLoading={markAllAsReadMutation.isPending}
          >
            Mark All as Read
          </Button>
        </HStack>

        {/* Filters */}
        <HStack spacing={4}>
          <Select
            value={filter}
            onChange={(e) => setFilter(e.target.value as 'all' | 'unread' | 'read')}
            maxW="200px"
          >
            <option value="all">All Notifications</option>
            <option value="unread">Unread Only</option>
            <option value="read">Read Only</option>
          </Select>

          <Select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value as NotificationType | 'all')}
            maxW="200px"
          >
            <option value="all">All Types</option>
            <option value="case_update">Case Updates</option>
            <option value="deadline_reminder">Deadlines</option>
            <option value="message">Messages</option>
            <option value="system">System</option>
          </Select>
        </HStack>

        {/* Notifications List */}
        {notifications?.data && notifications.data.length > 0 ? (
          <VStack spacing={4} align="stretch">
            {notifications.data.map((notification: NotificationPublic) => {
              const IconComponent = getNotificationIcon(notification.type)
              const priorityColor = getPriorityColor(notification.priority)

              return (
                <Card
                  key={notification.id}
                  bg={bgColor}
                  borderColor={borderColor}
                  borderWidth="1px"
                  opacity={notification.is_read ? 0.7 : 1}
                >
                  <CardBody>
                    <HStack spacing={4} align="start">
                      <Icon
                        as={IconComponent}
                        boxSize={5}
                        color={`${priorityColor}.500`}
                        mt={1}
                      />
                      
                      <VStack align="start" spacing={2} flex={1}>
                        <HStack justify="space-between" w="full">
                          <Text fontWeight="bold" fontSize="md">
                            {notification.title}
                          </Text>
                          <HStack spacing={2}>
                            <Badge colorScheme={priorityColor} size="sm">
                              {notification.priority}
                            </Badge>
                            {!notification.is_read && (
                              <Badge colorScheme="blue" size="sm">
                                New
                              </Badge>
                            )}
                          </HStack>
                        </HStack>

                        <Text color="gray.600" fontSize="sm">
                          {notification.message}
                        </Text>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="xs" color="gray.500">
                            {new Date(notification.created_at).toLocaleString()}
                          </Text>
                          
                          {!notification.is_read && (
                            <Button
                              size="xs"
                              variant="outline"
                              colorScheme="blue"
                              onClick={() => markAsReadMutation.mutate(notification.id)}
                              isLoading={markAsReadMutation.isPending}
                            >
                              <Icon as={FiCheck} mr={1} />
                              Mark as Read
                            </Button>
                          )}
                        </HStack>
                      </VStack>
                    </HStack>
                  </CardBody>
                </Card>
              )
            })}
          </VStack>
        ) : (
          <Card bg={bgColor} borderColor={borderColor}>
            <CardBody>
              <VStack spacing={4} py={8}>
                <Icon as={FiBell} boxSize={12} color="gray.400" />
                <Text color="gray.500" textAlign="center">
                  No notifications found
                </Text>
              </VStack>
            </CardBody>
          </Card>
        )}
      </VStack>
    </Container>
  )
}

export default NotificationCenter
