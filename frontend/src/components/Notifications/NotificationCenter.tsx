import React, { useState } from 'react'
import {
  Box,
  Container,
  Heading,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  Badge,
  Button,
  Select,
  Spinner,
  Alert,
  Icon,
  useColorModeValue,
  SimpleGrid,
} from '@chakra-ui/react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  FiBell,
  FiCheck,
  FiCheckCircle,
  FiAlertTriangle,
  FiInfo,
  FiCalendar,
  FiFileText,
  FiMessageSquare,
  FiSettings,
} from 'react-icons/fi'

import {
  NotificationsService,
  NotificationPublic,
  NotificationType,
  NotificationPriority,
  getNotificationTypeLabel,
  getNotificationPriorityColor,
} from '@/client/notifications'
import useCustomToast from '@/hooks/useCustomToast'
import { useColorModeValue } from '@/components/ui/color-mode'

const NotificationCenter: React.FC = () => {
  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const cardBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  // State for filters
  const [isReadFilter, setIsReadFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [priorityFilter, setPriorityFilter] = useState<string>('')

  // Fetch notifications
  const {
    data: notificationsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      'notifications',
      {
        is_read: isReadFilter === '' ? undefined : isReadFilter === 'true',
        notification_type: typeFilter || undefined,
        priority: priorityFilter || undefined,
      },
    ],
    queryFn: () =>
      NotificationsService.getNotifications({
        is_read: isReadFilter === '' ? undefined : isReadFilter === 'true',
        notification_type: typeFilter || undefined,
        priority: priorityFilter || undefined,
      }),
    onError: (error: any) => {
      toast.error('Failed to load notifications')
      console.error('Notifications error:', error)
    },
  })

  // Mark notification as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: (notificationId: string) =>
      NotificationsService.updateNotification({
        notificationId,
        notification: { is_read: true },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
      toast.success('Notification marked as read')
    },
    onError: () => {
      toast.error('Failed to mark notification as read')
    },
  })

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation({
    mutationFn: () => NotificationsService.markAllNotificationsRead(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] })
      toast.success('All notifications marked as read')
    },
    onError: () => {
      toast.error('Failed to mark all notifications as read')
    },
  })

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NotificationType.CASE_UPDATE:
        return FiFileText
      case NotificationType.DOCUMENT_SHARED:
        return FiFileText
      case NotificationType.APPOINTMENT_REMINDER:
        return FiCalendar
      case NotificationType.DEADLINE_APPROACHING:
        return FiAlertTriangle
      case NotificationType.MESSAGE_RECEIVED:
        return FiMessageSquare
      case NotificationType.STATUS_CHANGE:
        return FiInfo
      case NotificationType.MILESTONE_COMPLETED:
        return FiCheckCircle
      case NotificationType.SYSTEM_ANNOUNCEMENT:
        return FiBell
      default:
        return FiBell
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`
    } else if (diffInHours < 48) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })
    }
  }

  if (isLoading) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack spacing={8}>
          <Heading size="lg">Notification Center</Heading>
          <Spinner size="xl" />
          <Text>Loading notifications...</Text>
        </VStack>
      </Container>
    )
  }

  if (error) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack spacing={8}>
          <Heading size="lg">Notification Center</Heading>
          <Alert status="error">
            Failed to load notifications. Please try again later.
          </Alert>
          <Button onClick={() => refetch()}>Retry</Button>
        </VStack>
      </Container>
    )
  }

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Box>
            <Heading size="lg" mb={2}>
              Notification Center
            </Heading>
            <Text color="gray.600">
              Manage your notifications and preferences
            </Text>
          </Box>
          
          <HStack spacing={4}>
            <Button
              variant="outline"
              leftIcon={<FiSettings />}
              onClick={() => {
                window.location.href = '/notifications/preferences'
              }}
            >
              Preferences
            </Button>
            
            {notificationsData && notificationsData.unread_count > 0 && (
              <Button
                colorScheme="blue"
                leftIcon={<FiCheckCircle />}
                onClick={() => markAllAsReadMutation.mutate()}
                isLoading={markAllAsReadMutation.isLoading}
              >
                Mark All Read
              </Button>
            )}
          </HStack>
        </HStack>

        {/* Statistics */}
        {notificationsData && (
          <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6}>
            <Card bg={cardBg} borderColor={borderColor} variant="outline">
              <CardBody textAlign="center">
                <Text fontSize="2xl" fontWeight="bold">{notificationsData.count}</Text>
                <Text fontSize="sm" color="gray.600">Total Notifications</Text>
                <Text fontSize="xs" color="gray.500">All time</Text>
              </CardBody>
            </Card>

            <Card bg={cardBg} borderColor={borderColor} variant="outline">
              <CardBody textAlign="center">
                <Text fontSize="2xl" fontWeight="bold" color="blue.500">{notificationsData.unread_count}</Text>
                <Text fontSize="sm" color="gray.600">Unread</Text>
                <Text fontSize="xs" color="gray.500">Requires attention</Text>
              </CardBody>
            </Card>

            <Card bg={cardBg} borderColor={borderColor} variant="outline">
              <CardBody textAlign="center">
                <Text fontSize="2xl" fontWeight="bold" color="green.500">
                  {notificationsData.count - notificationsData.unread_count}
                </Text>
                <Text fontSize="sm" color="gray.600">Read</Text>
                <Text fontSize="xs" color="gray.500">Processed</Text>
              </CardBody>
            </Card>

            <Card bg={cardBg} borderColor={borderColor} variant="outline">
              <CardBody textAlign="center">
                <Text fontSize="2xl" fontWeight="bold">
                  {notificationsData.count > 0
                    ? Math.round(((notificationsData.count - notificationsData.unread_count) / notificationsData.count) * 100)
                    : 0}%
                </Text>
                <Text fontSize="sm" color="gray.600">Read Rate</Text>
                <Text fontSize="xs" color="gray.500">Engagement</Text>
              </CardBody>
            </Card>
          </SimpleGrid>
        )}

        {/* Filters */}
        <Card bg={cardBg} borderColor={borderColor} variant="outline">
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Text fontWeight="medium">Filters</Text>
              
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                <Box>
                  <Text fontSize="sm" mb={2} color="gray.600">
                    Status
                  </Text>
                  <Select
                    placeholder="All notifications"
                    value={isReadFilter}
                    onChange={(e) => setIsReadFilter(e.target.value)}
                  >
                    <option value="false">Unread only</option>
                    <option value="true">Read only</option>
                  </Select>
                </Box>

                <Box>
                  <Text fontSize="sm" mb={2} color="gray.600">
                    Type
                  </Text>
                  <Select
                    placeholder="All types"
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                  >
                    {Object.entries(NotificationType).map(([key, value]) => (
                      <option key={value} value={value}>
                        {getNotificationTypeLabel(value)}
                      </option>
                    ))}
                  </Select>
                </Box>

                <Box>
                  <Text fontSize="sm" mb={2} color="gray.600">
                    Priority
                  </Text>
                  <Select
                    placeholder="All priorities"
                    value={priorityFilter}
                    onChange={(e) => setPriorityFilter(e.target.value)}
                  >
                    {Object.entries(NotificationPriority).map(([key, value]) => (
                      <option key={value} value={value}>
                        {value.charAt(0).toUpperCase() + value.slice(1)}
                      </option>
                    ))}
                  </Select>
                </Box>
              </SimpleGrid>

              {(isReadFilter || typeFilter || priorityFilter) && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setIsReadFilter('')
                    setTypeFilter('')
                    setPriorityFilter('')
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Notifications List */}
        {!notificationsData?.data.length ? (
          <Card bg={cardBg} borderColor={borderColor} variant="outline">
            <CardBody>
              <VStack spacing={4} py={8}>
                <Icon as={FiBell} boxSize={12} color="gray.400" />
                <Text fontSize="lg" fontWeight="medium" color="gray.600">
                  No notifications found
                </Text>
                <Text color="gray.500" textAlign="center">
                  {isReadFilter || typeFilter || priorityFilter
                    ? 'Try adjusting your filters to see more notifications.'
                    : 'You\'re all caught up! No new notifications.'}
                </Text>
              </VStack>
            </CardBody>
          </Card>
        ) : (
          <VStack spacing={4} align="stretch">
            {notificationsData.data.map((notification) => (
              <NotificationCard
                key={notification.id}
                notification={notification}
                onMarkAsRead={() => markAsReadMutation.mutate(notification.id)}
                isMarkingAsRead={markAsReadMutation.isLoading}
              />
            ))}
          </VStack>
        )}

        {/* Summary */}
        {notificationsData && (
          <Box textAlign="center" color="gray.600">
            <Text fontSize="sm">
              Showing {notificationsData.data.length} of {notificationsData.count} notifications
              {notificationsData.unread_count > 0 && ` • ${notificationsData.unread_count} unread`}
            </Text>
          </Box>
        )}
      </VStack>
    </Container>
  )
}

interface NotificationCardProps {
  notification: NotificationPublic
  onMarkAsRead: () => void
  isMarkingAsRead: boolean
}

const NotificationCard: React.FC<NotificationCardProps> = ({
  notification,
  onMarkAsRead,
  isMarkingAsRead,
}) => {
  const cardBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  const unreadBg = useColorModeValue('blue.50', 'blue.900')

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NotificationType.CASE_UPDATE:
        return FiFileText
      case NotificationType.DOCUMENT_SHARED:
        return FiFileText
      case NotificationType.APPOINTMENT_REMINDER:
        return FiCalendar
      case NotificationType.DEADLINE_APPROACHING:
        return FiAlertTriangle
      case NotificationType.MESSAGE_RECEIVED:
        return FiMessageSquare
      case NotificationType.STATUS_CHANGE:
        return FiInfo
      case NotificationType.MILESTONE_COMPLETED:
        return FiCheckCircle
      case NotificationType.SYSTEM_ANNOUNCEMENT:
        return FiBell
      default:
        return FiBell
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`
    } else if (diffInHours < 48) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })
    }
  }

  return (
    <Card
      bg={notification.is_read ? cardBg : unreadBg}
      borderColor={borderColor}
      variant="outline"
      borderLeftWidth={4}
      borderLeftColor={`${getNotificationPriorityColor(notification.priority)}.500`}
    >
      <CardBody>
        <VStack spacing={3} align="stretch">
          {/* Header */}
          <HStack justify="space-between" align="start">
            <HStack spacing={3} flex={1}>
              <Box
                p={2}
                borderRadius="md"
                bg={`${getNotificationPriorityColor(notification.priority)}.100`}
                color={`${getNotificationPriorityColor(notification.priority)}.600`}
              >
                <Icon as={getNotificationIcon(notification.notification_type)} boxSize={5} />
              </Box>
              
              <VStack align="start" spacing={1} flex={1}>
                <HStack spacing={2} wrap="wrap">
                  <Text fontSize="lg" fontWeight="bold">
                    {notification.title}
                  </Text>
                  {!notification.is_read && (
                    <Badge colorScheme="blue" variant="solid" fontSize="xs">
                      New
                    </Badge>
                  )}
                </HStack>
                
                <HStack spacing={2} wrap="wrap">
                  <Badge
                    colorScheme={getNotificationPriorityColor(notification.priority)}
                    variant="subtle"
                  >
                    {notification.priority.toUpperCase()}
                  </Badge>
                  
                  <Badge variant="outline">
                    {getNotificationTypeLabel(notification.notification_type)}
                  </Badge>
                  
                  <Text fontSize="sm" color="gray.500">
                    {formatDate(notification.created_at)}
                  </Text>
                </HStack>
              </VStack>
            </HStack>
            
            {!notification.is_read && (
              <Button
                size="sm"
                variant="ghost"
                colorScheme="blue"
                leftIcon={<FiCheck />}
                onClick={onMarkAsRead}
                isLoading={isMarkingAsRead}
              >
                Mark Read
              </Button>
            )}
          </HStack>

          {/* Content */}
          <Text fontSize="sm" color="gray.700">
            {notification.message}
          </Text>

          {/* Case Info */}
          {notification.case && (
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600">
                <strong>Related Case:</strong> {notification.case.title}
              </Text>
            </Box>
          )}

          {/* Read Status */}
          {notification.is_read && notification.read_at && (
            <Text fontSize="xs" color="gray.500">
              Read on {formatDate(notification.read_at)}
            </Text>
          )}
        </VStack>
      </CardBody>
    </Card>
  )
}

export default NotificationCenter
