import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"

import { type UserPublic, UsersService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Text,
  VStack,
  Badge,
  Table,
} from "@chakra-ui/react"
import { FaUsers } from "react-icons/fa"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTrigger,
} from "../ui/dialog"

const UserAssignmentManager = () => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()

  // Get all lawyers
  const { data: lawyers } = useQuery({
    queryKey: ["lawyers"],
    queryFn: () => UsersService.getAllLawyers(),
  })

  const unassignMutation = useMutation({
    mutationFn: (userId: string) =>
      UsersService.updateUserAssignment({
        userId,
        requestBody: { assigned_lawyer_id: null },
      }),
    onSuccess: () => {
      showSuccessToast("User unassigned successfully.")
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
      queryClient.invalidateQueries({ queryKey: ["unassigned-users"] })
      queryClient.invalidateQueries({ queryKey: ["lawyer-assigned-users"] })
    },
  })

  const handleUnassign = (userId: string) => {
    unassignMutation.mutate(userId)
  }

  return (
    <DialogRoot
      size={{ base: "md", md: "lg" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsOpen(open)}
    >
      <DialogTrigger asChild>
        <Button value="manage-assignments" my={4} ml={2} variant="outline">
          <FaUsers fontSize="16px" />
          Manage Assignments
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Manage User Assignments</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Text mb={4}>
            View and manage user assignments to lawyers.
          </Text>

          {lawyers?.data?.map((lawyer) => (
            <LawyerAssignments
              key={lawyer.id}
              lawyer={lawyer}
              onUnassign={handleUnassign}
              isUnassigning={unassignMutation.isPending}
            />
          ))}
        </DialogBody>

        <DialogFooter>
          <DialogActionTrigger asChild>
            <Button variant="subtle" colorPalette="gray">
              Close
            </Button>
          </DialogActionTrigger>
        </DialogFooter>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

interface LawyerAssignmentsProps {
  lawyer: UserPublic
  onUnassign: (userId: string) => void
  isUnassigning: boolean
}

const LawyerAssignments = ({ lawyer, onUnassign, isUnassigning }: LawyerAssignmentsProps) => {
  const { data: assignedUsers } = useQuery({
    queryKey: ["lawyer-assigned-users", lawyer.id],
    queryFn: () => UsersService.getLawyerAssignedUsers({ lawyerId: lawyer.id }),
  })

  if (!assignedUsers?.data?.length) {
    return (
      <VStack mb={6} p={4} border="1px solid #e2e8f0" borderRadius="md">
        <Text fontWeight="bold">{lawyer.full_name || lawyer.email}</Text>
        <Text color="gray.500" fontSize="sm">No assigned users</Text>
      </VStack>
    )
  }

  return (
    <VStack mb={6} p={4} border="1px solid #e2e8f0" borderRadius="md" align="stretch">
      <Text fontWeight="bold" mb={2}>
        {lawyer.full_name || lawyer.email}
        <Badge ml={2} colorScheme="blue">
          {assignedUsers.data.length} assigned
        </Badge>
      </Text>

      <Table.Root size="sm">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader>Name</Table.ColumnHeader>
            <Table.ColumnHeader>Email</Table.ColumnHeader>
            <Table.ColumnHeader>Role</Table.ColumnHeader>
            <Table.ColumnHeader>Action</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {assignedUsers.data.map((user) => (
            <Table.Row key={user.id}>
              <Table.Cell>{user.full_name || "N/A"}</Table.Cell>
              <Table.Cell>{user.email}</Table.Cell>
              <Table.Cell>
                <Badge colorScheme={user.role === "client" ? "green" : "purple"}>
                  {user.role}
                </Badge>
              </Table.Cell>
              <Table.Cell>
                <Button
                  size="sm"
                  variant="outline"
                  colorScheme="red"
                  onClick={() => onUnassign(user.id)}
                  loading={isUnassigning}
                >
                  Unassign
                </Button>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    </VStack>
  )
}

export default UserAssignmentManager
