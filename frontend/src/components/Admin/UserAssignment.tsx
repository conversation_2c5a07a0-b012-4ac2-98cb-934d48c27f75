import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { Controller, type SubmitHandler, useForm } from "react-hook-form"

import { type UserAssignment, UsersService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Text,
  VStack,
} from "@chakra-ui/react"
import { useState } from "react"
import { FaUserPlus } from "react-icons/fa"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTrigger,
} from "../ui/dialog"
import { Field } from "../ui/field"

interface UserAssignmentForm {
  user_id: string
  assigned_lawyer_id: string
}

const UserAssignment = () => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()

  // Get unassigned users
  const { data: unassignedUsers } = useQuery({
    queryKey: ["unassigned-users"],
    queryFn: () => UsersService.getUnassignedUsers(),
  })

  // Get all lawyers
  const { data: lawyers } = useQuery({
    queryKey: ["lawyers"],
    queryFn: () => UsersService.getAllLawyers(),
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid, isSubmitting },
  } = useForm<UserAssignmentForm>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: {
      user_id: "",
      assigned_lawyer_id: "",
    },
  })

  const mutation = useMutation({
    mutationFn: (data: UserAssignment) =>
      UsersService.assignUserToLawyer({ requestBody: data }),
    onSuccess: () => {
      showSuccessToast("User assigned successfully.")
      reset()
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] })
      queryClient.invalidateQueries({ queryKey: ["unassigned-users"] })
    },
  })

  const onSubmit: SubmitHandler<UserAssignmentForm> = (data) => {
    mutation.mutate(data)
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsOpen(open)}
    >
      <DialogTrigger asChild>
        <Button value="assign-user" my={4} ml={2}>
          <FaUserPlus fontSize="16px" />
          Assign User
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Assign User to Lawyer</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>
              Assign a client or assistant to a lawyer.
            </Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.user_id}
                errorText={errors.user_id?.message}
                label="User"
              >
                <Controller
                  control={control}
                  name="user_id"
                  rules={{ required: "User is required" }}
                  render={({ field }) => (
                    <select
                      {...field}
                      style={{
                        width: "100%",
                        padding: "8px",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "14px",
                      }}
                    >
                      <option value="">Select user</option>
                      {unassignedUsers?.data?.map((user) => (
                        <option key={user.id} value={user.id}>
                          {user.full_name || user.email} ({user.role})
                        </option>
                      ))}
                    </select>
                  )}
                />
              </Field>

              <Field
                required
                invalid={!!errors.assigned_lawyer_id}
                errorText={errors.assigned_lawyer_id?.message}
                label="Lawyer"
              >
                <Controller
                  control={control}
                  name="assigned_lawyer_id"
                  rules={{ required: "Lawyer is required" }}
                  render={({ field }) => (
                    <select
                      {...field}
                      style={{
                        width: "100%",
                        padding: "8px",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "14px",
                      }}
                    >
                      <option value="">Select lawyer</option>
                      {lawyers?.data?.map((lawyer) => (
                        <option key={lawyer.id} value={lawyer.id}>
                          {lawyer.full_name || lawyer.email}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </Field>
            </VStack>
          </DialogBody>

          <DialogFooter gap={2}>
            <DialogActionTrigger asChild>
              <Button
                variant="subtle"
                colorPalette="gray"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              type="submit"
              disabled={!isValid}
              loading={isSubmitting}
            >
              Assign
            </Button>
          </DialogFooter>
        </form>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default UserAssignment
