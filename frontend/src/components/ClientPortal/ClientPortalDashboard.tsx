import React from 'react'
import {
  Box,
  Container,
  Heading,
  SimpleGrid,
  Card,
  CardBody,
  VStack,
  HStack,
  Text,
  Badge,
  <PERSON>ton,
  Icon,
  <PERSON>ner,
  <PERSON><PERSON>,
} from '@chakra-ui/react'
import { useQuery } from '@tanstack/react-query'
import {
  FiFolder,
  FiFileText,
  FiMail,
  FiBell,
  FiCalendar,
  FiClock,
  FiArrowRight,
} from 'react-icons/fi'

import { ClientPortalService } from '@/client/client-portal'
import useCustomToast from '@/hooks/useCustomToast'
import { useColorModeValue } from '@/components/ui/color-mode'

const ClientPortalDashboard: React.FC = () => {
  const toast = useCustomToast()
  const cardBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  // Fetch dashboard data
  const {
    data: dashboardStats,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['client-dashboard'],
    queryFn: () => ClientPortalService.getDashboard(),
    onError: (error: any) => {
      toast.error('Failed to load dashboard data')
      console.error('Dashboard error:', error)
    },
  })

  if (isLoading) {
    return (
      <Container maxW="7xl" py={8}>
        <VStack spacing={8}>
          <Heading size="lg">Client Portal Dashboard</Heading>
          <Spinner size="xl" />
          <Text>Loading your dashboard...</Text>
        </VStack>
      </Container>
    )
  }

  if (error) {
    return (
      <Container maxW="7xl" py={8}>
        <VStack spacing={8}>
          <Heading size="lg">Client Portal Dashboard</Heading>
          <Alert status="error">
            Failed to load dashboard data. Please try again later.
          </Alert>
        </VStack>
      </Container>
    )
  }

  const stats = [
    {
      label: 'Active Cases',
      value: dashboardStats?.active_cases || 0,
      icon: FiFolder,
      color: 'blue',
      helpText: 'Cases currently in progress',
    },
    {
      label: 'Shared Documents',
      value: dashboardStats?.total_documents || 0,
      icon: FiFileText,
      color: 'green',
      helpText: 'Documents available to you',
    },
    {
      label: 'Unread Messages',
      value: dashboardStats?.unread_messages || 0,
      icon: FiMail,
      color: 'orange',
      helpText: 'New messages from your lawyer',
    },
    {
      label: 'Notifications',
      value: dashboardStats?.unread_notifications || 0,
      icon: FiBell,
      color: 'purple',
      helpText: 'Unread notifications',
    },
    {
      label: 'Upcoming Appointments',
      value: dashboardStats?.upcoming_appointments || 0,
      icon: FiCalendar,
      color: 'teal',
      helpText: 'Scheduled appointments',
    },
    {
      label: 'Pending Deadlines',
      value: dashboardStats?.pending_deadlines || 0,
      icon: FiClock,
      color: 'red',
      helpText: 'Important deadlines approaching',
    },
  ]

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="lg" mb={2}>
            Welcome to Your Client Portal
          </Heading>
          <Text color="gray.600" fontSize="lg">
            Stay updated on your legal cases and communicate with your legal team
          </Text>
        </Box>

        {/* Stats Grid */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          {stats.map((stat, index) => (
            <Card key={index} bg={cardBg} borderColor={borderColor} variant="outline">
              <CardBody>
                <HStack justify="space-between" align="start">
                  <VStack align="start" spacing={1}>
                    <Text fontSize="sm" color="gray.600">
                      {stat.label}
                    </Text>
                    <Text fontSize="2xl" fontWeight="bold">
                      {stat.value}
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      {stat.helpText}
                    </Text>
                  </VStack>
                  <Box
                    p={3}
                    borderRadius="lg"
                    bg={`${stat.color}.50`}
                    color={`${stat.color}.500`}
                  >
                    <Icon as={stat.icon} boxSize={6} />
                  </Box>
                </HStack>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>

        {/* Quick Actions */}
        <Card bg={cardBg} borderColor={borderColor} variant="outline">
          <CardBody>
            <VStack spacing={6} align="stretch">
              <Heading size="md">Quick Actions</Heading>
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <Button
                  size="lg"
                  variant="outline"
                  colorScheme="blue"
                  leftIcon={<FiFolder />}
                  rightIcon={<FiArrowRight />}
                  justifyContent="space-between"
                  onClick={() => {
                    // Navigate to cases
                    window.location.href = '/client-portal/cases'
                  }}
                >
                  <Text>View My Cases</Text>
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  colorScheme="green"
                  leftIcon={<FiMail />}
                  rightIcon={<FiArrowRight />}
                  justifyContent="space-between"
                  onClick={() => {
                    // Navigate to messages
                    window.location.href = '/client-portal/messages'
                  }}
                >
                  <Text>Messages</Text>
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  colorScheme="purple"
                  leftIcon={<FiBell />}
                  rightIcon={<FiArrowRight />}
                  justifyContent="space-between"
                  onClick={() => {
                    // Navigate to notifications
                    window.location.href = '/client-portal/notifications'
                  }}
                >
                  <Text>Notifications</Text>
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  colorScheme="orange"
                  leftIcon={<FiFileText />}
                  rightIcon={<FiArrowRight />}
                  justifyContent="space-between"
                  onClick={() => {
                    // Navigate to documents
                    window.location.href = '/client-portal/documents'
                  }}
                >
                  <Text>Documents</Text>
                </Button>
              </SimpleGrid>
            </VStack>
          </CardBody>
        </Card>

        {/* Recent Activity */}
        <Card bg={cardBg} borderColor={borderColor} variant="outline">
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Heading size="md">Recent Activity</Heading>
                <Badge colorScheme="blue" variant="subtle">
                  Last 7 days
                </Badge>
              </HStack>
              
              <VStack spacing={3} align="stretch">
                {/* Placeholder for recent activity items */}
                <HStack spacing={3} p={3} borderRadius="md" bg="gray.50">
                  <Icon as={FiBell} color="blue.500" />
                  <VStack align="start" spacing={0} flex={1}>
                    <Text fontSize="sm" fontWeight="medium">
                      Case status updated
                    </Text>
                    <Text fontSize="xs" color="gray.600">
                      Your case "Contract Review" status changed to "In Progress"
                    </Text>
                  </VStack>
                  <Text fontSize="xs" color="gray.500">
                    2 days ago
                  </Text>
                </HStack>

                <HStack spacing={3} p={3} borderRadius="md" bg="gray.50">
                  <Icon as={FiFileText} color="green.500" />
                  <VStack align="start" spacing={0} flex={1}>
                    <Text fontSize="sm" fontWeight="medium">
                      New document shared
                    </Text>
                    <Text fontSize="xs" color="gray.600">
                      "Contract_Draft_v2.pdf" has been shared with you
                    </Text>
                  </VStack>
                  <Text fontSize="xs" color="gray.500">
                    3 days ago
                  </Text>
                </HStack>

                <HStack spacing={3} p={3} borderRadius="md" bg="gray.50">
                  <Icon as={FiMail} color="orange.500" />
                  <VStack align="start" spacing={0} flex={1}>
                    <Text fontSize="sm" fontWeight="medium">
                      Message received
                    </Text>
                    <Text fontSize="xs" color="gray.600">
                      New message from your lawyer regarding next steps
                    </Text>
                  </VStack>
                  <Text fontSize="xs" color="gray.500">
                    5 days ago
                  </Text>
                </HStack>
              </VStack>

              <Button variant="ghost" size="sm" colorScheme="blue">
                View All Activity
              </Button>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

export default ClientPortalDashboard
