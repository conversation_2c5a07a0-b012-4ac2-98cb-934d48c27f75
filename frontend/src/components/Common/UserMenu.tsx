import { Box, Button, Flex, <PERSON>, Badge } from "@chakra-ui/react"
import { Link } from "@tanstack/react-router"
import { FiLogOut, FiUser, FiSettings, FiBell, FiHelpCircle } from "react-icons/fi"

import useAuth from "@/hooks/useAuth"
import { MenuContent, MenuItem, MenuRoot, MenuTrigger } from "../ui/menu"
import { Avatar } from "../ui/avatar"
import UserProfileModal from "../UserProfile/UserProfileModal"

const UserMenu = () => {
  const { user, logout } = useAuth()

  const handleLogout = async () => {
    logout()
  }

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'admin': return 'red'
      case 'lawyer': return 'blue'
      case 'assistant': return 'green'
      case 'client': return 'purple'
      default: return 'gray'
    }
  }

  return (
    <>
      {/* Desktop */}
      <Flex align="center" gap={3}>
        {/* Notifications - placeholder for future implementation */}
        <Button variant="ghost" size="sm" position="relative">
          <FiBell />
          <Badge
            position="absolute"
            top="-1"
            right="-1"
            size="sm"
            colorScheme="red"
            borderRadius="full"
          >
            3
          </Badge>
        </Button>

        <MenuRoot>
          <MenuTrigger asChild>
            <Button
              data-testid="user-menu"
              variant="ghost"
              p={2}
              _hover={{ bg: "gray.100" }}
            >
              <Flex align="center" gap={2}>
                <Avatar
                  size="sm"
                  name={user?.full_name || user?.email || "User"}
                  bg={`${getRoleColor(user?.role)}.500`}
                />
                <Box textAlign="left" display={{ base: "none", lg: "block" }}>
                  <Text fontSize="sm" fontWeight="medium" truncate maxW="150px">
                    {user?.full_name || "User"}
                  </Text>
                  <Text fontSize="xs" color="gray.600" textTransform="capitalize">
                    {user?.role || 'User'}
                  </Text>
                </Box>
              </Flex>
            </Button>
          </MenuTrigger>

          <MenuContent minW="200px">
            {/* User Info Header */}
            <Box p={3} borderBottom="1px" borderColor="gray.100">
              <Flex align="center" gap={3}>
                <Avatar
                  size="md"
                  name={user?.full_name || user?.email || "User"}
                  bg={`${getRoleColor(user?.role)}.500`}
                />
                <Box flex="1" minW={0}>
                  <Text fontWeight="semibold" fontSize="sm" truncate>
                    {user?.full_name || user?.email || "User"}
                  </Text>
                  <Text fontSize="xs" color="gray.600" textTransform="capitalize">
                    {user?.role || 'User'}
                  </Text>
                  {user?.email && user?.full_name && (
                    <Text fontSize="xs" color="gray.500" truncate>
                      {user.email}
                    </Text>
                  )}
                </Box>
              </Flex>
            </Box>

            <UserProfileModal>
              <MenuItem
                closeOnSelect
                value="profile"
                gap={3}
                py={3}
                style={{ cursor: "pointer" }}
              >
                <FiUser fontSize="16px" />
                <Box flex="1">My Profile</Box>
              </MenuItem>
            </UserProfileModal>

            <Link to="/settings">
              <MenuItem
                closeOnSelect
                value="settings"
                gap={3}
                py={3}
                style={{ cursor: "pointer" }}
              >
                <FiSettings fontSize="16px" />
                <Box flex="1">Settings</Box>
              </MenuItem>
            </Link>

            <Link to="/notifications">
              <MenuItem
                closeOnSelect
                value="notifications"
                gap={3}
                py={3}
                style={{ cursor: "pointer" }}
              >
                <FiBell fontSize="16px" />
                <Box flex="1">Notifications</Box>
                <Badge size="sm" colorScheme="red">3</Badge>
              </MenuItem>
            </Link>

            <MenuItem
              value="help"
              gap={3}
              py={3}
              style={{ cursor: "pointer" }}
            >
              <FiHelpCircle fontSize="16px" />
              <Box flex="1">Help & Support</Box>
            </MenuItem>

            <Box borderTop="1px" borderColor="gray.100" mt={2} pt={2}>
              <MenuItem
                value="logout"
                gap={3}
                py={3}
                onClick={handleLogout}
                style={{ cursor: "pointer" }}
                _hover={{ bg: "red.50", color: "red.600" }}
              >
                <FiLogOut fontSize="16px" />
                Log Out
              </MenuItem>
            </Box>
          </MenuContent>
        </MenuRoot>
      </Flex>
    </>
  )
}

export default UserMenu
