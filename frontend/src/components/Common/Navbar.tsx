import { Flex, Image, useBreakpointValue, Text, Box } from "@chakra-ui/react"
import { Link } from "@tanstack/react-router"

import Logo from "/assets/images/fastapi-logo.svg"
import UserMenu from "./UserMenu"

function Navbar() {
  const display = useBreakpointValue({ base: "none", md: "flex" })

  return (
    <Flex
      display={display}
      justify="space-between"
      position="sticky"
      align="center"
      bg="white"
      borderBottom="1px"
      borderColor="gray.200"
      w="100%"
      top={0}
      px={6}
      py={3}
      zIndex={10}
      shadow="sm"
    >
      <Link to="/">
        <Flex align="center" gap={3} _hover={{ opacity: 0.8 }} transition="opacity 0.2s">
          <Image src={Logo} alt="EquiNova Logo" h="32px" />
          <Box>
            <Text fontSize="xl" fontWeight="bold" color="blue.600">
              EquiNova
            </Text>
            <Text fontSize="xs" color="gray.500" mt="-1">
              Legal Management System
            </Text>
          </Box>
        </Flex>
      </Link>

      <Flex gap={4} alignItems="center">
        <UserMenu />
      </Flex>
    </Flex>
  )
}

export default Navbar
