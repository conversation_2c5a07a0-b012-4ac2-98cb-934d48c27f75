import { Icon<PERSON>utton } from "@chakra-ui/react"
import { BsThreeDotsVertical } from "react-icons/bs"
import { MenuContent, MenuRoot, MenuTrigger } from "../ui/menu"

import type { LegalCasePublic } from "@/client"
import DeleteLegalCase from "../LegalCases/DeleteLegalCase"
import EditLegalCase from "../LegalCases/EditLegalCase"
import ViewLegalCase from "../LegalCases/ViewLegalCase"

interface LegalCaseActionsMenuProps {
  legalCase: LegalCasePublic
}

export const LegalCaseActionsMenu = ({ legalCase }: LegalCaseActionsMenuProps) => {
  return (
    <MenuRoot>
      <MenuTrigger asChild>
        <IconButton variant="ghost" color="inherit">
          <BsThreeDotsVertical />
        </IconButton>
      </MenuTrigger>
      <MenuContent>
        <ViewLegalCase legalCase={legalCase} />
        <EditLegalCase legalCase={legalCase} />
        <DeleteLegalCase id={legalCase.id} />
      </MenuContent>
    </MenuRoot>
  )
}
