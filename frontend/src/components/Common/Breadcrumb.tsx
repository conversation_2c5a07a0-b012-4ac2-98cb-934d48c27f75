import { Box, Flex, Text, Icon } from "@chakra-ui/react"
import { Link, useRouter } from "@tanstack/react-router"
import { FiChevronRight, FiHome } from "react-icons/fi"

interface BreadcrumbItem {
  label: string
  path: string
  icon?: React.ComponentType
}

const Breadcrumb = () => {
  const router = useRouter()
  const location = router.state.location

  // Define route mappings for breadcrumb generation
  const routeMap: Record<string, BreadcrumbItem> = {
    "/": { label: "Dashboard", path: "/", icon: FiHome },
    "/legal-cases": { label: "Legal Cases", path: "/legal-cases" },
    "/documents": { label: "Documents", path: "/documents" },
    "/items": { label: "Items", path: "/items" },
    "/settings": { label: "Settings", path: "/settings" },
    "/admin": { label: "Administration", path: "/admin" },
    "/admin/security": { label: "Security", path: "/admin/security" },
    "/admin/analytics": { label: "Analytics", path: "/admin/analytics" },
    "/messages": { label: "Messages", path: "/messages" },
    "/notifications": { label: "Notifications", path: "/notifications" },
    "/search": { label: "Search", path: "/search" },
    "/calendar": { label: "Calendar", path: "/calendar" },
    "/reports": { label: "Reports", path: "/reports" },
  }

  // Generate breadcrumb items based on current path
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = []

    // Always start with home
    breadcrumbs.push(routeMap["/"])

    // Build path progressively
    let currentPath = ""
    for (let i = 0; i < pathSegments.length; i++) {
      const segment = pathSegments[i]
      currentPath += `/${segment}`
      const routeInfo = routeMap[currentPath]

      if (routeInfo) {
        breadcrumbs.push(routeInfo)
      } else {
        // Handle special cases for dynamic routes
        if (pathSegments[i - 1] === 'legal-cases' && segment.match(/^[0-9a-f-]{36}$/)) {
          // This is a legal case ID - show "Case Details"
          breadcrumbs.push({
            label: "Case Details",
            path: currentPath
          })
        } else {
          // Fallback for other dynamic routes or unmapped paths
          breadcrumbs.push({
            label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
            path: currentPath
          })
        }
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  // Don't show breadcrumbs on home page
  if (location.pathname === "/") {
    return null
  }

  return (
    <Box
      mb={4}
      p={3}
      bg="gray.50"
      borderRadius="md"
      borderWidth="1px"
      borderColor="gray.200"
    >
      <Flex align="center" gap={2} flexWrap="wrap">
        {breadcrumbs.map((item, index) => (
          <Flex key={item.path} align="center" gap={2}>
            {index > 0 && (
              <Icon as={FiChevronRight} color="gray.400" fontSize="sm" />
            )}

            {index === breadcrumbs.length - 1 ? (
              // Current page - not clickable
              <Flex align="center" gap={2}>
                {item.icon && <Icon as={item.icon} fontSize="md" color="blue.500" />}
                <Text
                  fontSize="sm"
                  fontWeight="semibold"
                  color="blue.600"
                  aria-current="page"
                >
                  {item.label}
                </Text>
              </Flex>
            ) : (
              // Clickable breadcrumb
              <Link to={item.path}>
                <Flex
                  align="center"
                  gap={2}
                  _hover={{ color: "blue.600" }}
                  transition="color 0.2s"
                  cursor="pointer"
                >
                  {item.icon && <Icon as={item.icon} fontSize="md" />}
                  <Text
                    fontSize="sm"
                    color="gray.600"
                    _hover={{ color: "blue.600" }}
                  >
                    {item.label}
                  </Text>
                </Flex>
              </Link>
            )}
          </Flex>
        ))}
      </Flex>
    </Box>
  )
}

export default Breadcrumb
