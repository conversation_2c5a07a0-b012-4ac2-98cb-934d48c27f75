import { Box, Flex, Icon, <PERSON>, Badge, <PERSON><PERSON><PERSON><PERSON>, Separator } from "@chakra-ui/react"
import { useQueryClient } from "@tanstack/react-query"
import { Link as RouterLink, useRouter } from "@tanstack/react-router"
import {
  FiBriefcase,
  FiHome,
  FiSettings,
  FiUsers,
  FiUpload,
  FiFolder,
  FiMessageSquare,
  FiBell,
  FiBarChart,
  FiSearch,
  FiCalendar,
  FiShield,
  FiUser,
  FiFileText,
  FiMail,
  FiCheckSquare
} from "react-icons/fi"
import type { IconType } from "react-icons/lib"

import type { UserPublic } from "@/client"

interface MenuItem {
  icon: IconType
  title: string
  path: string
  badge?: string
  roles?: string[]
  category?: string
}

const menuItems: MenuItem[] = [
  // Main Navigation
  { icon: FiHome, title: "Dashboard", path: "/", category: "main" },

  // Case Management
  { icon: FiFolder, title: "Legal Cases", path: "/legal-cases", roles: ['admin', 'lawyer', 'assistant'], category: "cases" },
  { icon: <PERSON><PERSON><PERSON><PERSON><PERSON>, title: "Calendar", path: "/calendar", roles: ['admin', 'lawyer', 'assistant'], category: "cases" },
  { icon: FiBarChart, title: "Reports", path: "/reports", roles: ['admin', 'lawyer'], category: "cases" },

  // Document Management
  { icon: FiUpload, title: "Documents", path: "/documents", category: "documents" },
  { icon: FiSearch, title: "Search", path: "/search", category: "documents" },

  // Communication & Team Collaboration
  { icon: FiMessageSquare, title: "Messages", path: "/messages", category: "communication" },
  { icon: FiBell, title: "Notifications", path: "/notifications", category: "communication" },
  { icon: FiUsers, title: "Team Collaboration", path: "/team", roles: ['admin', 'lawyer', 'assistant'], category: "communication" },

  // Legacy/Other
  { icon: FiBriefcase, title: "Items", path: "/items", category: "other" },

  // Settings
  { icon: FiSettings, title: "User Settings", path: "/settings", category: "settings" },
]

// Client Portal Items (only for clients)
const clientPortalItems: MenuItem[] = [
  { icon: FiHome, title: "Portal Dashboard", path: "/client-portal", roles: ['client'], category: "client_portal" },
  { icon: FiFolder, title: "My Cases", path: "/client-portal/cases", roles: ['client'], category: "client_portal" },
  { icon: FiMail, title: "Messages", path: "/client-portal/messages", roles: ['client'], category: "client_portal" },
  { icon: FiBell, title: "Notifications", path: "/client-portal/notifications", roles: ['client'], category: "client_portal" },
  { icon: FiFileText, title: "Documents", path: "/client-portal/documents", roles: ['client'], category: "client_portal" },
]

const adminItems: MenuItem[] = [
  { icon: FiUsers, title: "User Management", path: "/admin", category: "admin" },
  { icon: FiShield, title: "Security", path: "/admin/security", category: "admin" },
  { icon: FiBarChart, title: "Analytics", path: "/admin/analytics", category: "admin" },
]

interface SidebarItemsProps {
  onClose?: () => void
  collapsed?: boolean
}

const SidebarItems = ({ onClose, collapsed = false }: SidebarItemsProps) => {
  const queryClient = useQueryClient()
  const currentUser = queryClient.getQueryData<UserPublic>(["currentUser"])
  const router = useRouter()
  const location = router.state.location

  // Filter items based on user role
  const getFilteredItems = (items: MenuItem[]) => {
    return items.filter(item => {
      if (item.roles) {
        return currentUser?.role && item.roles.includes(currentUser.role)
      }
      return true
    })
  }

  const filteredMenuItems = getFilteredItems(menuItems)
  const filteredAdminItems = currentUser?.is_superuser ? getFilteredItems(adminItems) : []
  const filteredClientPortalItems = currentUser?.role === 'client' ? getFilteredItems(clientPortalItems) : []

  // Group items by category
  const groupedItems = filteredMenuItems.reduce((acc, item) => {
    const category = item.category || 'other'
    if (!acc[category]) acc[category] = []
    acc[category].push(item)
    return acc
  }, {} as Record<string, MenuItem[]>)

  // Group client portal items by category
  const groupedClientPortalItems = filteredClientPortalItems.reduce((acc, item) => {
    const category = item.category || 'other'
    if (!acc[category]) acc[category] = []
    acc[category].push(item)
    return acc
  }, {} as Record<string, MenuItem[]>)

  const renderMenuItem = (item: MenuItem) => {
    const isActive = location.pathname === item.path

    return (
      <RouterLink key={item.path} to={item.path} onClick={onClose}>
        <Flex
          gap={3}
          px={4}
          py={3}
          mx={2}
          borderRadius="md"
          _hover={{
            bg: "gray.100",
            transform: "translateX(2px)",
          }}
          bg={isActive ? "blue.50" : "transparent"}
          borderLeft={isActive ? "3px solid" : "3px solid transparent"}
          borderColor={isActive ? "blue.500" : "transparent"}
          alignItems="center"
          fontSize="sm"
          fontWeight={isActive ? "semibold" : "normal"}
          color={isActive ? "blue.700" : "gray.700"}
          transition="all 0.2s"
          cursor="pointer"
          role="menuitem"
          aria-current={isActive ? "page" : undefined}
        >
          <Icon as={item.icon} fontSize="lg" />
          {!collapsed && (
            <>
              <Text flex="1">{item.title}</Text>
              {item.badge && (
                <Badge size="sm" colorScheme="blue">
                  {item.badge}
                </Badge>
              )}
            </>
          )}
        </Flex>
      </RouterLink>
    )
  }

  const renderSection = (title: string, items: MenuItem[]) => {
    if (items.length === 0) return null

    return (
      <Box key={title} mb={4}>
        {!collapsed && (
          <Text
            fontSize="xs"
            px={4}
            py={2}
            fontWeight="bold"
            color="gray.500"
            textTransform="uppercase"
            letterSpacing="wider"
          >
            {title}
          </Text>
        )}
        <VStack gap={1} align="stretch">
          {items.map(renderMenuItem)}
        </VStack>
      </Box>
    )
  }

  return (
    <Box role="navigation" aria-label="Main navigation">
      {/* Client Portal Section (only for clients) */}
      {filteredClientPortalItems.length > 0 && (
        <>
          {renderSection("Client Portal", groupedClientPortalItems.client_portal || [])}
          {!collapsed && <Separator my={4} />}
        </>
      )}

      {/* Regular Navigation (for non-clients) */}
      {currentUser?.role !== 'client' && (
        <>
          {renderSection("Main", groupedItems.main || [])}
          {renderSection("Cases", groupedItems.cases || [])}
          {renderSection("Documents", groupedItems.documents || [])}
          {renderSection("Communication", groupedItems.communication || [])}
          {renderSection("Other", groupedItems.other || [])}
        </>
      )}

      {filteredAdminItems.length > 0 && (
        <>
          {!collapsed && <Separator my={4} />}
          {renderSection("Administration", filteredAdminItems)}
        </>
      )}

      {!collapsed && <Separator my={4} />}
      {renderSection("Settings", groupedItems.settings || [])}
    </Box>
  )
}

export default SidebarItems
