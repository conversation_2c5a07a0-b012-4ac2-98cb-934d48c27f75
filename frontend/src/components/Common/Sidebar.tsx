import { Box, Flex, IconButton, Text, VStack } from "@chakra-ui/react"
import { useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { FaBars } from "react-icons/fa"
import { FiLogOut, FiChevronLeft, FiChevronRight } from "react-icons/fi"

import type { UserPublic } from "@/client"
import useAuth from "@/hooks/useAuth"
import {
  DrawerBackdrop,
  DrawerBody,
  DrawerCloseTrigger,
  DrawerContent,
  DrawerRoot,
  DrawerTrigger,
} from "../ui/drawer"
import { Avatar } from "../ui/avatar"
import { Tooltip } from "../ui/tooltip"
import SidebarItems from "./SidebarItems"

const Sidebar = () => {
  const queryClient = useQueryClient()
  const currentUser = queryClient.getQueryData<UserPublic>(["currentUser"])
  const { logout } = useAuth()
  const [open, setOpen] = useState(false)
  const [collapsed, setCollapsed] = useState(false)

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'admin': return 'red'
      case 'lawyer': return 'blue'
      case 'assistant': return 'green'
      case 'client': return 'purple'
      default: return 'gray'
    }
  }

  return (
    <>
      {/* Mobile */}
      <DrawerRoot
        placement="start"
        open={open}
        onOpenChange={(e) => setOpen(e.open)}
      >
        <DrawerBackdrop />
        <DrawerTrigger asChild>
          <IconButton
            variant="ghost"
            color="inherit"
            display={{ base: "flex", md: "none" }}
            aria-label="Open Menu"
            position="absolute"
            zIndex="100"
            m={4}
          >
            <FaBars />
          </IconButton>
        </DrawerTrigger>
        <DrawerContent maxW="xs">
          <DrawerCloseTrigger />
          <DrawerBody p={0}>
            <VStack h="100%" justify="space-between" align="stretch" p={4}>
              <Box flex="1" overflowY="auto">
                {/* User Profile Section */}
                {currentUser && (
                  <Box mb={6} p={4} bg="gray.50" borderRadius="lg">
                    <Flex align="center" gap={3}>
                      <Avatar
                        size="md"
                        name={currentUser.full_name || currentUser.email}
                        bg={`${getRoleColor(currentUser.role)}.500`}
                      />
                      <Box flex="1">
                        <Text fontWeight="semibold" fontSize="sm" truncate>
                          {currentUser.full_name || currentUser.email}
                        </Text>
                        <Text fontSize="xs" color="gray.600" textTransform="capitalize">
                          {currentUser.role || 'User'}
                        </Text>
                      </Box>
                    </Flex>
                  </Box>
                )}

                <SidebarItems onClose={() => setOpen(false)} />
              </Box>

              {/* Logout Button */}
              <Box borderTop="1px" borderColor="gray.200" pt={4}>
                <Flex
                  as="button"
                  onClick={() => {
                    logout()
                  }}
                  alignItems="center"
                  gap={3}
                  px={4}
                  py={3}
                  w="100%"
                  borderRadius="md"
                  _hover={{ bg: "red.50", color: "red.600" }}
                  transition="all 0.2s"
                >
                  <FiLogOut />
                  <Text>Log Out</Text>
                </Flex>
              </Box>
            </VStack>
          </DrawerBody>
          <DrawerCloseTrigger />
        </DrawerContent>
      </DrawerRoot>

      {/* Desktop */}
      <Box
        display={{ base: "none", md: "flex" }}
        position="sticky"
        bg="white"
        borderRight="1px"
        borderColor="gray.200"
        top={0}
        w={collapsed ? "80px" : "280px"}
        h="100vh"
        transition="width 0.3s ease"
        flexDirection="column"
      >
        {/* Header with collapse toggle */}
        <Flex
          justify={collapsed ? "center" : "space-between"}
          align="center"
          p={4}
          borderBottom="1px"
          borderColor="gray.200"
        >
          {!collapsed && (
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              EquiNova
            </Text>
          )}
          <Tooltip label={collapsed ? "Expand sidebar" : "Collapse sidebar"} placement="right">
            <IconButton
              variant="ghost"
              size="sm"
              onClick={() => setCollapsed(!collapsed)}
              aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {collapsed ? <FiChevronRight /> : <FiChevronLeft />}
            </IconButton>
          </Tooltip>
        </Flex>

        {/* User Profile Section */}
        {currentUser && (
          <Box p={4} borderBottom="1px" borderColor="gray.100">
            <Flex align="center" gap={3}>
              <Avatar
                size={collapsed ? "sm" : "md"}
                name={currentUser.full_name || currentUser.email}
                bg={`${getRoleColor(currentUser.role)}.500`}
              />
              {!collapsed && (
                <Box flex="1" minW={0}>
                  <Text fontWeight="semibold" fontSize="sm" truncate>
                    {currentUser.full_name || currentUser.email}
                  </Text>
                  <Text fontSize="xs" color="gray.600" textTransform="capitalize">
                    {currentUser.role || 'User'}
                  </Text>
                </Box>
              )}
            </Flex>
          </Box>
        )}

        {/* Navigation Items */}
        <Box flex="1" overflowY="auto" p={2}>
          <SidebarItems collapsed={collapsed} />
        </Box>

        {/* Logout Button */}
        <Box borderTop="1px" borderColor="gray.200" p={4}>
          <Tooltip label="Log Out" placement="right" isDisabled={!collapsed}>
            <Flex
              as="button"
              onClick={() => logout()}
              alignItems="center"
              gap={3}
              px={3}
              py={3}
              w="100%"
              borderRadius="md"
              _hover={{ bg: "red.50", color: "red.600" }}
              transition="all 0.2s"
              justify={collapsed ? "center" : "flex-start"}
            >
              <FiLogOut />
              {!collapsed && <Text>Log Out</Text>}
            </Flex>
          </Tooltip>
        </Box>
      </Box>
    </>
  )
}

export default Sidebar
