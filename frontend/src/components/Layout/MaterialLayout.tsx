/**
=========================================================
* EquiNova Material Layout
=========================================================

* Material-UI based layout component
* Replaces Chakra UI layout with Material Design patterns

=========================================================
*/

import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  useTheme,
  useMediaQuery,
  Container,
  Paper,
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';

import { useMaterialUIController } from '../../themes/context';
import MaterialSidebar from './MaterialSidebar';
import MaterialNavbar from './MaterialNavbar';
import MaterialBreadcrumb from './MaterialBreadcrumb';
import { ThemeControls } from '../../themes/components/ThemeToggle';
import ThemeConfigurator from '../../themes/components/ThemeConfigurator';

const DRAWER_WIDTH = 280;
const DRAWER_WIDTH_COLLAPSED = 80;

interface MaterialLayoutProps {
  children: React.ReactNode;
}

const MaterialLayout: React.FC<MaterialLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { state } = useMaterialUIController();
  const { miniSidenav, darkMode } = state;

  const [mobileOpen, setMobileOpen] = useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const drawerWidth = miniSidenav ? DRAWER_WIDTH_COLLAPSED : DRAWER_WIDTH;

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: isMobile ? '100%' : `calc(100% - ${drawerWidth}px)`,
          ml: isMobile ? 0 : `${drawerWidth}px`,
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          backgroundColor: darkMode ? theme.palette.grey[900] : theme.palette.background.paper,
          color: theme.palette.text.primary,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Toolbar>
          {isMobile && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}

          <Box sx={{ flexGrow: 1 }}>
            <MaterialNavbar />
          </Box>

          <ThemeControls />
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{
          width: isMobile ? 0 : drawerWidth,
          flexShrink: 0,
        }}
      >
        {/* Mobile Drawer */}
        {isMobile && (
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true, // Better open performance on mobile.
            }}
            sx={{
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: DRAWER_WIDTH,
                backgroundColor: darkMode ? theme.palette.grey[900] : theme.palette.background.paper,
                borderRight: `1px solid ${theme.palette.divider}`,
              },
            }}
          >
            <MaterialSidebar onClose={handleDrawerToggle} />
          </Drawer>
        )}

        {/* Desktop Drawer */}
        {!isMobile && (
          <Drawer
            variant="permanent"
            sx={{
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth,
                transition: theme.transitions.create('width', {
                  easing: theme.transitions.easing.sharp,
                  duration: theme.transitions.duration.enteringScreen,
                }),
                backgroundColor: darkMode ? theme.palette.grey[900] : theme.palette.background.paper,
                borderRight: `1px solid ${theme.palette.divider}`,
                overflowX: 'hidden',
              },
            }}
            open
          >
            <MaterialSidebar collapsed={miniSidenav} />
          </Drawer>
        )}
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: isMobile ? '100%' : `calc(100% - ${drawerWidth}px)`,
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        {/* Toolbar spacer */}
        <Toolbar />

        {/* Content Container */}
        <Container
          maxWidth={false}
          sx={{
            py: 3,
            px: { xs: 2, sm: 3 },
            backgroundColor: darkMode ? theme.palette.grey[100] : theme.palette.grey[50],
            minHeight: 'calc(100vh - 64px)',
          }}
        >
          {/* Breadcrumb */}
          <MaterialBreadcrumb />

          {/* Main Content Card */}
          <Paper
            elevation={1}
            sx={{
              p: { xs: 2, sm: 3 },
              borderRadius: 2,
              backgroundColor: theme.palette.background.paper,
              minHeight: 'calc(100vh - 140px)',
              mt: 2,
            }}
          >
            {children}
          </Paper>
        </Container>
      </Box>

      {/* Theme Configurator */}
      <ThemeConfigurator />
    </Box>
  );
};

export default MaterialLayout;
