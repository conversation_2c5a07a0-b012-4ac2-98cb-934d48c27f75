/**
=========================================================
* EquiNova Material Breadcrumb
=========================================================

* Material-UI based breadcrumb component
* Replaces Chakra UI breadcrumb with Material Design patterns

=========================================================
*/

import React from 'react';
import { Breadcrumbs, Link, Typography, Box } from '@mui/material';
import { NavigateNext as NavigateNextIcon, Home as HomeIcon } from '@mui/icons-material';
import { Link as RouterLink, useRouter } from '@tanstack/react-router';

const MaterialBreadcrumb: React.FC = () => {
  const router = useRouter();
  const location = router.state.location;
  
  // Generate breadcrumb items from current path
  const pathSegments = location.pathname.split('/').filter(Boolean);
  
  // Create breadcrumb items
  const breadcrumbItems = [
    {
      label: 'Dashboard',
      path: '/',
      icon: <HomeIcon sx={{ fontSize: 16, mr: 0.5 }} />,
    },
  ];

  // Add path segments as breadcrumb items
  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Convert segment to readable label
    const label = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
    
    breadcrumbItems.push({
      label,
      path: currentPath,
    });
  });

  // Don't show breadcrumb if we're on the home page
  if (location.pathname === '/') {
    return null;
  }

  return (
    <Box sx={{ mb: 2 }}>
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{
          '& .MuiBreadcrumbs-separator': {
            color: 'text.secondary',
          },
        }}
      >
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          
          if (isLast) {
            return (
              <Typography
                key={item.path}
                color="text.primary"
                fontWeight="medium"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                {item.icon}
                {item.label}
              </Typography>
            );
          }
          
          return (
            <Link
              key={item.path}
              component={RouterLink}
              to={item.path}
              underline="hover"
              color="inherit"
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                '&:hover': {
                  color: 'primary.main',
                },
              }}
            >
              {item.icon}
              {item.label}
            </Link>
          );
        })}
      </Breadcrumbs>
    </Box>
  );
};

export default MaterialBreadcrumb;
