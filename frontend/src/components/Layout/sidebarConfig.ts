/**
=========================================================
* EquiNova Sidebar Configuration
=========================================================

* Configuration for sidebar menu items and navigation
* Shared between Chakra UI and Material-UI implementations

=========================================================
*/

import {
  FiBriefcase,
  FiHome,
  FiSettings,
  FiUsers,
  FiUpload,
  FiFolder,
  FiMessageSquare,
  FiBell,
  FiBarChart,
  FiSearch,
  FiCalendar,
  FiShield
} from "react-icons/fi";
import type { IconType } from "react-icons/lib";

export interface MenuItem {
  icon: IconType;
  title: string;
  path: string;
  badge?: string;
  roles?: string[];
  category?: string;
}

export const menuItems: MenuItem[] = [
  // Main Navigation
  { icon: FiHome, title: "Dashboard", path: "/", category: "main" },

  // Case Management
  { icon: FiFolder, title: "Legal Cases", path: "/legal-cases", roles: ['admin', 'lawyer', 'assistant'], category: "cases" },
  { icon: FiCalendar, title: "Calendar", path: "/calendar", roles: ['admin', 'lawyer', 'assistant'], category: "cases" },
  { icon: FiBarChart, title: "Reports", path: "/reports", roles: ['admin', 'lawyer'], category: "cases" },

  // Document Management
  { icon: FiUpload, title: "Documents", path: "/documents", category: "documents" },
  { icon: FiSearch, title: "Search", path: "/search", category: "documents" },

  // Communication
  { icon: FiMessageSquare, title: "Messages", path: "/messages", category: "communication" },
  { icon: FiBell, title: "Notifications", path: "/notifications", category: "communication" },

  // Legacy/Other
  { icon: FiBriefcase, title: "Items", path: "/items", category: "other" },

  // Settings
  { icon: FiSettings, title: "User Settings", path: "/settings", category: "settings" },
];

export const adminItems: MenuItem[] = [
  { icon: FiUsers, title: "User Management", path: "/admin", category: "admin" },
  { icon: FiShield, title: "Security", path: "/admin/security", category: "admin" },
  { icon: FiBarChart, title: "Analytics", path: "/admin/analytics", category: "admin" },
];

// Helper function to get role color
export const getRoleColor = (role?: string): string => {
  switch (role) {
    case 'admin':
      return 'red';
    case 'lawyer':
      return 'blue';
    case 'assistant':
      return 'green';
    case 'client':
      return 'purple';
    default:
      return 'gray';
  }
};

// Category display names
export const categoryNames: Record<string, string> = {
  main: 'Main',
  cases: 'Case Management',
  documents: 'Documents',
  communication: 'Communication',
  other: 'Other',
  admin: 'Administration',
  settings: 'Settings',
};

// Category icons
export const categoryIcons: Record<string, IconType> = {
  main: FiHome,
  cases: FiFolder,
  documents: FiUpload,
  communication: FiMessageSquare,
  other: FiBriefcase,
  admin: FiShield,
  settings: FiSettings,
};
