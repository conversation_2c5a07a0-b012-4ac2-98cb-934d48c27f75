/**
=========================================================
* EquiNova Material Sidebar
=========================================================

* Material-UI based sidebar component
* Replaces Chakra UI sidebar with Material Design patterns

=========================================================
*/

import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
  Collapse,
  useTheme,
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  ExpandLess,
  ExpandMore,
} from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import { Link as RouterLink, useRouter } from '@tanstack/react-router';

import type { UserPublic } from '@/client';
import { useMaterialUIController, setMiniSidenav } from '../../themes/context';
import { menuItems, adminItems, getRoleColor } from './sidebarConfig';

interface MaterialSidebarProps {
  onClose?: () => void;
  collapsed?: boolean;
}

const MaterialSidebar: React.FC<MaterialSidebarProps> = ({ onClose, collapsed = false }) => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const currentUser = queryClient.getQueryData<UserPublic>(['currentUser']);
  const router = useRouter();
  const location = router.state.location;
  const { state, dispatch } = useMaterialUIController();
  const { darkMode } = state;

  const [expandedSections, setExpandedSections] = React.useState<Record<string, boolean>>({
    main: true,
    cases: true,
    documents: false,
    communication: false,
  });

  const handleToggleCollapse = () => {
    setMiniSidenav(dispatch, !collapsed);
  };

  const handleSectionToggle = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // Filter items based on user role
  const getFilteredItems = (items: typeof menuItems) => {
    return items.filter(item => {
      if (item.roles) {
        return currentUser?.role && item.roles.includes(currentUser.role);
      }
      return true;
    });
  };

  const filteredMenuItems = getFilteredItems(menuItems);
  const filteredAdminItems = currentUser?.is_superuser ? getFilteredItems(adminItems) : [];

  // Group items by category
  const groupedItems = filteredMenuItems.reduce((acc, item) => {
    const category = item.category || 'other';
    if (!acc[category]) acc[category] = [];
    acc[category].push(item);
    return acc;
  }, {} as Record<string, typeof menuItems>);

  const renderMenuItem = (item: typeof menuItems[0]) => {
    const isActive = location.pathname === item.path;
    const IconComponent = item.icon;

    return (
      <ListItem key={item.path} disablePadding>
        <ListItemButton
          component={RouterLink}
          to={item.path}
          onClick={onClose}
          selected={isActive}
          sx={{
            minHeight: 48,
            px: 2,
            py: 1,
            mx: 1,
            borderRadius: 1,
            '&.Mui-selected': {
              backgroundColor: theme.palette.primary.main + '20',
              borderLeft: `3px solid ${theme.palette.primary.main}`,
              '& .MuiListItemIcon-root': {
                color: theme.palette.primary.main,
              },
              '& .MuiListItemText-primary': {
                color: theme.palette.primary.main,
                fontWeight: 600,
              },
            },
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
              transform: 'translateX(2px)',
            },
            transition: 'all 0.2s ease',
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: collapsed ? 0 : 40,
              justifyContent: 'center',
            }}
          >
            <IconComponent size={20} />
          </ListItemIcon>
          {!collapsed && (
            <>
              <ListItemText
                primary={item.title}
                primaryTypographyProps={{
                  fontSize: '0.875rem',
                  fontWeight: isActive ? 600 : 400,
                }}
              />
              {item.badge && (
                <Chip
                  label={item.badge}
                  size="small"
                  color="primary"
                  sx={{ height: 20, fontSize: '0.75rem' }}
                />
              )}
            </>
          )}
        </ListItemButton>
      </ListItem>
    );
  };

  const renderSection = (title: string, items: typeof menuItems, sectionKey: string) => {
    if (items.length === 0) return null;

    const isExpanded = expandedSections[sectionKey];

    return (
      <Box key={title} sx={{ mb: 1 }}>
        {!collapsed && (
          <ListItem disablePadding>
            <ListItemButton
              onClick={() => handleSectionToggle(sectionKey)}
              sx={{
                minHeight: 32,
                px: 2,
                py: 0.5,
              }}
            >
              <ListItemText
                primary={title}
                primaryTypographyProps={{
                  fontSize: '0.75rem',
                  fontWeight: 700,
                  color: theme.palette.text.secondary,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                }}
              />
              {isExpanded ? <ExpandLess /> : <ExpandMore />}
            </ListItemButton>
          </ListItem>
        )}
        <Collapse in={collapsed || isExpanded} timeout="auto" unmountOnExit>
          <List disablePadding>
            {items.map(renderMenuItem)}
          </List>
        </Collapse>
      </Box>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'space-between',
          borderBottom: `1px solid ${theme.palette.divider}`,
          minHeight: 64,
        }}
      >
        {!collapsed && (
          <Typography variant="h6" fontWeight="bold" color="primary">
            EquiNova
          </Typography>
        )}
        {!onClose && (
          <Tooltip title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}>
            <IconButton onClick={handleToggleCollapse} size="small">
              {collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {/* User Profile */}
      {currentUser && (
        <Box
          sx={{
            p: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
            display: 'flex',
            alignItems: 'center',
            gap: collapsed ? 0 : 2,
            justifyContent: collapsed ? 'center' : 'flex-start',
          }}
        >
          <Avatar
            sx={{
              width: collapsed ? 32 : 40,
              height: collapsed ? 32 : 40,
              bgcolor: `${getRoleColor(currentUser.role)}.500`,
              fontSize: collapsed ? '0.875rem' : '1rem',
            }}
          >
            {(currentUser.full_name || currentUser.email).charAt(0).toUpperCase()}
          </Avatar>
          {!collapsed && (
            <Box sx={{ minWidth: 0, flex: 1 }}>
              <Typography variant="body2" fontWeight="semibold" noWrap>
                {currentUser.full_name || currentUser.email}
              </Typography>
              <Typography variant="caption" color="text.secondary" textTransform="capitalize">
                {currentUser.role || 'User'}
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* Navigation */}
      <Box sx={{ flex: 1, overflowY: 'auto', py: 1 }}>
        <List disablePadding>
          {renderSection('Main', groupedItems.main || [], 'main')}
          {renderSection('Cases', groupedItems.cases || [], 'cases')}
          {renderSection('Documents', groupedItems.documents || [], 'documents')}
          {renderSection('Communication', groupedItems.communication || [], 'communication')}
          {renderSection('Other', groupedItems.other || [], 'other')}

          {filteredAdminItems.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              {renderSection('Administration', filteredAdminItems, 'admin')}
            </>
          )}

          <Divider sx={{ my: 2 }} />
          {renderSection('Settings', groupedItems.settings || [], 'settings')}
        </List>
      </Box>
    </Box>
  );
};

export default MaterialSidebar;
