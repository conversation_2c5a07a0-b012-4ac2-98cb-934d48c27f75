/**
=========================================================
* EquiNova Material Navbar
=========================================================

* Material-UI based navbar component
* Replaces Chakra UI navbar with Material Design patterns

=========================================================
*/

import React from 'react';
import { Box, Typography, Avatar } from '@mui/material';
import { Link as RouterLink } from '@tanstack/react-router';

import Logo from '/assets/images/fastapi-logo.svg';
import UserMenu from '../Common/UserMenu';

const MaterialNavbar: React.FC = () => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      {/* Logo and Brand */}
      <Box 
        component={RouterLink} 
        to="/" 
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2, 
          textDecoration: 'none',
          '&:hover': { opacity: 0.8 },
          transition: 'opacity 0.2s',
        }}
      >
        <Box
          component="img"
          src={Logo}
          alt="EquiNova Logo"
          sx={{ height: 32 }}
        />
        <Box>
          <Typography 
            variant="h6" 
            fontWeight="bold" 
            color="primary"
            sx={{ lineHeight: 1 }}
          >
            EquiNova
          </Typography>
          <Typography 
            variant="caption" 
            color="text.secondary"
            sx={{ lineHeight: 1, mt: -0.5 }}
          >
            Legal Management System
          </Typography>
        </Box>
      </Box>

      {/* Spacer */}
      <Box sx={{ flexGrow: 1 }} />

      {/* User Menu */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <UserMenu />
      </Box>
    </Box>
  );
};

export default MaterialNavbar;
