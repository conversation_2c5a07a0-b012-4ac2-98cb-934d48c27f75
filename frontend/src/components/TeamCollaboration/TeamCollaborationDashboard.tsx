import React from 'react'
import {
  Box,
  Container,
  Heading,
  SimpleGrid,
  Card,
  CardBody,
  VStack,
  HStack,
  Text,
  Button,
  Icon,
} from '@chakra-ui/react'
import {
  FiUsers,
  FiMessageSquare,
  FiCheckSquare,
  FiArrowRight,
  FiCalendar,
  FiBarChart,
} from 'react-icons/fi'

import { useColorModeValue } from '@/components/ui/color-mode'

const TeamCollaborationDashboard: React.FC = () => {
  const cardBg = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const features = [
    {
      title: 'Team Management',
      description: 'Manage team members, roles, and permissions for your cases',
      icon: FiUsers,
      color: 'blue',
      path: '/cases',
      features: [
        'Add/remove team members',
        'Assign roles and permissions',
        'Track team member activities',
        'Manage access controls'
      ]
    },
    {
      title: 'Team Chat',
      description: 'Internal messaging and communication for case teams',
      icon: FiMessageSquare,
      color: 'green',
      path: '/cases',
      features: [
        'Real-time team messaging',
        'Case-specific discussions',
        'Message threading and replies',
        'Urgent message alerts'
      ]
    },
    {
      title: 'Task Management',
      description: 'Create, assign, and track tasks for your team members',
      icon: FiCheckSquare,
      color: 'orange',
      path: '/cases',
      features: [
        'Create and assign tasks',
        'Set priorities and deadlines',
        'Track progress and completion',
        'Task dependencies and workflows'
      ]
    }
  ]

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box textAlign="center">
          <Heading size="xl" mb={4}>
            Team Collaboration
          </Heading>
          <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
            Enhance your team's productivity with powerful collaboration tools designed for legal professionals
          </Text>
        </Box>

        {/* Features Grid */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8}>
          {features.map((feature, index) => (
            <Card
              key={index}
              bg={cardBg}
              borderColor={borderColor}
              variant="outline"
              _hover={{ shadow: 'lg', transform: 'translateY(-2px)' }}
              transition="all 0.2s"
            >
              <CardBody>
                <VStack spacing={4} align="stretch" h="full">
                  {/* Header */}
                  <HStack spacing={3}>
                    <Box
                      p={3}
                      borderRadius="lg"
                      bg={`${feature.color}.100`}
                      color={`${feature.color}.600`}
                    >
                      <Icon as={feature.icon} boxSize={6} />
                    </Box>
                    <VStack align="start" spacing={0} flex={1}>
                      <Text fontSize="lg" fontWeight="bold">
                        {feature.title}
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        {feature.description}
                      </Text>
                    </VStack>
                  </HStack>

                  {/* Features List */}
                  <VStack align="start" spacing={2} flex={1}>
                    {feature.features.map((item, index) => (
                      <HStack key={index} spacing={2}>
                        <Box w={2} h={2} borderRadius="full" bg={`${feature.color}.500`} />
                        <Text fontSize="sm" color="gray.700">
                          {item}
                        </Text>
                      </HStack>
                    ))}
                  </VStack>

                  {/* Action Button */}
                  <Button
                    colorScheme={feature.color}
                    variant="solid"
                    rightIcon={<FiArrowRight />}
                    onClick={() => {
                      window.location.href = feature.path
                    }}
                  >
                    Get Started
                  </Button>
                </VStack>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>

        {/* Getting Started */}
        <Card bg={cardBg} borderColor={borderColor} variant="outline">
          <CardBody>
            <VStack spacing={6} align="stretch">
              <Heading size="md" textAlign="center">
                Getting Started with Team Collaboration
              </Heading>
              
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
                <VStack spacing={3} textAlign="center">
                  <Box
                    p={4}
                    borderRadius="full"
                    bg="blue.100"
                    color="blue.600"
                  >
                    <Icon as={FiUsers} boxSize={8} />
                  </Box>
                  <Text fontWeight="semibold">1. Set Up Your Team</Text>
                  <Text fontSize="sm" color="gray.600">
                    Add team members to your cases and assign appropriate roles and permissions
                  </Text>
                </VStack>

                <VStack spacing={3} textAlign="center">
                  <Box
                    p={4}
                    borderRadius="full"
                    bg="green.100"
                    color="green.600"
                  >
                    <Icon as={FiMessageSquare} boxSize={8} />
                  </Box>
                  <Text fontWeight="semibold">2. Start Collaborating</Text>
                  <Text fontSize="sm" color="gray.600">
                    Use team chat for real-time communication and case discussions
                  </Text>
                </VStack>

                <VStack spacing={3} textAlign="center">
                  <Box
                    p={4}
                    borderRadius="full"
                    bg="orange.100"
                    color="orange.600"
                  >
                    <Icon as={FiCheckSquare} boxSize={8} />
                  </Box>
                  <Text fontWeight="semibold">3. Manage Tasks</Text>
                  <Text fontSize="sm" color="gray.600">
                    Create and assign tasks to keep your team organized and on track
                  </Text>
                </VStack>
              </SimpleGrid>

              <Box textAlign="center">
                <Button
                  colorScheme="blue"
                  size="lg"
                  rightIcon={<FiArrowRight />}
                  onClick={() => {
                    window.location.href = '/legal-cases'
                  }}
                >
                  Go to Cases to Get Started
                </Button>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

export default TeamCollaborationDashboard
