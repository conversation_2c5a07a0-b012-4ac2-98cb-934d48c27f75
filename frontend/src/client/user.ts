import axios from "axios"

export interface User {
  id: string
  email: string
  full_name: string
  role: string
  bar_association_id?: string
  specialization?: string
}

export const createUser = async (user: Partial<User>) => {
  const response = await axios.post("/api/users", user)
  return response.data
}

export const getUser = async (id: string) => {
  const response = await axios.get(`/api/users/${id}`)
  return response.data
}

export const updateUser = async (id: string, user: Partial<User>) => {
  const response = await axios.put(`/api/users/${id}`, user)
  return response.data
}

export const deleteUser = async (id: string) => {
  const response = await axios.delete(`/api/users/${id}`)
  return response.data
}
