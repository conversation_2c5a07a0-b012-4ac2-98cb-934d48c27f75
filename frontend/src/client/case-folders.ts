import axios from "axios"

export interface CaseFolder {
  id: string
  case_id: string
  name: string
  description?: string
  parent_folder_id?: string
  color?: string
  is_system_folder: boolean
  folder_order: number
  created_by: string
  created_at: string
  updated_at: string
  creator?: {
    id: string
    email: string
    full_name: string
    role: string
  }
  document_count: number
  subfolder_count: number
  total_size: number
  can_edit: boolean
  can_delete: boolean
  path: string[]
}

export interface CaseFolderCreate {
  name: string
  description?: string
  parent_folder_id?: string
  color?: string
}

export interface CaseFolderUpdate {
  name?: string
  description?: string
  parent_folder_id?: string
  color?: string
  folder_order?: number
}

export interface CaseFoldersResponse {
  data: CaseFolder[]
  count: number
}

export interface CaseFoldersParams {
  parent_folder_id?: string
  include_documents?: boolean
}

export const CaseFoldersService = {
  // Get folders for a case
  getCaseFolders: async (
    caseId: string,
    params?: CaseFoldersParams,
  ): Promise<CaseFoldersResponse> => {
    const response = await axios.get(`/api/v1/legal-cases/${caseId}/folders`, {
      params,
    })
    return response.data
  },

  // Create a new folder
  createCaseFolder: async (
    caseId: string,
    folderData: CaseFolderCreate,
  ): Promise<CaseFolder> => {
    const response = await axios.post(
      `/api/v1/legal-cases/${caseId}/folders`,
      folderData,
    )
    return response.data
  },

  // Update a folder
  updateCaseFolder: async (
    caseId: string,
    folderId: string,
    folderData: CaseFolderUpdate,
  ): Promise<CaseFolder> => {
    const response = await axios.put(
      `/api/v1/legal-cases/${caseId}/folders/${folderId}`,
      folderData,
    )
    return response.data
  },

  // Delete a folder
  deleteCaseFolder: async (
    caseId: string,
    folderId: string,
    moveContentsTo?: string,
  ): Promise<{ message: string }> => {
    const params = moveContentsTo ? { move_contents_to: moveContentsTo } : {}
    const response = await axios.delete(
      `/api/v1/legal-cases/${caseId}/folders/${folderId}`,
      { params },
    )
    return response.data
  },

  // Initialize default folder structure
  initializeCaseFolders: async (
    caseId: string,
  ): Promise<CaseFoldersResponse> => {
    const response = await axios.post(
      `/api/v1/legal-cases/${caseId}/folders/initialize`,
    )
    return response.data
  },
}

// Default folder colors
export const DEFAULT_FOLDER_COLORS = [
  "#ef4444", // red
  "#f97316", // orange
  "#eab308", // yellow
  "#22c55e", // green
  "#06b6d4", // cyan
  "#3b82f6", // blue
  "#8b5cf6", // violet
  "#ec4899", // pink
  "#6b7280", // gray
]

// Folder type configurations for system folders
export const SYSTEM_FOLDER_CONFIG = {
  Evidence: {
    label: "Evidence",
    description: "Photos, recordings, witness statements",
    color: "#ef4444",
    icon: "📸",
  },
  Contracts: {
    label: "Contracts",
    description: "Legal agreements, terms of service",
    color: "#3b82f6",
    icon: "📄",
  },
  Correspondence: {
    label: "Correspondence",
    description: "Emails, letters, communications",
    color: "#10b981",
    icon: "📧",
  },
  "Court Documents": {
    label: "Court Documents",
    description: "Filings, orders, judgments",
    color: "#8b5cf6",
    icon: "⚖️",
  },
  Research: {
    label: "Research",
    description: "Legal research, case law, precedents",
    color: "#06b6d4",
    icon: "🔍",
  },
  Internal: {
    label: "Internal",
    description: "Internal notes, drafts",
    color: "#6b7280",
    icon: "📝",
  },
}

// Utility functions
export const formatFolderSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes"

  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

export const getFolderIcon = (folder: CaseFolder): string => {
  if (folder.is_system_folder) {
    const config =
      SYSTEM_FOLDER_CONFIG[folder.name as keyof typeof SYSTEM_FOLDER_CONFIG]
    return config?.icon || "📁"
  }
  return "📁"
}

export const getFolderColor = (folder: CaseFolder): string => {
  return folder.color || "#6b7280"
}

export const buildFolderPath = (folder: CaseFolder): string => {
  if (folder.path.length === 0) {
    return folder.name
  }
  return [...folder.path, folder.name].join(" / ")
}

export const sortFolders = (folders: CaseFolder[]): CaseFolder[] => {
  return folders.sort((a, b) => {
    // System folders first
    if (a.is_system_folder && !b.is_system_folder) return -1
    if (!a.is_system_folder && b.is_system_folder) return 1

    // Then by folder order
    if (a.folder_order !== b.folder_order) {
      return a.folder_order - b.folder_order
    }

    // Finally by name
    return a.name.localeCompare(b.name)
  })
}

export const canMoveFolder = (
  folder: CaseFolder,
  targetFolder: CaseFolder | null,
): boolean => {
  // Cannot move system folders
  if (folder.is_system_folder) return false

  // Cannot move to itself
  if (targetFolder && folder.id === targetFolder.id) return false

  // Cannot move to a child folder (would create circular reference)
  if (targetFolder) {
    let current = targetFolder
    while (current.parent_folder_id) {
      if (current.parent_folder_id === folder.id) return false
      // We would need to find the parent folder to continue checking
      // This is a simplified check
      break
    }
  }

  return true
}

export const getMaxDepth = (folders: CaseFolder[]): number => {
  return Math.max(...folders.map((folder) => folder.path.length + 1), 0)
}
