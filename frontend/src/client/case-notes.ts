import axios from "axios"

export interface CaseNote {
  id: string
  case_id: string
  content: string
  note_type: "private" | "team" | "client" | "admin" | "task" | "communication"
  category:
    | "general"
    | "research"
    | "meeting"
    | "deadline"
    | "strategy"
    | "evidence"
    | "correspondence"
    | "billing"
  is_pinned: boolean
  tags?: string[]
  mentioned_users?: string[]
  parent_note_id?: string
  author_id: string
  created_at: string
  updated_at: string
  author?: {
    id: string
    email: string
    full_name: string
    role: string
  }
  replies_count: number
  can_edit: boolean
  can_delete: boolean
}

export interface CaseNoteCreate {
  content: string
  note_type?: "private" | "team" | "client" | "admin" | "task" | "communication"
  category?:
    | "general"
    | "research"
    | "meeting"
    | "deadline"
    | "strategy"
    | "evidence"
    | "correspondence"
    | "billing"
  is_pinned?: boolean
  tags?: string[]
  mentioned_users?: string[]
  parent_note_id?: string
}

export interface CaseNoteUpdate {
  content?: string
  note_type?: "private" | "team" | "client" | "admin" | "task" | "communication"
  category?:
    | "general"
    | "research"
    | "meeting"
    | "deadline"
    | "strategy"
    | "evidence"
    | "correspondence"
    | "billing"
  is_pinned?: boolean
  tags?: string[]
}

export interface CaseNotesResponse {
  data: CaseNote[]
  count: number
}

export interface CaseNotesParams {
  skip?: number
  limit?: number
  note_type?: string
  category?: string
  search?: string
  sort_by?: "created_at" | "updated_at" | "category"
  sort_order?: "asc" | "desc"
}

export const CaseNotesService = {
  // Get notes for a case
  getCaseNotes: async (
    caseId: string,
    params?: CaseNotesParams,
  ): Promise<CaseNotesResponse> => {
    const response = await axios.get(`/api/v1/legal-cases/${caseId}/notes`, {
      params,
    })
    return response.data
  },

  // Create a new note
  createCaseNote: async (
    caseId: string,
    noteData: CaseNoteCreate,
  ): Promise<CaseNote> => {
    const response = await axios.post(
      `/api/v1/legal-cases/${caseId}/notes`,
      noteData,
    )
    return response.data
  },

  // Update a note
  updateCaseNote: async (
    caseId: string,
    noteId: string,
    noteData: CaseNoteUpdate,
  ): Promise<CaseNote> => {
    const response = await axios.put(
      `/api/v1/legal-cases/${caseId}/notes/${noteId}`,
      noteData,
    )
    return response.data
  },

  // Delete a note
  deleteCaseNote: async (
    caseId: string,
    noteId: string,
  ): Promise<{ message: string }> => {
    const response = await axios.delete(
      `/api/v1/legal-cases/${caseId}/notes/${noteId}`,
    )
    return response.data
  },

  // Get replies to a note
  getNoteReplies: async (
    caseId: string,
    noteId: string,
    params?: { skip?: number; limit?: number },
  ): Promise<CaseNotesResponse> => {
    const response = await axios.get(
      `/api/v1/legal-cases/${caseId}/notes/${noteId}/replies`,
      { params },
    )
    return response.data
  },
}

// Note type configurations
export const NOTE_TYPE_CONFIG = {
  private: {
    label: "Private",
    description: "Only visible to you",
    color: "gray",
    icon: "🔒",
  },
  team: {
    label: "Team",
    description: "Visible to case team",
    color: "blue",
    icon: "👥",
  },
  client: {
    label: "Client",
    description: "Shared with client",
    color: "green",
    icon: "👤",
  },
  admin: {
    label: "Admin",
    description: "Admin and lawyers only",
    color: "purple",
    icon: "⚙️",
  },
  task: {
    label: "Task",
    description: "Task assignment",
    color: "orange",
    icon: "✅",
  },
  communication: {
    label: "Communication",
    description: "Client communication log",
    color: "teal",
    icon: "💬",
  },
}

// Category configurations
export const NOTE_CATEGORY_CONFIG = {
  general: {
    label: "General",
    description: "General notes",
    color: "gray",
    icon: "📝",
  },
  research: {
    label: "Research",
    description: "Legal research",
    color: "blue",
    icon: "🔍",
  },
  meeting: {
    label: "Meeting",
    description: "Meeting notes",
    color: "green",
    icon: "🤝",
  },
  deadline: {
    label: "Deadline",
    description: "Important deadlines",
    color: "red",
    icon: "⏰",
  },
  strategy: {
    label: "Strategy",
    description: "Case strategy",
    color: "purple",
    icon: "🎯",
  },
  evidence: {
    label: "Evidence",
    description: "Evidence notes",
    color: "orange",
    icon: "📋",
  },
  correspondence: {
    label: "Correspondence",
    description: "Letters and emails",
    color: "teal",
    icon: "📧",
  },
  billing: {
    label: "Billing",
    description: "Billing information",
    color: "yellow",
    icon: "💰",
  },
}
