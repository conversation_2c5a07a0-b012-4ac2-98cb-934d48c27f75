import axios from "axios"

export interface CaseDocument {
  id: string
  case_id: string
  folder_id?: string
  filename: string
  original_filename: string
  file_path: string
  content_type: string
  file_size: number
  category:
    | "evidence"
    | "contracts"
    | "correspondence"
    | "court_documents"
    | "research"
    | "billing"
    | "internal"
    | "other"
  description?: string
  tags?: string[]
  is_confidential: boolean
  is_shared_with_client: boolean
  uploaded_by: string
  uploaded_at: string
  updated_at: string
  uploader?: {
    id: string
    email: string
    full_name: string
    role: string
  }
  folder_path: string[]
  can_edit: boolean
  can_delete: boolean
  can_download: boolean
  download_url?: string
}

export interface CaseDocumentCreate {
  folder_id?: string
  category?:
    | "evidence"
    | "contracts"
    | "correspondence"
    | "court_documents"
    | "research"
    | "billing"
    | "internal"
    | "other"
  description?: string
  tags?: string
  is_confidential?: boolean
  is_shared_with_client?: boolean
}

export interface CaseDocumentUpdate {
  folder_id?: string
  category?:
    | "evidence"
    | "contracts"
    | "correspondence"
    | "court_documents"
    | "research"
    | "billing"
    | "internal"
    | "other"
  description?: string
  tags?: string[]
  is_confidential?: boolean
  is_shared_with_client?: boolean
}

export interface CaseDocumentsResponse {
  data: CaseDocument[]
  count: number
}

export interface CaseDocumentsParams {
  skip?: number
  limit?: number
  folder_id?: string
  category?: string
  search?: string
  sort_by?: "uploaded_at" | "filename" | "file_size" | "category"
  sort_order?: "asc" | "desc"
}

export interface DocumentStats {
  total_documents: number
  total_size_bytes: number
  total_size_mb: number
  recent_uploads: number
  categories: Record<string, { count: number; size: number }>
}

export const CaseDocumentsService = {
  // Get documents for a case
  getCaseDocuments: async (
    caseId: string,
    params?: CaseDocumentsParams,
  ): Promise<CaseDocumentsResponse> => {
    const response = await axios.get(
      `/api/v1/legal-cases/${caseId}/documents`,
      { params },
    )
    return response.data
  },

  // Upload a document to a case
  uploadCaseDocument: async (
    caseId: string,
    file: File,
    metadata: CaseDocumentCreate = {},
  ): Promise<CaseDocument> => {
    const formData = new FormData()
    formData.append("file", file)

    // Add metadata as query parameters
    const params = new URLSearchParams()
    if (metadata.folder_id) params.append("folder_id", metadata.folder_id)
    if (metadata.category) params.append("category", metadata.category)
    if (metadata.description) params.append("description", metadata.description)
    if (metadata.tags) params.append("tags", metadata.tags)
    if (metadata.is_confidential !== undefined)
      params.append("is_confidential", String(metadata.is_confidential))
    if (metadata.is_shared_with_client !== undefined)
      params.append(
        "is_shared_with_client",
        String(metadata.is_shared_with_client),
      )

    const response = await axios.post(
      `/api/v1/legal-cases/${caseId}/documents/upload?${params.toString()}`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      },
    )
    return response.data
  },

  // Update document metadata
  updateCaseDocument: async (
    caseId: string,
    documentId: string,
    documentData: CaseDocumentUpdate,
  ): Promise<CaseDocument> => {
    const response = await axios.put(
      `/api/v1/legal-cases/${caseId}/documents/${documentId}`,
      documentData,
    )
    return response.data
  },

  // Delete a document
  deleteCaseDocument: async (
    caseId: string,
    documentId: string,
  ): Promise<{ message: string }> => {
    const response = await axios.delete(
      `/api/v1/legal-cases/${caseId}/documents/${documentId}`,
    )
    return response.data
  },

  // Download a document
  downloadCaseDocument: async (
    caseId: string,
    documentId: string,
  ): Promise<Blob> => {
    const response = await axios.get(
      `/api/v1/legal-cases/${caseId}/documents/${documentId}/download`,
      {
        responseType: "blob",
      },
    )
    return response.data
  },

  // Get document categories
  getDocumentCategories: async (
    caseId: string,
  ): Promise<Record<string, { label: string; value: string }>> => {
    const response = await axios.get(
      `/api/v1/legal-cases/${caseId}/documents/categories`,
    )
    return response.data.categories
  },

  // Get document statistics
  getDocumentStats: async (caseId: string): Promise<DocumentStats> => {
    const response = await axios.get(
      `/api/v1/legal-cases/${caseId}/documents/stats`,
    )
    return response.data
  },
}

// Document category configurations
export const DOCUMENT_CATEGORY_CONFIG = {
  evidence: {
    label: "Evidence",
    description: "Photos, recordings, witness statements",
    color: "red",
    icon: "📸",
  },
  contracts: {
    label: "Contracts",
    description: "Legal agreements, terms of service",
    color: "blue",
    icon: "📄",
  },
  correspondence: {
    label: "Correspondence",
    description: "Emails, letters, communications",
    color: "green",
    icon: "📧",
  },
  court_documents: {
    label: "Court Documents",
    description: "Filings, orders, judgments",
    color: "purple",
    icon: "⚖️",
  },
  research: {
    label: "Research",
    description: "Legal research, case law, precedents",
    color: "teal",
    icon: "🔍",
  },
  billing: {
    label: "Billing",
    description: "Invoices, payment records",
    color: "yellow",
    icon: "💰",
  },
  internal: {
    label: "Internal",
    description: "Internal notes, drafts",
    color: "gray",
    icon: "📝",
  },
  other: {
    label: "Other",
    description: "Miscellaneous case-related documents",
    color: "gray",
    icon: "📁",
  },
}

// File type configurations
export const FILE_TYPE_CONFIG = {
  "application/pdf": {
    label: "PDF",
    icon: "📄",
    color: "red",
  },
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
    label: "Word",
    icon: "📝",
    color: "blue",
  },
  "image/jpeg": {
    label: "JPEG",
    icon: "🖼️",
    color: "green",
  },
  "image/png": {
    label: "PNG",
    icon: "🖼️",
    color: "green",
  },
  "image/gif": {
    label: "GIF",
    icon: "🖼️",
    color: "green",
  },
  "text/plain": {
    label: "Text",
    icon: "📄",
    color: "gray",
  },
}

// Utility functions
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes"

  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

export const getFileTypeInfo = (contentType: string) => {
  return (
    FILE_TYPE_CONFIG[contentType as keyof typeof FILE_TYPE_CONFIG] || {
      label: "Unknown",
      icon: "📄",
      color: "gray",
    }
  )
}

export const getCategoryInfo = (category: string) => {
  return (
    DOCUMENT_CATEGORY_CONFIG[
      category as keyof typeof DOCUMENT_CATEGORY_CONFIG
    ] || {
      label: "Other",
      description: "Unknown category",
      color: "gray",
      icon: "📁",
    }
  )
}

export const formatFolderPath = (folderPath: string[]): string => {
  if (folderPath.length === 0) return "Root"
  return folderPath.join(" / ")
}

export const getDocumentLocation = (document: CaseDocument): string => {
  if (document.folder_path.length === 0) {
    return "Root"
  }
  return document.folder_path.join(" / ")
}
