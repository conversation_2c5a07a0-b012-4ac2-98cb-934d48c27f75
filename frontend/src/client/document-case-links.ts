import axios from "axios"

export interface DocumentCaseLink {
  id: string
  document_id: string
  case_id: string
  linked_by: string
  linked_at: string
  link_type: string
  notes?: string
  case?: {
    id: string
    title: string
    case_number: string
    status: string
    priority: string
  }
  linked_by_user?: {
    id: string
    email: string
    full_name: string
    role: string
  }
}

export interface DocumentCaseLinkCreate {
  case_id: string
  link_type?: string
  notes?: string
}

export interface DocumentCaseLinkUpdate {
  link_type?: string
  notes?: string
}

export interface DocumentCaseLinksResponse {
  data: DocumentCaseLink[]
  count: number
}

export interface LinkedDocument {
  id: string
  case_id: string
  folder_id?: string
  filename: string
  original_filename: string
  file_path: string
  content_type: string
  file_size: number
  category: string
  description?: string
  tags?: string[]
  is_confidential: boolean
  is_shared_with_client: boolean
  uploaded_by: string
  uploaded_at: string
  updated_at: string
  uploader?: {
    id: string
    email: string
    full_name: string
    role: string
  }
  link: {
    id: string
    link_type: string
    notes?: string
    linked_at: string
  }
}

export interface CaseLinkedDocumentsResponse {
  data: LinkedDocument[]
  count: number
}

export const DocumentCaseLinksService = {
  // Get all case links for a document
  getDocumentCaseLinks: async (
    documentId: string,
  ): Promise<DocumentCaseLinksResponse> => {
    const response = await axios.get(`/api/v1/documents/${documentId}/links`)
    return response.data
  },

  // Create a new document-case link
  createDocumentCaseLink: async (
    documentId: string,
    linkData: DocumentCaseLinkCreate,
  ): Promise<DocumentCaseLink> => {
    const response = await axios.post(
      `/api/v1/documents/${documentId}/links`,
      linkData,
    )
    return response.data
  },

  // Update a document-case link
  updateDocumentCaseLink: async (
    documentId: string,
    linkId: string,
    linkData: DocumentCaseLinkUpdate,
  ): Promise<DocumentCaseLink> => {
    const response = await axios.put(
      `/api/v1/documents/${documentId}/links/${linkId}`,
      linkData,
    )
    return response.data
  },

  // Delete a document-case link
  deleteDocumentCaseLink: async (
    documentId: string,
    linkId: string,
  ): Promise<{ message: string }> => {
    const response = await axios.delete(
      `/api/v1/documents/${documentId}/links/${linkId}`,
    )
    return response.data
  },

  // Get all documents linked to a case
  getCaseLinkedDocuments: async (
    caseId: string,
    params?: { link_type?: string; skip?: number; limit?: number },
  ): Promise<CaseLinkedDocumentsResponse> => {
    const response = await axios.get(
      `/api/v1/cases/${caseId}/linked-documents`,
      { params },
    )
    return response.data
  },
}

// Link type configurations
export const LINK_TYPE_CONFIG = {
  related: {
    label: "Related",
    description: "General relationship to the case",
    color: "blue",
    icon: "🔗",
  },
  evidence: {
    label: "Evidence",
    description: "Document serves as evidence",
    color: "red",
    icon: "📸",
  },
  reference: {
    label: "Reference",
    description: "Reference material for the case",
    color: "green",
    icon: "📚",
  },
  contract: {
    label: "Contract",
    description: "Legal contract or agreement",
    color: "purple",
    icon: "📄",
  },
  correspondence: {
    label: "Correspondence",
    description: "Communication related to case",
    color: "orange",
    icon: "📧",
  },
  filing: {
    label: "Court Filing",
    description: "Document filed with court",
    color: "teal",
    icon: "⚖️",
  },
  research: {
    label: "Research",
    description: "Legal research and analysis",
    color: "cyan",
    icon: "🔍",
  },
  billing: {
    label: "Billing",
    description: "Billing and financial documents",
    color: "yellow",
    icon: "💰",
  },
}

// Utility functions
export const getLinkTypeInfo = (linkType: string) => {
  return (
    LINK_TYPE_CONFIG[linkType as keyof typeof LINK_TYPE_CONFIG] || {
      label: "Unknown",
      description: "Unknown link type",
      color: "gray",
      icon: "❓",
    }
  )
}

export const formatLinkDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)
      return diffInMinutes <= 1 ? "Just now" : `${diffInMinutes} minutes ago`
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours)
      return `${hours} hour${hours !== 1 ? "s" : ""} ago`
    } else if (diffInHours < 24 * 7) {
      const days = Math.floor(diffInHours / 24)
      return `${days} day${days !== 1 ? "s" : ""} ago`
    } else {
      return date.toLocaleDateString()
    }
  } catch {
    return "Unknown time"
  }
}

export const groupLinksByType = (
  links: DocumentCaseLink[],
): Record<string, DocumentCaseLink[]> => {
  const groups: Record<string, DocumentCaseLink[]> = {}

  links.forEach((link) => {
    const type = link.link_type || "related"
    if (!groups[type]) {
      groups[type] = []
    }
    groups[type].push(link)
  })

  return groups
}

export const sortLinksByDate = (
  links: DocumentCaseLink[],
): DocumentCaseLink[] => {
  return links.sort(
    (a, b) => new Date(b.linked_at).getTime() - new Date(a.linked_at).getTime(),
  )
}

export const filterLinksByType = (
  links: DocumentCaseLink[],
  linkType?: string,
): DocumentCaseLink[] => {
  if (!linkType) return links
  return links.filter((link) => link.link_type === linkType)
}

export const getLinkStats = (links: DocumentCaseLink[]) => {
  const stats = {
    total: links.length,
    byType: {} as Record<string, number>,
    byCase: {} as Record<string, number>,
    recentCount: 0, // Last 24 hours
  }

  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

  links.forEach((link) => {
    // Count by type
    const type = link.link_type || "related"
    stats.byType[type] = (stats.byType[type] || 0) + 1

    // Count by case
    const caseTitle = link.case?.title || "Unknown Case"
    stats.byCase[caseTitle] = (stats.byCase[caseTitle] || 0) + 1

    // Count recent links
    try {
      const linkDate = new Date(link.linked_at)
      if (linkDate > oneDayAgo) {
        stats.recentCount++
      }
    } catch {
      // Ignore invalid dates
    }
  })

  return stats
}

export const validateLinkData = (
  linkData: DocumentCaseLinkCreate,
): string[] => {
  const errors: string[] = []

  if (!linkData.case_id) {
    errors.push("Case ID is required")
  }

  if (
    linkData.link_type &&
    !LINK_TYPE_CONFIG[linkData.link_type as keyof typeof LINK_TYPE_CONFIG]
  ) {
    errors.push("Invalid link type")
  }

  if (linkData.notes && linkData.notes.length > 1000) {
    errors.push("Notes cannot exceed 1000 characters")
  }

  return errors
}

export const canUserManageLink = (
  link: DocumentCaseLink,
  currentUserId: string,
): boolean => {
  // User can manage link if they created it or have admin privileges
  return (
    link.linked_by === currentUserId || link.linked_by_user?.role === "admin"
  )
}

export const getDefaultLinkType = (documentCategory?: string): string => {
  switch (documentCategory) {
    case "evidence":
      return "evidence"
    case "contracts":
      return "contract"
    case "correspondence":
      return "correspondence"
    case "court_documents":
      return "filing"
    case "research":
      return "research"
    case "billing":
      return "billing"
    default:
      return "related"
  }
}
