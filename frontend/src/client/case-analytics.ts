import axios from "axios"

export interface CaseOverview {
  case_id: string
  title: string
  client_name: string
  case_type: string
  status: string
  priority: string
  opening_date: string
  case_age_days: number
  lawyer: {
    id: string
    name: string
    email: string
  } | null
  totals: {
    activities: number
    documents: number
    notes: number
    recent_activities: number
  }
}

export interface ActivityAnalytics {
  total_activities: number
  activity_velocity: number
  activity_by_type: Record<string, number>
  activity_by_user: Array<{
    user_name: string
    user_id: string
    activity_count: number
  }>
  activity_timeline: Array<{
    date: string
    activity_count: number
  }>
  most_active_day: {
    date: string
    activity_count: number
  } | null
  average_daily_activities: number
}

export interface DocumentAnalytics {
  total_documents: number
  total_size_bytes: number
  total_size_mb: number
  average_size_bytes: number
  largest_file_bytes: number
  category_distribution: Record<string, number>
  total_links: number
  upload_timeline: Array<{
    date: string
    upload_count: number
  }>
  upload_velocity: number
}

export interface ProgressAnalytics {
  total_milestones: number
  completed_milestones: number
  overdue_milestones: number
  progress_percentage: number
  total_deadlines: number
  upcoming_deadlines: number
  milestone_completion_rate: number
  on_track: boolean
}

export interface CollaborationAnalytics {
  team_size: number
  team_members: Array<{
    user_id: string
    name: string
    role: string
  }>
  user_interactions: Record<
    string,
    {
      name: string
      role: string
      activity_count: number
    }
  >
  notes_collaboration: Record<string, number>
  collaboration_score: number
}

export interface PerformanceMetrics {
  case_age_days: number
  activity_frequency: number
  status_changes: number
  document_productivity: number
  average_response_time_hours: number
  performance_score: number
  efficiency_rating: string
}

export interface TimeAnalytics {
  case_age_days: number
  estimated_total_hours: number
  estimated_daily_hours: number
  activity_time_distribution: Record<
    string,
    {
      count: number
      estimated_hours: number
    }
  >
  billable_hours_estimate: number
  time_efficiency: {
    efficiency_ratio: number
    efficiency_status: string
    expected_hours: number
    actual_hours: number
    variance_hours: number
  }
}

export interface HealthIndicators {
  health_score: number
  health_status: string
  health_color: string
  health_factors: string[]
  recommendations: string[]
}

export interface ComparativeAnalytics {
  similar_cases_count: number
  comparison_metrics: {
    average_duration_days: number
    average_activities: number
    current_case_age_days: number
    current_activities: number
  }
  performance_vs_average: {
    duration_comparison: string
    activity_comparison: string
  }
  percentile_ranking: {
    duration: number
    activity: number
  }
}

export interface CaseAnalytics {
  case_id: string
  analysis_period: {
    start_date: string
    end_date: string
    days: number
  }
  case_overview: CaseOverview
  activity_analytics: ActivityAnalytics
  document_analytics: DocumentAnalytics
  progress_analytics: ProgressAnalytics
  collaboration_analytics: CollaborationAnalytics
  performance_metrics: PerformanceMetrics
  time_analytics: TimeAnalytics
  health_indicators: HealthIndicators
  comparative_analytics: ComparativeAnalytics
  generated_at: string
  generated_by: {
    user_id: string
    user_name: string
    user_role: string
  }
}

export const CaseAnalyticsService = {
  getCaseAnalytics: async (
    caseId: string,
    params?: {
      period_days?: number
      include_comparisons?: boolean
    },
  ): Promise<CaseAnalytics> => {
    const response = await axios.get(`/legal-cases/${caseId}/analytics`, {
      params,
    })
    return response.data
  },
}

// Health status configurations
export const HEALTH_STATUS_CONFIG = {
  excellent: {
    label: "Excellent",
    color: "green",
    icon: "🟢",
    description: "Case is performing exceptionally well",
  },
  good: {
    label: "Good",
    color: "blue",
    icon: "🔵",
    description: "Case is progressing well with minor areas for improvement",
  },
  fair: {
    label: "Fair",
    color: "yellow",
    icon: "🟡",
    description: "Case has some issues that need attention",
  },
  poor: {
    label: "Poor",
    color: "orange",
    icon: "🟠",
    description: "Case has significant issues requiring immediate action",
  },
  critical: {
    label: "Critical",
    color: "red",
    icon: "🔴",
    description: "Case is in critical condition and needs urgent intervention",
  },
}

// Efficiency rating configurations
export const EFFICIENCY_RATING_CONFIG = {
  excellent: {
    label: "Excellent",
    color: "green",
    icon: "⭐",
    description: "Outstanding efficiency and performance",
  },
  good: {
    label: "Good",
    color: "blue",
    icon: "👍",
    description: "Good efficiency with room for optimization",
  },
  average: {
    label: "Average",
    color: "yellow",
    icon: "➖",
    description: "Average efficiency, consider improvements",
  },
  below_average: {
    label: "Below Average",
    color: "orange",
    icon: "⚠️",
    description: "Below average efficiency, needs attention",
  },
  poor: {
    label: "Poor",
    color: "red",
    icon: "❌",
    description: "Poor efficiency, requires immediate action",
  },
}

// Time efficiency status configurations
export const TIME_EFFICIENCY_CONFIG = {
  over_allocated: {
    label: "Over Allocated",
    color: "red",
    icon: "⏰",
    description: "More time spent than expected",
  },
  well_allocated: {
    label: "Well Allocated",
    color: "green",
    icon: "✅",
    description: "Time allocation is optimal",
  },
  under_allocated: {
    label: "Under Allocated",
    color: "yellow",
    icon: "⚡",
    description: "Less time spent than expected",
  },
  significantly_under_allocated: {
    label: "Significantly Under Allocated",
    color: "orange",
    icon: "🚨",
    description: "Much less time spent than expected",
  },
}

// Utility functions
export const getHealthStatusInfo = (status: string) => {
  return (
    HEALTH_STATUS_CONFIG[status as keyof typeof HEALTH_STATUS_CONFIG] ||
    HEALTH_STATUS_CONFIG.fair
  )
}

export const getEfficiencyRatingInfo = (rating: string) => {
  return (
    EFFICIENCY_RATING_CONFIG[rating as keyof typeof EFFICIENCY_RATING_CONFIG] ||
    EFFICIENCY_RATING_CONFIG.average
  )
}

export const getTimeEfficiencyInfo = (status: string) => {
  return (
    TIME_EFFICIENCY_CONFIG[status as keyof typeof TIME_EFFICIENCY_CONFIG] ||
    TIME_EFFICIENCY_CONFIG.well_allocated
  )
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B"

  const k = 1024
  const sizes = ["B", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

export const formatDuration = (days: number): string => {
  if (days < 1) return "< 1 day"
  if (days === 1) return "1 day"
  if (days < 7) return `${days} days`
  if (days < 30) return `${Math.round(days / 7)} weeks`
  if (days < 365) return `${Math.round(days / 30)} months`
  return `${Math.round(days / 365)} years`
}

export const formatHours = (hours: number): string => {
  if (hours < 1) return `${Math.round(hours * 60)} min`
  if (hours < 24) return `${hours.toFixed(1)} hrs`
  return `${Math.round(hours / 24)} days`
}

export const calculateTrendDirection = (
  current: number,
  previous: number,
): "up" | "down" | "stable" => {
  const threshold = 0.05 // 5% threshold for stability
  const change = (current - previous) / Math.max(previous, 1)

  if (Math.abs(change) < threshold) return "stable"
  return change > 0 ? "up" : "down"
}

export const formatPercentageChange = (
  current: number,
  previous: number,
): string => {
  if (previous === 0) return current > 0 ? "+∞%" : "0%"

  const change = ((current - previous) / previous) * 100
  const sign = change >= 0 ? "+" : ""

  return `${sign}${change.toFixed(1)}%`
}

export const getActivityTypeLabel = (activityType: string): string => {
  const labels: Record<string, string> = {
    case_created: "Case Created",
    case_updated: "Case Updated",
    status_changed: "Status Changed",
    priority_changed: "Priority Changed",
    document_uploaded: "Document Uploaded",
    document_downloaded: "Document Downloaded",
    document_deleted: "Document Deleted",
    note_added: "Note Added",
    note_updated: "Note Updated",
    note_deleted: "Note Deleted",
    user_assigned: "User Assigned",
    milestone_created: "Milestone Created",
    milestone_updated: "Milestone Updated",
    milestone_deleted: "Milestone Deleted",
    deadline_created: "Deadline Created",
    deadline_updated: "Deadline Updated",
    deadline_deleted: "Deadline Deleted",
  }

  return (
    labels[activityType] ||
    activityType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
  )
}

export const getDocumentCategoryLabel = (category: string): string => {
  const labels: Record<string, string> = {
    evidence: "Evidence",
    contracts: "Contracts",
    correspondence: "Correspondence",
    pleadings: "Pleadings",
    discovery: "Discovery",
    research: "Research",
    other: "Other",
  }

  return (
    labels[category] ||
    category.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
  )
}

export const getPerformanceColor = (score: number): string => {
  if (score >= 85) return "green"
  if (score >= 70) return "blue"
  if (score >= 55) return "yellow"
  if (score >= 40) return "orange"
  return "red"
}

export const getHealthScoreColor = (score: number): string => {
  if (score >= 80) return "green"
  if (score >= 60) return "blue"
  if (score >= 40) return "yellow"
  if (score >= 20) return "orange"
  return "red"
}
