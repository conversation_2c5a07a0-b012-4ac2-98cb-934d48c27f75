// This file is auto-generated by @hey-api/openapi-ts

import type { CancelablePromise } from "./core/CancelablePromise"
import { OpenAPI } from "./core/OpenAPI"
import { request as __request } from "./core/request"
import type {
  CaseAnalyticsGetCaseAnalyticsData,
  CaseAnalyticsGetCaseAnalyticsResponse,
  CaseDocumentsGetCaseDocumentsData,
  CaseDocumentsGetCaseDocumentsResponse,
  CaseDocumentsUploadCaseDocumentData,
  CaseDocumentsUploadCaseDocumentResponse,
  CaseDocumentsUpdateCaseDocumentData,
  CaseDocumentsUpdateCaseDocumentResponse,
  CaseDocumentsDeleteCaseDocumentData,
  CaseDocumentsDeleteCaseDocumentResponse,
  CaseDocumentsDownloadCaseDocumentData,
  CaseDocumentsDownloadCaseDocumentResponse,
  CaseDocumentsGetDocumentCategoriesResponse,
  CaseDocumentsGetCaseDocumentStatsData,
  CaseDocumentsGetCaseDocumentStatsResponse,
  CaseFoldersGetCaseFoldersData,
  CaseFoldersGetCaseFoldersResponse,
  CaseFoldersCreateCaseFolderData,
  CaseFoldersCreateCaseFolderResponse,
  CaseFoldersUpdateCaseFolderData,
  CaseFoldersUpdateCaseFolderResponse,
  CaseFoldersDeleteCaseFolderData,
  CaseFoldersDeleteCaseFolderResponse,
  CaseFoldersInitializeCaseFoldersData,
  CaseFoldersInitializeCaseFoldersResponse,
  CaseNotesGetCaseNotesData,
  CaseNotesGetCaseNotesResponse,
  CaseNotesCreateCaseNoteData,
  CaseNotesCreateCaseNoteResponse,
  CaseNotesUpdateCaseNoteData,
  CaseNotesUpdateCaseNoteResponse,
  CaseNotesDeleteCaseNoteData,
  CaseNotesDeleteCaseNoteResponse,
  CaseNotesGetNoteRepliesData,
  CaseNotesGetNoteRepliesResponse,
  CaseProgressGetCaseProgressSummaryData,
  CaseProgressGetCaseProgressSummaryResponse,
  CaseProgressGetCaseMilestonesData,
  CaseProgressGetCaseMilestonesResponse,
  CaseProgressCreateCaseMilestoneData,
  CaseProgressCreateCaseMilestoneResponse,
  CaseProgressUpdateCaseMilestoneData,
  CaseProgressUpdateCaseMilestoneResponse,
  CaseProgressDeleteCaseMilestoneData,
  CaseProgressDeleteCaseMilestoneResponse,
  CaseProgressGetCaseDeadlinesData,
  CaseProgressGetCaseDeadlinesResponse,
  CaseProgressCreateCaseDeadlineData,
  CaseProgressCreateCaseDeadlineResponse,
  CaseProgressUpdateCaseDeadlineData,
  CaseProgressUpdateCaseDeadlineResponse,
  CaseProgressDeleteCaseDeadlineData,
  CaseProgressDeleteCaseDeadlineResponse,
  CaseProgressGetMilestoneTemplatesData,
  CaseProgressGetMilestoneTemplatesResponse,
  CaseProgressCreateMilestoneTemplateData,
  CaseProgressCreateMilestoneTemplateResponse,
  CaseProgressCreateMilestonesFromTemplateData,
  CaseProgressCreateMilestonesFromTemplateResponse,
  CaseTemplatesGetCaseTemplatesData,
  CaseTemplatesGetCaseTemplatesResponse,
  CaseTemplatesCreateCaseTemplateData,
  CaseTemplatesCreateCaseTemplateResponse,
  CaseTemplatesGetCaseTemplateData,
  CaseTemplatesGetCaseTemplateResponse,
  CaseTemplatesUpdateCaseTemplateData,
  CaseTemplatesUpdateCaseTemplateResponse,
  CaseTemplatesDeleteCaseTemplateData,
  CaseTemplatesDeleteCaseTemplateResponse,
  CaseTemplatesApplyCaseTemplateData,
  CaseTemplatesApplyCaseTemplateResponse,
  CaseTemplatesCreateCaseFromTemplateData,
  CaseTemplatesCreateCaseFromTemplateResponse,
  CaseTemplatesGetDocumentTemplatesData,
  CaseTemplatesGetDocumentTemplatesResponse,
  CaseTemplatesCreateDocumentTemplateData,
  CaseTemplatesCreateDocumentTemplateResponse,
  CaseTemplatesGetDocumentTemplateData,
  CaseTemplatesGetDocumentTemplateResponse,
  CaseTemplatesUpdateDocumentTemplateData,
  CaseTemplatesUpdateDocumentTemplateResponse,
  CaseTemplatesDeleteDocumentTemplateData,
  CaseTemplatesDeleteDocumentTemplateResponse,
  CaseTemplatesGenerateDocumentFromTemplateData,
  CaseTemplatesGenerateDocumentFromTemplateResponse,
  CaseTemplatesGetWorkflowTemplatesData,
  CaseTemplatesGetWorkflowTemplatesResponse,
  CaseTemplatesCreateWorkflowTemplateData,
  CaseTemplatesCreateWorkflowTemplateResponse,
  CaseTemplatesGetWorkflowTemplateData,
  CaseTemplatesGetWorkflowTemplateResponse,
  CaseTemplatesUpdateWorkflowTemplateData,
  CaseTemplatesUpdateWorkflowTemplateResponse,
  CaseTemplatesDeleteWorkflowTemplateData,
  CaseTemplatesDeleteWorkflowTemplateResponse,
  DocumentCaseLinksGetDocumentCaseLinksData,
  DocumentCaseLinksGetDocumentCaseLinksResponse,
  DocumentCaseLinksCreateDocumentCaseLinkData,
  DocumentCaseLinksCreateDocumentCaseLinkResponse,
  DocumentCaseLinksUpdateDocumentCaseLinkData,
  DocumentCaseLinksUpdateDocumentCaseLinkResponse,
  DocumentCaseLinksDeleteDocumentCaseLinkData,
  DocumentCaseLinksDeleteDocumentCaseLinkResponse,
  DocumentCaseLinksGetCaseLinkedDocumentsData,
  DocumentCaseLinksGetCaseLinkedDocumentsResponse,
  DocumentsUploadDocumentData,
  DocumentsUploadDocumentResponse,
  DocumentsDocumentsHealthCheckResponse,
  InternalCreateUserData,
  InternalCreateUserResponse,
  ItemsReadItemsData,
  ItemsReadItemsResponse,
  ItemsCreateItemData,
  ItemsCreateItemResponse,
  ItemsReadItemData,
  ItemsReadItemResponse,
  ItemsUpdateItemData,
  ItemsUpdateItemResponse,
  ItemsDeleteItemData,
  ItemsDeleteItemResponse,
  LegalCasesCreateLegalCaseData,
  LegalCasesCreateLegalCaseResponse,
  LegalCasesReadLegalCasesData,
  LegalCasesReadLegalCasesResponse,
  LegalCasesGetAvailableLawyersResponse,
  LegalCasesReadLegalCaseData,
  LegalCasesReadLegalCaseResponse,
  LegalCasesUpdateLegalCaseData,
  LegalCasesUpdateLegalCaseResponse,
  LegalCasesPartialUpdateLegalCaseData,
  LegalCasesPartialUpdateLegalCaseResponse,
  LegalCasesDeleteLegalCaseData,
  LegalCasesDeleteLegalCaseResponse,
  LegalCasesBulkUpdateCaseStatusData,
  LegalCasesBulkUpdateCaseStatusResponse,
  LegalCasesUpdateCaseStatusData,
  LegalCasesUpdateCaseStatusResponse,
  LegalCasesReadCaseActivitiesData,
  LegalCasesReadCaseActivitiesResponse,
  LegalCasesReadCaseStatusHistoryData,
  LegalCasesReadCaseStatusHistoryResponse,
  LegalCasesUpdateCasePriorityData,
  LegalCasesUpdateCasePriorityResponse,
  LegalCasesGetStatusOptionsResponse,
  LegalCasesGetPriorityOptionsResponse,
  LegalCasesGetValidStatusTransitionsData,
  LegalCasesGetValidStatusTransitionsResponse,
  LoginLoginAccessTokenData,
  LoginLoginAccessTokenResponse,
  LoginTestTokenResponse,
  LoginRecoverPasswordData,
  LoginRecoverPasswordResponse,
  LoginResetPasswordData,
  LoginResetPasswordResponse,
  LoginRecoverPasswordHtmlContentData,
  LoginRecoverPasswordHtmlContentResponse,
  ProtectedLawyerRouteResponse,
  ProtectedAdminOnlyRouteResponse,
  ProtectedClientOnlyRouteResponse,
  ProtectedAssistantOnlyRouteResponse,
  ReportsGetDashboardReportData,
  ReportsGetDashboardReportResponse,
  UsersReadUsersData,
  UsersReadUsersResponse,
  UsersCreateUserData,
  UsersCreateUserResponse,
  UsersReadUserMeResponse,
  UsersDeleteUserMeResponse,
  UsersUpdateUserMeData,
  UsersUpdateUserMeResponse,
  UsersUpdatePasswordMeData,
  UsersUpdatePasswordMeResponse,
  UsersRegisterUserData,
  UsersRegisterUserResponse,
  UsersAssignUserToLawyerData,
  UsersAssignUserToLawyerResponse,
  UsersGetUnassignedUsersResponse,
  UsersGetAllLawyersResponse,
  UsersGetLawyerClientsData,
  UsersGetLawyerClientsResponse,
  UsersGetLawyerAssistantsData,
  UsersGetLawyerAssistantsResponse,
  UsersGetLawyerAssignedUsersData,
  UsersGetLawyerAssignedUsersResponse,
  UsersReadUserByIdData,
  UsersReadUserByIdResponse,
  UsersUpdateUserData,
  UsersUpdateUserResponse,
  UsersDeleteUserData,
  UsersDeleteUserResponse,
  UsersUpdateUserAssignmentData,
  UsersUpdateUserAssignmentResponse,
  UtilsTestEmailData,
  UtilsTestEmailResponse,
  UtilsHealthCheckResponse,
} from "./types.gen"

export class CaseAnalyticsService {
  /**
   * Get Case Analytics
   * Get comprehensive analytics for a specific case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.periodDays Analysis period in days
   * @param data.includeComparisons Include comparative analytics
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static getCaseAnalytics(
    data: CaseAnalyticsGetCaseAnalyticsData,
  ): CancelablePromise<CaseAnalyticsGetCaseAnalyticsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/analytics",
      path: {
        case_id: data.caseId,
      },
      query: {
        period_days: data.periodDays,
        include_comparisons: data.includeComparisons,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class CaseDocumentsService {
  /**
   * Get Case Documents
   * Get documents for a specific case with filtering and pagination
   * @param data The data for the request.
   * @param data.caseId
   * @param data.skip
   * @param data.limit
   * @param data.folderId
   * @param data.category
   * @param data.search
   * @param data.sortBy
   * @param data.sortOrder
   * @returns CaseDocumentsPublic Successful Response
   * @throws ApiError
   */
  public static getCaseDocuments(
    data: CaseDocumentsGetCaseDocumentsData,
  ): CancelablePromise<CaseDocumentsGetCaseDocumentsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/documents",
      path: {
        case_id: data.caseId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
        folder_id: data.folderId,
        category: data.category,
        search: data.search,
        sort_by: data.sortBy,
        sort_order: data.sortOrder,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Upload Case Document
   * Upload a document to a specific case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.formData
   * @param data.folderId
   * @param data.category
   * @param data.description
   * @param data.tags
   * @param data.isConfidential
   * @param data.isSharedWithClient
   * @returns CaseDocumentPublic Successful Response
   * @throws ApiError
   */
  public static uploadCaseDocument(
    data: CaseDocumentsUploadCaseDocumentData,
  ): CancelablePromise<CaseDocumentsUploadCaseDocumentResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/{case_id}/documents/upload",
      path: {
        case_id: data.caseId,
      },
      query: {
        folder_id: data.folderId,
        category: data.category,
        description: data.description,
        tags: data.tags,
        is_confidential: data.isConfidential,
        is_shared_with_client: data.isSharedWithClient,
      },
      formData: data.formData,
      mediaType: "multipart/form-data",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Document
   * Update document metadata
   * @param data The data for the request.
   * @param data.caseId
   * @param data.documentId
   * @param data.requestBody
   * @returns CaseDocumentPublic Successful Response
   * @throws ApiError
   */
  public static updateCaseDocument(
    data: CaseDocumentsUpdateCaseDocumentData,
  ): CancelablePromise<CaseDocumentsUpdateCaseDocumentResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/legal-cases/{case_id}/documents/{document_id}",
      path: {
        case_id: data.caseId,
        document_id: data.documentId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Case Document
   * Delete a case document
   * @param data The data for the request.
   * @param data.caseId
   * @param data.documentId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static deleteCaseDocument(
    data: CaseDocumentsDeleteCaseDocumentData,
  ): CancelablePromise<CaseDocumentsDeleteCaseDocumentResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/legal-cases/{case_id}/documents/{document_id}",
      path: {
        case_id: data.caseId,
        document_id: data.documentId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Download Case Document
   * Download a case document
   * @param data The data for the request.
   * @param data.caseId
   * @param data.documentId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static downloadCaseDocument(
    data: CaseDocumentsDownloadCaseDocumentData,
  ): CancelablePromise<CaseDocumentsDownloadCaseDocumentResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/documents/{document_id}/download",
      path: {
        case_id: data.caseId,
        document_id: data.documentId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Document Categories
   * Get available document categories
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static getDocumentCategories(): CancelablePromise<CaseDocumentsGetDocumentCategoriesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/documents/categories",
    })
  }

  /**
   * Get Case Document Stats
   * Get document statistics for a case
   * @param data The data for the request.
   * @param data.caseId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static getCaseDocumentStats(
    data: CaseDocumentsGetCaseDocumentStatsData,
  ): CancelablePromise<CaseDocumentsGetCaseDocumentStatsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/documents/stats",
      path: {
        case_id: data.caseId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class CaseFoldersService {
  /**
   * Get Case Folders
   * Get folders for a specific case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.parentFolderId
   * @param data.includeDocuments
   * @returns CaseFoldersPublic Successful Response
   * @throws ApiError
   */
  public static getCaseFolders(
    data: CaseFoldersGetCaseFoldersData,
  ): CancelablePromise<CaseFoldersGetCaseFoldersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/folders",
      path: {
        case_id: data.caseId,
      },
      query: {
        parent_folder_id: data.parentFolderId,
        include_documents: data.includeDocuments,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Case Folder
   * Create a new folder for a case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.requestBody
   * @returns CaseFolderPublic Successful Response
   * @throws ApiError
   */
  public static createCaseFolder(
    data: CaseFoldersCreateCaseFolderData,
  ): CancelablePromise<CaseFoldersCreateCaseFolderResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/{case_id}/folders",
      path: {
        case_id: data.caseId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Folder
   * Update an existing folder
   * @param data The data for the request.
   * @param data.caseId
   * @param data.folderId
   * @param data.requestBody
   * @returns CaseFolderPublic Successful Response
   * @throws ApiError
   */
  public static updateCaseFolder(
    data: CaseFoldersUpdateCaseFolderData,
  ): CancelablePromise<CaseFoldersUpdateCaseFolderResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/legal-cases/{case_id}/folders/{folder_id}",
      path: {
        case_id: data.caseId,
        folder_id: data.folderId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Case Folder
   * Delete a folder and optionally move its contents
   * @param data The data for the request.
   * @param data.caseId
   * @param data.folderId
   * @param data.moveContentsTo
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static deleteCaseFolder(
    data: CaseFoldersDeleteCaseFolderData,
  ): CancelablePromise<CaseFoldersDeleteCaseFolderResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/legal-cases/{case_id}/folders/{folder_id}",
      path: {
        case_id: data.caseId,
        folder_id: data.folderId,
      },
      query: {
        move_contents_to: data.moveContentsTo,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Initialize Case Folders
   * Initialize default folder structure for a case
   * @param data The data for the request.
   * @param data.caseId
   * @returns CaseFoldersPublic Successful Response
   * @throws ApiError
   */
  public static initializeCaseFolders(
    data: CaseFoldersInitializeCaseFoldersData,
  ): CancelablePromise<CaseFoldersInitializeCaseFoldersResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/{case_id}/folders/initialize",
      path: {
        case_id: data.caseId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class CaseNotesService {
  /**
   * Get Case Notes
   * Get notes for a specific case with filtering and pagination
   * @param data The data for the request.
   * @param data.caseId
   * @param data.skip
   * @param data.limit
   * @param data.noteType
   * @param data.category
   * @param data.search
   * @param data.sortBy
   * @param data.sortOrder
   * @returns CaseNotesPublic Successful Response
   * @throws ApiError
   */
  public static getCaseNotes(
    data: CaseNotesGetCaseNotesData,
  ): CancelablePromise<CaseNotesGetCaseNotesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/notes",
      path: {
        case_id: data.caseId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
        note_type: data.noteType,
        category: data.category,
        search: data.search,
        sort_by: data.sortBy,
        sort_order: data.sortOrder,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Case Note
   * Create a new note for a case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.requestBody
   * @returns CaseNotePublic Successful Response
   * @throws ApiError
   */
  public static createCaseNote(
    data: CaseNotesCreateCaseNoteData,
  ): CancelablePromise<CaseNotesCreateCaseNoteResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/{case_id}/notes",
      path: {
        case_id: data.caseId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Note
   * Update an existing note
   * @param data The data for the request.
   * @param data.caseId
   * @param data.noteId
   * @param data.requestBody
   * @returns CaseNotePublic Successful Response
   * @throws ApiError
   */
  public static updateCaseNote(
    data: CaseNotesUpdateCaseNoteData,
  ): CancelablePromise<CaseNotesUpdateCaseNoteResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/legal-cases/{case_id}/notes/{note_id}",
      path: {
        case_id: data.caseId,
        note_id: data.noteId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Case Note
   * Delete a note
   * @param data The data for the request.
   * @param data.caseId
   * @param data.noteId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static deleteCaseNote(
    data: CaseNotesDeleteCaseNoteData,
  ): CancelablePromise<CaseNotesDeleteCaseNoteResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/legal-cases/{case_id}/notes/{note_id}",
      path: {
        case_id: data.caseId,
        note_id: data.noteId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Note Replies
   * Get replies to a specific note
   * @param data The data for the request.
   * @param data.caseId
   * @param data.noteId
   * @param data.skip
   * @param data.limit
   * @returns CaseNotesPublic Successful Response
   * @throws ApiError
   */
  public static getNoteReplies(
    data: CaseNotesGetNoteRepliesData,
  ): CancelablePromise<CaseNotesGetNoteRepliesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/notes/{note_id}/replies",
      path: {
        case_id: data.caseId,
        note_id: data.noteId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class CaseProgressService {
  /**
   * Get Case Progress Summary
   * Get comprehensive progress summary for a case
   * @param data The data for the request.
   * @param data.caseId
   * @returns CaseProgressSummary Successful Response
   * @throws ApiError
   */
  public static getCaseProgressSummary(
    data: CaseProgressGetCaseProgressSummaryData,
  ): CancelablePromise<CaseProgressGetCaseProgressSummaryResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/progress",
      path: {
        case_id: data.caseId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Case Milestones
   * Get milestones for a case with filtering options
   * @param data The data for the request.
   * @param data.caseId
   * @param data.skip
   * @param data.limit
   * @param data.status
   * @param data.milestoneType
   * @param data.assignedTo
   * @param data.includeCompleted
   * @returns CaseMilestonesPublic Successful Response
   * @throws ApiError
   */
  public static getCaseMilestones(
    data: CaseProgressGetCaseMilestonesData,
  ): CancelablePromise<CaseProgressGetCaseMilestonesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/milestones",
      path: {
        case_id: data.caseId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
        status: data.status,
        milestone_type: data.milestoneType,
        assigned_to: data.assignedTo,
        include_completed: data.includeCompleted,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Case Milestone
   * Create a new milestone for a case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.requestBody
   * @returns CaseMilestonePublic Successful Response
   * @throws ApiError
   */
  public static createCaseMilestone(
    data: CaseProgressCreateCaseMilestoneData,
  ): CancelablePromise<CaseProgressCreateCaseMilestoneResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/{case_id}/milestones",
      path: {
        case_id: data.caseId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Milestone
   * Update a case milestone
   * @param data The data for the request.
   * @param data.caseId
   * @param data.milestoneId
   * @param data.requestBody
   * @returns CaseMilestonePublic Successful Response
   * @throws ApiError
   */
  public static updateCaseMilestone(
    data: CaseProgressUpdateCaseMilestoneData,
  ): CancelablePromise<CaseProgressUpdateCaseMilestoneResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/legal-cases/{case_id}/milestones/{milestone_id}",
      path: {
        case_id: data.caseId,
        milestone_id: data.milestoneId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Case Milestone
   * Delete a case milestone
   * @param data The data for the request.
   * @param data.caseId
   * @param data.milestoneId
   * @returns string Successful Response
   * @throws ApiError
   */
  public static deleteCaseMilestone(
    data: CaseProgressDeleteCaseMilestoneData,
  ): CancelablePromise<CaseProgressDeleteCaseMilestoneResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/legal-cases/{case_id}/milestones/{milestone_id}",
      path: {
        case_id: data.caseId,
        milestone_id: data.milestoneId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Case Deadlines
   * Get deadlines for a case with filtering options
   * @param data The data for the request.
   * @param data.caseId
   * @param data.skip
   * @param data.limit
   * @param data.deadlineType
   * @param data.isCritical
   * @param data.assignedTo
   * @param data.includeCompleted
   * @param data.upcomingDays
   * @returns CaseDeadlinesPublic Successful Response
   * @throws ApiError
   */
  public static getCaseDeadlines(
    data: CaseProgressGetCaseDeadlinesData,
  ): CancelablePromise<CaseProgressGetCaseDeadlinesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{case_id}/deadlines",
      path: {
        case_id: data.caseId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
        deadline_type: data.deadlineType,
        is_critical: data.isCritical,
        assigned_to: data.assignedTo,
        include_completed: data.includeCompleted,
        upcoming_days: data.upcomingDays,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Case Deadline
   * Create a new deadline for a case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.requestBody
   * @returns CaseDeadlinePublic Successful Response
   * @throws ApiError
   */
  public static createCaseDeadline(
    data: CaseProgressCreateCaseDeadlineData,
  ): CancelablePromise<CaseProgressCreateCaseDeadlineResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/{case_id}/deadlines",
      path: {
        case_id: data.caseId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Deadline
   * Update a case deadline
   * @param data The data for the request.
   * @param data.caseId
   * @param data.deadlineId
   * @param data.requestBody
   * @returns CaseDeadlinePublic Successful Response
   * @throws ApiError
   */
  public static updateCaseDeadline(
    data: CaseProgressUpdateCaseDeadlineData,
  ): CancelablePromise<CaseProgressUpdateCaseDeadlineResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/legal-cases/{case_id}/deadlines/{deadline_id}",
      path: {
        case_id: data.caseId,
        deadline_id: data.deadlineId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Case Deadline
   * Delete a case deadline
   * @param data The data for the request.
   * @param data.caseId
   * @param data.deadlineId
   * @returns string Successful Response
   * @throws ApiError
   */
  public static deleteCaseDeadline(
    data: CaseProgressDeleteCaseDeadlineData,
  ): CancelablePromise<CaseProgressDeleteCaseDeadlineResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/legal-cases/{case_id}/deadlines/{deadline_id}",
      path: {
        case_id: data.caseId,
        deadline_id: data.deadlineId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Milestone Templates
   * Get milestone templates, optionally filtered by case type
   * @param data The data for the request.
   * @param data.caseType
   * @param data.skip
   * @param data.limit
   * @returns MilestoneTemplatesPublic Successful Response
   * @throws ApiError
   */
  public static getMilestoneTemplates(
    data: CaseProgressGetMilestoneTemplatesData = {},
  ): CancelablePromise<CaseProgressGetMilestoneTemplatesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/templates/milestones",
      query: {
        case_type: data.caseType,
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Milestone Template
   * Create a new milestone template (admin/lawyer only)
   * @param data The data for the request.
   * @param data.requestBody
   * @returns MilestoneTemplatePublic Successful Response
   * @throws ApiError
   */
  public static createMilestoneTemplate(
    data: CaseProgressCreateMilestoneTemplateData,
  ): CancelablePromise<CaseProgressCreateMilestoneTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/templates/milestones",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Milestones From Template
   * Create milestones for a case based on templates
   * @param data The data for the request.
   * @param data.caseId
   * @param data.caseType Case type to filter templates
   * @returns CaseMilestonesPublic Successful Response
   * @throws ApiError
   */
  public static createMilestonesFromTemplate(
    data: CaseProgressCreateMilestonesFromTemplateData,
  ): CancelablePromise<CaseProgressCreateMilestonesFromTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/{case_id}/milestones/from-template",
      path: {
        case_id: data.caseId,
      },
      query: {
        case_type: data.caseType,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class CaseTemplatesService {
  /**
   * Get Case Templates
   * Get case templates with filtering
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @param data.caseType
   * @param data.isPublic
   * @param data.search
   * @returns CaseTemplatesPublic Successful Response
   * @throws ApiError
   */
  public static getCaseTemplates(
    data: CaseTemplatesGetCaseTemplatesData = {},
  ): CancelablePromise<CaseTemplatesGetCaseTemplatesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/templates/case-templates",
      query: {
        skip: data.skip,
        limit: data.limit,
        case_type: data.caseType,
        is_public: data.isPublic,
        search: data.search,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Case Template
   * Create a new case template
   * @param data The data for the request.
   * @param data.requestBody
   * @returns CaseTemplatePublic Successful Response
   * @throws ApiError
   */
  public static createCaseTemplate(
    data: CaseTemplatesCreateCaseTemplateData,
  ): CancelablePromise<CaseTemplatesCreateCaseTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/api/v1/templates/case-templates",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Case Template
   * Get a specific case template
   * @param data The data for the request.
   * @param data.templateId
   * @returns CaseTemplatePublic Successful Response
   * @throws ApiError
   */
  public static getCaseTemplate(
    data: CaseTemplatesGetCaseTemplateData,
  ): CancelablePromise<CaseTemplatesGetCaseTemplateResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/templates/case-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Template
   * Update a case template
   * @param data The data for the request.
   * @param data.templateId
   * @param data.requestBody
   * @returns CaseTemplatePublic Successful Response
   * @throws ApiError
   */
  public static updateCaseTemplate(
    data: CaseTemplatesUpdateCaseTemplateData,
  ): CancelablePromise<CaseTemplatesUpdateCaseTemplateResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/api/v1/templates/case-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Case Template
   * Delete a case template
   * @param data The data for the request.
   * @param data.templateId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static deleteCaseTemplate(
    data: CaseTemplatesDeleteCaseTemplateData,
  ): CancelablePromise<CaseTemplatesDeleteCaseTemplateResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/api/v1/templates/case-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Apply Case Template
   * Apply a case template to an existing case
   * @param data The data for the request.
   * @param data.templateId
   * @param data.caseId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static applyCaseTemplate(
    data: CaseTemplatesApplyCaseTemplateData,
  ): CancelablePromise<CaseTemplatesApplyCaseTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/api/v1/templates/case-templates/{template_id}/apply/{case_id}",
      path: {
        template_id: data.templateId,
        case_id: data.caseId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Case From Template
   * Create a new case from a template
   * @param data The data for the request.
   * @param data.templateId
   * @param data.requestBody
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static createCaseFromTemplate(
    data: CaseTemplatesCreateCaseFromTemplateData,
  ): CancelablePromise<CaseTemplatesCreateCaseFromTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/api/v1/templates/case-templates/{template_id}/create-case",
      path: {
        template_id: data.templateId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Document Templates
   * Get document templates with filtering
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @param data.caseType
   * @param data.documentType
   * @param data.isPublic
   * @param data.search
   * @returns DocumentTemplatesPublic Successful Response
   * @throws ApiError
   */
  public static getDocumentTemplates(
    data: CaseTemplatesGetDocumentTemplatesData = {},
  ): CancelablePromise<CaseTemplatesGetDocumentTemplatesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/templates/document-templates",
      query: {
        skip: data.skip,
        limit: data.limit,
        case_type: data.caseType,
        document_type: data.documentType,
        is_public: data.isPublic,
        search: data.search,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Document Template
   * Create a new document template
   * @param data The data for the request.
   * @param data.requestBody
   * @returns DocumentTemplatePublic Successful Response
   * @throws ApiError
   */
  public static createDocumentTemplate(
    data: CaseTemplatesCreateDocumentTemplateData,
  ): CancelablePromise<CaseTemplatesCreateDocumentTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/api/v1/templates/document-templates",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Document Template
   * Get a specific document template
   * @param data The data for the request.
   * @param data.templateId
   * @returns DocumentTemplatePublic Successful Response
   * @throws ApiError
   */
  public static getDocumentTemplate(
    data: CaseTemplatesGetDocumentTemplateData,
  ): CancelablePromise<CaseTemplatesGetDocumentTemplateResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/templates/document-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Document Template
   * Update a document template
   * @param data The data for the request.
   * @param data.templateId
   * @param data.requestBody
   * @returns DocumentTemplatePublic Successful Response
   * @throws ApiError
   */
  public static updateDocumentTemplate(
    data: CaseTemplatesUpdateDocumentTemplateData,
  ): CancelablePromise<CaseTemplatesUpdateDocumentTemplateResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/api/v1/templates/document-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Document Template
   * Delete a document template
   * @param data The data for the request.
   * @param data.templateId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static deleteDocumentTemplate(
    data: CaseTemplatesDeleteDocumentTemplateData,
  ): CancelablePromise<CaseTemplatesDeleteDocumentTemplateResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/api/v1/templates/document-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Generate Document From Template
   * Generate a document from a template with placeholder data
   * @param data The data for the request.
   * @param data.templateId
   * @param data.requestBody
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static generateDocumentFromTemplate(
    data: CaseTemplatesGenerateDocumentFromTemplateData,
  ): CancelablePromise<CaseTemplatesGenerateDocumentFromTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/api/v1/templates/document-templates/{template_id}/generate",
      path: {
        template_id: data.templateId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Workflow Templates
   * Get workflow templates with filtering
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @param data.caseType
   * @param data.isPublic
   * @param data.search
   * @returns WorkflowTemplatesPublic Successful Response
   * @throws ApiError
   */
  public static getWorkflowTemplates(
    data: CaseTemplatesGetWorkflowTemplatesData = {},
  ): CancelablePromise<CaseTemplatesGetWorkflowTemplatesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/templates/workflow-templates",
      query: {
        skip: data.skip,
        limit: data.limit,
        case_type: data.caseType,
        is_public: data.isPublic,
        search: data.search,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Workflow Template
   * Create a new workflow template
   * @param data The data for the request.
   * @param data.requestBody
   * @returns WorkflowTemplatePublic Successful Response
   * @throws ApiError
   */
  public static createWorkflowTemplate(
    data: CaseTemplatesCreateWorkflowTemplateData,
  ): CancelablePromise<CaseTemplatesCreateWorkflowTemplateResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/api/v1/templates/workflow-templates",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Workflow Template
   * Get a specific workflow template
   * @param data The data for the request.
   * @param data.templateId
   * @returns WorkflowTemplatePublic Successful Response
   * @throws ApiError
   */
  public static getWorkflowTemplate(
    data: CaseTemplatesGetWorkflowTemplateData,
  ): CancelablePromise<CaseTemplatesGetWorkflowTemplateResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/templates/workflow-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Workflow Template
   * Update a workflow template
   * @param data The data for the request.
   * @param data.templateId
   * @param data.requestBody
   * @returns WorkflowTemplatePublic Successful Response
   * @throws ApiError
   */
  public static updateWorkflowTemplate(
    data: CaseTemplatesUpdateWorkflowTemplateData,
  ): CancelablePromise<CaseTemplatesUpdateWorkflowTemplateResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/api/v1/templates/workflow-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Workflow Template
   * Delete a workflow template
   * @param data The data for the request.
   * @param data.templateId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static deleteWorkflowTemplate(
    data: CaseTemplatesDeleteWorkflowTemplateData,
  ): CancelablePromise<CaseTemplatesDeleteWorkflowTemplateResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/api/v1/templates/workflow-templates/{template_id}",
      path: {
        template_id: data.templateId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class DocumentCaseLinksService {
  /**
   * Get Document Case Links
   * Get all case links for a specific document
   * @param data The data for the request.
   * @param data.documentId
   * @param data.skip
   * @param data.limit
   * @returns DocumentCaseLinksPublic Successful Response
   * @throws ApiError
   */
  public static getDocumentCaseLinks(
    data: DocumentCaseLinksGetDocumentCaseLinksData,
  ): CancelablePromise<DocumentCaseLinksGetDocumentCaseLinksResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/documents/{document_id}/links",
      path: {
        document_id: data.documentId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Document Case Link
   * Create a new link between a document and a case
   * @param data The data for the request.
   * @param data.documentId
   * @param data.requestBody
   * @returns DocumentCaseLinkPublic Successful Response
   * @throws ApiError
   */
  public static createDocumentCaseLink(
    data: DocumentCaseLinksCreateDocumentCaseLinkData,
  ): CancelablePromise<DocumentCaseLinksCreateDocumentCaseLinkResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/api/v1/documents/{document_id}/links",
      path: {
        document_id: data.documentId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Document Case Link
   * Update a document-case link
   * @param data The data for the request.
   * @param data.documentId
   * @param data.linkId
   * @param data.requestBody
   * @returns DocumentCaseLinkPublic Successful Response
   * @throws ApiError
   */
  public static updateDocumentCaseLink(
    data: DocumentCaseLinksUpdateDocumentCaseLinkData,
  ): CancelablePromise<DocumentCaseLinksUpdateDocumentCaseLinkResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/api/v1/documents/{document_id}/links/{link_id}",
      path: {
        document_id: data.documentId,
        link_id: data.linkId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Document Case Link
   * Delete a document-case link
   * @param data The data for the request.
   * @param data.documentId
   * @param data.linkId
   * @returns string Successful Response
   * @throws ApiError
   */
  public static deleteDocumentCaseLink(
    data: DocumentCaseLinksDeleteDocumentCaseLinkData,
  ): CancelablePromise<DocumentCaseLinksDeleteDocumentCaseLinkResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/api/v1/documents/{document_id}/links/{link_id}",
      path: {
        document_id: data.documentId,
        link_id: data.linkId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Case Linked Documents
   * Get all documents linked to a specific case
   * @param data The data for the request.
   * @param data.caseId
   * @param data.skip
   * @param data.limit
   * @param data.linkType
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static getCaseLinkedDocuments(
    data: DocumentCaseLinksGetCaseLinkedDocumentsData,
  ): CancelablePromise<DocumentCaseLinksGetCaseLinkedDocumentsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/api/v1/cases/{case_id}/linked-documents",
      path: {
        case_id: data.caseId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
        link_type: data.linkType,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class DocumentsService {
  /**
   * Upload Document
   * Upload a PDF or DOCX document.
   * @param data The data for the request.
   * @param data.formData
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static uploadDocument(
    data: DocumentsUploadDocumentData,
  ): CancelablePromise<DocumentsUploadDocumentResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/documents/upload/",
      formData: data.formData,
      mediaType: "multipart/form-data",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Documents Health Check
   * Health check for documents service.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static documentsHealthCheck(): CancelablePromise<DocumentsDocumentsHealthCheckResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/documents/health/",
    })
  }
}

export class InternalService {
  /**
   * Create User
   * Create a new user.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static createUser(
    data: InternalCreateUserData,
  ): CancelablePromise<InternalCreateUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/internal/private/users/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class ItemsService {
  /**
   * Read Items
   * Retrieve items.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns ItemsPublic Successful Response
   * @throws ApiError
   */
  public static readItems(
    data: ItemsReadItemsData = {},
  ): CancelablePromise<ItemsReadItemsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/items/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create Item
   * Create new item.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns ItemPublic Successful Response
   * @throws ApiError
   */
  public static createItem(
    data: ItemsCreateItemData,
  ): CancelablePromise<ItemsCreateItemResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/items/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Item
   * Get item by ID.
   * @param data The data for the request.
   * @param data.id
   * @returns ItemPublic Successful Response
   * @throws ApiError
   */
  public static readItem(
    data: ItemsReadItemData,
  ): CancelablePromise<ItemsReadItemResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/items/{id}",
      path: {
        id: data.id,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Item
   * Update an item.
   * @param data The data for the request.
   * @param data.id
   * @param data.requestBody
   * @returns ItemPublic Successful Response
   * @throws ApiError
   */
  public static updateItem(
    data: ItemsUpdateItemData,
  ): CancelablePromise<ItemsUpdateItemResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/items/{id}",
      path: {
        id: data.id,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Item
   * Delete an item.
   * @param data The data for the request.
   * @param data.id
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteItem(
    data: ItemsDeleteItemData,
  ): CancelablePromise<ItemsDeleteItemResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/items/{id}",
      path: {
        id: data.id,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class LegalCasesService {
  /**
   * Create Legal Case
   * @param data The data for the request.
   * @param data.requestBody
   * @returns LegalCasePublic Successful Response
   * @throws ApiError
   */
  public static createLegalCase(
    data: LegalCasesCreateLegalCaseData,
  ): CancelablePromise<LegalCasesCreateLegalCaseResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/legal-cases/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Legal Cases
   * Retrieve legal cases with pagination, search, filtering, and sorting.
   * Role-based access control:
   * - Admins can see all cases
   * - Lawyers can only see cases assigned to them
   * - Clients and assistants can only see cases where they are the client (via assigned lawyer)
   * @param data The data for the request.
   * @param data.skip Number of records to skip
   * @param data.limit Number of records to return
   * @param data.search Search in title and client name
   * @param data.caseType Filter by case type
   * @param data.status Filter by case status
   * @param data.priority Filter by case priority
   * @param data.lawyerId Filter by assigned lawyer
   * @param data.sortBy Sort by field (title, client_name, opening_date, case_type, status, priority)
   * @param data.sortOrder Sort order (asc, desc)
   * @param data.dateFrom Filter cases from this date
   * @param data.dateTo Filter cases to this date
   * @returns LegalCasesPublic Successful Response
   * @throws ApiError
   */
  public static readLegalCases(
    data: LegalCasesReadLegalCasesData = {},
  ): CancelablePromise<LegalCasesReadLegalCasesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/",
      query: {
        skip: data.skip,
        limit: data.limit,
        search: data.search,
        case_type: data.caseType,
        status: data.status,
        priority: data.priority,
        lawyer_id: data.lawyerId,
        sort_by: data.sortBy,
        sort_order: data.sortOrder,
        date_from: data.dateFrom,
        date_to: data.dateTo,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Available Lawyers
   * Get all users with LAWYER role for assignment to legal cases.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static getAvailableLawyers(): CancelablePromise<LegalCasesGetAvailableLawyersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/lawyers",
    })
  }

  /**
   * Read Legal Case
   * @param data The data for the request.
   * @param data.legalCaseId
   * @returns LegalCaseDetail Successful Response
   * @throws ApiError
   */
  public static readLegalCase(
    data: LegalCasesReadLegalCaseData,
  ): CancelablePromise<LegalCasesReadLegalCaseResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{legal_case_id}",
      path: {
        legal_case_id: data.legalCaseId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Legal Case
   * @param data The data for the request.
   * @param data.legalCaseId
   * @param data.requestBody
   * @returns LegalCasePublic Successful Response
   * @throws ApiError
   */
  public static updateLegalCase(
    data: LegalCasesUpdateLegalCaseData,
  ): CancelablePromise<LegalCasesUpdateLegalCaseResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/api/v1/legal-cases/{legal_case_id}",
      path: {
        legal_case_id: data.legalCaseId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Partial Update Legal Case
   * @param data The data for the request.
   * @param data.legalCaseId
   * @param data.requestBody
   * @returns LegalCasePublic Successful Response
   * @throws ApiError
   */
  public static partialUpdateLegalCase(
    data: LegalCasesPartialUpdateLegalCaseData,
  ): CancelablePromise<LegalCasesPartialUpdateLegalCaseResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/legal-cases/{legal_case_id}",
      path: {
        legal_case_id: data.legalCaseId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete Legal Case
   * @param data The data for the request.
   * @param data.legalCaseId
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static deleteLegalCase(
    data: LegalCasesDeleteLegalCaseData,
  ): CancelablePromise<LegalCasesDeleteLegalCaseResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/legal-cases/{legal_case_id}",
      path: {
        legal_case_id: data.legalCaseId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Bulk Update Case Status
   * Update status for multiple cases at once.
   * Only lawyers and admins can perform bulk status updates.
   * Each case is validated individually for access control and status transitions.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns BulkUpdateResult Successful Response
   * @throws ApiError
   */
  public static bulkUpdateCaseStatus(
    data: LegalCasesBulkUpdateCaseStatusData,
  ): CancelablePromise<LegalCasesBulkUpdateCaseStatusResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/legal-cases/bulk/status",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Status
   * Update case status with enhanced tracking and history
   * @param data The data for the request.
   * @param data.legalCaseId
   * @param data.requestBody
   * @returns LegalCasePublic Successful Response
   * @throws ApiError
   */
  public static updateCaseStatus(
    data: LegalCasesUpdateCaseStatusData,
  ): CancelablePromise<LegalCasesUpdateCaseStatusResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/legal-cases/{legal_case_id}/status",
      path: {
        legal_case_id: data.legalCaseId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Case Activities
   * Get activities for a specific case
   * @param data The data for the request.
   * @param data.legalCaseId
   * @param data.skip Number of records to skip
   * @param data.limit Number of records to return
   * @param data.activityType Filter by activity type
   * @param data.search Search in activity descriptions
   * @param data.startDate Filter activities from this date
   * @param data.endDate Filter activities until this date
   * @returns CaseActivitiesPublic Successful Response
   * @throws ApiError
   */
  public static readCaseActivities(
    data: LegalCasesReadCaseActivitiesData,
  ): CancelablePromise<LegalCasesReadCaseActivitiesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{legal_case_id}/activities",
      path: {
        legal_case_id: data.legalCaseId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
        activity_type: data.activityType,
        search: data.search,
        start_date: data.startDate,
        end_date: data.endDate,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read Case Status History
   * Get status change history for a specific case
   * @param data The data for the request.
   * @param data.legalCaseId
   * @param data.skip Number of records to skip
   * @param data.limit Number of records to return
   * @returns CaseStatusHistoriesPublic Successful Response
   * @throws ApiError
   */
  public static readCaseStatusHistory(
    data: LegalCasesReadCaseStatusHistoryData,
  ): CancelablePromise<LegalCasesReadCaseStatusHistoryResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{legal_case_id}/status-history",
      path: {
        legal_case_id: data.legalCaseId,
      },
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Case Priority
   * Update case priority and log the activity
   * @param data The data for the request.
   * @param data.legalCaseId
   * @param data.priority
   * @returns LegalCasePublic Successful Response
   * @throws ApiError
   */
  public static updateCasePriority(
    data: LegalCasesUpdateCasePriorityData,
  ): CancelablePromise<LegalCasesUpdateCasePriorityResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/legal-cases/{legal_case_id}/priority",
      path: {
        legal_case_id: data.legalCaseId,
      },
      query: {
        priority: data.priority,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Status Options
   * Get all available case status options
   * @returns string Successful Response
   * @throws ApiError
   */
  public static getStatusOptions(): CancelablePromise<LegalCasesGetStatusOptionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/status-options",
    })
  }

  /**
   * Get Priority Options
   * Get all available case priority options
   * @returns string Successful Response
   * @throws ApiError
   */
  public static getPriorityOptions(): CancelablePromise<LegalCasesGetPriorityOptionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/priority-options",
    })
  }

  /**
   * Get Valid Status Transitions
   * Get valid status transitions for a specific case based on current status and user role
   * @param data The data for the request.
   * @param data.legalCaseId
   * @returns string Successful Response
   * @throws ApiError
   */
  public static getValidStatusTransitions(
    data: LegalCasesGetValidStatusTransitionsData,
  ): CancelablePromise<LegalCasesGetValidStatusTransitionsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/legal-cases/{legal_case_id}/valid-status-transitions",
      path: {
        legal_case_id: data.legalCaseId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class LoginService {
  /**
   * Login Access Token
   * OAuth2 compatible token login, get an access token for future requests
   * @param data The data for the request.
   * @param data.formData
   * @returns Token Successful Response
   * @throws ApiError
   */
  public static loginAccessToken(
    data: LoginLoginAccessTokenData,
  ): CancelablePromise<LoginLoginAccessTokenResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/login/access-token",
      formData: data.formData,
      mediaType: "application/x-www-form-urlencoded",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Test Token
   * Test access token
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static testToken(): CancelablePromise<LoginTestTokenResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/login/test-token",
    })
  }

  /**
   * Recover Password
   * Password Recovery
   * @param data The data for the request.
   * @param data.email
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static recoverPassword(
    data: LoginRecoverPasswordData,
  ): CancelablePromise<LoginRecoverPasswordResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/login/password-recovery/{email}",
      path: {
        email: data.email,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Reset Password
   * Reset password
   * @param data The data for the request.
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static resetPassword(
    data: LoginResetPasswordData,
  ): CancelablePromise<LoginResetPasswordResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/login/reset-password/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Recover Password Html Content
   * HTML Content for Password Recovery
   * @param data The data for the request.
   * @param data.email
   * @returns string Successful Response
   * @throws ApiError
   */
  public static recoverPasswordHtmlContent(
    data: LoginRecoverPasswordHtmlContentData,
  ): CancelablePromise<LoginRecoverPasswordHtmlContentResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/login/password-recovery-html-content/{email}",
      path: {
        email: data.email,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class PrivateService {
  /**
   * Create User
   * Create a new user.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static internalCreateUser(
    data: InternalCreateUserData,
  ): CancelablePromise<InternalCreateUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/internal/private/users/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class ProtectedService {
  /**
   * Lawyer Route
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static lawyerRoute(): CancelablePromise<ProtectedLawyerRouteResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/lawyer-only",
    })
  }

  /**
   * Admin Only Route
   * Example endpoint that requires ADMIN role
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static adminOnlyRoute(): CancelablePromise<ProtectedAdminOnlyRouteResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/admin-only",
    })
  }

  /**
   * Client Only Route
   * Example endpoint that requires CLIENT role
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static clientOnlyRoute(): CancelablePromise<ProtectedClientOnlyRouteResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/client-only",
    })
  }

  /**
   * Assistant Only Route
   * Example endpoint that requires ASSISTANT role
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static assistantOnlyRoute(): CancelablePromise<ProtectedAssistantOnlyRouteResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/assistant-only",
    })
  }
}

export class ReportsService {
  /**
   * Get Dashboard Report
   * Get comprehensive dashboard report with case statistics, user activity, and document analytics
   * @param data The data for the request.
   * @param data.startDate Start date for filtering
   * @param data.endDate End date for filtering
   * @param data.includeTrends Include trend calculations
   * @returns unknown Successful Response
   * @throws ApiError
   */
  public static getDashboardReport(
    data: ReportsGetDashboardReportData = {},
  ): CancelablePromise<ReportsGetDashboardReportResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/reports/dashboard",
      query: {
        start_date: data.startDate,
        end_date: data.endDate,
        include_trends: data.includeTrends,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class UsersService {
  /**
   * Read Users
   * Retrieve users.
   * @param data The data for the request.
   * @param data.skip
   * @param data.limit
   * @returns UsersPublic Successful Response
   * @throws ApiError
   */
  public static readUsers(
    data: UsersReadUsersData = {},
  ): CancelablePromise<UsersReadUsersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/",
      query: {
        skip: data.skip,
        limit: data.limit,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Create User
   * Create new user.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static createUser(
    data: UsersCreateUserData,
  ): CancelablePromise<UsersCreateUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/users/",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read User Me
   * Get current user.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static readUserMe(): CancelablePromise<UsersReadUserMeResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/me",
    })
  }

  /**
   * Delete User Me
   * Delete own user.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteUserMe(): CancelablePromise<UsersDeleteUserMeResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/users/me",
    })
  }

  /**
   * Update User Me
   * Update own user.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static updateUserMe(
    data: UsersUpdateUserMeData,
  ): CancelablePromise<UsersUpdateUserMeResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/users/me",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update Password Me
   * Update own password.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static updatePasswordMe(
    data: UsersUpdatePasswordMeData,
  ): CancelablePromise<UsersUpdatePasswordMeResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/users/me/password",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Register User
   * Create new user without the need to be logged in.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static registerUser(
    data: UsersRegisterUserData,
  ): CancelablePromise<UsersRegisterUserResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/users/signup",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Assign User To Lawyer
   * Assign a client or assistant to a lawyer.
   * @param data The data for the request.
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static assignUserToLawyer(
    data: UsersAssignUserToLawyerData,
  ): CancelablePromise<UsersAssignUserToLawyerResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/users/assign",
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Unassigned Users
   * Get all clients and assistants that are not assigned to any lawyer.
   * @returns UsersPublic Successful Response
   * @throws ApiError
   */
  public static getUnassignedUsers(): CancelablePromise<UsersGetUnassignedUsersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/unassigned",
    })
  }

  /**
   * Get All Lawyers
   * Get all lawyers in the system.
   * @returns UsersPublic Successful Response
   * @throws ApiError
   */
  public static getAllLawyers(): CancelablePromise<UsersGetAllLawyersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/lawyers",
    })
  }

  /**
   * Get Lawyer Clients
   * Get all clients assigned to a specific lawyer.
   * @param data The data for the request.
   * @param data.lawyerId
   * @returns UsersPublic Successful Response
   * @throws ApiError
   */
  public static getLawyerClients(
    data: UsersGetLawyerClientsData,
  ): CancelablePromise<UsersGetLawyerClientsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/lawyers/{lawyer_id}/clients",
      path: {
        lawyer_id: data.lawyerId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Lawyer Assistants
   * Get all assistants assigned to a specific lawyer.
   * @param data The data for the request.
   * @param data.lawyerId
   * @returns UsersPublic Successful Response
   * @throws ApiError
   */
  public static getLawyerAssistants(
    data: UsersGetLawyerAssistantsData,
  ): CancelablePromise<UsersGetLawyerAssistantsResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/lawyers/{lawyer_id}/assistants",
      path: {
        lawyer_id: data.lawyerId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Get Lawyer Assigned Users
   * Get all users (clients and assistants) assigned to a specific lawyer.
   * @param data The data for the request.
   * @param data.lawyerId
   * @returns UsersPublic Successful Response
   * @throws ApiError
   */
  public static getLawyerAssignedUsers(
    data: UsersGetLawyerAssignedUsersData,
  ): CancelablePromise<UsersGetLawyerAssignedUsersResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/lawyers/{lawyer_id}/assigned-users",
      path: {
        lawyer_id: data.lawyerId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Read User By Id
   * Get a specific user by id.
   * @param data The data for the request.
   * @param data.userId
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static readUserById(
    data: UsersReadUserByIdData,
  ): CancelablePromise<UsersReadUserByIdResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/users/{user_id}",
      path: {
        user_id: data.userId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update User
   * Update a user.
   * @param data The data for the request.
   * @param data.userId
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static updateUser(
    data: UsersUpdateUserData,
  ): CancelablePromise<UsersUpdateUserResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/users/{user_id}",
      path: {
        user_id: data.userId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Delete User
   * Delete a user.
   * @param data The data for the request.
   * @param data.userId
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteUser(
    data: UsersDeleteUserData,
  ): CancelablePromise<UsersDeleteUserResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/api/v1/users/{user_id}",
      path: {
        user_id: data.userId,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Update User Assignment
   * Update user assignment to a lawyer.
   * @param data The data for the request.
   * @param data.userId
   * @param data.requestBody
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static updateUserAssignment(
    data: UsersUpdateUserAssignmentData,
  ): CancelablePromise<UsersUpdateUserAssignmentResponse> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: "/api/v1/users/{user_id}/assignment",
      path: {
        user_id: data.userId,
      },
      body: data.requestBody,
      mediaType: "application/json",
      errors: {
        422: "Validation Error",
      },
    })
  }
}

export class UtilsService {
  /**
   * Test Email
   * Test emails.
   * @param data The data for the request.
   * @param data.emailTo
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static testEmail(
    data: UtilsTestEmailData,
  ): CancelablePromise<UtilsTestEmailResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/utils/test-email/",
      query: {
        email_to: data.emailTo,
      },
      errors: {
        422: "Validation Error",
      },
    })
  }

  /**
   * Health Check
   * @returns boolean Successful Response
   * @throws ApiError
   */
  public static healthCheck(): CancelablePromise<UtilsHealthCheckResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/utils/health-check/",
    })
  }
}
