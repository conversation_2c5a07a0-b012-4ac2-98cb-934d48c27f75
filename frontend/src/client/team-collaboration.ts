/**
 * Team Collaboration API Service
 *
 * This module provides API services for team collaboration functionality,
 * including team member management, internal messaging, and task assignment.
 */

import { ApiError } from "./core/ApiError"
import { CancelablePromise } from "./core/CancelablePromise"
import { OpenAPI } from "./core/OpenAPI"
import { request as __request } from "./core/request"

// Types for Team Collaboration
export interface CaseTeamMemberPublic {
  id: string
  case_id: string
  user_id: string
  role: string
  permissions: string[]
  is_active: boolean
  notes?: string
  assigned_at: string
  assigned_by: string
  user?: {
    id: string
    full_name: string
    email: string
    role: string
    specialization?: string
  }
  assigner?: {
    id: string
    full_name: string
    email: string
  }
}

export interface CaseTeamMembersPublic {
  data: CaseTeamMemberPublic[]
  count: number
}

export interface CaseTeamMemberCreate {
  user_id: string
  role: string
  permissions?: string[]
  notes?: string
}

export interface CaseTeamMemberUpdate {
  role?: string
  permissions?: string[]
  is_active?: boolean
  notes?: string
}

export interface TeamMessagePublic {
  id: string
  case_id?: string
  message_type: string
  subject: string
  content: string
  mentions: string[]
  attachments: string[]
  is_pinned: boolean
  is_urgent: boolean
  sender_id: string
  created_at: string
  updated_at: string
  sender?: {
    id: string
    full_name: string
    email: string
  }
  case?: {
    id: string
    title: string
  }
  replies_count: number
  is_read: boolean
}

export interface TeamMessagesPublic {
  data: TeamMessagePublic[]
  count: number
  unread_count: number
}

export interface TeamMessageCreate {
  case_id?: string
  message_type?: string
  subject: string
  content: string
  mentions?: string[]
  attachments?: string[]
  is_urgent?: boolean
}

export interface TeamMessageReplyPublic {
  id: string
  parent_message_id: string
  content: string
  mentions: string[]
  sender_id: string
  created_at: string
  sender?: {
    id: string
    full_name: string
    email: string
  }
}

export interface TeamMessageReplyCreate {
  content: string
  mentions?: string[]
}

export interface TeamTaskPublic {
  id: string
  case_id: string
  title: string
  description?: string
  status: string
  priority: string
  assigned_to?: string
  due_date?: string
  estimated_hours?: number
  actual_hours?: number
  tags: string[]
  dependencies: string[]
  created_by: string
  created_at: string
  updated_at: string
  completed_at?: string
  creator?: {
    id: string
    full_name: string
    email: string
  }
  assignee?: {
    id: string
    full_name: string
    email: string
  }
  case?: {
    id: string
    title: string
  }
  is_overdue: boolean
  progress_percentage: number
}

export interface TeamTasksPublic {
  data: TeamTaskPublic[]
  count: number
}

export interface TeamTaskCreate {
  title: string
  description?: string
  priority?: string
  assigned_to?: string
  due_date?: string
  estimated_hours?: number
  tags?: string[]
  dependencies?: string[]
}

export interface TeamTaskUpdate {
  title?: string
  description?: string
  status?: string
  priority?: string
  assigned_to?: string
  due_date?: string
  estimated_hours?: number
  actual_hours?: number
  tags?: string[]
  dependencies?: string[]
}

/**
 * Team Collaboration Service
 */
export class TeamCollaborationService {
  /**
   * Get team members for a case
   */
  public static getCaseTeamMembers({
    caseId,
    skip = 0,
    limit = 100,
    role,
    is_active,
  }: {
    caseId: string
    skip?: number
    limit?: number
    role?: string
    is_active?: boolean
  }): CancelablePromise<CaseTeamMembersPublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/team/cases/${caseId}/team`,
      query: {
        skip,
        limit,
        role,
        is_active,
      },
    })
  }

  /**
   * Add a team member to a case
   */
  public static addTeamMember({
    caseId,
    member,
  }: {
    caseId: string
    member: CaseTeamMemberCreate
  }): CancelablePromise<CaseTeamMemberPublic> {
    return __request(OpenAPI, {
      method: "POST",
      url: `/api/v1/team/cases/${caseId}/team`,
      body: member,
    })
  }

  /**
   * Update a team member
   */
  public static updateTeamMember({
    caseId,
    memberId,
    member,
  }: {
    caseId: string
    memberId: string
    member: CaseTeamMemberUpdate
  }): CancelablePromise<CaseTeamMemberPublic> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: `/api/v1/team/cases/${caseId}/team/${memberId}`,
      body: member,
    })
  }

  /**
   * Remove a team member from a case
   */
  public static removeTeamMember({
    caseId,
    memberId,
  }: {
    caseId: string
    memberId: string
  }): CancelablePromise<{ message: string }> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: `/api/v1/team/cases/${caseId}/team/${memberId}`,
    })
  }

  /**
   * Get team messages for a case
   */
  public static getTeamMessages({
    caseId,
    skip = 0,
    limit = 50,
    message_type,
    is_pinned,
    search,
  }: {
    caseId: string
    skip?: number
    limit?: number
    message_type?: string
    is_pinned?: boolean
    search?: string
  }): CancelablePromise<TeamMessagesPublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/team/cases/${caseId}/messages`,
      query: {
        skip,
        limit,
        message_type,
        is_pinned,
        search,
      },
    })
  }

  /**
   * Send a team message
   */
  public static sendTeamMessage({
    caseId,
    message,
  }: {
    caseId: string
    message: TeamMessageCreate
  }): CancelablePromise<TeamMessagePublic> {
    return __request(OpenAPI, {
      method: "POST",
      url: `/api/v1/team/cases/${caseId}/messages`,
      body: message,
    })
  }

  /**
   * Get a specific team message
   */
  public static getTeamMessage({
    caseId,
    messageId,
  }: {
    caseId: string
    messageId: string
  }): CancelablePromise<TeamMessagePublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/team/cases/${caseId}/messages/${messageId}`,
    })
  }

  /**
   * Get replies to a team message
   */
  public static getMessageReplies({
    caseId,
    messageId,
    skip = 0,
    limit = 50,
  }: {
    caseId: string
    messageId: string
    skip?: number
    limit?: number
  }): CancelablePromise<TeamMessageReplyPublic[]> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/team/cases/${caseId}/messages/${messageId}/replies`,
      query: {
        skip,
        limit,
      },
    })
  }

  /**
   * Reply to a team message
   */
  public static replyToTeamMessage({
    caseId,
    messageId,
    reply,
  }: {
    caseId: string
    messageId: string
    reply: TeamMessageReplyCreate
  }): CancelablePromise<TeamMessageReplyPublic> {
    return __request(OpenAPI, {
      method: "POST",
      url: `/api/v1/team/cases/${caseId}/messages/${messageId}/replies`,
      body: reply,
    })
  }

  /**
   * Get tasks for a case
   */
  public static getCaseTasks({
    caseId,
    skip = 0,
    limit = 100,
    status,
    priority,
    assigned_to,
    search,
  }: {
    caseId: string
    skip?: number
    limit?: number
    status?: string
    priority?: string
    assigned_to?: string
    search?: string
  }): CancelablePromise<TeamTasksPublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/team/cases/${caseId}/tasks`,
      query: {
        skip,
        limit,
        status,
        priority,
        assigned_to,
        search,
      },
    })
  }

  /**
   * Create a new task
   */
  public static createTask({
    caseId,
    task,
  }: {
    caseId: string
    task: TeamTaskCreate
  }): CancelablePromise<TeamTaskPublic> {
    return __request(OpenAPI, {
      method: "POST",
      url: `/api/v1/team/cases/${caseId}/tasks`,
      body: task,
    })
  }

  /**
   * Get a specific task
   */
  public static getTask({
    caseId,
    taskId,
  }: {
    caseId: string
    taskId: string
  }): CancelablePromise<TeamTaskPublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: `/api/v1/team/cases/${caseId}/tasks/${taskId}`,
    })
  }

  /**
   * Update a task
   */
  public static updateTask({
    caseId,
    taskId,
    task,
  }: {
    caseId: string
    taskId: string
    task: TeamTaskUpdate
  }): CancelablePromise<TeamTaskPublic> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: `/api/v1/team/cases/${caseId}/tasks/${taskId}`,
      body: task,
    })
  }

  /**
   * Delete a task
   */
  public static deleteTask({
    caseId,
    taskId,
  }: {
    caseId: string
    taskId: string
  }): CancelablePromise<{ message: string }> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: `/api/v1/team/cases/${caseId}/tasks/${taskId}`,
    })
  }
}
