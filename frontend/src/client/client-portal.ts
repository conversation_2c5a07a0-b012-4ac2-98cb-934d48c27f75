/**
 * Client Portal API Service
 * 
 * This module provides API services for the client portal functionality,
 * including dashboard, cases, notifications, messaging, and document access.
 */

import { ApiError } from './core/ApiError'
import { CancelablePromise } from './core/CancelablePromise'
import { OpenAPI } from './core/OpenAPI'
import { request as __request } from './core/request'

// Types for Client Portal
export interface ClientDashboardStats {
  active_cases: number
  total_documents: number
  unread_messages: number
  unread_notifications: number
  upcoming_appointments: number
  pending_deadlines: number
}

export interface ClientCaseView {
  id: string
  title: string
  case_type: string
  status: string
  opening_date: string
  description?: string
  next_hearing_date?: string
  lawyer?: {
    id: string
    full_name: string
    email: string
    specialization?: string
  }
  recent_updates: string[]
  shared_documents_count: number
  unread_messages_count: number
}

export interface ClientCasesPublic {
  data: ClientCaseView[]
  count: number
}

export interface NotificationPublic {
  id: string
  title: string
  message: string
  notification_type: string
  priority: string
  is_read: boolean
  user_id: string
  case_id?: string
  created_at: string
  read_at?: string
  case?: {
    id: string
    title: string
    case_type: string
  }
}

export interface NotificationsPublic {
  data: NotificationPublic[]
  count: number
  unread_count: number
}

export interface ClientMessagePublic {
  id: string
  subject: string
  content: string
  sender_id: string
  recipient_id: string
  case_id?: string
  parent_message_id?: string
  status: string
  created_at: string
  read_at?: string
  sender?: {
    id: string
    full_name: string
    email: string
  }
  recipient?: {
    id: string
    full_name: string
    email: string
  }
  case?: {
    id: string
    title: string
  }
  replies_count: number
  can_reply: boolean
}

export interface ClientMessagesPublic {
  data: ClientMessagePublic[]
  count: number
  unread_count: number
}

export interface ClientMessageCreate {
  subject: string
  content: string
  recipient_id: string
  case_id?: string
  parent_message_id?: string
  is_confidential?: boolean
  attachments?: string[]
}

export interface ClientDocumentPublic {
  id: string
  filename: string
  description?: string
  category: string
  file_size: number
  uploaded_at: string
  uploader_name: string
  can_download: boolean
  download_url: string
}

export interface ClientDocumentsPublic {
  data: ClientDocumentPublic[]
  count: number
}

export interface FilterOption {
  value: string
  label: string
}

/**
 * Client Portal Service
 */
export class ClientPortalService {
  /**
   * Get client dashboard statistics
   */
  public static getDashboard(): CancelablePromise<ClientDashboardStats> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/client-portal/dashboard',
    })
  }

  /**
   * Get client cases with limited information
   */
  public static getClientCases({
    skip = 0,
    limit = 100,
    status,
    case_type,
  }: {
    skip?: number
    limit?: number
    status?: string
    case_type?: string
  } = {}): CancelablePromise<ClientCasesPublic> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/client-portal/cases',
      query: {
        skip,
        limit,
        status,
        case_type,
      },
    })
  }

  /**
   * Get detailed information about a specific case
   */
  public static getClientCase(caseId: string): CancelablePromise<ClientCaseView> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/client-portal/cases/${caseId}`,
    })
  }

  /**
   * Get client notifications
   */
  public static getNotifications({
    skip = 0,
    limit = 50,
    is_read,
    notification_type,
    priority,
  }: {
    skip?: number
    limit?: number
    is_read?: boolean
    notification_type?: string
    priority?: string
  } = {}): CancelablePromise<NotificationsPublic> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/client-portal/notifications',
      query: {
        skip,
        limit,
        is_read,
        notification_type,
        priority,
      },
    })
  }

  /**
   * Mark a notification as read
   */
  public static markNotificationRead(notificationId: string): CancelablePromise<NotificationPublic> {
    return __request(OpenAPI, {
      method: 'PATCH',
      url: `/api/v1/client-portal/notifications/${notificationId}`,
    })
  }

  /**
   * Mark all notifications as read
   */
  public static markAllNotificationsRead(): CancelablePromise<{ message: string }> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/client-portal/notifications/mark-all-read',
    })
  }

  /**
   * Get client messages
   */
  public static getMessages({
    skip = 0,
    limit = 50,
    case_id,
    status,
    search,
  }: {
    skip?: number
    limit?: number
    case_id?: string
    status?: string
    search?: string
  } = {}): CancelablePromise<ClientMessagesPublic> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/client-portal/messages',
      query: {
        skip,
        limit,
        case_id,
        status,
        search,
      },
    })
  }

  /**
   * Send a message to lawyer
   */
  public static sendMessage(message: ClientMessageCreate): CancelablePromise<ClientMessagePublic> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/client-portal/messages',
      body: message,
    })
  }

  /**
   * Get a specific message
   */
  public static getMessage(messageId: string): CancelablePromise<ClientMessagePublic> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/client-portal/messages/${messageId}`,
    })
  }

  /**
   * Get replies to a message
   */
  public static getMessageReplies({
    messageId,
    skip = 0,
    limit = 50,
  }: {
    messageId: string
    skip?: number
    limit?: number
  }): CancelablePromise<ClientMessagesPublic> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/client-portal/messages/${messageId}/replies`,
      query: {
        skip,
        limit,
      },
    })
  }

  /**
   * Get documents shared with client for a specific case
   */
  public static getCaseDocuments({
    caseId,
    skip = 0,
    limit = 50,
    category,
    search,
  }: {
    caseId: string
    skip?: number
    limit?: number
    category?: string
    search?: string
  }): CancelablePromise<ClientDocumentsPublic> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/client-portal/cases/${caseId}/documents`,
      query: {
        skip,
        limit,
        category,
        search,
      },
    })
  }

  /**
   * Download a document
   */
  public static downloadDocument(documentId: string): CancelablePromise<{
    download_url: string
    filename: string
    content_type: string
    file_size: number
  }> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/client-portal/documents/${documentId}/download`,
    })
  }

  /**
   * Get case types for filtering
   */
  public static getCaseTypes(): CancelablePromise<FilterOption[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/client-portal/case-types',
    })
  }

  /**
   * Get case statuses for filtering
   */
  public static getCaseStatuses(): CancelablePromise<FilterOption[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/client-portal/case-statuses',
    })
  }

  /**
   * Get document categories for filtering
   */
  public static getDocumentCategories(): CancelablePromise<FilterOption[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/client-portal/document-categories',
    })
  }
}
