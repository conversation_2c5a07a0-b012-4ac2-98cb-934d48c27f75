// This file is auto-generated by @hey-api/openapi-ts

export type ActivityType =
  | "case_created"
  | "case_updated"
  | "status_changed"
  | "priority_changed"
  | "document_uploaded"
  | "document_downloaded"
  | "document_deleted"
  | "note_added"
  | "note_updated"
  | "note_deleted"
  | "user_assigned"
  | "milestone_created"
  | "milestone_updated"
  | "milestone_deleted"
  | "deadline_created"
  | "deadline_updated"
  | "deadline_deleted"
  | "milestones_created_from_template"
  | "template_applied"

export type Body_case_documents_upload_case_document = {
  file: Blob | File
}

export type Body_documents_upload_document = {
  file: Blob | File
}

export type Body_login_login_access_token = {
  grant_type?: string | null
  username: string
  password: string
  scope?: string
  client_id?: string | null
  client_secret?: string | null
}

/**
 * Request model for bulk status updates
 */
export type BulkStatusUpdateRequest = {
  case_ids: Array<string>
  new_status: string
  notes?: string | null
}

/**
 * Result model for bulk operations
 */
export type BulkUpdateResult = {
  success_count: number
  failed_count: number
  failed_cases: Array<{
    [key: string]: unknown
  }>
  message: string
}

export type CaseActivitiesPublic = {
  data: Array<CaseActivityPublic>
  count: number
}

export type CaseActivityPublic = {
  case_id: string
  activity_type: ActivityType
  description: string
  user_id: string
  activity_metadata?: {
    [key: string]: unknown
  } | null
  id: string
  created_at: string
  user?: UserPublic | null
}

export type CaseDeadlineCreate = {
  title: string
  description?: string | null
  deadline_type?: DeadlineType
  deadline_date: string
  is_critical?: boolean
  reminder_days?: Array<number>
  assigned_to?: string | null
  notes?: string | null
}

export type CaseDeadlinePublic = {
  case_id: string
  title: string
  description?: string | null
  deadline_type?: DeadlineType
  deadline_date: string
  /**
   * Whether missing this deadline has serious consequences
   */
  is_critical?: boolean
  /**
   * Days before deadline to send reminders
   */
  reminder_days?: Array<number>
  assigned_to?: string | null
  is_completed?: boolean
  completion_date?: string | null
  notes?: string | null
  id: string
  created_at: string
  updated_at: string
  created_by: string
  assigned_user?: UserPublic | null
  creator?: UserPublic | null
}

export type CaseDeadlinesPublic = {
  data: Array<CaseDeadlinePublic>
  count: number
}

export type CaseDeadlineUpdate = {
  title?: string | null
  description?: string | null
  deadline_type?: DeadlineType | null
  deadline_date?: string | null
  is_critical?: boolean | null
  reminder_days?: Array<number> | null
  assigned_to?: string | null
  is_completed?: boolean | null
  completion_date?: string | null
  notes?: string | null
}

export type CaseDocumentPublic = {
  case_id: string
  folder_id?: string | null
  filename: string
  original_filename: string
  file_path: string
  content_type: string
  file_size: number
  category?: DocumentCategory
  description?: string | null
  tags?: Array<string> | null
  is_confidential?: boolean
  is_shared_with_client?: boolean
  id: string
  uploaded_by: string
  uploaded_at: string
  updated_at: string
  uploader?: UserPublic | null
  folder_path?: Array<string>
  linked_cases?: Array<DocumentCaseLinkPublic>
  can_edit?: boolean
  can_delete?: boolean
  can_download?: boolean
  download_url?: string | null
}

export type CaseDocumentsPublic = {
  data: Array<CaseDocumentPublic>
  count: number
}

export type CaseDocumentUpdate = {
  folder_id?: string | null
  category?: DocumentCategory | null
  description?: string | null
  tags?: Array<string> | null
  is_confidential?: boolean | null
  is_shared_with_client?: boolean | null
}

export type CaseFolderCreate = {
  name: string
  description?: string | null
  parent_folder_id?: string | null
  color?: string | null
}

export type CaseFolderPublic = {
  case_id: string
  name: string
  description?: string | null
  parent_folder_id?: string | null
  color?: string | null
  is_system_folder?: boolean
  folder_order?: number
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: UserPublic | null
  document_count?: number
  subfolder_count?: number
  total_size?: number
  can_edit?: boolean
  can_delete?: boolean
  path?: Array<string>
}

export type CaseFoldersPublic = {
  data: Array<CaseFolderPublic>
  count: number
}

export type CaseFolderUpdate = {
  name?: string | null
  description?: string | null
  parent_folder_id?: string | null
  color?: string | null
  folder_order?: number | null
}

export type CaseMilestoneCreate = {
  title: string
  description?: string | null
  milestone_type?: MilestoneType
  target_date?: string | null
  order_index?: number
  is_required?: boolean
  assigned_to?: string | null
  notes?: string | null
}

export type CaseMilestonePublic = {
  case_id: string
  title: string
  description?: string | null
  milestone_type?: MilestoneType
  status?: MilestoneStatus
  target_date?: string | null
  completion_date?: string | null
  /**
   * Order of milestone in case progression
   */
  order_index?: number
  /**
   * Whether this milestone is required for case completion
   */
  is_required?: boolean
  assigned_to?: string | null
  progress_percentage?: number
  notes?: string | null
  id: string
  created_at: string
  updated_at: string
  created_by: string
  assigned_user?: UserPublic | null
  creator?: UserPublic | null
}

export type CaseMilestonesPublic = {
  data: Array<CaseMilestonePublic>
  count: number
}

export type CaseMilestoneUpdate = {
  title?: string | null
  description?: string | null
  milestone_type?: MilestoneType | null
  status?: MilestoneStatus | null
  target_date?: string | null
  completion_date?: string | null
  order_index?: number | null
  is_required?: boolean | null
  assigned_to?: string | null
  progress_percentage?: number | null
  notes?: string | null
}

export type CaseNoteCreate = {
  content: string
  note_type?: NoteType
  category?: NoteCategory
  is_pinned?: boolean
  tags?: Array<string> | null
  mentioned_users?: Array<string> | null
  parent_note_id?: string | null
}

export type CaseNotePublic = {
  case_id: string
  content: string
  note_type?: NoteType
  category?: NoteCategory
  is_pinned?: boolean
  tags?: Array<string> | null
  mentioned_users?: Array<string> | null
  parent_note_id?: string | null
  id: string
  author_id: string
  created_at: string
  updated_at: string
  author?: UserPublic | null
  replies_count?: number
  can_edit?: boolean
  can_delete?: boolean
}

export type CaseNotesPublic = {
  data: Array<CaseNotePublic>
  count: number
}

export type CaseNoteUpdate = {
  content?: string | null
  note_type?: NoteType | null
  category?: NoteCategory | null
  is_pinned?: boolean | null
  tags?: Array<string> | null
}

export type CasePriority = "low" | "medium" | "high" | "urgent"

export type CaseProgressSummary = {
  case_id: string
  total_milestones: number
  completed_milestones: number
  progress_percentage: number
  overdue_milestones: number
  upcoming_deadlines: number
  critical_deadlines: number
  next_milestone?: CaseMilestonePublic | null
  next_deadline?: CaseDeadlinePublic | null
}

export type CaseStatus =
  | "open"
  | "in_progress"
  | "under_review"
  | "closed"
  | "archived"

export type CaseStatusHistoriesPublic = {
  data: Array<CaseStatusHistoryPublic>
  count: number
}

export type CaseStatusHistoryPublic = {
  case_id: string
  old_status?: string | null
  new_status: string
  changed_by: string
  notes?: string | null
  id: string
  changed_at: string
  changed_by_user?: UserPublic | null
}

export type CaseTeamMemberCreate = {
  user_id: string
  role: TeamMemberRole
  permissions?: Array<string>
  notes?: string | null
}

export type CaseTeamMemberPublic = {
  case_id: string
  user_id: string
  role: TeamMemberRole
  permissions?: Array<string>
  is_active?: boolean
  notes?: string | null
  id: string
  assigned_at: string
  assigned_by: string
  user?: UserPublic | null
  assigner?: UserPublic | null
}

export type CaseTeamMembersPublic = {
  data: Array<CaseTeamMemberPublic>
  count: number
}

export type CaseTeamMemberUpdate = {
  role?: TeamMemberRole | null
  permissions?: Array<string> | null
  is_active?: boolean | null
  notes?: string | null
}

export type CaseTemplateCreate = {
  name: string
  description?: string | null
  case_type: CaseType
  template_data?: {
    [key: string]: unknown
  }
  is_active?: boolean
  is_public?: boolean
  tags?: Array<string>
}

export type CaseTemplatePublic = {
  name: string
  description?: string | null
  case_type: CaseType
  /**
   * JSON data for template configuration
   */
  template_data?: {
    [key: string]: unknown
  }
  is_active?: boolean
  /**
   * Whether template is available to all users
   */
  is_public?: boolean
  /**
   * Tags for template categorization
   */
  tags?: Array<string>
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: UserPublic | null
}

export type CaseTemplatesPublic = {
  data: Array<CaseTemplatePublic>
  count: number
}

export type CaseTemplateUpdate = {
  name?: string | null
  description?: string | null
  case_type?: CaseType | null
  template_data?: {
    [key: string]: unknown
  } | null
  is_active?: boolean | null
  is_public?: boolean | null
  tags?: Array<string> | null
}

export type CaseType =
  | "civil"
  | "criminal"
  | "family"
  | "corporate"
  | "immigration"
  | "personal_injury"
  | "real_estate"
  | "intellectual_property"
  | "employment"
  | "other"

export type ClientCasesPublic = {
  data: Array<ClientCaseView>
  count: number
}

export type ClientCaseView = {
  id: string
  title: string
  case_type: CaseType
  status: CaseStatus
  opening_date: string
  description?: string | null
  next_hearing_date?: string | null
  lawyer?: UserPublic | null
  recent_updates?: Array<string>
  shared_documents_count?: number
  unread_messages_count?: number
}

export type ClientDashboardStats = {
  active_cases: number
  total_documents: number
  unread_messages: number
  unread_notifications: number
  upcoming_appointments: number
  pending_deadlines: number
}

export type ClientMessageCreate = {
  subject: string
  content: string
  recipient_id: string
  case_id?: string | null
  parent_message_id?: string | null
  is_confidential?: boolean
  attachments?: Array<string>
}

export type ClientMessagePublic = {
  subject: string
  content: string
  is_confidential?: boolean
  attachments?: Array<string>
  message_metadata?: {
    [key: string]: unknown
  }
  id: string
  sender_id: string
  recipient_id: string
  case_id: string | null
  parent_message_id: string | null
  status: MessageStatus
  created_at: string
  read_at: string | null
  sender?: UserPublic | null
  recipient?: UserPublic | null
  case?: LegalCasePublic | null
  replies_count?: number
  can_reply?: boolean
}

export type ClientMessagesPublic = {
  data: Array<ClientMessagePublic>
  count: number
  unread_count: number
}

export type DeadlineType =
  | "court_deadline"
  | "internal_deadline"
  | "client_deadline"
  | "statute_limitation"
  | "filing_deadline"
  | "response_deadline"
  | "discovery_deadline"
  | "other"

export type DocumentCaseLinkCreate = {
  case_id: string
  link_type?: string
  notes?: string | null
}

export type DocumentCaseLinkPublic = {
  id: string
  document_id: string
  case_id: string
  linked_by: string
  linked_at: string
  link_type: string
  notes?: string | null
  case?: LegalCasePublic | null
  linked_by_user?: UserPublic | null
}

export type DocumentCaseLinksPublic = {
  data: Array<DocumentCaseLinkPublic>
  count: number
}

export type DocumentCaseLinkUpdate = {
  link_type?: string | null
  notes?: string | null
}

export type DocumentCategory =
  | "evidence"
  | "contracts"
  | "correspondence"
  | "court_documents"
  | "research"
  | "billing"
  | "internal"
  | "other"

export type DocumentTemplateCreate = {
  name: string
  description?: string | null
  case_type?: CaseType | null
  document_type: string
  template_content: string
  placeholders?: Array<string>
  is_active?: boolean
  is_public?: boolean
  tags?: Array<string>
}

export type DocumentTemplatePublic = {
  name: string
  description?: string | null
  /**
   * Associated case type, null for universal
   */
  case_type?: CaseType | null
  /**
   * Type of document (contract, letter, etc.)
   */
  document_type: string
  /**
   * Template content with placeholders
   */
  template_content: string
  /**
   * List of available placeholders
   */
  placeholders?: Array<string>
  is_active?: boolean
  is_public?: boolean
  tags?: Array<string>
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: UserPublic | null
}

export type DocumentTemplatesPublic = {
  data: Array<DocumentTemplatePublic>
  count: number
}

export type DocumentTemplateUpdate = {
  name?: string | null
  description?: string | null
  case_type?: CaseType | null
  document_type?: string | null
  template_content?: string | null
  placeholders?: Array<string> | null
  is_active?: boolean | null
  is_public?: boolean | null
  tags?: Array<string> | null
}

export type HTTPValidationError = {
  detail?: Array<ValidationError>
}

export type ItemCreate = {
  title: string
  description?: string | null
}

export type ItemPublic = {
  title: string
  description?: string | null
  id: string
  owner_id: string
}

export type ItemsPublic = {
  data: Array<ItemPublic>
  count: number
}

export type ItemUpdate = {
  title?: string | null
  description?: string | null
}

export type LegalCaseCreate = {
  title: string
  client_name: string
  case_type?: CaseType
  opening_date?: string
  description?: string | null
  status?: CaseStatus
  priority?: CasePriority
  lawyer_id: string
}

export type LegalCaseDetail = {
  title: string
  client_name: string
  case_type?: CaseType
  opening_date?: string
  description?: string | null
  status?: CaseStatus
  priority?: CasePriority
  id: string
  lawyer_id: string
  lawyer?: UserPublic | null
  recent_activities?: Array<CaseActivityPublic>
  status_history?: Array<CaseStatusHistoryPublic>
}

export type LegalCasePublic = {
  title: string
  client_name: string
  case_type?: CaseType
  opening_date?: string
  description?: string | null
  status?: CaseStatus
  priority?: CasePriority
  id: string
  lawyer_id: string
}

export type LegalCasesPublic = {
  data: Array<LegalCaseWithLawyer>
  count: number
}

export type LegalCaseUpdate = {
  title?: string | null
  client_name?: string | null
  case_type?: CaseType | null
  opening_date?: string | null
  description?: string | null
  status?: CaseStatus | null
  priority?: CasePriority | null
}

export type LegalCaseWithLawyer = {
  title: string
  client_name: string
  case_type?: CaseType
  opening_date?: string
  description?: string | null
  status?: CaseStatus
  priority?: CasePriority
  id: string
  lawyer_id: string
  lawyer?: UserPublic | null
}

export type Message = {
  message: string
}

export type MessageStatus = "sent" | "delivered" | "read"

export type MilestoneStatus =
  | "not_started"
  | "in_progress"
  | "completed"
  | "overdue"
  | "cancelled"

export type MilestoneTemplateCreate = {
  case_type: CaseType
  title: string
  description?: string | null
  milestone_type?: MilestoneType
  order_index?: number
  is_required?: boolean
  estimated_days?: number | null
  notes?: string | null
}

export type MilestoneTemplatePublic = {
  case_type: CaseType
  title: string
  description?: string | null
  milestone_type?: MilestoneType
  order_index?: number
  is_required?: boolean
  /**
   * Estimated days from case opening
   */
  estimated_days?: number | null
  notes?: string | null
  id: string
  created_at: string
  created_by: string
  is_active: boolean
  creator?: UserPublic | null
}

export type MilestoneTemplatesPublic = {
  data: Array<MilestoneTemplatePublic>
  count: number
}

export type MilestoneType =
  | "case_opening"
  | "discovery"
  | "filing"
  | "hearing"
  | "negotiation"
  | "trial"
  | "settlement"
  | "appeal"
  | "case_closure"
  | "custom"

export type NewPassword = {
  token: string
  new_password: string
}

export type NoteCategory =
  | "general"
  | "research"
  | "meeting"
  | "deadline"
  | "strategy"
  | "evidence"
  | "correspondence"
  | "billing"

export type NoteType =
  | "private"
  | "team"
  | "client"
  | "admin"
  | "task"
  | "communication"

export type NotificationChannel =
  | "in_app"
  | "email"
  | "sms"
  | "push"
  | "webhook"

export type NotificationPreferencesCreate = {
  notification_type: NotificationType
  channels?: Array<NotificationChannel>
  is_enabled?: boolean
  frequency?: string
  quiet_hours_start?: string | null
  quiet_hours_end?: string | null
  priority_threshold?: NotificationPriority
}

export type NotificationPreferencesPublic = {
  user_id: string
  notification_type: NotificationType
  channels?: Array<NotificationChannel>
  is_enabled?: boolean
  frequency?: string
  quiet_hours_start?: string | null
  quiet_hours_end?: string | null
  priority_threshold?: NotificationPriority
  id: string
  created_at: string
  updated_at: string
}

export type NotificationPreferencesUpdate = {
  channels?: Array<NotificationChannel> | null
  is_enabled?: boolean | null
  frequency?: string | null
  quiet_hours_start?: string | null
  quiet_hours_end?: string | null
  priority_threshold?: NotificationPriority | null
}

export type NotificationPriority = "low" | "medium" | "high" | "urgent"

export type NotificationPublic = {
  title: string
  message: string
  notification_type: NotificationType
  priority?: NotificationPriority
  is_read?: boolean
  notification_metadata?: {
    [key: string]: unknown
  }
  expires_at?: string | null
  id: string
  user_id: string
  case_id: string | null
  created_at: string
  read_at: string | null
  case?: LegalCasePublic | null
}

export type NotificationsPublic = {
  data: Array<NotificationPublic>
  count: number
  unread_count: number
}

export type NotificationTemplatePublic = {
  name: string
  description?: string | null
  notification_type: NotificationType
  channel: NotificationChannel
  subject_template: string
  body_template: string
  variables?: Array<string>
  is_active?: boolean
  is_system?: boolean
  template_metadata?: {
    [key: string]: unknown
  }
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: UserPublic | null
}

export type NotificationTemplatesPublic = {
  data: Array<NotificationTemplatePublic>
  count: number
}

export type NotificationType =
  | "case_update"
  | "document_shared"
  | "appointment_reminder"
  | "deadline_approaching"
  | "message_received"
  | "status_change"
  | "milestone_completed"
  | "system_announcement"

export type NotificationUpdate = {
  is_read?: boolean | null
  read_at?: string | null
}

export type PrivateUserCreate = {
  email: string
  password: string
  full_name: string
  is_verified?: boolean
}

export type StatusChangeRequest = {
  new_status: CaseStatus
  notes?: string | null
}

export type TaskPriority = "low" | "medium" | "high" | "urgent"

export type TaskStatus =
  | "not_started"
  | "in_progress"
  | "under_review"
  | "completed"
  | "cancelled"

export type TeamMemberRole =
  | "lead_lawyer"
  | "associate_lawyer"
  | "legal_assistant"
  | "paralegal"
  | "administrative_staff"
  | "external_consultant"
  | "observer"

export type TeamMessageCreate = {
  case_id?: string | null
  message_type?: TeamMessageType
  subject: string
  content: string
  mentions?: Array<string>
  attachments?: Array<string>
  is_urgent?: boolean
}

export type TeamMessagePublic = {
  case_id: string | null
  message_type?: TeamMessageType
  subject: string
  content: string
  mentions?: Array<string>
  attachments?: Array<string>
  is_pinned?: boolean
  is_urgent?: boolean
  id: string
  sender_id: string
  created_at: string
  updated_at: string
  sender?: UserPublic | null
  case?: LegalCasePublic | null
  replies_count?: number
  is_read?: boolean
}

export type TeamMessagesPublic = {
  data: Array<TeamMessagePublic>
  count: number
  unread_count: number
}

export type TeamMessageType =
  | "general"
  | "case_discussion"
  | "task_assignment"
  | "announcement"
  | "urgent"

export type TeamTaskCreate = {
  title: string
  description?: string | null
  priority?: TaskPriority
  assigned_to?: string | null
  due_date?: string | null
  estimated_hours?: number | null
  tags?: Array<string>
  dependencies?: Array<string>
}

export type TeamTaskPublic = {
  case_id: string
  title: string
  description?: string | null
  status?: TaskStatus
  priority?: TaskPriority
  assigned_to: string | null
  due_date?: string | null
  estimated_hours?: number | null
  actual_hours?: number | null
  tags?: Array<string>
  dependencies?: Array<string>
  id: string
  created_by: string
  created_at: string
  updated_at: string
  completed_at: string | null
  creator?: UserPublic | null
  assignee?: UserPublic | null
  case?: LegalCasePublic | null
  is_overdue?: boolean
  progress_percentage?: number
}

export type TeamTasksPublic = {
  data: Array<TeamTaskPublic>
  count: number
}

export type TeamTaskUpdate = {
  title?: string | null
  description?: string | null
  status?: TaskStatus | null
  priority?: TaskPriority | null
  assigned_to?: string | null
  due_date?: string | null
  estimated_hours?: number | null
  actual_hours?: number | null
  tags?: Array<string> | null
  dependencies?: Array<string> | null
}

export type Token = {
  access_token: string
  token_type?: string
  role?: string | null
}

export type UpdatePassword = {
  current_password: string
  new_password: string
}

export type UserAssignment = {
  user_id: string
  assigned_lawyer_id: string
}

export type UserAssignmentUpdate = {
  assigned_lawyer_id?: string | null
}

export type UserCreate = {
  email: string
  is_active?: boolean
  is_superuser?: boolean
  full_name?: string | null
  role?: UserRole
  /**
   * ID de l'association du barreau
   */
  bar_association_id?: string | null
  /**
   * Spécialisation de l'avocat
   */
  specialization?: string | null
  /**
   * Lawyer assigned to this client/assistant
   */
  assigned_lawyer_id?: string | null
  password: string
}

export type UserNotificationPreferencesPublic = {
  data: Array<NotificationPreferencesPublic>
  count: number
}

export type UserPublic = {
  email: string
  is_active?: boolean
  is_superuser?: boolean
  full_name?: string | null
  role?: UserRole
  /**
   * ID de l'association du barreau
   */
  bar_association_id?: string | null
  /**
   * Spécialisation de l'avocat
   */
  specialization?: string | null
  /**
   * Lawyer assigned to this client/assistant
   */
  assigned_lawyer_id?: string | null
  id: string
}

export type UserRegister = {
  email: string
  password: string
  full_name?: string | null
  role?: UserRole | null
}

export type UserRole = "lawyer" | "client" | "admin" | "assistant"

export type UsersPublic = {
  data: Array<UserPublic>
  count: number
}

export type UserUpdate = {
  email?: string | null
  is_active?: boolean
  is_superuser?: boolean
  full_name?: string | null
  role?: UserRole
  /**
   * ID de l'association du barreau
   */
  bar_association_id?: string | null
  /**
   * Spécialisation de l'avocat
   */
  specialization?: string | null
  /**
   * Lawyer assigned to this client/assistant
   */
  assigned_lawyer_id?: string | null
  password?: string | null
}

export type UserUpdateMe = {
  full_name?: string | null
  email?: string | null
}

export type ValidationError = {
  loc: Array<string | number>
  msg: string
  type: string
}

export type WorkflowTemplateCreate = {
  name: string
  description?: string | null
  case_type: CaseType
  workflow_steps?: Array<{
    [key: string]: unknown
  }>
  automation_rules?: Array<{
    [key: string]: unknown
  }>
  is_active?: boolean
  is_public?: boolean
  tags?: Array<string>
}

export type WorkflowTemplatePublic = {
  name: string
  description?: string | null
  case_type: CaseType
  /**
   * Ordered list of workflow steps
   */
  workflow_steps?: Array<{
    [key: string]: unknown
  }>
  /**
   * Automation rules and triggers
   */
  automation_rules?: Array<{
    [key: string]: unknown
  }>
  is_active?: boolean
  is_public?: boolean
  tags?: Array<string>
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: UserPublic | null
}

export type WorkflowTemplatesPublic = {
  data: Array<WorkflowTemplatePublic>
  count: number
}

export type WorkflowTemplateUpdate = {
  name?: string | null
  description?: string | null
  case_type?: CaseType | null
  workflow_steps?: Array<{
    [key: string]: unknown
  }> | null
  automation_rules?: Array<{
    [key: string]: unknown
  }> | null
  is_active?: boolean | null
  is_public?: boolean | null
  tags?: Array<string> | null
}

export type CaseAnalyticsGetCaseAnalyticsData = {
  caseId: string
  /**
   * Include comparative analytics
   */
  includeComparisons?: boolean
  /**
   * Analysis period in days
   */
  periodDays?: number
}

export type CaseAnalyticsGetCaseAnalyticsResponse = {
  [key: string]: unknown
}

export type CaseDocumentsGetCaseDocumentsData = {
  caseId: string
  category?: DocumentCategory | null
  folderId?: string | null
  limit?: number
  search?: string | null
  skip?: number
  sortBy?: string
  sortOrder?: string
}

export type CaseDocumentsGetCaseDocumentsResponse = CaseDocumentsPublic

export type CaseDocumentsUploadCaseDocumentData = {
  caseId: string
  category?: DocumentCategory
  description?: string | null
  folderId?: string | null
  formData: Body_case_documents_upload_case_document
  isConfidential?: boolean
  isSharedWithClient?: boolean
  tags?: string | null
}

export type CaseDocumentsUploadCaseDocumentResponse = CaseDocumentPublic

export type CaseDocumentsUpdateCaseDocumentData = {
  caseId: string
  documentId: string
  requestBody: CaseDocumentUpdate
}

export type CaseDocumentsUpdateCaseDocumentResponse = CaseDocumentPublic

export type CaseDocumentsDeleteCaseDocumentData = {
  caseId: string
  documentId: string
}

export type CaseDocumentsDeleteCaseDocumentResponse = {
  [key: string]: unknown
}

export type CaseDocumentsDownloadCaseDocumentData = {
  caseId: string
  documentId: string
}

export type CaseDocumentsDownloadCaseDocumentResponse = unknown

export type CaseDocumentsGetDocumentCategoriesResponse = {
  [key: string]: unknown
}

export type CaseDocumentsGetCaseDocumentStatsData = {
  caseId: string
}

export type CaseDocumentsGetCaseDocumentStatsResponse = {
  [key: string]: unknown
}

export type CaseFoldersGetCaseFoldersData = {
  caseId: string
  includeDocuments?: boolean
  parentFolderId?: string | null
}

export type CaseFoldersGetCaseFoldersResponse = CaseFoldersPublic

export type CaseFoldersCreateCaseFolderData = {
  caseId: string
  requestBody: CaseFolderCreate
}

export type CaseFoldersCreateCaseFolderResponse = CaseFolderPublic

export type CaseFoldersUpdateCaseFolderData = {
  caseId: string
  folderId: string
  requestBody: CaseFolderUpdate
}

export type CaseFoldersUpdateCaseFolderResponse = CaseFolderPublic

export type CaseFoldersDeleteCaseFolderData = {
  caseId: string
  folderId: string
  moveContentsTo?: string | null
}

export type CaseFoldersDeleteCaseFolderResponse = {
  [key: string]: unknown
}

export type CaseFoldersInitializeCaseFoldersData = {
  caseId: string
}

export type CaseFoldersInitializeCaseFoldersResponse = CaseFoldersPublic

export type CaseNotesGetCaseNotesData = {
  caseId: string
  category?: NoteCategory | null
  limit?: number
  noteType?: NoteType | null
  search?: string | null
  skip?: number
  sortBy?: string
  sortOrder?: string
}

export type CaseNotesGetCaseNotesResponse = CaseNotesPublic

export type CaseNotesCreateCaseNoteData = {
  caseId: string
  requestBody: CaseNoteCreate
}

export type CaseNotesCreateCaseNoteResponse = CaseNotePublic

export type CaseNotesUpdateCaseNoteData = {
  caseId: string
  noteId: string
  requestBody: CaseNoteUpdate
}

export type CaseNotesUpdateCaseNoteResponse = CaseNotePublic

export type CaseNotesDeleteCaseNoteData = {
  caseId: string
  noteId: string
}

export type CaseNotesDeleteCaseNoteResponse = {
  [key: string]: unknown
}

export type CaseNotesGetNoteRepliesData = {
  caseId: string
  limit?: number
  noteId: string
  skip?: number
}

export type CaseNotesGetNoteRepliesResponse = CaseNotesPublic

export type CaseProgressGetCaseProgressSummaryData = {
  caseId: string
}

export type CaseProgressGetCaseProgressSummaryResponse = CaseProgressSummary

export type CaseProgressGetCaseMilestonesData = {
  assignedTo?: string | null
  caseId: string
  includeCompleted?: boolean
  limit?: number
  milestoneType?: MilestoneType | null
  skip?: number
  status?: MilestoneStatus | null
}

export type CaseProgressGetCaseMilestonesResponse = CaseMilestonesPublic

export type CaseProgressCreateCaseMilestoneData = {
  caseId: string
  requestBody: CaseMilestoneCreate
}

export type CaseProgressCreateCaseMilestoneResponse = CaseMilestonePublic

export type CaseProgressUpdateCaseMilestoneData = {
  caseId: string
  milestoneId: string
  requestBody: CaseMilestoneUpdate
}

export type CaseProgressUpdateCaseMilestoneResponse = CaseMilestonePublic

export type CaseProgressDeleteCaseMilestoneData = {
  caseId: string
  milestoneId: string
}

export type CaseProgressDeleteCaseMilestoneResponse = {
  [key: string]: string
}

export type CaseProgressGetCaseDeadlinesData = {
  assignedTo?: string | null
  caseId: string
  deadlineType?: DeadlineType | null
  includeCompleted?: boolean
  isCritical?: boolean | null
  limit?: number
  skip?: number
  upcomingDays?: number | null
}

export type CaseProgressGetCaseDeadlinesResponse = CaseDeadlinesPublic

export type CaseProgressCreateCaseDeadlineData = {
  caseId: string
  requestBody: CaseDeadlineCreate
}

export type CaseProgressCreateCaseDeadlineResponse = CaseDeadlinePublic

export type CaseProgressUpdateCaseDeadlineData = {
  caseId: string
  deadlineId: string
  requestBody: CaseDeadlineUpdate
}

export type CaseProgressUpdateCaseDeadlineResponse = CaseDeadlinePublic

export type CaseProgressDeleteCaseDeadlineData = {
  caseId: string
  deadlineId: string
}

export type CaseProgressDeleteCaseDeadlineResponse = {
  [key: string]: string
}

export type CaseProgressGetMilestoneTemplatesData = {
  caseType?: CaseType | null
  limit?: number
  skip?: number
}

export type CaseProgressGetMilestoneTemplatesResponse = MilestoneTemplatesPublic

export type CaseProgressCreateMilestoneTemplateData = {
  requestBody: MilestoneTemplateCreate
}

export type CaseProgressCreateMilestoneTemplateResponse =
  MilestoneTemplatePublic

export type CaseProgressCreateMilestonesFromTemplateData = {
  caseId: string
  /**
   * Case type to filter templates
   */
  caseType?: CaseType | null
}

export type CaseProgressCreateMilestonesFromTemplateResponse =
  CaseMilestonesPublic

export type CaseTemplatesGetCaseTemplatesData = {
  caseType?: CaseType | null
  isPublic?: boolean | null
  limit?: number
  search?: string | null
  skip?: number
}

export type CaseTemplatesGetCaseTemplatesResponse = CaseTemplatesPublic

export type CaseTemplatesCreateCaseTemplateData = {
  requestBody: CaseTemplateCreate
}

export type CaseTemplatesCreateCaseTemplateResponse = CaseTemplatePublic

export type CaseTemplatesGetCaseTemplateData = {
  templateId: string
}

export type CaseTemplatesGetCaseTemplateResponse = CaseTemplatePublic

export type CaseTemplatesUpdateCaseTemplateData = {
  requestBody: CaseTemplateUpdate
  templateId: string
}

export type CaseTemplatesUpdateCaseTemplateResponse = CaseTemplatePublic

export type CaseTemplatesDeleteCaseTemplateData = {
  templateId: string
}

export type CaseTemplatesDeleteCaseTemplateResponse = unknown

export type CaseTemplatesApplyCaseTemplateData = {
  caseId: string
  templateId: string
}

export type CaseTemplatesApplyCaseTemplateResponse = unknown

export type CaseTemplatesCreateCaseFromTemplateData = {
  requestBody: {
    [key: string]: unknown
  }
  templateId: string
}

export type CaseTemplatesCreateCaseFromTemplateResponse = {
  [key: string]: unknown
}

export type CaseTemplatesGetDocumentTemplatesData = {
  caseType?: CaseType | null
  documentType?: string | null
  isPublic?: boolean | null
  limit?: number
  search?: string | null
  skip?: number
}

export type CaseTemplatesGetDocumentTemplatesResponse = DocumentTemplatesPublic

export type CaseTemplatesCreateDocumentTemplateData = {
  requestBody: DocumentTemplateCreate
}

export type CaseTemplatesCreateDocumentTemplateResponse = DocumentTemplatePublic

export type CaseTemplatesGetDocumentTemplateData = {
  templateId: string
}

export type CaseTemplatesGetDocumentTemplateResponse = DocumentTemplatePublic

export type CaseTemplatesUpdateDocumentTemplateData = {
  requestBody: DocumentTemplateUpdate
  templateId: string
}

export type CaseTemplatesUpdateDocumentTemplateResponse = DocumentTemplatePublic

export type CaseTemplatesDeleteDocumentTemplateData = {
  templateId: string
}

export type CaseTemplatesDeleteDocumentTemplateResponse = unknown

export type CaseTemplatesGenerateDocumentFromTemplateData = {
  requestBody: {
    [key: string]: unknown
  }
  templateId: string
}

export type CaseTemplatesGenerateDocumentFromTemplateResponse = unknown

export type CaseTemplatesGetWorkflowTemplatesData = {
  caseType?: CaseType | null
  isPublic?: boolean | null
  limit?: number
  search?: string | null
  skip?: number
}

export type CaseTemplatesGetWorkflowTemplatesResponse = WorkflowTemplatesPublic

export type CaseTemplatesCreateWorkflowTemplateData = {
  requestBody: WorkflowTemplateCreate
}

export type CaseTemplatesCreateWorkflowTemplateResponse = WorkflowTemplatePublic

export type CaseTemplatesGetWorkflowTemplateData = {
  templateId: string
}

export type CaseTemplatesGetWorkflowTemplateResponse = WorkflowTemplatePublic

export type CaseTemplatesUpdateWorkflowTemplateData = {
  requestBody: WorkflowTemplateUpdate
  templateId: string
}

export type CaseTemplatesUpdateWorkflowTemplateResponse = WorkflowTemplatePublic

export type CaseTemplatesDeleteWorkflowTemplateData = {
  templateId: string
}

export type CaseTemplatesDeleteWorkflowTemplateResponse = unknown

export type ClientPortalGetClientDashboardResponse = ClientDashboardStats

export type ClientPortalGetClientCasesData = {
  caseType?: CaseType | null
  limit?: number
  skip?: number
  status?: string | null
}

export type ClientPortalGetClientCasesResponse = ClientCasesPublic

export type ClientPortalGetClientCaseDetailData = {
  caseId: string
}

export type ClientPortalGetClientCaseDetailResponse = ClientCaseView

export type ClientPortalGetClientNotificationsData = {
  isRead?: boolean | null
  limit?: number
  notificationType?: NotificationType | null
  priority?: NotificationPriority | null
  skip?: number
}

export type ClientPortalGetClientNotificationsResponse = NotificationsPublic

export type ClientPortalMarkNotificationReadData = {
  notificationId: string
}

export type ClientPortalMarkNotificationReadResponse = NotificationPublic

export type ClientPortalMarkAllNotificationsReadResponse = unknown

export type ClientPortalGetClientMessagesData = {
  caseId?: string | null
  limit?: number
  search?: string | null
  skip?: number
  status?: MessageStatus | null
}

export type ClientPortalGetClientMessagesResponse = ClientMessagesPublic

export type ClientPortalSendClientMessageData = {
  requestBody: ClientMessageCreate
}

export type ClientPortalSendClientMessageResponse = ClientMessagePublic

export type ClientPortalGetClientMessageData = {
  messageId: string
}

export type ClientPortalGetClientMessageResponse = ClientMessagePublic

export type ClientPortalGetMessageRepliesData = {
  limit?: number
  messageId: string
  skip?: number
}

export type ClientPortalGetMessageRepliesResponse = ClientMessagesPublic

export type ClientPortalGetClientCaseDocumentsData = {
  caseId: string
  category?: DocumentCategory | null
  limit?: number
  search?: string | null
  skip?: number
}

export type ClientPortalGetClientCaseDocumentsResponse = unknown

export type ClientPortalDownloadClientDocumentData = {
  documentId: string
}

export type ClientPortalDownloadClientDocumentResponse = unknown

export type ClientPortalGetCaseTypesForClientResponse = unknown

export type ClientPortalGetCaseStatusesForClientResponse = unknown

export type ClientPortalGetDocumentCategoriesForClientResponse = unknown

export type DocumentCaseLinksGetDocumentCaseLinksData = {
  documentId: string
  limit?: number
  skip?: number
}

export type DocumentCaseLinksGetDocumentCaseLinksResponse =
  DocumentCaseLinksPublic

export type DocumentCaseLinksCreateDocumentCaseLinkData = {
  documentId: string
  requestBody: DocumentCaseLinkCreate
}

export type DocumentCaseLinksCreateDocumentCaseLinkResponse =
  DocumentCaseLinkPublic

export type DocumentCaseLinksUpdateDocumentCaseLinkData = {
  documentId: string
  linkId: string
  requestBody: DocumentCaseLinkUpdate
}

export type DocumentCaseLinksUpdateDocumentCaseLinkResponse =
  DocumentCaseLinkPublic

export type DocumentCaseLinksDeleteDocumentCaseLinkData = {
  documentId: string
  linkId: string
}

export type DocumentCaseLinksDeleteDocumentCaseLinkResponse = {
  [key: string]: string
}

export type DocumentCaseLinksGetCaseLinkedDocumentsData = {
  caseId: string
  limit?: number
  linkType?: string | null
  skip?: number
}

export type DocumentCaseLinksGetCaseLinkedDocumentsResponse = {
  [key: string]: unknown
}

export type DocumentsUploadDocumentData = {
  formData: Body_documents_upload_document
}

export type DocumentsUploadDocumentResponse = unknown

export type DocumentsDocumentsHealthCheckResponse = Message

export type InternalCreateUserData = {
  requestBody: PrivateUserCreate
}

export type InternalCreateUserResponse = UserPublic

export type ItemsReadItemsData = {
  limit?: number
  skip?: number
}

export type ItemsReadItemsResponse = ItemsPublic

export type ItemsCreateItemData = {
  requestBody: ItemCreate
}

export type ItemsCreateItemResponse = ItemPublic

export type ItemsReadItemData = {
  id: string
}

export type ItemsReadItemResponse = ItemPublic

export type ItemsUpdateItemData = {
  id: string
  requestBody: ItemUpdate
}

export type ItemsUpdateItemResponse = ItemPublic

export type ItemsDeleteItemData = {
  id: string
}

export type ItemsDeleteItemResponse = Message

export type LegalCasesCreateLegalCaseData = {
  requestBody: LegalCaseCreate
}

export type LegalCasesCreateLegalCaseResponse = LegalCasePublic

export type LegalCasesReadLegalCasesData = {
  /**
   * Filter by case type
   */
  caseType?: CaseType | null
  /**
   * Filter cases from this date
   */
  dateFrom?: string | null
  /**
   * Filter cases to this date
   */
  dateTo?: string | null
  /**
   * Filter by assigned lawyer
   */
  lawyerId?: string | null
  /**
   * Number of records to return
   */
  limit?: number
  /**
   * Filter by case priority
   */
  priority?: string | null
  /**
   * Search in title and client name
   */
  search?: string | null
  /**
   * Number of records to skip
   */
  skip?: number
  /**
   * Sort by field (title, client_name, opening_date, case_type, status, priority)
   */
  sortBy?: string | null
  /**
   * Sort order (asc, desc)
   */
  sortOrder?: string | null
  /**
   * Filter by case status
   */
  status?: string | null
}

export type LegalCasesReadLegalCasesResponse = LegalCasesPublic

export type LegalCasesGetAvailableLawyersResponse = Array<UserPublic>

export type LegalCasesReadLegalCaseData = {
  legalCaseId: string
}

export type LegalCasesReadLegalCaseResponse = LegalCaseDetail

export type LegalCasesUpdateLegalCaseData = {
  legalCaseId: string
  requestBody: LegalCaseUpdate
}

export type LegalCasesUpdateLegalCaseResponse = LegalCasePublic

export type LegalCasesPartialUpdateLegalCaseData = {
  legalCaseId: string
  requestBody: LegalCaseUpdate
}

export type LegalCasesPartialUpdateLegalCaseResponse = LegalCasePublic

export type LegalCasesDeleteLegalCaseData = {
  legalCaseId: string
}

export type LegalCasesDeleteLegalCaseResponse = {
  [key: string]: unknown
}

export type LegalCasesBulkUpdateCaseStatusData = {
  requestBody: BulkStatusUpdateRequest
}

export type LegalCasesBulkUpdateCaseStatusResponse = BulkUpdateResult

export type LegalCasesUpdateCaseStatusData = {
  legalCaseId: string
  requestBody: StatusChangeRequest
}

export type LegalCasesUpdateCaseStatusResponse = LegalCasePublic

export type LegalCasesReadCaseActivitiesData = {
  /**
   * Filter by activity type
   */
  activityType?: string | null
  /**
   * Filter activities until this date
   */
  endDate?: string | null
  legalCaseId: string
  /**
   * Number of records to return
   */
  limit?: number
  /**
   * Search in activity descriptions
   */
  search?: string | null
  /**
   * Number of records to skip
   */
  skip?: number
  /**
   * Filter activities from this date
   */
  startDate?: string | null
}

export type LegalCasesReadCaseActivitiesResponse = CaseActivitiesPublic

export type LegalCasesReadCaseStatusHistoryData = {
  legalCaseId: string
  /**
   * Number of records to return
   */
  limit?: number
  /**
   * Number of records to skip
   */
  skip?: number
}

export type LegalCasesReadCaseStatusHistoryResponse = CaseStatusHistoriesPublic

export type LegalCasesUpdateCasePriorityData = {
  legalCaseId: string
  priority: CasePriority
}

export type LegalCasesUpdateCasePriorityResponse = LegalCasePublic

export type LegalCasesGetStatusOptionsResponse = Array<string>

export type LegalCasesGetPriorityOptionsResponse = Array<string>

export type LegalCasesGetValidStatusTransitionsData = {
  legalCaseId: string
}

export type LegalCasesGetValidStatusTransitionsResponse = Array<string>

export type LoginLoginAccessTokenData = {
  formData: Body_login_login_access_token
}

export type LoginLoginAccessTokenResponse = Token

export type LoginTestTokenResponse = UserPublic

export type LoginRecoverPasswordData = {
  email: string
}

export type LoginRecoverPasswordResponse = Message

export type LoginResetPasswordData = {
  requestBody: NewPassword
}

export type LoginResetPasswordResponse = Message

export type LoginRecoverPasswordHtmlContentData = {
  email: string
}

export type LoginRecoverPasswordHtmlContentResponse = string

export type NotificationsGetNotificationsData = {
  isRead?: boolean | null
  limit?: number
  notificationType?: NotificationType | null
  priority?: NotificationPriority | null
  skip?: number
}

export type NotificationsGetNotificationsResponse = NotificationsPublic

export type NotificationsUpdateNotificationData = {
  notificationId: string
  requestBody: NotificationUpdate
}

export type NotificationsUpdateNotificationResponse = NotificationPublic

export type NotificationsMarkAllNotificationsReadResponse = unknown

export type NotificationsGetNotificationPreferencesResponse =
  UserNotificationPreferencesPublic

export type NotificationsCreateNotificationPreferenceData = {
  requestBody: NotificationPreferencesCreate
}

export type NotificationsCreateNotificationPreferenceResponse =
  NotificationPreferencesPublic

export type NotificationsUpdateNotificationPreferenceData = {
  preferenceId: string
  requestBody: NotificationPreferencesUpdate
}

export type NotificationsUpdateNotificationPreferenceResponse =
  NotificationPreferencesPublic

export type NotificationsTrackEmailOpenData = {
  deliveryId: string
}

export type NotificationsTrackEmailOpenResponse = unknown

export type NotificationsTrackEmailClickData = {
  deliveryId: string
  url: string
}

export type NotificationsTrackEmailClickResponse = unknown

export type NotificationsGetNotificationTemplatesData = {
  channel?: NotificationChannel | null
  isActive?: boolean | null
  limit?: number
  notificationType?: NotificationType | null
  skip?: number
}

export type NotificationsGetNotificationTemplatesResponse =
  NotificationTemplatesPublic

export type ProtectedLawyerRouteResponse = unknown

export type ProtectedAdminOnlyRouteResponse = unknown

export type ProtectedClientOnlyRouteResponse = unknown

export type ProtectedAssistantOnlyRouteResponse = unknown

export type ReportsGetDashboardReportData = {
  /**
   * End date for filtering
   */
  endDate?: string | null
  /**
   * Include trend calculations
   */
  includeTrends?: boolean
  /**
   * Start date for filtering
   */
  startDate?: string | null
}

export type ReportsGetDashboardReportResponse = {
  [key: string]: unknown
}

export type TeamCollaborationGetCaseTeamMembersData = {
  caseId: string
  isActive?: boolean | null
  limit?: number
  role?: TeamMemberRole | null
  skip?: number
}

export type TeamCollaborationGetCaseTeamMembersResponse = CaseTeamMembersPublic

export type TeamCollaborationAddTeamMemberData = {
  caseId: string
  requestBody: CaseTeamMemberCreate
}

export type TeamCollaborationAddTeamMemberResponse = CaseTeamMemberPublic

export type TeamCollaborationUpdateTeamMemberData = {
  caseId: string
  memberId: string
  requestBody: CaseTeamMemberUpdate
}

export type TeamCollaborationUpdateTeamMemberResponse = CaseTeamMemberPublic

export type TeamCollaborationRemoveTeamMemberData = {
  caseId: string
  memberId: string
}

export type TeamCollaborationRemoveTeamMemberResponse = unknown

export type TeamCollaborationGetTeamMessagesData = {
  caseId: string
  isPinned?: boolean | null
  limit?: number
  messageType?: TeamMessageType | null
  search?: string | null
  skip?: number
}

export type TeamCollaborationGetTeamMessagesResponse = TeamMessagesPublic

export type TeamCollaborationSendTeamMessageData = {
  caseId: string
  requestBody: TeamMessageCreate
}

export type TeamCollaborationSendTeamMessageResponse = TeamMessagePublic

export type TeamTasksGetCaseTasksData = {
  assignedTo?: string | null
  caseId: string
  limit?: number
  priority?: TaskPriority | null
  search?: string | null
  skip?: number
  status?: TaskStatus | null
}

export type TeamTasksGetCaseTasksResponse = TeamTasksPublic

export type TeamTasksCreateTaskData = {
  caseId: string
  requestBody: TeamTaskCreate
}

export type TeamTasksCreateTaskResponse = TeamTaskPublic

export type TeamTasksGetTaskData = {
  caseId: string
  taskId: string
}

export type TeamTasksGetTaskResponse = TeamTaskPublic

export type TeamTasksUpdateTaskData = {
  caseId: string
  requestBody: TeamTaskUpdate
  taskId: string
}

export type TeamTasksUpdateTaskResponse = TeamTaskPublic

export type TeamTasksDeleteTaskData = {
  caseId: string
  taskId: string
}

export type TeamTasksDeleteTaskResponse = unknown

export type UsersReadUsersData = {
  limit?: number
  skip?: number
}

export type UsersReadUsersResponse = UsersPublic

export type UsersCreateUserData = {
  requestBody: UserCreate
}

export type UsersCreateUserResponse = UserPublic

export type UsersReadUserMeResponse = UserPublic

export type UsersDeleteUserMeResponse = Message

export type UsersUpdateUserMeData = {
  requestBody: UserUpdateMe
}

export type UsersUpdateUserMeResponse = UserPublic

export type UsersUpdatePasswordMeData = {
  requestBody: UpdatePassword
}

export type UsersUpdatePasswordMeResponse = Message

export type UsersRegisterUserData = {
  requestBody: UserRegister
}

export type UsersRegisterUserResponse = UserPublic

export type UsersAssignUserToLawyerData = {
  requestBody: UserAssignment
}

export type UsersAssignUserToLawyerResponse = UserPublic

export type UsersGetUnassignedUsersResponse = UsersPublic

export type UsersGetAllLawyersResponse = UsersPublic

export type UsersGetLawyerClientsData = {
  lawyerId: string
}

export type UsersGetLawyerClientsResponse = UsersPublic

export type UsersGetLawyerAssistantsData = {
  lawyerId: string
}

export type UsersGetLawyerAssistantsResponse = UsersPublic

export type UsersGetLawyerAssignedUsersData = {
  lawyerId: string
}

export type UsersGetLawyerAssignedUsersResponse = UsersPublic

export type UsersReadUserByIdData = {
  userId: string
}

export type UsersReadUserByIdResponse = UserPublic

export type UsersUpdateUserData = {
  requestBody: UserUpdate
  userId: string
}

export type UsersUpdateUserResponse = UserPublic

export type UsersDeleteUserData = {
  userId: string
}

export type UsersDeleteUserResponse = Message

export type UsersUpdateUserAssignmentData = {
  requestBody: UserAssignmentUpdate
  userId: string
}

export type UsersUpdateUserAssignmentResponse = UserPublic

export type UtilsTestEmailData = {
  emailTo: string
}

export type UtilsTestEmailResponse = Message

export type UtilsHealthCheckResponse = boolean
