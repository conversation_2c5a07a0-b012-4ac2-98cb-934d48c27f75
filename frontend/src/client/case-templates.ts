import axios from "axios"

export interface CaseTemplateBase {
  name: string
  description?: string
  case_type: string
  template_data: Record<string, any>
  is_active: boolean
  is_public: boolean
  tags: string[]
}

export interface CaseTemplate extends CaseTemplateBase {
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string
    email: string
  }
}

export interface CaseTemplateCreate {
  name: string
  description?: string
  case_type: string
  template_data?: Record<string, any>
  is_active?: boolean
  is_public?: boolean
  tags?: string[]
}

export interface CaseTemplateUpdate {
  name?: string
  description?: string
  case_type?: string
  template_data?: Record<string, any>
  is_active?: boolean
  is_public?: boolean
  tags?: string[]
}

export interface DocumentTemplateBase {
  name: string
  description?: string
  case_type?: string
  document_type: string
  template_content: string
  placeholders: string[]
  is_active: boolean
  is_public: boolean
  tags: string[]
}

export interface DocumentTemplate extends DocumentTemplateBase {
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string
    email: string
  }
}

export interface DocumentTemplateCreate {
  name: string
  description?: string
  case_type?: string
  document_type: string
  template_content: string
  placeholders?: string[]
  is_active?: boolean
  is_public?: boolean
  tags?: string[]
}

export interface DocumentTemplateUpdate {
  name?: string
  description?: string
  case_type?: string
  document_type?: string
  template_content?: string
  placeholders?: string[]
  is_active?: boolean
  is_public?: boolean
  tags?: string[]
}

export interface WorkflowTemplateBase {
  name: string
  description?: string
  case_type: string
  workflow_steps: Array<Record<string, any>>
  automation_rules: Array<Record<string, any>>
  is_active: boolean
  is_public: boolean
  tags: string[]
}

export interface WorkflowTemplate extends WorkflowTemplateBase {
  id: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string
    email: string
  }
}

export interface WorkflowTemplateCreate {
  name: string
  description?: string
  case_type: string
  workflow_steps?: Array<Record<string, any>>
  automation_rules?: Array<Record<string, any>>
  is_active?: boolean
  is_public?: boolean
  tags?: string[]
}

export interface WorkflowTemplateUpdate {
  name?: string
  description?: string
  case_type?: string
  workflow_steps?: Array<Record<string, any>>
  automation_rules?: Array<Record<string, any>>
  is_active?: boolean
  is_public?: boolean
  tags?: string[]
}

export interface TemplateApplicationResult {
  message: string
  applied_items: string[]
  template_id: string
  case_id: string
}

export interface CaseCreationResult {
  message: string
  case_id: string
  case_title: string
  template_applied: TemplateApplicationResult
}

export interface DocumentGenerationResult {
  template_id: string
  template_name: string
  generated_content: string
  placeholders_used: string[]
  generated_at: string
}

export const CaseTemplatesService = {
  // Case Templates
  getCaseTemplates: async (params?: {
    skip?: number
    limit?: number
    case_type?: string
    is_public?: boolean
    search?: string
  }): Promise<{ data: CaseTemplate[]; count: number }> => {
    const response = await axios.get("/api/v1/templates/case-templates", {
      params,
    })
    return response.data
  },

  createCaseTemplate: async (
    template: CaseTemplateCreate,
  ): Promise<CaseTemplate> => {
    const response = await axios.post(
      "/api/v1/templates/case-templates",
      template,
    )
    return response.data
  },

  getCaseTemplate: async (templateId: string): Promise<CaseTemplate> => {
    const response = await axios.get(
      `/api/v1/templates/case-templates/${templateId}`,
    )
    return response.data
  },

  updateCaseTemplate: async (
    templateId: string,
    template: CaseTemplateUpdate,
  ): Promise<CaseTemplate> => {
    const response = await axios.patch(
      `/api/v1/templates/case-templates/${templateId}`,
      template,
    )
    return response.data
  },

  deleteCaseTemplate: async (templateId: string): Promise<void> => {
    await axios.delete(`/api/v1/templates/case-templates/${templateId}`)
  },

  applyCaseTemplate: async (
    templateId: string,
    caseId: string,
  ): Promise<TemplateApplicationResult> => {
    const response = await axios.post(
      `/api/v1/templates/case-templates/${templateId}/apply/${caseId}`,
    )
    return response.data
  },

  createCaseFromTemplate: async (
    templateId: string,
    caseData: Record<string, any>,
  ): Promise<CaseCreationResult> => {
    const response = await axios.post(
      `/api/v1/templates/case-templates/${templateId}/create-case`,
      caseData,
    )
    return response.data
  },

  // Document Templates
  getDocumentTemplates: async (params?: {
    skip?: number
    limit?: number
    case_type?: string
    document_type?: string
    is_public?: boolean
    search?: string
  }): Promise<{ data: DocumentTemplate[]; count: number }> => {
    const response = await axios.get("/api/v1/templates/document-templates", {
      params,
    })
    return response.data
  },

  createDocumentTemplate: async (
    template: DocumentTemplateCreate,
  ): Promise<DocumentTemplate> => {
    const response = await axios.post(
      "/api/v1/templates/document-templates",
      template,
    )
    return response.data
  },

  getDocumentTemplate: async (
    templateId: string,
  ): Promise<DocumentTemplate> => {
    const response = await axios.get(
      `/api/v1/templates/document-templates/${templateId}`,
    )
    return response.data
  },

  updateDocumentTemplate: async (
    templateId: string,
    template: DocumentTemplateUpdate,
  ): Promise<DocumentTemplate> => {
    const response = await axios.patch(
      `/api/v1/templates/document-templates/${templateId}`,
      template,
    )
    return response.data
  },

  deleteDocumentTemplate: async (templateId: string): Promise<void> => {
    await axios.delete(`/api/v1/templates/document-templates/${templateId}`)
  },

  generateDocumentFromTemplate: async (
    templateId: string,
    placeholderData: Record<string, any>,
  ): Promise<DocumentGenerationResult> => {
    const response = await axios.post(
      `/api/v1/templates/document-templates/${templateId}/generate`,
      placeholderData,
    )
    return response.data
  },

  // Workflow Templates
  getWorkflowTemplates: async (params?: {
    skip?: number
    limit?: number
    case_type?: string
    is_public?: boolean
    search?: string
  }): Promise<{ data: WorkflowTemplate[]; count: number }> => {
    const response = await axios.get("/api/v1/templates/workflow-templates", {
      params,
    })
    return response.data
  },

  createWorkflowTemplate: async (
    template: WorkflowTemplateCreate,
  ): Promise<WorkflowTemplate> => {
    const response = await axios.post(
      "/api/v1/templates/workflow-templates",
      template,
    )
    return response.data
  },

  getWorkflowTemplate: async (
    templateId: string,
  ): Promise<WorkflowTemplate> => {
    const response = await axios.get(
      `/api/v1/templates/workflow-templates/${templateId}`,
    )
    return response.data
  },

  updateWorkflowTemplate: async (
    templateId: string,
    template: WorkflowTemplateUpdate,
  ): Promise<WorkflowTemplate> => {
    const response = await axios.patch(
      `/api/v1/templates/workflow-templates/${templateId}`,
      template,
    )
    return response.data
  },

  deleteWorkflowTemplate: async (templateId: string): Promise<void> => {
    await axios.delete(`/api/v1/templates/workflow-templates/${templateId}`)
  },
}

// Case type configurations
export const CASE_TYPE_CONFIG = {
  civil: {
    label: "Civil",
    color: "blue",
    icon: "⚖️",
    description: "Civil litigation and disputes",
  },
  criminal: {
    label: "Criminal",
    color: "red",
    icon: "🚨",
    description: "Criminal defense and prosecution",
  },
  family: {
    label: "Family",
    color: "purple",
    icon: "👨‍👩‍👧‍👦",
    description: "Family law and domestic relations",
  },
  corporate: {
    label: "Corporate",
    color: "green",
    icon: "🏢",
    description: "Corporate law and business matters",
  },
  immigration: {
    label: "Immigration",
    color: "orange",
    icon: "🌍",
    description: "Immigration and citizenship matters",
  },
  personal_injury: {
    label: "Personal Injury",
    color: "yellow",
    icon: "🩹",
    description: "Personal injury and tort claims",
  },
  real_estate: {
    label: "Real Estate",
    color: "teal",
    icon: "🏠",
    description: "Real estate transactions and disputes",
  },
  intellectual_property: {
    label: "Intellectual Property",
    color: "pink",
    icon: "💡",
    description: "Patents, trademarks, and copyrights",
  },
  employment: {
    label: "Employment",
    color: "cyan",
    icon: "💼",
    description: "Employment law and workplace issues",
  },
  contract_dispute: {
    label: "Contract Dispute",
    color: "indigo",
    icon: "📄",
    description: "Contract disputes and negotiations",
  },
  other: {
    label: "Other",
    color: "gray",
    icon: "📋",
    description: "Other legal matters",
  },
}

// Document type configurations
export const DOCUMENT_TYPE_CONFIG = {
  contract: {
    label: "Contract",
    color: "blue",
    icon: "📄",
    description: "Legal contracts and agreements",
  },
  letter: {
    label: "Letter",
    color: "green",
    icon: "✉️",
    description: "Legal correspondence and letters",
  },
  motion: {
    label: "Motion",
    color: "purple",
    icon: "⚖️",
    description: "Court motions and pleadings",
  },
  brief: {
    label: "Brief",
    color: "orange",
    icon: "📝",
    description: "Legal briefs and memoranda",
  },
  notice: {
    label: "Notice",
    color: "yellow",
    icon: "📢",
    description: "Legal notices and notifications",
  },
  agreement: {
    label: "Agreement",
    color: "teal",
    icon: "🤝",
    description: "Legal agreements and settlements",
  },
  petition: {
    label: "Petition",
    color: "pink",
    icon: "📋",
    description: "Petitions and applications",
  },
  other: {
    label: "Other",
    color: "gray",
    icon: "📄",
    description: "Other document types",
  },
}

// Utility functions
export const getCaseTypeInfo = (caseType: string) => {
  return (
    CASE_TYPE_CONFIG[caseType as keyof typeof CASE_TYPE_CONFIG] ||
    CASE_TYPE_CONFIG.other
  )
}

export const getDocumentTypeInfo = (documentType: string) => {
  return (
    DOCUMENT_TYPE_CONFIG[documentType as keyof typeof DOCUMENT_TYPE_CONFIG] ||
    DOCUMENT_TYPE_CONFIG.other
  )
}

export const formatCaseType = (caseType: string): string => {
  return getCaseTypeInfo(caseType).label
}

export const formatDocumentType = (documentType: string): string => {
  return getDocumentTypeInfo(documentType).label
}

export const extractPlaceholders = (content: string): string[] => {
  const placeholderRegex = /\{\{([^}]+)\}\}/g
  const placeholders: string[] = []
  let match

  while ((match = placeholderRegex.exec(content)) !== null) {
    if (!placeholders.includes(match[1])) {
      placeholders.push(match[1])
    }
  }

  return placeholders
}

export const validateTemplateContent = (
  content: string,
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check for unmatched braces
  const openBraces = (content.match(/\{\{/g) || []).length
  const closeBraces = (content.match(/\}\}/g) || []).length

  if (openBraces !== closeBraces) {
    errors.push("Unmatched placeholder braces")
  }

  // Check for empty placeholders
  if (content.includes("{{}}")) {
    errors.push("Empty placeholders found")
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}
