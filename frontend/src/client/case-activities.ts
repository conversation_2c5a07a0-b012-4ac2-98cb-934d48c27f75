import axios from "axios"

export interface CaseActivity {
  id: string
  case_id: string
  activity_type:
    | "case_created"
    | "case_updated"
    | "status_changed"
    | "priority_changed"
    | "document_uploaded"
    | "document_downloaded"
    | "document_deleted"
    | "note_added"
    | "note_updated"
    | "note_deleted"
    | "user_assigned"
  description: string
  user_id: string
  activity_metadata?: Record<string, any> | null
  created_at: string
  user?: {
    id: string
    email: string
    full_name: string
    role: string
  } | null
}

export interface CaseActivitiesResponse {
  data: CaseActivity[]
  count: number
}

export interface CaseActivitiesParams {
  skip?: number
  limit?: number
  activity_type?: string
  search?: string
  start_date?: string
  end_date?: string
}

export const CaseActivitiesService = {
  // Get activities for a case
  getCaseActivities: async (
    caseId: string,
    params?: CaseActivitiesParams,
  ): Promise<CaseActivitiesResponse> => {
    const response = await axios.get(
      `/api/v1/legal-cases/${caseId}/activities`,
      { params },
    )
    return response.data
  },
}

// Activity type configurations
export const ACTIVITY_TYPE_CONFIG = {
  case_created: {
    label: "Case Created",
    description: "Case was created",
    color: "green",
    icon: "📋",
  },
  case_updated: {
    label: "Case Updated",
    description: "Case information was modified",
    color: "blue",
    icon: "✏️",
  },
  status_changed: {
    label: "Status Changed",
    description: "Case status was updated",
    color: "purple",
    icon: "🔄",
  },
  priority_changed: {
    label: "Priority Changed",
    description: "Case priority was modified",
    color: "orange",
    icon: "⚡",
  },
  document_uploaded: {
    label: "Document Uploaded",
    description: "New document was added",
    color: "teal",
    icon: "📄",
  },
  document_downloaded: {
    label: "Document Downloaded",
    description: "Document was downloaded",
    color: "cyan",
    icon: "⬇️",
  },
  document_deleted: {
    label: "Document Deleted",
    description: "Document was removed",
    color: "red",
    icon: "🗑️",
  },
  note_added: {
    label: "Note Added",
    description: "New note was created",
    color: "yellow",
    icon: "📝",
  },
  note_updated: {
    label: "Note Updated",
    description: "Note was modified",
    color: "yellow",
    icon: "✏️",
  },
  note_deleted: {
    label: "Note Deleted",
    description: "Note was removed",
    color: "red",
    icon: "🗑️",
  },
  user_assigned: {
    label: "User Assigned",
    description: "User was assigned to case",
    color: "pink",
    icon: "👤",
  },
}

// Utility functions
export const getActivityTypeInfo = (activityType: string) => {
  return (
    ACTIVITY_TYPE_CONFIG[activityType as keyof typeof ACTIVITY_TYPE_CONFIG] || {
      label: "Unknown Activity",
      description: "Unknown activity type",
      color: "gray",
      icon: "❓",
    }
  )
}

export const formatActivityDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)
      return diffInMinutes <= 1 ? "Just now" : `${diffInMinutes} minutes ago`
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours)
      return `${hours} hour${hours !== 1 ? "s" : ""} ago`
    } else if (diffInHours < 24 * 7) {
      const days = Math.floor(diffInHours / 24)
      return `${days} day${days !== 1 ? "s" : ""} ago`
    } else {
      return date.toLocaleDateString()
    }
  } catch {
    return "Unknown time"
  }
}

export const groupActivitiesByDate = (
  activities: CaseActivity[],
): Record<string, CaseActivity[]> => {
  const groups: Record<string, CaseActivity[]> = {}

  activities.forEach((activity) => {
    try {
      const date = new Date(activity.created_at)
      const dateKey = date.toDateString()

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(activity)
    } catch {
      // Handle invalid dates
      if (!groups["Invalid Date"]) {
        groups["Invalid Date"] = []
      }
      groups["Invalid Date"].push(activity)
    }
  })

  return groups
}

export const getActivityMetadataDisplay = (
  activity: CaseActivity,
): string[] => {
  const metadata = activity.activity_metadata
  if (!metadata) return []

  const details: string[] = []

  switch (activity.activity_type) {
    case "status_changed":
      if (metadata.old_status && metadata.new_status) {
        details.push(`From: ${metadata.old_status}`)
        details.push(`To: ${metadata.new_status}`)
      }
      if (metadata.notes) {
        details.push(`Notes: ${metadata.notes}`)
      }
      break

    case "priority_changed":
      if (metadata.old_priority && metadata.new_priority) {
        details.push(`From: ${metadata.old_priority}`)
        details.push(`To: ${metadata.new_priority}`)
      }
      break

    case "document_uploaded":
    case "document_downloaded":
    case "document_deleted":
      if (metadata.filename) {
        details.push(`File: ${metadata.filename}`)
      }
      if (metadata.category) {
        details.push(`Category: ${metadata.category}`)
      }
      if (metadata.file_size) {
        details.push(`Size: ${formatFileSize(metadata.file_size)}`)
      }
      break

    case "note_added":
    case "note_updated":
    case "note_deleted":
      if (metadata.note_type) {
        details.push(`Type: ${metadata.note_type}`)
      }
      if (metadata.category) {
        details.push(`Category: ${metadata.category}`)
      }
      break

    default:
      // For other activity types, show all metadata as key-value pairs
      Object.entries(metadata).forEach(([key, value]) => {
        if (value && typeof value === "string") {
          details.push(`${key}: ${value}`)
        }
      })
  }

  return details
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes"

  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

export const filterActivitiesByDateRange = (
  activities: CaseActivity[],
  startDate?: string,
  endDate?: string,
): CaseActivity[] => {
  if (!startDate && !endDate) return activities

  return activities.filter((activity) => {
    try {
      const activityDate = new Date(activity.created_at)

      if (startDate) {
        const start = new Date(startDate)
        if (activityDate < start) return false
      }

      if (endDate) {
        const end = new Date(endDate)
        end.setHours(23, 59, 59, 999) // Include the entire end date
        if (activityDate > end) return false
      }

      return true
    } catch {
      return false
    }
  })
}

export const getActivityStats = (activities: CaseActivity[]) => {
  const stats = {
    total: activities.length,
    byType: {} as Record<string, number>,
    byUser: {} as Record<string, number>,
    recentCount: 0, // Last 24 hours
  }

  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

  activities.forEach((activity) => {
    // Count by type
    stats.byType[activity.activity_type] =
      (stats.byType[activity.activity_type] || 0) + 1

    // Count by user
    const userName = activity.user?.full_name || "Unknown User"
    stats.byUser[userName] = (stats.byUser[userName] || 0) + 1

    // Count recent activities
    try {
      const activityDate = new Date(activity.created_at)
      if (activityDate > oneDayAgo) {
        stats.recentCount++
      }
    } catch {
      // Ignore invalid dates
    }
  })

  return stats
}
