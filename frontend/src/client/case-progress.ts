import axios from "axios"

export interface CaseMilestone {
  id: string
  case_id: string
  title: string
  description?: string
  milestone_type: string
  status: string
  target_date?: string
  completion_date?: string
  order_index: number
  is_required: boolean
  assigned_to?: string
  progress_percentage: number
  notes?: string
  created_at: string
  updated_at: string
  created_by: string
  assigned_user?: {
    id: string
    email: string
    full_name: string
    role: string
  }
  creator?: {
    id: string
    email: string
    full_name: string
    role: string
  }
}

export interface CaseDeadline {
  id: string
  case_id: string
  title: string
  description?: string
  deadline_type: string
  deadline_date: string
  is_critical: boolean
  reminder_days: number[]
  assigned_to?: string
  is_completed: boolean
  completion_date?: string
  notes?: string
  created_at: string
  updated_at: string
  created_by: string
  assigned_user?: {
    id: string
    email: string
    full_name: string
    role: string
  }
  creator?: {
    id: string
    email: string
    full_name: string
    role: string
  }
}

export interface CaseProgressSummary {
  case_id: string
  total_milestones: number
  completed_milestones: number
  progress_percentage: number
  overdue_milestones: number
  upcoming_deadlines: number
  critical_deadlines: number
  next_milestone?: CaseMilestone
  next_deadline?: CaseDeadline
}

export interface MilestoneTemplate {
  id: string
  case_type: string
  title: string
  description?: string
  milestone_type: string
  order_index: number
  is_required: boolean
  estimated_days?: number
  notes?: string
  created_at: string
  created_by: string
  is_active: boolean
  creator?: {
    id: string
    email: string
    full_name: string
    role: string
  }
}

export interface CaseMilestoneCreate {
  title: string
  description?: string
  milestone_type?: string
  target_date?: string
  order_index?: number
  is_required?: boolean
  assigned_to?: string
  notes?: string
}

export interface CaseMilestoneUpdate {
  title?: string
  description?: string
  milestone_type?: string
  status?: string
  target_date?: string
  completion_date?: string
  order_index?: number
  is_required?: boolean
  assigned_to?: string
  progress_percentage?: number
  notes?: string
}

export interface CaseDeadlineCreate {
  title: string
  description?: string
  deadline_type?: string
  deadline_date: string
  is_critical?: boolean
  reminder_days?: number[]
  assigned_to?: string
  notes?: string
}

export interface CaseDeadlineUpdate {
  title?: string
  description?: string
  deadline_type?: string
  deadline_date?: string
  is_critical?: boolean
  reminder_days?: number[]
  assigned_to?: string
  is_completed?: boolean
  completion_date?: string
  notes?: string
}

export interface MilestoneTemplateCreate {
  case_type: string
  title: string
  description?: string
  milestone_type?: string
  order_index?: number
  is_required?: boolean
  estimated_days?: number
  notes?: string
}

export interface CaseMilestonesResponse {
  data: CaseMilestone[]
  count: number
}

export interface CaseDeadlinesResponse {
  data: CaseDeadline[]
  count: number
}

export interface MilestoneTemplatesResponse {
  data: MilestoneTemplate[]
  count: number
}

export const CaseProgressService = {
  // Progress Summary
  getCaseProgressSummary: async (
    caseId: string,
  ): Promise<CaseProgressSummary> => {
    const response = await axios.get(`/legal-cases/${caseId}/progress`)
    return response.data
  },

  // Milestones
  getCaseMilestones: async (
    caseId: string,
    params?: {
      skip?: number
      limit?: number
      status?: string
      milestone_type?: string
      assigned_to?: string
      include_completed?: boolean
    },
  ): Promise<CaseMilestonesResponse> => {
    const response = await axios.get(`/legal-cases/${caseId}/milestones`, {
      params,
    })
    return response.data
  },

  createCaseMilestone: async (
    caseId: string,
    milestoneData: CaseMilestoneCreate,
  ): Promise<CaseMilestone> => {
    const response = await axios.post(
      `/legal-cases/${caseId}/milestones`,
      milestoneData,
    )
    return response.data
  },

  updateCaseMilestone: async (
    caseId: string,
    milestoneId: string,
    milestoneData: CaseMilestoneUpdate,
  ): Promise<CaseMilestone> => {
    const response = await axios.put(
      `/legal-cases/${caseId}/milestones/${milestoneId}`,
      milestoneData,
    )
    return response.data
  },

  deleteCaseMilestone: async (
    caseId: string,
    milestoneId: string,
  ): Promise<{ message: string }> => {
    const response = await axios.delete(
      `/legal-cases/${caseId}/milestones/${milestoneId}`,
    )
    return response.data
  },

  // Deadlines
  getCaseDeadlines: async (
    caseId: string,
    params?: {
      skip?: number
      limit?: number
      deadline_type?: string
      is_critical?: boolean
      assigned_to?: string
      include_completed?: boolean
      upcoming_days?: number
    },
  ): Promise<CaseDeadlinesResponse> => {
    const response = await axios.get(`/legal-cases/${caseId}/deadlines`, {
      params,
    })
    return response.data
  },

  createCaseDeadline: async (
    caseId: string,
    deadlineData: CaseDeadlineCreate,
  ): Promise<CaseDeadline> => {
    const response = await axios.post(
      `/legal-cases/${caseId}/deadlines`,
      deadlineData,
    )
    return response.data
  },

  updateCaseDeadline: async (
    caseId: string,
    deadlineId: string,
    deadlineData: CaseDeadlineUpdate,
  ): Promise<CaseDeadline> => {
    const response = await axios.put(
      `/legal-cases/${caseId}/deadlines/${deadlineId}`,
      deadlineData,
    )
    return response.data
  },

  deleteCaseDeadline: async (
    caseId: string,
    deadlineId: string,
  ): Promise<{ message: string }> => {
    const response = await axios.delete(
      `/legal-cases/${caseId}/deadlines/${deadlineId}`,
    )
    return response.data
  },

  // Templates
  getMilestoneTemplates: async (params?: {
    case_type?: string
    skip?: number
    limit?: number
  }): Promise<MilestoneTemplatesResponse> => {
    const response = await axios.get("/legal-cases/templates/milestones", {
      params,
    })
    return response.data
  },

  createMilestoneTemplate: async (
    templateData: MilestoneTemplateCreate,
  ): Promise<MilestoneTemplate> => {
    const response = await axios.post(
      "/legal-cases/templates/milestones",
      templateData,
    )
    return response.data
  },

  createMilestonesFromTemplate: async (
    caseId: string,
    caseType?: string,
  ): Promise<CaseMilestonesResponse> => {
    const response = await axios.post(
      `/legal-cases/${caseId}/milestones/from-template`,
      null,
      {
        params: { case_type: caseType },
      },
    )
    return response.data
  },
}

// Milestone status configurations
export const MILESTONE_STATUS_CONFIG = {
  not_started: {
    label: "Not Started",
    color: "gray",
    icon: "⚪",
    description: "Milestone has not been started yet",
  },
  in_progress: {
    label: "In Progress",
    color: "blue",
    icon: "🔵",
    description: "Milestone is currently being worked on",
  },
  completed: {
    label: "Completed",
    color: "green",
    icon: "✅",
    description: "Milestone has been completed",
  },
  overdue: {
    label: "Overdue",
    color: "red",
    icon: "🔴",
    description: "Milestone is past its target date",
  },
  cancelled: {
    label: "Cancelled",
    color: "orange",
    icon: "❌",
    description: "Milestone has been cancelled",
  },
}

// Milestone type configurations
export const MILESTONE_TYPE_CONFIG = {
  case_opening: {
    label: "Case Opening",
    color: "green",
    icon: "🚀",
    description: "Initial case setup and opening procedures",
  },
  discovery: {
    label: "Discovery",
    color: "blue",
    icon: "🔍",
    description: "Evidence gathering and discovery phase",
  },
  filing: {
    label: "Filing",
    color: "purple",
    icon: "📄",
    description: "Document filing and submissions",
  },
  hearing: {
    label: "Hearing",
    color: "orange",
    icon: "⚖️",
    description: "Court hearings and proceedings",
  },
  negotiation: {
    label: "Negotiation",
    color: "teal",
    icon: "🤝",
    description: "Settlement negotiations and discussions",
  },
  trial: {
    label: "Trial",
    color: "red",
    icon: "🏛️",
    description: "Trial proceedings and court appearances",
  },
  settlement: {
    label: "Settlement",
    color: "green",
    icon: "✅",
    description: "Settlement agreements and resolutions",
  },
  appeal: {
    label: "Appeal",
    color: "yellow",
    icon: "📈",
    description: "Appeal processes and procedures",
  },
  case_closure: {
    label: "Case Closure",
    color: "gray",
    icon: "🔒",
    description: "Final case closure and wrap-up",
  },
  custom: {
    label: "Custom",
    color: "cyan",
    icon: "⚙️",
    description: "Custom milestone specific to this case",
  },
}

// Deadline type configurations
export const DEADLINE_TYPE_CONFIG = {
  court_deadline: {
    label: "Court Deadline",
    color: "red",
    icon: "⚖️",
    description: "Court-imposed deadline",
  },
  internal_deadline: {
    label: "Internal Deadline",
    color: "blue",
    icon: "🏢",
    description: "Internal firm deadline",
  },
  client_deadline: {
    label: "Client Deadline",
    color: "green",
    icon: "👤",
    description: "Client-requested deadline",
  },
  statute_limitation: {
    label: "Statute of Limitations",
    color: "red",
    icon: "⏰",
    description: "Legal statute of limitations",
  },
  filing_deadline: {
    label: "Filing Deadline",
    color: "purple",
    icon: "📄",
    description: "Document filing deadline",
  },
  response_deadline: {
    label: "Response Deadline",
    color: "orange",
    icon: "💬",
    description: "Response or reply deadline",
  },
  discovery_deadline: {
    label: "Discovery Deadline",
    color: "teal",
    icon: "🔍",
    description: "Discovery phase deadline",
  },
  other: {
    label: "Other",
    color: "gray",
    icon: "📅",
    description: "Other type of deadline",
  },
}

// Utility functions
export const getMilestoneStatusInfo = (status: string) => {
  return (
    MILESTONE_STATUS_CONFIG[status as keyof typeof MILESTONE_STATUS_CONFIG] ||
    MILESTONE_STATUS_CONFIG.not_started
  )
}

export const getMilestoneTypeInfo = (type: string) => {
  return (
    MILESTONE_TYPE_CONFIG[type as keyof typeof MILESTONE_TYPE_CONFIG] ||
    MILESTONE_TYPE_CONFIG.custom
  )
}

export const getDeadlineTypeInfo = (type: string) => {
  return (
    DEADLINE_TYPE_CONFIG[type as keyof typeof DEADLINE_TYPE_CONFIG] ||
    DEADLINE_TYPE_CONFIG.other
  )
}

export const formatProgressPercentage = (percentage: number): string => {
  return `${Math.round(percentage)}%`
}

export const calculateDaysUntilDeadline = (deadlineDate: string): number => {
  const deadline = new Date(deadlineDate)
  const now = new Date()
  const diffTime = deadline.getTime() - now.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

export const isDeadlineOverdue = (deadlineDate: string): boolean => {
  return calculateDaysUntilDeadline(deadlineDate) < 0
}

export const isDeadlineUpcoming = (
  deadlineDate: string,
  days: number = 7,
): boolean => {
  const daysUntil = calculateDaysUntilDeadline(deadlineDate)
  return daysUntil >= 0 && daysUntil <= days
}

export const formatDeadlineUrgency = (
  deadlineDate: string,
): { label: string; color: string } => {
  const daysUntil = calculateDaysUntilDeadline(deadlineDate)

  if (daysUntil < 0) {
    return { label: `${Math.abs(daysUntil)} days overdue`, color: "red" }
  } else if (daysUntil === 0) {
    return { label: "Due today", color: "red" }
  } else if (daysUntil === 1) {
    return { label: "Due tomorrow", color: "orange" }
  } else if (daysUntil <= 7) {
    return { label: `Due in ${daysUntil} days`, color: "yellow" }
  } else if (daysUntil <= 30) {
    return { label: `Due in ${daysUntil} days`, color: "blue" }
  } else {
    return { label: `Due in ${daysUntil} days`, color: "gray" }
  }
}

export const sortMilestonesByOrder = (
  milestones: CaseMilestone[],
): CaseMilestone[] => {
  return milestones.sort((a, b) => {
    if (a.order_index !== b.order_index) {
      return a.order_index - b.order_index
    }
    // If same order, sort by target date
    if (a.target_date && b.target_date) {
      return (
        new Date(a.target_date).getTime() - new Date(b.target_date).getTime()
      )
    }
    return 0
  })
}

export const sortDeadlinesByDate = (
  deadlines: CaseDeadline[],
): CaseDeadline[] => {
  return deadlines.sort(
    (a, b) =>
      new Date(a.deadline_date).getTime() - new Date(b.deadline_date).getTime(),
  )
}

export const getProgressColor = (percentage: number): string => {
  if (percentage >= 100) return "green"
  if (percentage >= 75) return "blue"
  if (percentage >= 50) return "yellow"
  if (percentage >= 25) return "orange"
  return "red"
}
