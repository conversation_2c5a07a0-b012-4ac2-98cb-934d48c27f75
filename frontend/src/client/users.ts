import axios from "axios"

export interface User {
  id: string
  email: string
  full_name?: string
  role: string
  is_active: boolean
  created_at: string
  assigned_lawyer_id?: string
}

export interface UsersResponse {
  data: User[]
  count: number
}

export const UsersService = {
  getUsers: async (params?: {
    skip?: number
    limit?: number
    role?: string
  }): Promise<UsersResponse> => {
    const response = await axios.get("/users/", { params })
    return response.data
  },
}
