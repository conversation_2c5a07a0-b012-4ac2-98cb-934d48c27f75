/**
 * Advanced Notifications API Service
 *
 * This module provides API services for advanced notification management,
 * including preferences, templates, delivery tracking, and multi-channel notifications.
 */

import { ApiError } from "./core/ApiError"
import { CancelablePromise } from "./core/CancelablePromise"
import { OpenAPI } from "./core/OpenAPI"
import { request as __request } from "./core/request"

// Types for Advanced Notifications
export interface NotificationPublic {
  id: string
  title: string
  message: string
  notification_type: string
  priority: string
  is_read: boolean
  notification_metadata: Record<string, any>
  expires_at?: string
  user_id: string
  case_id?: string
  created_at: string
  read_at?: string
  case?: {
    id: string
    title: string
  }
}

export interface NotificationsPublic {
  data: NotificationPublic[]
  count: number
  unread_count: number
}

export interface NotificationUpdate {
  is_read?: boolean
  read_at?: string
}

export interface NotificationPreferencesPublic {
  id: string
  user_id: string
  notification_type: string
  channels: string[]
  is_enabled: boolean
  frequency: string
  quiet_hours_start?: string
  quiet_hours_end?: string
  priority_threshold: string
  created_at: string
  updated_at: string
}

export interface UserNotificationPreferencesPublic {
  data: NotificationPreferencesPublic[]
  count: number
}

export interface NotificationPreferencesCreate {
  notification_type: string
  channels?: string[]
  is_enabled?: boolean
  frequency?: string
  quiet_hours_start?: string
  quiet_hours_end?: string
  priority_threshold?: string
}

export interface NotificationPreferencesUpdate {
  channels?: string[]
  is_enabled?: boolean
  frequency?: string
  quiet_hours_start?: string
  quiet_hours_end?: string
  priority_threshold?: string
}

export interface NotificationTemplatePublic {
  id: string
  name: string
  description?: string
  notification_type: string
  channel: string
  subject_template: string
  body_template: string
  variables: string[]
  is_active: boolean
  is_system: boolean
  template_metadata: Record<string, any>
  created_by: string
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string
    email: string
  }
}

export interface NotificationTemplatesPublic {
  data: NotificationTemplatePublic[]
  count: number
}

export interface NotificationDeliveryPublic {
  id: string
  notification_id: string
  channel: string
  status: string
  recipient_address: string
  delivery_metadata: Record<string, any>
  error_message?: string
  external_id?: string
  sent_at?: string
  delivered_at?: string
  opened_at?: string
  clicked_at?: string
  failed_at?: string
  created_at: string
}

// Enums
export const NotificationType = {
  CASE_UPDATE: "case_update",
  DOCUMENT_SHARED: "document_shared",
  APPOINTMENT_REMINDER: "appointment_reminder",
  DEADLINE_APPROACHING: "deadline_approaching",
  MESSAGE_RECEIVED: "message_received",
  STATUS_CHANGE: "status_change",
  MILESTONE_COMPLETED: "milestone_completed",
  SYSTEM_ANNOUNCEMENT: "system_announcement",
} as const

export const NotificationPriority = {
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  URGENT: "urgent",
} as const

export const NotificationChannel = {
  IN_APP: "in_app",
  EMAIL: "email",
  SMS: "sms",
  PUSH: "push",
  WEBHOOK: "webhook",
} as const

export const NotificationFrequency = {
  IMMEDIATE: "immediate",
  DAILY: "daily",
  WEEKLY: "weekly",
  NEVER: "never",
} as const

/**
 * Advanced Notifications Service
 */
export class NotificationsService {
  /**
   * Get notifications for the current user
   */
  public static getNotifications({
    skip = 0,
    limit = 50,
    is_read,
    notification_type,
    priority,
  }: {
    skip?: number
    limit?: number
    is_read?: boolean
    notification_type?: string
    priority?: string
  }): CancelablePromise<NotificationsPublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/notifications/",
      query: {
        skip,
        limit,
        is_read,
        notification_type,
        priority,
      },
    })
  }

  /**
   * Update a notification (mark as read/unread)
   */
  public static updateNotification({
    notificationId,
    notification,
  }: {
    notificationId: string
    notification: NotificationUpdate
  }): CancelablePromise<NotificationPublic> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: `/api/v1/notifications/${notificationId}`,
      body: notification,
    })
  }

  /**
   * Mark all notifications as read
   */
  public static markAllNotificationsRead(): CancelablePromise<{
    message: string
  }> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/notifications/mark-all-read",
    })
  }

  /**
   * Get notification preferences for the current user
   */
  public static getNotificationPreferences(): CancelablePromise<UserNotificationPreferencesPublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/notifications/preferences",
    })
  }

  /**
   * Create or update notification preference
   */
  public static createNotificationPreference({
    preference,
  }: {
    preference: NotificationPreferencesCreate
  }): CancelablePromise<NotificationPreferencesPublic> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/api/v1/notifications/preferences",
      body: preference,
    })
  }

  /**
   * Update notification preference
   */
  public static updateNotificationPreference({
    preferenceId,
    preference,
  }: {
    preferenceId: string
    preference: NotificationPreferencesUpdate
  }): CancelablePromise<NotificationPreferencesPublic> {
    return __request(OpenAPI, {
      method: "PATCH",
      url: `/api/v1/notifications/preferences/${preferenceId}`,
      body: preference,
    })
  }

  /**
   * Get notification templates (admin only)
   */
  public static getNotificationTemplates({
    skip = 0,
    limit = 50,
    notification_type,
    channel,
    is_active,
  }: {
    skip?: number
    limit?: number
    notification_type?: string
    channel?: string
    is_active?: boolean
  }): CancelablePromise<NotificationTemplatesPublic> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/notifications/templates",
      query: {
        skip,
        limit,
        notification_type,
        channel,
        is_active,
      },
    })
  }

  /**
   * Track email open (internal use)
   */
  public static trackEmailOpen({
    deliveryId,
  }: {
    deliveryId: string
  }): CancelablePromise<void> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/notifications/track/open",
      query: {
        delivery_id: deliveryId,
      },
    })
  }

  /**
   * Track email click (internal use)
   */
  public static trackEmailClick({
    deliveryId,
    url,
  }: {
    deliveryId: string
    url: string
  }): CancelablePromise<void> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/api/v1/notifications/track/click",
      query: {
        delivery_id: deliveryId,
        url,
      },
    })
  }
}

// Utility functions
export const getNotificationTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    [NotificationType.CASE_UPDATE]: "Case Update",
    [NotificationType.DOCUMENT_SHARED]: "Document Shared",
    [NotificationType.APPOINTMENT_REMINDER]: "Appointment Reminder",
    [NotificationType.DEADLINE_APPROACHING]: "Deadline Approaching",
    [NotificationType.MESSAGE_RECEIVED]: "Message Received",
    [NotificationType.STATUS_CHANGE]: "Status Change",
    [NotificationType.MILESTONE_COMPLETED]: "Milestone Completed",
    [NotificationType.SYSTEM_ANNOUNCEMENT]: "System Announcement",
  }
  return labels[type] || type
}

export const getNotificationPriorityColor = (priority: string): string => {
  const colors: Record<string, string> = {
    [NotificationPriority.LOW]: "gray",
    [NotificationPriority.MEDIUM]: "blue",
    [NotificationPriority.HIGH]: "orange",
    [NotificationPriority.URGENT]: "red",
  }
  return colors[priority] || "gray"
}

export const getNotificationChannelLabel = (channel: string): string => {
  const labels: Record<string, string> = {
    [NotificationChannel.IN_APP]: "In-App",
    [NotificationChannel.EMAIL]: "Email",
    [NotificationChannel.SMS]: "SMS",
    [NotificationChannel.PUSH]: "Push",
    [NotificationChannel.WEBHOOK]: "Webhook",
  }
  return labels[channel] || channel
}

export const getNotificationFrequencyLabel = (frequency: string): string => {
  const labels: Record<string, string> = {
    [NotificationFrequency.IMMEDIATE]: "Immediate",
    [NotificationFrequency.DAILY]: "Daily Digest",
    [NotificationFrequency.WEEKLY]: "Weekly Digest",
    [NotificationFrequency.NEVER]: "Never",
  }
  return labels[frequency] || frequency
}
