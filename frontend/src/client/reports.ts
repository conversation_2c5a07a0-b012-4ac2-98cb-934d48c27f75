import axios from "axios"

export interface DashboardReport {
  period: {
    start_date: string
    end_date: string
    days: number
  }
  case_statistics: {
    total_cases: number
    cases_in_period: number
    cases_by_status: Record<string, number>
    cases_by_priority: Record<string, number>
    cases_by_lawyer: Array<{
      lawyer_name: string
      lawyer_id: string
      case_count: number
    }>
    average_cases_per_day: number
  }
  user_activity: {
    total_users: number
    users_by_role: Record<string, number>
    active_users_in_period: number
    activity_rate: number
  }
  document_analytics: {
    total_documents: number
    total_size_bytes: number
    total_size_mb: number
    documents_in_period: number
    documents_by_category: Record<string, number>
    total_document_links: number
    average_documents_per_day: number
  }
  activity_analytics: {
    total_activities: number
    activities_in_period: number
    activities_by_type: Record<string, number>
    average_activities_per_day: number
  }
  performance_indicators: {
    case_activity_rate: number
    active_cases_last_7_days: number
    average_documents_per_case: number
    system_health_score: number
  }
  trends: {
    cases_trend: {
      current_period: number
      previous_period: number
      percentage_change: number
      is_positive: boolean
    }
    documents_trend: {
      current_period: number
      previous_period: number
      percentage_change: number
      is_positive: boolean
    }
  }
  generated_at: string
  generated_by: {
    user_id: string
    user_name: string
    user_role: string
  }
}

export interface ReportParams {
  start_date?: string
  end_date?: string
  include_trends?: boolean
}

export const ReportsService = {
  // Get dashboard report
  getDashboardReport: async (
    params?: ReportParams,
  ): Promise<DashboardReport> => {
    const response = await axios.get("/reports/dashboard", { params })
    return response.data
  },
}

// Utility functions for report data processing
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes"

  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

export const formatPercentage = (value: number): string => {
  return `${value >= 0 ? "+" : ""}${value.toFixed(1)}%`
}

export const formatNumber = (value: number): string => {
  return new Intl.NumberFormat().format(value)
}

export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case "active":
      return "green"
    case "pending":
      return "yellow"
    case "closed":
      return "gray"
    case "on_hold":
      return "orange"
    default:
      return "blue"
  }
}

export const getPriorityColor = (priority: string): string => {
  switch (priority.toLowerCase()) {
    case "high":
      return "red"
    case "medium":
      return "orange"
    case "low":
      return "green"
    default:
      return "blue"
  }
}

export const getRoleColor = (role: string): string => {
  switch (role.toLowerCase()) {
    case "admin":
      return "purple"
    case "lawyer":
      return "blue"
    case "assistant":
      return "teal"
    case "client":
      return "green"
    default:
      return "gray"
  }
}

export const getActivityTypeColor = (activityType: string): string => {
  switch (activityType) {
    case "case_created":
      return "green"
    case "case_updated":
      return "blue"
    case "status_changed":
      return "purple"
    case "priority_changed":
      return "orange"
    case "document_uploaded":
      return "teal"
    case "document_downloaded":
      return "cyan"
    case "document_deleted":
      return "red"
    case "note_added":
      return "yellow"
    case "note_updated":
      return "yellow"
    case "note_deleted":
      return "red"
    case "user_assigned":
      return "pink"
    default:
      return "gray"
  }
}

export const getHealthScoreColor = (score: number): string => {
  if (score >= 80) return "green"
  if (score >= 60) return "yellow"
  if (score >= 40) return "orange"
  return "red"
}

export const getHealthScoreLabel = (score: number): string => {
  if (score >= 80) return "Excellent"
  if (score >= 60) return "Good"
  if (score >= 40) return "Fair"
  return "Needs Attention"
}

export const calculateGrowthRate = (
  current: number,
  previous: number,
): number => {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}

export const getDateRangePresets = () => {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  const lastWeek = new Date(today)
  lastWeek.setDate(lastWeek.getDate() - 7)

  const lastMonth = new Date(today)
  lastMonth.setMonth(lastMonth.getMonth() - 1)

  const lastQuarter = new Date(today)
  lastQuarter.setMonth(lastQuarter.getMonth() - 3)

  const lastYear = new Date(today)
  lastYear.setFullYear(lastYear.getFullYear() - 1)

  return {
    today: {
      label: "Today",
      start_date: today.toISOString().split("T")[0],
      end_date: today.toISOString().split("T")[0],
    },
    yesterday: {
      label: "Yesterday",
      start_date: yesterday.toISOString().split("T")[0],
      end_date: yesterday.toISOString().split("T")[0],
    },
    last7days: {
      label: "Last 7 Days",
      start_date: lastWeek.toISOString().split("T")[0],
      end_date: today.toISOString().split("T")[0],
    },
    last30days: {
      label: "Last 30 Days",
      start_date: lastMonth.toISOString().split("T")[0],
      end_date: today.toISOString().split("T")[0],
    },
    last90days: {
      label: "Last 90 Days",
      start_date: lastQuarter.toISOString().split("T")[0],
      end_date: today.toISOString().split("T")[0],
    },
    lastyear: {
      label: "Last Year",
      start_date: lastYear.toISOString().split("T")[0],
      end_date: today.toISOString().split("T")[0],
    },
  }
}

export const exportReportToPDF = async (
  report: DashboardReport,
): Promise<void> => {
  // This would integrate with a PDF generation library like jsPDF
  // For now, we'll create a simple implementation
  const printWindow = window.open("", "_blank")
  if (!printWindow) return

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Dashboard Report - ${report.period.start_date} to ${
        report.period.end_date
      }</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 20px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; border: 1px solid #ddd; }
        .trend-positive { color: green; }
        .trend-negative { color: red; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Dashboard Report</h1>
        <p>Period: ${report.period.start_date} to ${report.period.end_date}</p>
        <p>Generated: ${new Date(report.generated_at).toLocaleString()}</p>
      </div>
      
      <div class="section">
        <h2>Case Statistics</h2>
        <div class="metric">Total Cases: ${report.case_statistics.total_cases}</div>
        <div class="metric">Cases in Period: ${
          report.case_statistics.cases_in_period
        }</div>
        <div class="metric">Avg Cases/Day: ${
          report.case_statistics.average_cases_per_day
        }</div>
      </div>
      
      <div class="section">
        <h2>Document Analytics</h2>
        <div class="metric">Total Documents: ${
          report.document_analytics.total_documents
        }</div>
        <div class="metric">Total Size: ${formatFileSize(
          report.document_analytics.total_size_bytes,
        )}</div>
        <div class="metric">Documents in Period: ${
          report.document_analytics.documents_in_period
        }</div>
      </div>
      
      <div class="section">
        <h2>Performance Indicators</h2>
        <div class="metric">System Health: ${
          report.performance_indicators.system_health_score
        }%</div>
        <div class="metric">Activity Rate: ${
          report.performance_indicators.case_activity_rate
        }%</div>
        <div class="metric">Active Cases (7d): ${
          report.performance_indicators.active_cases_last_7_days
        }</div>
      </div>
      
      ${
        report.trends
          ? `
      <div class="section">
        <h2>Trends</h2>
        <div class="metric">
          Cases Trend: 
          <span class="${
            report.trends.cases_trend.is_positive
              ? "trend-positive"
              : "trend-negative"
          }">
            ${formatPercentage(report.trends.cases_trend.percentage_change)}
          </span>
        </div>
        <div class="metric">
          Documents Trend: 
          <span class="${
            report.trends.documents_trend.is_positive
              ? "trend-positive"
              : "trend-negative"
          }">
            ${formatPercentage(report.trends.documents_trend.percentage_change)}
          </span>
        </div>
      </div>
      `
          : ""
      }
    </body>
    </html>
  `

  printWindow.document.write(html)
  printWindow.document.close()
  printWindow.print()
}

export const exportReportToCSV = (report: DashboardReport): void => {
  const csvData = [
    ["Metric", "Value"],
    ["Report Period Start", report.period.start_date],
    ["Report Period End", report.period.end_date],
    ["Total Cases", report.case_statistics.total_cases.toString()],
    ["Cases in Period", report.case_statistics.cases_in_period.toString()],
    ["Total Documents", report.document_analytics.total_documents.toString()],
    ["Total Storage (MB)", report.document_analytics.total_size_mb.toString()],
    ["Total Activities", report.activity_analytics.total_activities.toString()],
    [
      "System Health Score",
      report.performance_indicators.system_health_score.toString(),
    ],
    [
      "Activity Rate",
      report.performance_indicators.case_activity_rate.toString(),
    ],
  ]

  const csvContent = csvData.map((row) => row.join(",")).join("\n")
  const blob = new Blob([csvContent], { type: "text/csv" })
  const url = window.URL.createObjectURL(blob)

  const link = document.createElement("a")
  link.href = url
  link.download = `dashboard-report-${report.period.start_date}-${report.period.end_date}.csv`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
