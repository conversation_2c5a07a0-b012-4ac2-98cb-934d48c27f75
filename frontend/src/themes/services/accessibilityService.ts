/**
=========================================================
* EquiNova Accessibility Service
=========================================================

* Service for ensuring theme system accessibility compliance
* WCAG 2.1 AA compliance utilities and validation

=========================================================
*/

// Color contrast utilities
export class ColorContrastValidator {
  /**
   * Calculate relative luminance of a color
   */
  private static getRelativeLuminance(color: string): number {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Apply gamma correction
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    // Calculate relative luminance
    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  }

  /**
   * Calculate contrast ratio between two colors
   */
  static getContrastRatio(color1: string, color2: string): number {
    const l1 = this.getRelativeLuminance(color1);
    const l2 = this.getRelativeLuminance(color2);
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Check if color combination meets WCAG AA standards
   */
  static meetsWCAGAA(foreground: string, background: string, isLargeText: boolean = false): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    return isLargeText ? ratio >= 3 : ratio >= 4.5;
  }

  /**
   * Check if color combination meets WCAG AAA standards
   */
  static meetsWCAGAAA(foreground: string, background: string, isLargeText: boolean = false): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    return isLargeText ? ratio >= 4.5 : ratio >= 7;
  }

  /**
   * Validate theme color palette for accessibility
   */
  static validateThemePalette(palette: Record<string, string>): {
    valid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check primary color against white and black backgrounds
    if (palette.primary) {
      if (!this.meetsWCAGAA(palette.primary, '#ffffff')) {
        issues.push('Primary color has insufficient contrast against white background');
        recommendations.push('Consider darkening the primary color or using a different shade');
      }
      if (!this.meetsWCAGAA('#ffffff', palette.primary)) {
        issues.push('White text has insufficient contrast against primary color');
        recommendations.push('Consider using a darker primary color for better text readability');
      }
    }

    // Check error color visibility
    if (palette.error && !this.meetsWCAGAA(palette.error, '#ffffff')) {
      issues.push('Error color may not be visible enough against light backgrounds');
      recommendations.push('Ensure error messages are clearly visible');
    }

    return {
      valid: issues.length === 0,
      issues,
      recommendations,
    };
  }
}

// Keyboard navigation utilities
export class KeyboardNavigationHelper {
  /**
   * Add keyboard navigation support to element
   */
  static addKeyboardSupport(element: HTMLElement, options: {
    onEnter?: () => void;
    onSpace?: () => void;
    onEscape?: () => void;
    onArrowKeys?: (direction: 'up' | 'down' | 'left' | 'right') => void;
  }): void {
    element.addEventListener('keydown', (event) => {
      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          options.onEnter?.();
          break;
        case ' ':
          event.preventDefault();
          options.onSpace?.();
          break;
        case 'Escape':
          event.preventDefault();
          options.onEscape?.();
          break;
        case 'ArrowUp':
          event.preventDefault();
          options.onArrowKeys?.('up');
          break;
        case 'ArrowDown':
          event.preventDefault();
          options.onArrowKeys?.('down');
          break;
        case 'ArrowLeft':
          event.preventDefault();
          options.onArrowKeys?.('left');
          break;
        case 'ArrowRight':
          event.preventDefault();
          options.onArrowKeys?.('right');
          break;
      }
    });
  }

  /**
   * Create focus trap for modal dialogs
   */
  static createFocusTrap(container: HTMLElement): () => void {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        if (event.shiftKey) {
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }
}

// ARIA utilities
export class ARIAHelper {
  /**
   * Generate unique ID for ARIA relationships
   */
  static generateId(prefix: string = 'equinova'): string {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Set up ARIA live region for announcements
   */
  static createLiveRegion(politeness: 'polite' | 'assertive' = 'polite'): HTMLElement {
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', politeness);
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';
    document.body.appendChild(liveRegion);
    return liveRegion;
  }

  /**
   * Announce message to screen readers
   */
  static announce(message: string, politeness: 'polite' | 'assertive' = 'polite'): void {
    const liveRegion = this.createLiveRegion(politeness);
    liveRegion.textContent = message;
    
    // Clean up after announcement
    setTimeout(() => {
      document.body.removeChild(liveRegion);
    }, 1000);
  }

  /**
   * Add comprehensive ARIA labels to theme components
   */
  static addThemeComponentLabels(element: HTMLElement, type: 'configurator' | 'customizer' | 'preview'): void {
    switch (type) {
      case 'configurator':
        element.setAttribute('role', 'dialog');
        element.setAttribute('aria-label', 'Theme configuration panel');
        element.setAttribute('aria-describedby', 'theme-config-description');
        break;
      case 'customizer':
        element.setAttribute('role', 'group');
        element.setAttribute('aria-label', 'Brand customization controls');
        break;
      case 'preview':
        element.setAttribute('role', 'img');
        element.setAttribute('aria-label', 'Theme preview');
        break;
    }
  }
}

// Screen reader utilities
export class ScreenReaderHelper {
  /**
   * Check if user is using screen reader
   */
  static isUsingScreenReader(): boolean {
    return window.navigator.userAgent.includes('NVDA') ||
           window.navigator.userAgent.includes('JAWS') ||
           window.speechSynthesis?.getVoices().length > 0;
  }

  /**
   * Provide alternative text for visual elements
   */
  static addAlternativeText(element: HTMLElement, description: string): void {
    element.setAttribute('aria-label', description);
    element.setAttribute('title', description);
  }

  /**
   * Create text description for color swatches
   */
  static describeColor(color: string, name?: string): string {
    const colorName = name || color;
    const brightness = ColorContrastValidator.getRelativeLuminance(color);
    const brightnessDesc = brightness > 0.5 ? 'light' : 'dark';
    return `${colorName}, ${brightnessDesc} color`;
  }
}

// Accessibility audit utilities
export class AccessibilityAuditor {
  /**
   * Audit theme system for accessibility issues
   */
  static auditThemeSystem(): {
    score: number;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for missing ARIA labels
    const unlabeledButtons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
    if (unlabeledButtons.length > 0) {
      issues.push(`${unlabeledButtons.length} buttons missing ARIA labels`);
      recommendations.push('Add aria-label or aria-labelledby to all interactive elements');
    }

    // Check for keyboard accessibility
    const focusableElements = document.querySelectorAll('button, [href], input, select, textarea');
    focusableElements.forEach((element) => {
      if (element.getAttribute('tabindex') === '-1') {
        issues.push('Interactive element removed from tab order');
        recommendations.push('Ensure all interactive elements are keyboard accessible');
      }
    });

    // Check for color contrast (simplified check)
    const colorElements = document.querySelectorAll('[style*="color"]');
    if (colorElements.length > 0) {
      recommendations.push('Verify color contrast ratios meet WCAG AA standards');
    }

    const score = Math.max(0, 100 - (issues.length * 10));

    return {
      score,
      issues,
      recommendations,
    };
  }

  /**
   * Generate accessibility report
   */
  static generateReport(): string {
    const audit = this.auditThemeSystem();
    
    return `
# EquiNova Theme System Accessibility Report

## Overall Score: ${audit.score}/100

## Issues Found (${audit.issues.length})
${audit.issues.map(issue => `- ${issue}`).join('\n')}

## Recommendations (${audit.recommendations.length})
${audit.recommendations.map(rec => `- ${rec}`).join('\n')}

## WCAG 2.1 Compliance
- Level A: ✅ Compliant
- Level AA: ${audit.score >= 80 ? '✅' : '⚠️'} ${audit.score >= 80 ? 'Compliant' : 'Needs improvement'}
- Level AAA: ${audit.score >= 95 ? '✅' : '⚠️'} ${audit.score >= 95 ? 'Compliant' : 'Aspirational'}

Generated on: ${new Date().toISOString()}
    `.trim();
  }
}

export default {
  ColorContrastValidator,
  KeyboardNavigationHelper,
  ARIAHelper,
  ScreenReaderHelper,
  AccessibilityAuditor,
};
