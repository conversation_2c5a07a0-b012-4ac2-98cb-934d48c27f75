/**
=========================================================
* EquiNova Theme Preferences Service
=========================================================

* Service for persisting and managing user theme preferences
* Handles localStorage and backend synchronization

=========================================================
*/

import { ThemeState } from '../context';

export interface UserThemePreferences {
  darkMode: boolean;
  themeVariant: 'default' | 'corporate' | 'modern' | 'classic';
  sidenavColor: string;
  miniSidenav: boolean;
  transparentSidenav: boolean;
  whiteSidenav: boolean;
  transparentNavbar: boolean;
  fixedNavbar: boolean;
  direction: 'ltr' | 'rtl';
  layout: 'dashboard' | 'page' | 'vr';
  customColors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
  };
  customLogo?: string;
  customFont?: string;
}

const STORAGE_KEY = 'equinova_theme_preferences';
const STORAGE_VERSION = '1.0';

export class ThemePreferencesService {
  /**
   * Save preferences to localStorage
   */
  static saveToLocalStorage(preferences: UserThemePreferences): void {
    try {
      const data = {
        version: STORAGE_VERSION,
        timestamp: new Date().toISOString(),
        preferences,
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      console.log('✅ Theme preferences saved to localStorage');
    } catch (error) {
      console.error('❌ Failed to save theme preferences to localStorage:', error);
    }
  }

  /**
   * Load preferences from localStorage
   */
  static loadFromLocalStorage(): UserThemePreferences | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) return null;

      const data = JSON.parse(stored);
      
      // Check version compatibility
      if (data.version !== STORAGE_VERSION) {
        console.warn('⚠️ Theme preferences version mismatch, using defaults');
        return null;
      }

      console.log('✅ Theme preferences loaded from localStorage');
      return data.preferences;
    } catch (error) {
      console.error('❌ Failed to load theme preferences from localStorage:', error);
      return null;
    }
  }

  /**
   * Clear localStorage preferences
   */
  static clearLocalStorage(): void {
    try {
      localStorage.removeItem(STORAGE_KEY);
      console.log('✅ Theme preferences cleared from localStorage');
    } catch (error) {
      console.error('❌ Failed to clear theme preferences from localStorage:', error);
    }
  }

  /**
   * Convert ThemeState to UserThemePreferences
   */
  static stateToPreferences(state: ThemeState): UserThemePreferences {
    return {
      darkMode: state.darkMode,
      themeVariant: state.themeVariant,
      sidenavColor: state.sidenavColor,
      miniSidenav: state.miniSidenav,
      transparentSidenav: state.transparentSidenav,
      whiteSidenav: state.whiteSidenav,
      transparentNavbar: state.transparentNavbar,
      fixedNavbar: state.fixedNavbar,
      direction: state.direction,
      layout: state.layout,
    };
  }

  /**
   * Convert UserThemePreferences to partial ThemeState
   */
  static preferencesToState(preferences: UserThemePreferences): Partial<ThemeState> {
    return {
      darkMode: preferences.darkMode,
      themeVariant: preferences.themeVariant,
      sidenavColor: preferences.sidenavColor,
      miniSidenav: preferences.miniSidenav,
      transparentSidenav: preferences.transparentSidenav,
      whiteSidenav: preferences.whiteSidenav,
      transparentNavbar: preferences.transparentNavbar,
      fixedNavbar: preferences.fixedNavbar,
      direction: preferences.direction,
      layout: preferences.layout,
    };
  }

  /**
   * Get default preferences
   */
  static getDefaultPreferences(): UserThemePreferences {
    return {
      darkMode: false,
      themeVariant: 'default',
      sidenavColor: 'info',
      miniSidenav: false,
      transparentSidenav: false,
      whiteSidenav: false,
      transparentNavbar: true,
      fixedNavbar: true,
      direction: 'ltr',
      layout: 'dashboard',
    };
  }

  /**
   * Export preferences as JSON
   */
  static exportPreferences(preferences: UserThemePreferences): string {
    const exportData = {
      version: STORAGE_VERSION,
      exportedAt: new Date().toISOString(),
      appName: 'EquiNova',
      preferences,
    };
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Import preferences from JSON
   */
  static importPreferences(jsonString: string): UserThemePreferences | null {
    try {
      const data = JSON.parse(jsonString);
      
      // Validate structure
      if (!data.preferences || !data.version) {
        throw new Error('Invalid theme preferences format');
      }

      // Version compatibility check
      if (data.version !== STORAGE_VERSION) {
        console.warn('⚠️ Imported theme preferences version mismatch');
      }

      console.log('✅ Theme preferences imported successfully');
      return data.preferences;
    } catch (error) {
      console.error('❌ Failed to import theme preferences:', error);
      return null;
    }
  }

  /**
   * Merge preferences with defaults
   */
  static mergeWithDefaults(preferences: Partial<UserThemePreferences>): UserThemePreferences {
    const defaults = this.getDefaultPreferences();
    return { ...defaults, ...preferences };
  }

  /**
   * Validate preferences object
   */
  static validatePreferences(preferences: any): boolean {
    if (!preferences || typeof preferences !== 'object') return false;

    const requiredFields = ['darkMode', 'themeVariant', 'sidenavColor'];
    return requiredFields.every(field => field in preferences);
  }

  /**
   * Auto-save preferences with debouncing
   */
  private static saveTimeout: NodeJS.Timeout | null = null;
  
  static autoSave(preferences: UserThemePreferences, delay: number = 1000): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(() => {
      this.saveToLocalStorage(preferences);
    }, delay);
  }
}

export default ThemePreferencesService;
