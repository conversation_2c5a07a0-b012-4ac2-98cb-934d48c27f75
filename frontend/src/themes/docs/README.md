# EquiNova Material-UI Theme System

## 🎨 Overview

The EquiNova Material-UI Theme System is a comprehensive theming solution that provides:

- **Multi-variant themes** (Default, Corporate, Modern, Classic)
- **Dark/Light mode switching** with seamless transitions
- **Brand customization** (colors, logos, fonts)
- **Preferences persistence** (localStorage + auto-save)
- **Export/Import functionality** for theme sharing
- **Accessibility compliance** (WCAG 2.1 AA)
- **Performance optimization** with lazy loading

## 🚀 Quick Start

### Basic Usage

```tsx
import { MaterialUIControllerProvider } from '@/themes/context';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@/themes/theme';

function App() {
  return (
    <MaterialUIControllerProvider>
      <ThemeProvider theme={createTheme()}>
        <YourApp />
      </ThemeProvider>
    </MaterialUIControllerProvider>
  );
}
```

### Using Theme Context

```tsx
import { useMaterialUIController, setDarkMode, setThemeVariant } from '@/themes/context';

function ThemeControls() {
  const { state, dispatch } = useMaterialUIController();
  
  const toggleDarkMode = () => {
    setDarkMode(dispatch, !state.darkMode);
  };
  
  const changeVariant = (variant) => {
    setThemeVariant(dispatch, variant);
  };
  
  return (
    <div>
      <button onClick={toggleDarkMode}>
        {state.darkMode ? 'Light' : 'Dark'} Mode
      </button>
      <select onChange={(e) => changeVariant(e.target.value)}>
        <option value="default">Default</option>
        <option value="corporate">Corporate</option>
        <option value="modern">Modern</option>
        <option value="classic">Classic</option>
      </select>
    </div>
  );
}
```

## 🎛️ Components

### ThemeConfigurator

The main theme configuration panel with preferences management.

```tsx
import ThemeConfigurator from '@/themes/components/ThemeConfigurator';

// Opens automatically when openConfigurator is true in context
<ThemeConfigurator />
```

**Features:**
- Dark/Light mode toggle
- Theme variant selection
- Preferences save/export/import
- Reset to defaults

### BrandCustomizer

Advanced brand customization component.

```tsx
import BrandCustomizer from '@/themes/components/BrandCustomizer';

<BrandCustomizer
  onCustomColorsChange={(colors) => console.log(colors)}
  onCustomLogoChange={(logo) => console.log(logo)}
  onCustomFontChange={(font) => console.log(font)}
/>
```

**Features:**
- Custom color palettes (6 color types)
- Logo upload with preview
- Font family selection
- Quick color presets
- Real-time preview

### AdvancedThemeConfigurator

Tabbed interface combining basic settings and brand customization.

```tsx
import AdvancedThemeConfigurator from '@/themes/components/AdvancedThemeConfigurator';

<AdvancedThemeConfigurator />
```

## 🔧 Services

### ThemePreferencesService

Handles persistence and management of user preferences.

```tsx
import { ThemePreferencesService } from '@/themes/services/themePreferences';

// Save preferences to localStorage
ThemePreferencesService.saveToLocalStorage(preferences);

// Load preferences from localStorage
const preferences = ThemePreferencesService.loadFromLocalStorage();

// Export preferences as JSON
const jsonData = ThemePreferencesService.exportPreferences(preferences);

// Import preferences from JSON
const imported = ThemePreferencesService.importPreferences(jsonString);
```

### PerformanceOptimization

Utilities for optimizing theme system performance.

```tsx
import { PerformanceMonitor, BundleOptimizer } from '@/themes/services/performanceOptimization';

// Measure performance
PerformanceMonitor.measureFunction('themeSwitch', () => {
  // Theme switching logic
});

// Check bundle optimization
const shouldLazy = BundleOptimizer.shouldLazyLoad('BrandCustomizer');
```

### AccessibilityService

Ensures WCAG 2.1 AA compliance and accessibility best practices.

```tsx
import { ColorContrastValidator, ARIAHelper } from '@/themes/services/accessibilityService';

// Validate color contrast
const isAccessible = ColorContrastValidator.meetsWCAGAA('#1976d2', '#ffffff');

// Add ARIA labels
ARIAHelper.addThemeComponentLabels(element, 'configurator');

// Announce to screen readers
ARIAHelper.announce('Theme changed to dark mode');
```

## 🎨 Hooks

### useThemePreferences

Custom hook for managing theme preferences with persistence.

```tsx
import { useThemePreferences } from '@/themes/hooks/useThemePreferences';

function MyComponent() {
  const {
    savePreferences,
    loadPreferences,
    exportPreferences,
    importPreferences,
    resetToDefaults,
  } = useThemePreferences();
  
  const handleExport = () => {
    const jsonData = exportPreferences();
    // Download or share the JSON data
  };
  
  return (
    <button onClick={handleExport}>Export Theme</button>
  );
}
```

## 🎯 Theme Variants

### Default Theme
- **Primary:** Blue (#1976d2)
- **Use case:** General purpose, professional
- **Best for:** Business applications

### Corporate Theme
- **Primary:** Green (#2e7d32)
- **Use case:** Corporate environments
- **Best for:** Enterprise applications

### Modern Theme
- **Primary:** Purple (#7b1fa2)
- **Use case:** Creative, modern interfaces
- **Best for:** Design-focused applications

### Classic Theme
- **Primary:** Red (#d32f2f)
- **Use case:** Traditional, formal interfaces
- **Best for:** Legal, government applications

## 📱 Responsive Design

The theme system is fully responsive with breakpoints:

```tsx
const theme = useTheme();

// Use theme breakpoints
sx={{
  [theme.breakpoints.up('sm')]: {
    // Styles for small screens and up
  },
  [theme.breakpoints.up('md')]: {
    // Styles for medium screens and up
  },
}}
```

## ♿ Accessibility

### WCAG 2.1 AA Compliance

- **Color Contrast:** All color combinations meet 4.5:1 ratio
- **Keyboard Navigation:** Full keyboard accessibility
- **Screen Reader Support:** Comprehensive ARIA labels
- **Focus Management:** Proper focus indicators and trapping

### Best Practices

```tsx
// Always provide ARIA labels
<Button aria-label="Switch to dark mode">
  <DarkModeIcon />
</Button>

// Use semantic HTML
<nav role="navigation" aria-label="Theme settings">
  {/* Navigation content */}
</nav>

// Announce important changes
ARIAHelper.announce('Theme changed to corporate variant');
```

## 🚀 Performance

### Bundle Optimization

- **Lazy Loading:** Heavy components are lazy-loaded
- **Tree Shaking:** Optimized Material-UI imports
- **Code Splitting:** Theme variants loaded on demand

### Runtime Performance

- **Memoization:** Expensive calculations are cached
- **Debouncing:** Theme changes are debounced
- **CSS Variables:** Fast theme switching with custom properties

## 🧪 Testing

### Manual Testing

1. **Theme Switching:** Verify all variants work correctly
2. **Persistence:** Check localStorage functionality
3. **Export/Import:** Test theme sharing workflow
4. **Accessibility:** Use screen reader and keyboard navigation
5. **Performance:** Monitor bundle size and runtime performance

### Automated Testing

```tsx
import { render, screen } from '@testing-library/react';
import { MaterialUIControllerProvider } from '@/themes/context';

test('theme context provides default values', () => {
  render(
    <MaterialUIControllerProvider>
      <TestComponent />
    </MaterialUIControllerProvider>
  );
  
  // Test assertions
});
```

## 📚 Migration Guide

### From Chakra UI to Material-UI

1. **Replace Chakra components** with Material-UI equivalents
2. **Update theme context** to use Material-UI theme provider
3. **Migrate custom styles** to Material-UI sx prop
4. **Update responsive breakpoints** to Material-UI system

### Example Migration

```tsx
// Before (Chakra UI)
<Box bg="blue.500" p={4}>
  <Text fontSize="lg">Hello World</Text>
</Box>

// After (Material-UI)
<Box sx={{ bgcolor: 'primary.main', p: 2 }}>
  <Typography variant="h6">Hello World</Typography>
</Box>
```

## 🔧 Configuration

### Environment Variables

```env
# Theme system configuration
REACT_APP_THEME_PERSISTENCE=true
REACT_APP_THEME_DEBUG=false
REACT_APP_DEFAULT_THEME_VARIANT=default
```

### Build Configuration

```json
{
  "build": {
    "optimization": {
      "splitChunks": {
        "chunks": "all",
        "cacheGroups": {
          "theme": {
            "test": /[\\/]themes[\\/]/,
            "name": "theme",
            "chunks": "all"
          }
        }
      }
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Theme not persisting:** Check localStorage permissions
2. **Performance issues:** Enable lazy loading for heavy components
3. **Accessibility warnings:** Run accessibility audit
4. **Bundle size:** Optimize Material-UI imports

### Debug Mode

```tsx
// Enable debug logging
localStorage.setItem('equinova_theme_debug', 'true');

// Check theme state
console.log(useMaterialUIController().state);
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

---

**EquiNova Theme System v2.0** • Built with Material-UI • Designed for accessibility and performance
