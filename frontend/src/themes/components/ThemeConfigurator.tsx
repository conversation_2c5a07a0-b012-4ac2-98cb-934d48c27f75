/**
=========================================================
* EquiNova Theme Configurator
=========================================================

* Component for real-time theme customization
* Allows users to switch between light/dark modes and theme variants

=========================================================
*/

import React, { useState } from 'react';
import {
  Drawer,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Divider,
  IconButton,
  Paper,
  Stack,
  Chip,
  Button,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Close as CloseIcon,
  Palette as PaletteIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Save as SaveIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Refresh as ResetIcon,
  CloudSync as SyncIcon,
} from '@mui/icons-material';

import {
  useMaterialUIController,
  setOpenConfigurator,
  setDarkMode,
  setThemeVariant,
} from '../context';
import { useThemePreferences } from '../hooks/useThemePreferences';

const ThemeConfigurator: React.FC = () => {
  const { state, dispatch } = useMaterialUIController();
  const { openConfigurator, darkMode, themeVariant } = state;
  const {
    savePreferences,
    exportPreferences,
    importPreferences,
    resetToDefaults,
  } = useThemePreferences();

  // Local state for UI
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [importText, setImportText] = useState('');

  const handleCloseConfigurator = () => {
    setOpenConfigurator(dispatch, false);
  };

  const handleDarkModeToggle = () => {
    setDarkMode(dispatch, !darkMode);
  };

  const handleThemeVariantChange = (variant: 'default' | 'corporate' | 'modern' | 'classic') => {
    setThemeVariant(dispatch, variant);
  };

  // New handlers for preferences management
  const handleSavePreferences = () => {
    try {
      savePreferences();
      setShowSuccess(true);
    } catch (error) {
      setErrorMessage('Failed to save preferences');
      setShowError(true);
    }
  };

  const handleExportPreferences = () => {
    try {
      const jsonData = exportPreferences();
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `equinova-theme-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      setShowSuccess(true);
    } catch (error) {
      setErrorMessage('Failed to export preferences');
      setShowError(true);
    }
  };

  const handleImportPreferences = () => {
    try {
      const success = importPreferences(importText);
      if (success) {
        setShowImportDialog(false);
        setImportText('');
        setShowSuccess(true);
      } else {
        setErrorMessage('Invalid theme preferences format');
        setShowError(true);
      }
    } catch (error) {
      setErrorMessage('Failed to import preferences');
      setShowError(true);
    }
  };

  const handleResetToDefaults = () => {
    try {
      resetToDefaults();
      setShowSuccess(true);
    } catch (error) {
      setErrorMessage('Failed to reset preferences');
      setShowError(true);
    }
  };

  const themeVariants = [
    { value: 'default', label: 'Default', color: '#1976d2' },
    { value: 'corporate', label: 'Corporate', color: '#2e7d32' },
    { value: 'modern', label: 'Modern', color: '#7b1fa2' },
    { value: 'classic', label: 'Classic', color: '#d32f2f' },
  ] as const;

  return (
    <Drawer
      anchor="right"
      open={openConfigurator}
      onClose={handleCloseConfigurator}
      PaperProps={{
        sx: {
          width: 320,
          padding: 2,
        },
      }}
    >
      <Box>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <SettingsIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Theme Settings
            </Typography>
          </Box>
          <IconButton onClick={handleCloseConfigurator} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Dark Mode Toggle */}
        <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            {darkMode ? <DarkModeIcon /> : <LightModeIcon />}
            <Typography variant="subtitle1" fontWeight="medium">
              Appearance
            </Typography>
          </Box>
          <FormControlLabel
            control={
              <Switch
                checked={darkMode}
                onChange={handleDarkModeToggle}
                color="primary"
              />
            }
            label={darkMode ? 'Dark Mode' : 'Light Mode'}
          />
          <Typography variant="body2" color="text.secondary" mt={1}>
            Toggle between light and dark themes
          </Typography>
        </Paper>

        {/* Theme Variants */}
        <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <PaletteIcon />
            <Typography variant="subtitle1" fontWeight="medium">
              Theme Variant
            </Typography>
          </Box>
          <Stack spacing={1}>
            {themeVariants.map((variant) => (
              <Chip
                key={variant.value}
                label={variant.label}
                onClick={() => handleThemeVariantChange(variant.value)}
                variant={themeVariant === variant.value ? 'filled' : 'outlined'}
                color={themeVariant === variant.value ? 'primary' : 'default'}
                sx={{
                  justifyContent: 'flex-start',
                  '&::before': {
                    content: '""',
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: variant.color,
                    marginRight: 1,
                  },
                }}
              />
            ))}
          </Stack>
          <Typography variant="body2" color="text.secondary" mt={1}>
            Choose your preferred color scheme
          </Typography>
        </Paper>

        {/* Preferences Management */}
        <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <SyncIcon />
            <Typography variant="subtitle1" fontWeight="medium">
              Preferences
            </Typography>
          </Box>
          <Stack spacing={1}>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={handleSavePreferences}
              size="small"
              fullWidth
            >
              Save Preferences
            </Button>
            <Box display="flex" gap={1}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={handleExportPreferences}
                size="small"
                flex={1}
              >
                Export
              </Button>
              <Button
                variant="outlined"
                startIcon={<UploadIcon />}
                onClick={() => setShowImportDialog(true)}
                size="small"
                flex={1}
              >
                Import
              </Button>
            </Box>
            <Button
              variant="outlined"
              startIcon={<ResetIcon />}
              onClick={handleResetToDefaults}
              size="small"
              color="warning"
              fullWidth
            >
              Reset to Defaults
            </Button>
          </Stack>
          <Typography variant="body2" color="text.secondary" mt={1}>
            Manage your theme preferences and settings
          </Typography>
        </Paper>

        {/* Preview Section */}
        <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
          <Typography variant="subtitle1" fontWeight="medium" mb={2}>
            Preview
          </Typography>
          <Box
            sx={{
              height: 100,
              borderRadius: 1,
              background: darkMode
                ? 'linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%)'
                : 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
              border: '1px solid',
              borderColor: 'divider',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography
              variant="body2"
              color="text.secondary"
              textAlign="center"
            >
              Theme Preview
              <br />
              {darkMode ? 'Dark' : 'Light'} • {themeVariant}
            </Typography>
          </Box>
        </Paper>

        {/* Footer */}
        <Box mt={3} textAlign="center">
          <Typography variant="caption" color="text.secondary">
            EquiNova Theme System v2.0
          </Typography>
        </Box>
      </Box>

      {/* Import Dialog */}
      <Dialog
        open={showImportDialog}
        onClose={() => setShowImportDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Import Theme Preferences</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Paste your exported theme preferences JSON below:
          </Typography>
          <TextField
            multiline
            rows={8}
            fullWidth
            value={importText}
            onChange={(e) => setImportText(e.target.value)}
            placeholder="Paste JSON content here..."
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowImportDialog(false)}>Cancel</Button>
          <Button
            onClick={handleImportPreferences}
            variant="contained"
            disabled={!importText.trim()}
          >
            Import
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success Snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={3000}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setShowSuccess(false)}
          severity="success"
          sx={{ width: '100%' }}
        >
          Operation completed successfully!
        </Alert>
      </Snackbar>

      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={4000}
        onClose={() => setShowError(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setShowError(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          {errorMessage}
        </Alert>
      </Snackbar>
    </Drawer>
  );
};

export default ThemeConfigurator;
