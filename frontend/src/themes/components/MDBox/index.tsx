/**
=========================================================
* EquiNova MDBox Component
=========================================================

* Material Dashboard Box component for EquiNova
* Adapted from Material Dashboard 2 React

=========================================================
*/

import React, { forwardRef } from 'react';
import { Box, BoxProps } from '@mui/material';
import { styled } from '@mui/material/styles';

interface MDBoxProps extends BoxProps {
  variant?: 'contained' | 'gradient';
  bgColor?: string;
  color?: string;
  opacity?: number;
  borderRadius?: string;
  shadow?: string;
  coloredShadow?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark' | 'none';
}

const MDBoxRoot = styled(Box)<{ ownerState: MDBoxProps }>(({ theme, ownerState }) => {
  const { variant, bgColor, color, opacity, borderRadius, shadow, coloredShadow } = ownerState;

  // Background color
  let backgroundValue = 'transparent';
  if (bgColor && bgColor !== 'transparent') {
    if (theme.palette[bgColor as keyof typeof theme.palette]) {
      backgroundValue = (theme.palette[bgColor as keyof typeof theme.palette] as any).main;
    } else {
      backgroundValue = bgColor;
    }
  }

  // Text color
  let colorValue = theme.palette.text.primary;
  if (color) {
    if (theme.palette[color as keyof typeof theme.palette]) {
      colorValue = (theme.palette[color as keyof typeof theme.palette] as any).main;
    } else {
      colorValue = color;
    }
  }

  // Border radius
  let borderRadiusValue = 0;
  if (borderRadius === 'xs') borderRadiusValue = theme.shape.borderRadius * 0.5;
  else if (borderRadius === 'sm') borderRadiusValue = theme.shape.borderRadius * 0.75;
  else if (borderRadius === 'md') borderRadiusValue = theme.shape.borderRadius;
  else if (borderRadius === 'lg') borderRadiusValue = theme.shape.borderRadius * 1.5;
  else if (borderRadius === 'xl') borderRadiusValue = theme.shape.borderRadius * 2;
  else if (borderRadius === 'xxl') borderRadiusValue = theme.shape.borderRadius * 3;
  else if (borderRadius && borderRadius !== 'none') borderRadiusValue = parseInt(borderRadius);

  // Box shadow
  let boxShadowValue = 'none';
  if (shadow === 'xs') boxShadowValue = '0 1px 2px rgba(0,0,0,0.05)';
  else if (shadow === 'sm') boxShadowValue = '0 1px 3px rgba(0,0,0,0.1)';
  else if (shadow === 'md') boxShadowValue = '0 4px 6px rgba(0,0,0,0.1)';
  else if (shadow === 'lg') boxShadowValue = '0 10px 15px rgba(0,0,0,0.1)';
  else if (shadow === 'xl') boxShadowValue = '0 20px 25px rgba(0,0,0,0.1)';
  else if (shadow === 'xxl') boxShadowValue = '0 25px 50px rgba(0,0,0,0.25)';
  else if (shadow && shadow !== 'none') boxShadowValue = shadow;

  // Colored shadow
  if (coloredShadow && coloredShadow !== 'none') {
    const shadowColor = theme.palette[coloredShadow as keyof typeof theme.palette] 
      ? (theme.palette[coloredShadow as keyof typeof theme.palette] as any).main 
      : coloredShadow;
    boxShadowValue = `0 4px 20px ${shadowColor}40`;
  }

  return {
    backgroundColor: variant === 'gradient' 
      ? `linear-gradient(135deg, ${backgroundValue} 0%, ${backgroundValue}CC 100%)`
      : backgroundValue,
    color: colorValue,
    opacity,
    borderRadius: borderRadiusValue,
    boxShadow: boxShadowValue,
  };
});

const MDBox = forwardRef<HTMLDivElement, MDBoxProps>(
  ({ variant = 'contained', bgColor = 'transparent', color = 'dark', opacity = 1, borderRadius = 'none', shadow = 'none', coloredShadow = 'none', ...rest }, ref) => (
    <MDBoxRoot
      {...rest}
      ref={ref}
      ownerState={{ variant, bgColor, color, opacity, borderRadius, shadow, coloredShadow }}
    />
  )
);

MDBox.displayName = 'MDBox';

export default MDBox;
