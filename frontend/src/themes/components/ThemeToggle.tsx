/**
=========================================================
* EquiNova Theme Toggle Components
=========================================================

* Quick theme switching components for navbar/toolbar
* Includes dark mode toggle and configurator trigger

=========================================================
*/

import React from 'react';
import {
  IconButton,
  Tooltip,
  Box,
  Fab,
} from '@mui/material';
import {
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

import {
  useMaterialUIController,
  setDarkMode,
  setOpenConfigurator,
} from '../context';

// Dark Mode Toggle Button
export const DarkModeToggle: React.FC = () => {
  const { state, dispatch } = useMaterialUIController();
  const { darkMode } = state;

  const handleToggle = () => {
    setDarkMode(dispatch, !darkMode);
  };

  return (
    <Tooltip title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
      <IconButton onClick={handleToggle} color="inherit">
        {darkMode ? <LightModeIcon /> : <DarkModeIcon />}
      </IconButton>
    </Tooltip>
  );
};

// Theme Configurator Trigger Button
export const ThemeConfiguratorButton: React.FC = () => {
  const { dispatch } = useMaterialUIController();

  const handleOpenConfigurator = () => {
    setOpenConfigurator(dispatch, true);
  };

  return (
    <Tooltip title="Theme Settings">
      <IconButton onClick={handleOpenConfigurator} color="inherit">
        <SettingsIcon />
      </IconButton>
    </Tooltip>
  );
};

// Floating Theme Settings Button (for bottom-right corner)
export const FloatingThemeButton: React.FC = () => {
  const { dispatch } = useMaterialUIController();

  const handleOpenConfigurator = () => {
    setOpenConfigurator(dispatch, true);
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 24,
        right: 24,
        zIndex: 1000,
      }}
    >
      <Tooltip title="Customize Theme">
        <Fab
          color="primary"
          onClick={handleOpenConfigurator}
          sx={{
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            '&:hover': {
              boxShadow: '0 6px 16px rgba(0,0,0,0.2)',
            },
          }}
        >
          <SettingsIcon />
        </Fab>
      </Tooltip>
    </Box>
  );
};

// Combined Theme Controls (for navbar)
export const ThemeControls: React.FC = () => {
  return (
    <Box display="flex" alignItems="center" gap={1}>
      <DarkModeToggle />
      <ThemeConfiguratorButton />
    </Box>
  );
};

export default {
  DarkModeToggle,
  ThemeConfiguratorButton,
  FloatingThemeButton,
  ThemeControls,
};
