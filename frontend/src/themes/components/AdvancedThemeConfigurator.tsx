/**
=========================================================
* EquiNova Advanced Theme Configurator
=========================================================

* Enhanced theme configurator with brand customization
* Phase 4 implementation with advanced features

=========================================================
*/

import React, { useState } from 'react';
import {
  Drawer,
  Box,
  Typography,
  Tabs,
  Tab,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Close as CloseIcon,
  Palette as PaletteIcon,
  Brush as BrushIcon,
  Tune as TuneIcon,
} from '@mui/icons-material';

import {
  useMaterialUIController,
  setOpenConfigurator,
} from '../context';
import ThemeConfigurator from './ThemeConfigurator';
import BrandCustomizer from './BrandCustomizer';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`theme-tabpanel-${index}`}
      aria-labelledby={`theme-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
    </div>
  );
}

const AdvancedThemeConfigurator: React.FC = () => {
  const { state, dispatch } = useMaterialUIController();
  const { openConfigurator } = state;
  const [activeTab, setActiveTab] = useState(0);

  const handleCloseConfigurator = () => {
    setOpenConfigurator(dispatch, false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleCustomColorsChange = (colors: any) => {
    console.log('Custom colors changed:', colors);
    // TODO: Implement custom colors in theme
  };

  const handleCustomLogoChange = (logo: string | null) => {
    console.log('Custom logo changed:', logo);
    // TODO: Implement custom logo in theme
  };

  const handleCustomFontChange = (font: string) => {
    console.log('Custom font changed:', font);
    // TODO: Implement custom font in theme
  };

  return (
    <Drawer
      anchor="right"
      open={openConfigurator}
      onClose={handleCloseConfigurator}
      PaperProps={{
        sx: {
          width: 400,
          padding: 0,
        },
      }}
    >
      <Box>
        {/* Header */}
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          p={2}
          sx={{
            borderBottom: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Box display="flex" alignItems="center" gap={1}>
            <SettingsIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Theme Studio
            </Typography>
          </Box>
          <IconButton onClick={handleCloseConfigurator} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            aria-label="theme configurator tabs"
          >
            <Tab
              icon={<TuneIcon />}
              label="Settings"
              id="theme-tab-0"
              aria-controls="theme-tabpanel-0"
            />
            <Tab
              icon={<BrushIcon />}
              label="Brand"
              id="theme-tab-1"
              aria-controls="theme-tabpanel-1"
            />
          </Tabs>
        </Box>

        {/* Tab Content */}
        <Box sx={{ height: 'calc(100vh - 120px)', overflow: 'auto', p: 2 }}>
          <TabPanel value={activeTab} index={0}>
            {/* Basic Theme Settings */}
            <Box>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Basic Theme Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Configure appearance, theme variants, and preferences
              </Typography>
              
              {/* Render the original ThemeConfigurator content without the drawer wrapper */}
              <Box>
                {/* We'll need to extract the content from ThemeConfigurator */}
                <Typography variant="body2" color="text.secondary">
                  Theme settings will be rendered here...
                </Typography>
              </Box>
            </Box>
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            {/* Brand Customization */}
            <BrandCustomizer
              onCustomColorsChange={handleCustomColorsChange}
              onCustomLogoChange={handleCustomLogoChange}
              onCustomFontChange={handleCustomFontChange}
            />
          </TabPanel>
        </Box>

        {/* Footer */}
        <Box
          p={2}
          textAlign="center"
          sx={{
            borderTop: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Typography variant="caption" color="text.secondary">
            EquiNova Theme Studio v2.0 • Phase 4
          </Typography>
        </Box>
      </Box>
    </Drawer>
  );
};

export default AdvancedThemeConfigurator;
