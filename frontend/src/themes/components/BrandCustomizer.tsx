/**
=========================================================
* EquiNova Brand Customizer
=========================================================

* Component for customizing brand elements like colors, logos, and fonts
* Advanced theming features for Phase 4

=========================================================
*/

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stack,
  TextField,
  Button,
  Chip,
  Grid,
  Avatar,
  IconButton,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Palette as PaletteIcon,
  Image as ImageIcon,
  FontDownload as FontIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Refresh as ResetIcon,
  Preview as PreviewIcon,
} from '@mui/icons-material';

interface BrandCustomizerProps {
  onCustomColorsChange?: (colors: CustomColors) => void;
  onCustomLogoChange?: (logo: string | null) => void;
  onCustomFontChange?: (font: string) => void;
}

interface CustomColors {
  primary?: string;
  secondary?: string;
  accent?: string;
  success?: string;
  warning?: string;
  error?: string;
}

const BrandCustomizer: React.FC<BrandCustomizerProps> = ({
  onCustomColorsChange,
  onCustomLogoChange,
  onCustomFontChange,
}) => {
  const [customColors, setCustomColors] = useState<CustomColors>({
    primary: '#1976d2',
    secondary: '#dc004e',
    accent: '#9c27b0',
    success: '#2e7d32',
    warning: '#ed6c02',
    error: '#d32f2f',
  });

  const [customLogo, setCustomLogo] = useState<string | null>(null);
  const [customFont, setCustomFont] = useState<string>('Roboto');

  const predefinedColors = [
    { name: 'Blue', value: '#1976d2' },
    { name: 'Green', value: '#2e7d32' },
    { name: 'Purple', value: '#9c27b0' },
    { name: 'Orange', value: '#ed6c02' },
    { name: 'Red', value: '#d32f2f' },
    { name: 'Teal', value: '#00695c' },
    { name: 'Indigo', value: '#3f51b5' },
    { name: 'Pink', value: '#e91e63' },
  ];

  const availableFonts = [
    'Roboto',
    'Open Sans',
    'Lato',
    'Montserrat',
    'Source Sans Pro',
    'Raleway',
    'Poppins',
    'Inter',
  ];

  const handleColorChange = (colorType: keyof CustomColors, value: string) => {
    const newColors = { ...customColors, [colorType]: value };
    setCustomColors(newColors);
    onCustomColorsChange?.(newColors);
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCustomLogo(result);
        onCustomLogoChange?.(result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleLogoRemove = () => {
    setCustomLogo(null);
    onCustomLogoChange?.(null);
  };

  const handleFontChange = (font: string) => {
    setCustomFont(font);
    onCustomFontChange?.(font);
  };

  const resetToDefaults = () => {
    const defaultColors = {
      primary: '#1976d2',
      secondary: '#dc004e',
      accent: '#9c27b0',
      success: '#2e7d32',
      warning: '#ed6c02',
      error: '#d32f2f',
    };
    setCustomColors(defaultColors);
    setCustomLogo(null);
    setCustomFont('Roboto');
    onCustomColorsChange?.(defaultColors);
    onCustomLogoChange?.(null);
    onCustomFontChange?.('Roboto');
  };

  return (
    <Stack spacing={3}>
      {/* Header */}
      <Box display="flex" alignItems="center" gap={1}>
        <PaletteIcon color="primary" />
        <Typography variant="h6" fontWeight="bold">
          Brand Customization
        </Typography>
      </Box>

      {/* Custom Colors */}
      <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
          Custom Colors
        </Typography>
        <Typography variant="body2" color="text.secondary" mb={2}>
          Customize your brand colors to match your organization's identity
        </Typography>

        <Grid container spacing={2}>
          {Object.entries(customColors).map(([colorType, value]) => (
            <Grid item xs={12} sm={6} key={colorType}>
              <Box display="flex" alignItems="center" gap={2}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: 1,
                    backgroundColor: value,
                    border: '1px solid',
                    borderColor: 'divider',
                  }}
                />
                <Box flex={1}>
                  <Typography variant="body2" fontWeight="medium" textTransform="capitalize">
                    {colorType}
                  </Typography>
                  <TextField
                    size="small"
                    value={value}
                    onChange={(e) => handleColorChange(colorType as keyof CustomColors, e.target.value)}
                    placeholder="#1976d2"
                    sx={{ mt: 0.5 }}
                  />
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* Predefined Colors */}
        <Box mt={3}>
          <Typography variant="body2" fontWeight="medium" gutterBottom>
            Quick Colors
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {predefinedColors.map((color) => (
              <Chip
                key={color.name}
                label={color.name}
                onClick={() => handleColorChange('primary', color.value)}
                sx={{
                  '&::before': {
                    content: '""',
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: color.value,
                    marginRight: 1,
                  },
                }}
              />
            ))}
          </Stack>
        </Box>
      </Paper>

      {/* Custom Logo */}
      <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
          Custom Logo
        </Typography>
        <Typography variant="body2" color="text.secondary" mb={2}>
          Upload your organization's logo to personalize the interface
        </Typography>

        <Box display="flex" alignItems="center" gap={2} mb={2}>
          {customLogo ? (
            <Avatar
              src={customLogo}
              sx={{ width: 60, height: 60 }}
              variant="rounded"
            />
          ) : (
            <Avatar
              sx={{ width: 60, height: 60, bgcolor: 'grey.200' }}
              variant="rounded"
            >
              <ImageIcon color="disabled" />
            </Avatar>
          )}
          <Box flex={1}>
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="logo-upload"
              type="file"
              onChange={handleLogoUpload}
            />
            <label htmlFor="logo-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                size="small"
              >
                Upload Logo
              </Button>
            </label>
            {customLogo && (
              <IconButton
                onClick={handleLogoRemove}
                size="small"
                color="error"
                sx={{ ml: 1 }}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Box>
        </Box>

        <Alert severity="info" sx={{ borderRadius: 1 }}>
          <Typography variant="body2">
            Recommended: PNG or SVG format, max 2MB, square aspect ratio for best results
          </Typography>
        </Alert>
      </Paper>

      {/* Custom Font */}
      <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
          Typography
        </Typography>
        <Typography variant="body2" color="text.secondary" mb={2}>
          Choose a font family that reflects your brand personality
        </Typography>

        <FormControl fullWidth size="small">
          <InputLabel>Font Family</InputLabel>
          <Select
            value={customFont}
            label="Font Family"
            onChange={(e) => handleFontChange(e.target.value)}
          >
            {availableFonts.map((font) => (
              <MenuItem key={font} value={font} sx={{ fontFamily: font }}>
                {font}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Box mt={2} p={2} bgcolor="grey.50" borderRadius={1}>
          <Typography variant="h6" sx={{ fontFamily: customFont }}>
            Sample Text
          </Typography>
          <Typography variant="body1" sx={{ fontFamily: customFont }}>
            This is how your text will look with the selected font family.
          </Typography>
        </Box>
      </Paper>

      {/* Actions */}
      <Box display="flex" gap={2}>
        <Button
          variant="outlined"
          startIcon={<ResetIcon />}
          onClick={resetToDefaults}
          color="warning"
        >
          Reset to Defaults
        </Button>
        <Button
          variant="contained"
          startIcon={<PreviewIcon />}
          onClick={() => {
            // TODO: Implement preview functionality
            console.log('Preview customizations');
          }}
        >
          Preview Changes
        </Button>
      </Box>
    </Stack>
  );
};

export default BrandCustomizer;
