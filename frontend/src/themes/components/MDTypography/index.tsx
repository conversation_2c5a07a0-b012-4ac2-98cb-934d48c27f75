/**
=========================================================
* EquiNova MDTypography Component
=========================================================

* Material Dashboard Typography component for EquiNova
* Adapted from Material Dashboard 2 React

=========================================================
*/

import React, { forwardRef } from 'react';
import { Typography, TypographyProps } from '@mui/material';
import { styled } from '@mui/material/styles';

interface MDTypographyProps extends TypographyProps {
  color?: string;
  fontWeight?: 'light' | 'regular' | 'medium' | 'bold';
  textTransform?: 'none' | 'capitalize' | 'uppercase' | 'lowercase';
  verticalAlign?: 'unset' | 'baseline' | 'sub' | 'super' | 'text-top' | 'text-bottom' | 'middle' | 'top' | 'bottom';
  textGradient?: boolean;
  opacity?: number;
}

const MDTypographyRoot = styled(Typography)<{ ownerState: MDTypographyProps }>(({ theme, ownerState }) => {
  const { color, textTransform, verticalAlign, fontWeight, opacity, textGradient } = ownerState;

  // Font weights
  const fontWeights = {
    light: 300,
    regular: 400,
    medium: 500,
    bold: 700,
  };

  // Text color
  let colorValue = theme.palette.text.primary;
  if (color) {
    if (color === 'inherit' || color === 'primary' || color === 'secondary' || color === 'text') {
      colorValue = 'inherit';
    } else if (theme.palette[color as keyof typeof theme.palette]) {
      colorValue = (theme.palette[color as keyof typeof theme.palette] as any).main;
    } else if (color === 'white') {
      colorValue = '#ffffff';
    } else if (color === 'dark') {
      colorValue = theme.palette.grey[800];
    } else if (color === 'light') {
      colorValue = theme.palette.grey[400];
    } else {
      colorValue = color;
    }
  }

  return {
    color: textGradient ? 'transparent' : colorValue,
    fontWeight: fontWeight ? fontWeights[fontWeight] : 'inherit',
    textTransform: textTransform || 'none',
    verticalAlign: verticalAlign || 'unset',
    opacity: opacity || 1,
    ...(textGradient && {
      background: `linear-gradient(135deg, ${colorValue} 0%, ${colorValue}80 100%)`,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
    }),
  };
});

const MDTypography = forwardRef<HTMLElement, MDTypographyProps>(
  ({ 
    color = 'dark', 
    fontWeight = 'regular', 
    textTransform = 'none', 
    verticalAlign = 'unset', 
    textGradient = false, 
    opacity = 1, 
    children,
    ...rest 
  }, ref) => (
    <MDTypographyRoot
      {...rest}
      ref={ref}
      ownerState={{ color, fontWeight, textTransform, verticalAlign, textGradient, opacity }}
    >
      {children}
    </MDTypographyRoot>
  )
);

MDTypography.displayName = 'MDTypography';

export default MDTypography;
