/**
=========================================================
* EquiNova SidenavRoot Component
=========================================================

* Material Dashboard SidenavRoot component for EquiNova
* Adapted from Material Dashboard 2 React

=========================================================
*/

import { Drawer } from '@mui/material';
import { styled } from '@mui/material/styles';

interface SidenavRootProps {
  transparentSidenav: boolean;
  whiteSidenav: boolean;
  miniSidenav: boolean;
  darkMode: boolean;
}

export default styled(Drawer)<{ ownerState: SidenavRootProps }>(({ theme, ownerState }) => {
  const { transparentSidenav, whiteSidenav, miniSidenav, darkMode } = ownerState;

  const sidebarWidth = 250;
  const miniSidebarWidth = 96;

  // Background color logic
  let backgroundValue = darkMode
    ? theme.palette.grey[900]
    : `linear-gradient(135deg, ${theme.palette.grey[800]} 0%, ${theme.palette.grey[900]} 100%)`;

  if (transparentSidenav) {
    backgroundValue = 'transparent';
  } else if (whiteSidenav) {
    backgroundValue = theme.palette.background.paper;
  }

  // Styles for the sidenav when miniSidenav={false}
  const drawerOpenStyles = () => ({
    background: backgroundValue,
    transform: 'translateX(0)',
    transition: theme.transitions.create('transform', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.shorter,
    }),

    [theme.breakpoints.up('xl')]: {
      boxShadow: transparentSidenav ? 'none' : '0 20px 27px 0 rgba(0,0,0,0.05)',
      marginBottom: transparentSidenav ? 0 : 'inherit',
      left: '0',
      width: sidebarWidth,
      transform: 'translateX(0)',
      transition: theme.transitions.create(['width', 'background-color'], {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.enteringScreen,
      }),
    },
  });

  // Styles for the sidenav when miniSidenav={true}
  const drawerCloseStyles = () => ({
    background: backgroundValue,
    transform: 'translateX(-320px)',
    transition: theme.transitions.create('transform', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.shorter,
    }),

    [theme.breakpoints.up('xl')]: {
      boxShadow: transparentSidenav ? 'none' : '0 20px 27px 0 rgba(0,0,0,0.05)',
      marginBottom: transparentSidenav ? 0 : 'inherit',
      left: '0',
      width: miniSidebarWidth,
      overflowX: 'hidden',
      transform: 'translateX(0)',
      transition: theme.transitions.create(['width', 'background-color'], {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.shorter,
      }),
    },
  });

  return {
    '& .MuiDrawer-paper': {
      boxShadow: '0 20px 27px 0 rgba(0,0,0,0.05)',
      border: 'none',
      height: '100vh',
      margin: 0,
      position: 'fixed',
      top: 0,
      backgroundColor: backgroundValue,
      color: whiteSidenav && !darkMode ? theme.palette.text.primary : theme.palette.common.white,

      ...(miniSidenav ? drawerCloseStyles() : drawerOpenStyles()),
    },
  };
});
