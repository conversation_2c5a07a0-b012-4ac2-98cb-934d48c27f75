/**
=========================================================
* EquiNova SidenavCollapse Component
=========================================================

* Material Dashboard SidenavCollapse component for EquiNova
* Adapted from Material Dashboard 2 React

=========================================================
*/

import React from 'react';
import { ListItem, ListItemIcon, ListItemText, Icon } from '@mui/material';
import { Link as RouterLink } from '@tanstack/react-router';

import MDBox from '../MDBox';
import { useMaterialUIController } from '../../context';

interface SidenavCollapseProps {
  icon?: React.ComponentType<any> | string;
  name: string;
  active?: boolean;
  route?: string;
  noCollapse?: boolean;
}

const SidenavCollapse: React.FC<SidenavCollapseProps> = ({ 
  icon, 
  name, 
  active = false, 
  route,
  noCollapse = false,
  ...rest 
}) => {
  const { state } = useMaterialUIController();
  const { miniSidenav, transparentSidenav, whiteSidenav, darkMode, sidenavColor } = state;

  // Styles for the collapse item
  const collapseItemStyles = (theme: any) => ({
    width: '100%',
    padding: '8px 16px',
    margin: '0 8px',
    borderRadius: '8px',
    cursor: 'pointer',
    userSelect: 'none',
    whiteSpace: 'nowrap',
    transition: theme.transitions.create(['background-color', 'color'], {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.shorter,
    }),
    backgroundColor: active 
      ? (transparentSidenav && !whiteSidenav 
          ? theme.palette.common.white 
          : theme.palette[sidenavColor]?.main || theme.palette.primary.main)
      : 'transparent',
    color: active
      ? (transparentSidenav && !whiteSidenav 
          ? theme.palette.grey[800]
          : theme.palette.common.white)
      : (transparentSidenav || whiteSidenav 
          ? theme.palette.grey[800] 
          : theme.palette.common.white),
    '&:hover': {
      backgroundColor: active 
        ? (transparentSidenav && !whiteSidenav 
            ? theme.palette.common.white 
            : theme.palette[sidenavColor]?.main || theme.palette.primary.main)
        : (transparentSidenav || whiteSidenav 
            ? theme.palette.grey[100] 
            : 'rgba(255, 255, 255, 0.1)'),
    },
  });

  // Styles for the icon box
  const iconBoxStyles = (theme: any) => ({
    minWidth: '32px',
    width: '32px',
    height: '32px',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: miniSidenav ? 0 : '12px',
    transition: theme.transitions.create(['margin'], {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.shorter,
    }),
    backgroundColor: active && !transparentSidenav && !whiteSidenav
      ? 'rgba(255, 255, 255, 0.2)'
      : 'transparent',
  });

  // Styles for the icon
  const iconStyles = (theme: any) => ({
    fontSize: '16px',
    color: active
      ? (transparentSidenav && !whiteSidenav 
          ? theme.palette.grey[800]
          : theme.palette.common.white)
      : (transparentSidenav || whiteSidenav 
          ? theme.palette.grey[600] 
          : theme.palette.common.white),
  });

  // Styles for the text
  const textStyles = (theme: any) => ({
    opacity: miniSidenav ? 0 : 1,
    maxWidth: miniSidenav ? 0 : '100%',
    transition: theme.transitions.create(['opacity', 'max-width'], {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.shorter,
    }),
    '& .MuiTypography-root': {
      fontSize: '14px',
      fontWeight: active ? 600 : 400,
      color: 'inherit',
    },
  });

  const content = (
    <ListItem component="li" disablePadding>
      <MDBox sx={collapseItemStyles} {...rest}>
        <ListItemIcon sx={iconBoxStyles}>
          {typeof icon === 'string' ? (
            <Icon sx={iconStyles}>{icon}</Icon>
          ) : icon ? (
            React.createElement(icon, { 
              size: 16, 
              style: { 
                color: active
                  ? (transparentSidenav && !whiteSidenav ? '#424242' : '#ffffff')
                  : (transparentSidenav || whiteSidenav ? '#666666' : '#ffffff')
              } 
            })
          ) : null}
        </ListItemIcon>

        <ListItemText
          primary={name}
          sx={textStyles}
        />
      </MDBox>
    </ListItem>
  );

  // If route is provided, wrap with RouterLink
  if (route) {
    return (
      <RouterLink to={route} style={{ textDecoration: 'none', color: 'inherit' }}>
        {content}
      </RouterLink>
    );
  }

  return content;
};

export default SidenavCollapse;
