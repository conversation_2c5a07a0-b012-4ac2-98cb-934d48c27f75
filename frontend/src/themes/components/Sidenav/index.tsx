/**
=========================================================
* EquiNova Sidenav Component
=========================================================

* Material Dashboard Sidenav component for EquiNova
* Adapted from Material Dashboard 2 React

=========================================================
*/

import React, { useEffect } from 'react';
import { useRouter } from '@tanstack/react-router';
import { List, Divider, Icon } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';

import type { UserPublic } from '@/client';
import MDBox from '../MDBox';
import MDTypography from '../MDTypography';
import SidenavCollapse from './SidenavCollapse';
import SidenavRoot from './SidenavRoot';

import {
  useMaterialUIController,
  setMiniSidenav,
  setTransparentSidenav,
  setWhiteSidenav,
} from '../../context';

interface Route {
  type: 'collapse' | 'title' | 'divider';
  name?: string;
  icon?: React.ComponentType<any>;
  title?: string;
  noCollapse?: boolean;
  key: string;
  href?: string;
  route?: string;
  roles?: string[];
}

interface SidenavProps {
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'dark';
  brand?: string;
  brandName: string;
  routes: Route[];
}

const Sidenav: React.FC<SidenavProps> = ({ 
  color = 'info', 
  brand = '', 
  brandName, 
  routes, 
  ...rest 
}) => {
  const { state, dispatch } = useMaterialUIController();
  const { miniSidenav, transparentSidenav, whiteSidenav, darkMode, sidenavColor } = state;
  const router = useRouter();
  const queryClient = useQueryClient();
  const currentUser = queryClient.getQueryData<UserPublic>(['currentUser']);

  const collapseName = router.state.location.pathname.replace('/', '');

  let textColor = 'white';

  if (transparentSidenav || (whiteSidenav && !darkMode)) {
    textColor = 'dark';
  } else if (whiteSidenav && darkMode) {
    textColor = 'inherit';
  }

  const closeSidenav = () => setMiniSidenav(dispatch, true);

  useEffect(() => {
    // A function that sets the mini state of the sidenav.
    function handleMiniSidenav() {
      setMiniSidenav(dispatch, window.innerWidth < 1200);
      setTransparentSidenav(dispatch, window.innerWidth < 1200 ? false : transparentSidenav);
      setWhiteSidenav(dispatch, window.innerWidth < 1200 ? false : whiteSidenav);
    }

    /** 
     The event listener that's calling the handleMiniSidenav function when resizing the window.
    */
    window.addEventListener('resize', handleMiniSidenav);

    // Call the handleMiniSidenav function to set the state with the initial value.
    handleMiniSidenav();

    // Remove event listener on cleanup
    return () => window.removeEventListener('resize', handleMiniSidenav);
  }, [dispatch, router.state.location]);

  // Filter routes based on user role
  const getFilteredRoutes = (routes: Route[]) => {
    return routes.filter(route => {
      if (route.roles) {
        return currentUser?.role && route.roles.includes(currentUser.role);
      }
      return true;
    });
  };

  // Render all the routes from the routes (All the visible items on the Sidenav)
  const renderRoutes = getFilteredRoutes(routes).map(({ type, name, icon, title, noCollapse, key, href, route }) => {
    let returnValue;

    if (type === 'collapse') {
      returnValue = href ? (
        <MDBox
          key={key}
          component="a"
          href={href}
          target="_blank"
          rel="noreferrer"
          sx={{ textDecoration: 'none' }}
        >
          <SidenavCollapse
            name={name || ''}
            icon={icon}
            active={key === collapseName}
            noCollapse={noCollapse}
          />
        </MDBox>
      ) : (
        <SidenavCollapse 
          key={key} 
          name={name || ''} 
          icon={icon} 
          active={key === collapseName} 
          route={route}
        />
      );
    } else if (type === 'title') {
      returnValue = (
        <MDTypography
          key={key}
          color={textColor}
          variant="caption"
          fontWeight="bold"
          textTransform="uppercase"
          sx={{
            pl: 3,
            mt: 2,
            mb: 1,
            ml: 1,
            display: 'block',
          }}
        >
          {title}
        </MDTypography>
      );
    } else if (type === 'divider') {
      returnValue = (
        <Divider
          key={key}
          light={
            (!darkMode && !whiteSidenav && !transparentSidenav) ||
            (darkMode && !transparentSidenav && whiteSidenav)
          }
        />
      );
    }

    return returnValue;
  });

  return (
    <SidenavRoot
      {...rest}
      variant="permanent"
      ownerState={{ transparentSidenav, whiteSidenav, miniSidenav, darkMode }}
    >
      <MDBox pt={3} pb={1} px={4} textAlign="center">
        <MDBox
          display={{ xs: 'block', xl: 'none' }}
          position="absolute"
          top={0}
          right={0}
          p={1.625}
          onClick={closeSidenav}
          sx={{ cursor: 'pointer' }}
        >
          <MDTypography variant="h6" color="secondary">
            <Icon sx={{ fontWeight: 'bold' }}>close</Icon>
          </MDTypography>
        </MDBox>
        <MDBox 
          component="div" 
          display="flex" 
          alignItems="center"
          sx={{ cursor: 'pointer' }}
        >
          {brand && <MDBox component="img" src={brand} alt="Brand" width="2rem" />}
          <MDBox
            width={!brandName ? '100%' : 'auto'}
            sx={(theme) => ({
              ml: 0.5,
              transition: theme.transitions.create('opacity', {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.standard,
              }),
              opacity: miniSidenav ? 0 : 1,
            })}
          >
            <MDTypography component="h6" variant="button" fontWeight="medium" color={textColor}>
              {brandName}
            </MDTypography>
          </MDBox>
        </MDBox>
      </MDBox>
      <Divider
        light={
          (!darkMode && !whiteSidenav && !transparentSidenav) ||
          (darkMode && !transparentSidenav && whiteSidenav)
        }
      />
      <List>{renderRoutes}</List>
    </SidenavRoot>
  );
};

export default Sidenav;
