/**
=========================================================
* EquiNova Sidenav Routes Configuration
=========================================================

* Routes configuration for the Material Dashboard Sidenav
* Defines navigation structure and menu items

=========================================================
*/

import {
  FiHome,
  FiFolder,
  FiCalendar,
  FiBarChart,
  FiUpload,
  FiSearch,
  FiMessageSquare,
  FiBell,
  FiBriefcase,
  FiSettings,
  FiUsers,
  FiShield,
  FiCheckCircle,
} from 'react-icons/fi';

export interface SidenavRoute {
  type: 'collapse' | 'title' | 'divider';
  name?: string;
  icon?: React.ComponentType<any>;
  title?: string;
  noCollapse?: boolean;
  key: string;
  href?: string;
  route?: string;
  roles?: string[];
}

export const routes: SidenavRoute[] = [
  // Main Navigation
  {
    type: 'collapse',
    name: 'Dashboard',
    key: 'dashboard',
    icon: FiHome,
    route: '/',
  },

  // Case Management Section
  {
    type: 'title',
    title: 'Case Management',
    key: 'case-management-title',
  },
  {
    type: 'collapse',
    name: 'Legal Cases',
    key: 'legal-cases',
    icon: FiFolder,
    route: '/legal-cases',
    roles: ['admin', 'lawyer', 'assistant'],
  },
  {
    type: 'collapse',
    name: 'Calendar',
    key: 'calendar',
    icon: FiCalendar,
    route: '/calendar',
    roles: ['admin', 'lawyer', 'assistant'],
  },
  {
    type: 'collapse',
    name: 'Reports',
    key: 'reports',
    icon: FiBarChart,
    route: '/reports',
    roles: ['admin', 'lawyer'],
  },

  // Document Management Section
  {
    type: 'title',
    title: 'Documents',
    key: 'documents-title',
  },
  {
    type: 'collapse',
    name: 'Documents',
    key: 'documents',
    icon: FiUpload,
    route: '/documents',
  },
  {
    type: 'collapse',
    name: 'Search',
    key: 'search',
    icon: FiSearch,
    route: '/search',
  },

  // Communication Section
  {
    type: 'title',
    title: 'Communication',
    key: 'communication-title',
  },
  {
    type: 'collapse',
    name: 'Messages',
    key: 'messages',
    icon: FiMessageSquare,
    route: '/messages',
  },
  {
    type: 'collapse',
    name: 'Notifications',
    key: 'notifications',
    icon: FiBell,
    route: '/notifications',
  },

  // Divider
  {
    type: 'divider',
    key: 'divider-1',
  },

  // Legacy/Other
  {
    type: 'collapse',
    name: 'Items',
    key: 'items',
    icon: FiBriefcase,
    route: '/items',
  },

  // Theme Demo
  {
    type: 'collapse',
    name: 'Theme Demo',
    key: 'theme-demo',
    icon: FiSettings,
    route: '/theme-demo',
  },
  {
    type: 'collapse',
    name: 'Material Table Demo',
    key: 'material-legal-cases-table',
    icon: FiFolder,
    route: '/material-legal-cases-table',
  },
  {
    type: 'collapse',
    name: 'Theme Studio',
    key: 'theme-studio',
    icon: FiSettings,
    route: '/theme-studio',
  },
  {
    type: 'collapse',
    name: 'System Validation',
    key: 'theme-system-validation',
    icon: FiCheckCircle,
    route: '/theme-system-validation',
  },

  // Administration Section (for admins only)
  {
    type: 'title',
    title: 'Administration',
    key: 'administration-title',
    roles: ['admin'],
  },
  {
    type: 'collapse',
    name: 'User Management',
    key: 'admin',
    icon: FiUsers,
    route: '/admin',
    roles: ['admin'],
  },
  {
    type: 'collapse',
    name: 'Security',
    key: 'admin-security',
    icon: FiShield,
    route: '/admin/security',
    roles: ['admin'],
  },
  {
    type: 'collapse',
    name: 'Analytics',
    key: 'admin-analytics',
    icon: FiBarChart,
    route: '/admin/analytics',
    roles: ['admin'],
  },

  // Divider
  {
    type: 'divider',
    key: 'divider-2',
  },

  // Settings
  {
    type: 'collapse',
    name: 'User Settings',
    key: 'settings',
    icon: FiSettings,
    route: '/settings',
  },
];

export default routes;
