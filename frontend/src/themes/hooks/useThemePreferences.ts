/**
=========================================================
* EquiNova Theme Preferences Hook
=========================================================

* Custom hook for managing theme preferences persistence
* Handles localStorage sync and auto-save functionality

=========================================================
*/

import { useEffect, useCallback } from 'react';
import { useMaterialUIController } from '../context';
import { ThemePreferencesService, UserThemePreferences } from '../services/themePreferences';

export interface UseThemePreferencesReturn {
  savePreferences: () => void;
  loadPreferences: () => void;
  clearPreferences: () => void;
  exportPreferences: () => string;
  importPreferences: (jsonString: string) => boolean;
  resetToDefaults: () => void;
}

export const useThemePreferences = (): UseThemePreferencesReturn => {
  const { state, dispatch } = useMaterialUIController();

  /**
   * Save current state to localStorage
   */
  const savePreferences = useCallback(() => {
    const preferences = ThemePreferencesService.stateToPreferences(state);
    ThemePreferencesService.saveToLocalStorage(preferences);
  }, [state]);

  /**
   * Load preferences from localStorage and apply to state
   */
  const loadPreferences = useCallback(() => {
    const preferences = ThemePreferencesService.loadFromLocalStorage();
    if (preferences) {
      const stateUpdates = ThemePreferencesService.preferencesToState(preferences);
      
      // Apply each preference to the state
      Object.entries(stateUpdates).forEach(([key, value]) => {
        switch (key) {
          case 'darkMode':
            dispatch({ type: 'DARKMODE', value });
            break;
          case 'themeVariant':
            dispatch({ type: 'THEME_VARIANT', value });
            break;
          case 'sidenavColor':
            dispatch({ type: 'SIDENAV_COLOR', value });
            break;
          case 'miniSidenav':
            dispatch({ type: 'MINI_SIDENAV', value });
            break;
          case 'transparentSidenav':
            dispatch({ type: 'TRANSPARENT_SIDENAV', value });
            break;
          case 'whiteSidenav':
            dispatch({ type: 'WHITE_SIDENAV', value });
            break;
          case 'transparentNavbar':
            dispatch({ type: 'TRANSPARENT_NAVBAR', value });
            break;
          case 'fixedNavbar':
            dispatch({ type: 'FIXED_NAVBAR', value });
            break;
          case 'direction':
            dispatch({ type: 'DIRECTION', value });
            break;
          case 'layout':
            dispatch({ type: 'LAYOUT', value });
            break;
        }
      });
    }
  }, [dispatch]);

  /**
   * Clear all preferences
   */
  const clearPreferences = useCallback(() => {
    ThemePreferencesService.clearLocalStorage();
  }, []);

  /**
   * Export current preferences as JSON string
   */
  const exportPreferences = useCallback((): string => {
    const preferences = ThemePreferencesService.stateToPreferences(state);
    return ThemePreferencesService.exportPreferences(preferences);
  }, [state]);

  /**
   * Import preferences from JSON string
   */
  const importPreferences = useCallback((jsonString: string): boolean => {
    const preferences = ThemePreferencesService.importPreferences(jsonString);
    if (preferences) {
      const stateUpdates = ThemePreferencesService.preferencesToState(preferences);
      
      // Apply imported preferences to state
      Object.entries(stateUpdates).forEach(([key, value]) => {
        switch (key) {
          case 'darkMode':
            dispatch({ type: 'DARKMODE', value });
            break;
          case 'themeVariant':
            dispatch({ type: 'THEME_VARIANT', value });
            break;
          case 'sidenavColor':
            dispatch({ type: 'SIDENAV_COLOR', value });
            break;
          case 'miniSidenav':
            dispatch({ type: 'MINI_SIDENAV', value });
            break;
          case 'transparentSidenav':
            dispatch({ type: 'TRANSPARENT_SIDENAV', value });
            break;
          case 'whiteSidenav':
            dispatch({ type: 'WHITE_SIDENAV', value });
            break;
          case 'transparentNavbar':
            dispatch({ type: 'TRANSPARENT_NAVBAR', value });
            break;
          case 'fixedNavbar':
            dispatch({ type: 'FIXED_NAVBAR', value });
            break;
          case 'direction':
            dispatch({ type: 'DIRECTION', value });
            break;
          case 'layout':
            dispatch({ type: 'LAYOUT', value });
            break;
        }
      });
      
      // Save imported preferences
      savePreferences();
      return true;
    }
    return false;
  }, [dispatch, savePreferences]);

  /**
   * Reset to default preferences
   */
  const resetToDefaults = useCallback(() => {
    const defaults = ThemePreferencesService.getDefaultPreferences();
    const stateUpdates = ThemePreferencesService.preferencesToState(defaults);
    
    // Apply defaults to state
    Object.entries(stateUpdates).forEach(([key, value]) => {
      switch (key) {
        case 'darkMode':
          dispatch({ type: 'DARKMODE', value });
          break;
        case 'themeVariant':
          dispatch({ type: 'THEME_VARIANT', value });
          break;
        case 'sidenavColor':
          dispatch({ type: 'SIDENAV_COLOR', value });
          break;
        case 'miniSidenav':
          dispatch({ type: 'MINI_SIDENAV', value });
          break;
        case 'transparentSidenav':
          dispatch({ type: 'TRANSPARENT_SIDENAV', value });
          break;
        case 'whiteSidenav':
          dispatch({ type: 'WHITE_SIDENAV', value });
          break;
        case 'transparentNavbar':
          dispatch({ type: 'TRANSPARENT_NAVBAR', value });
          break;
        case 'fixedNavbar':
          dispatch({ type: 'FIXED_NAVBAR', value });
          break;
        case 'direction':
          dispatch({ type: 'DIRECTION', value });
          break;
        case 'layout':
          dispatch({ type: 'LAYOUT', value });
          break;
      }
    });
    
    // Clear localStorage
    clearPreferences();
  }, [dispatch, clearPreferences]);

  /**
   * Auto-save preferences when state changes
   */
  useEffect(() => {
    const preferences = ThemePreferencesService.stateToPreferences(state);
    ThemePreferencesService.autoSave(preferences, 2000); // 2 second delay
  }, [state]);

  /**
   * Load preferences on mount
   */
  useEffect(() => {
    loadPreferences();
  }, [loadPreferences]);

  return {
    savePreferences,
    loadPreferences,
    clearPreferences,
    exportPreferences,
    importPreferences,
    resetToDefaults,
  };
};

export default useThemePreferences;
