/**
=========================================================
* EquiNova Material Theme System
=========================================================

* Adapted from Material Dashboard 2 React
* Enhanced for EquiNova Legal Management System

 =========================================================
*/

/**
  This file is used for controlling the global states of the theme components,
  you can customize the states for the different components here.
*/

import { createContext, useContext, useReducer, useMemo, ReactNode } from "react";

// TypeScript interfaces
export interface ThemeState {
  miniSidenav: boolean;
  transparentSidenav: boolean;
  whiteSidenav: boolean;
  sidenavColor: string;
  transparentNavbar: boolean;
  fixedNavbar: boolean;
  openConfigurator: boolean;
  direction: 'ltr' | 'rtl';
  layout: 'dashboard' | 'page' | 'vr';
  darkMode: boolean;
  themeVariant: 'default' | 'corporate' | 'modern' | 'classic';
}

export interface ThemeAction {
  type: string;
  value: any;
}

export interface ThemeContextType {
  state: ThemeState;
  dispatch: React.Dispatch<ThemeAction>;
}

export interface ThemeProviderProps {
  children: ReactNode;
}

// EquiNova Material Theme main context
const MaterialUIContext = createContext<ThemeContextType | undefined>(undefined);

// Setting custom name for the context which is visible on react dev tools
MaterialUIContext.displayName = "EquiNovaMaterialUIContext";

// EquiNova Material Theme reducer
function reducer(state: ThemeState, action: ThemeAction): ThemeState {
  switch (action.type) {
    case "MINI_SIDENAV": {
      return { ...state, miniSidenav: action.value };
    }
    case "TRANSPARENT_SIDENAV": {
      return { ...state, transparentSidenav: action.value };
    }
    case "WHITE_SIDENAV": {
      return { ...state, whiteSidenav: action.value };
    }
    case "SIDENAV_COLOR": {
      return { ...state, sidenavColor: action.value };
    }
    case "TRANSPARENT_NAVBAR": {
      return { ...state, transparentNavbar: action.value };
    }
    case "FIXED_NAVBAR": {
      return { ...state, fixedNavbar: action.value };
    }
    case "OPEN_CONFIGURATOR": {
      return { ...state, openConfigurator: action.value };
    }
    case "DIRECTION": {
      return { ...state, direction: action.value };
    }
    case "LAYOUT": {
      return { ...state, layout: action.value };
    }
    case "DARKMODE": {
      return { ...state, darkMode: action.value };
    }
    case "THEME_VARIANT": {
      return { ...state, themeVariant: action.value };
    }
    default: {
      throw new Error(`Unhandled action type: ${action.type}`);
    }
  }
}

// EquiNova Material Theme context provider
export function MaterialUIControllerProvider({ children }: ThemeProviderProps) {
  const initialState: ThemeState = {
    miniSidenav: false,
    transparentSidenav: false,
    whiteSidenav: false,
    sidenavColor: "info",
    transparentNavbar: true,
    fixedNavbar: true,
    openConfigurator: false,
    direction: "ltr",
    layout: "dashboard",
    darkMode: false,
    themeVariant: "default",
  };

  const [state, dispatch] = useReducer(reducer, initialState);

  const value = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return <MaterialUIContext.Provider value={value}>{children}</MaterialUIContext.Provider>;
}

// EquiNova Material Theme custom hook for using context
export function useMaterialUIController(): ThemeContextType {
  const context = useContext(MaterialUIContext);

  if (!context) {
    throw new Error(
      "useMaterialUIController should be used inside the MaterialUIControllerProvider."
    );
  }

  return context;
}

// Context action creators with TypeScript
export const setMiniSidenav = (dispatch: React.Dispatch<ThemeAction>, value: boolean) =>
  dispatch({ type: "MINI_SIDENAV", value });

export const setTransparentSidenav = (dispatch: React.Dispatch<ThemeAction>, value: boolean) =>
  dispatch({ type: "TRANSPARENT_SIDENAV", value });

export const setWhiteSidenav = (dispatch: React.Dispatch<ThemeAction>, value: boolean) =>
  dispatch({ type: "WHITE_SIDENAV", value });

export const setSidenavColor = (dispatch: React.Dispatch<ThemeAction>, value: string) =>
  dispatch({ type: "SIDENAV_COLOR", value });

export const setTransparentNavbar = (dispatch: React.Dispatch<ThemeAction>, value: boolean) =>
  dispatch({ type: "TRANSPARENT_NAVBAR", value });

export const setFixedNavbar = (dispatch: React.Dispatch<ThemeAction>, value: boolean) =>
  dispatch({ type: "FIXED_NAVBAR", value });

export const setOpenConfigurator = (dispatch: React.Dispatch<ThemeAction>, value: boolean) =>
  dispatch({ type: "OPEN_CONFIGURATOR", value });

export const setDirection = (dispatch: React.Dispatch<ThemeAction>, value: 'ltr' | 'rtl') =>
  dispatch({ type: "DIRECTION", value });

export const setLayout = (dispatch: React.Dispatch<ThemeAction>, value: 'dashboard' | 'page' | 'vr') =>
  dispatch({ type: "LAYOUT", value });

export const setDarkMode = (dispatch: React.Dispatch<ThemeAction>, value: boolean) =>
  dispatch({ type: "DARKMODE", value });

export const setThemeVariant = (dispatch: React.Dispatch<ThemeAction>, value: 'default' | 'corporate' | 'modern' | 'classic') =>
  dispatch({ type: "THEME_VARIANT", value });
