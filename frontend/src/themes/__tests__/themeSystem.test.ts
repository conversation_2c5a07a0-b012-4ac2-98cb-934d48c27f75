/**
=========================================================
* EquiNova Theme System Tests
=========================================================

* Unit tests for theme system services and utilities
* Phase 5 testing implementation

=========================================================
*/

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ThemePreferencesService, UserThemePreferences } from '../services/themePreferences';
import { ColorContrastValidator, AccessibilityAuditor } from '../services/accessibilityService';
import { PerformanceMonitor, BundleOptimizer, MemoryOptimizer } from '../services/performanceOptimization';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('ThemePreferencesService', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  describe('saveToLocalStorage', () => {
    it('should save preferences to localStorage', () => {
      const preferences: UserThemePreferences = {
        darkMode: true,
        themeVariant: 'corporate',
        sidenavColor: 'primary',
        miniSidenav: false,
        transparentSidenav: false,
        whiteSidenav: false,
        transparentNavbar: true,
        fixedNavbar: true,
        direction: 'ltr',
        layout: 'dashboard',
      };

      ThemePreferencesService.saveToLocalStorage(preferences);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'equinova_theme_preferences',
        expect.stringContaining('"darkMode":true')
      );
    });
  });

  describe('loadFromLocalStorage', () => {
    it('should load preferences from localStorage', () => {
      const mockData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        preferences: {
          darkMode: true,
          themeVariant: 'modern',
        },
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData));

      const result = ThemePreferencesService.loadFromLocalStorage();

      expect(result).toEqual(mockData.preferences);
    });

    it('should return null for invalid data', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');

      const result = ThemePreferencesService.loadFromLocalStorage();

      expect(result).toBeNull();
    });

    it('should return null for version mismatch', () => {
      const mockData = {
        version: '0.9',
        preferences: { darkMode: true },
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData));

      const result = ThemePreferencesService.loadFromLocalStorage();

      expect(result).toBeNull();
    });
  });

  describe('exportPreferences', () => {
    it('should export preferences as JSON string', () => {
      const preferences: UserThemePreferences = {
        darkMode: false,
        themeVariant: 'default',
        sidenavColor: 'info',
        miniSidenav: false,
        transparentSidenav: false,
        whiteSidenav: false,
        transparentNavbar: true,
        fixedNavbar: true,
        direction: 'ltr',
        layout: 'dashboard',
      };

      const result = ThemePreferencesService.exportPreferences(preferences);
      const parsed = JSON.parse(result);

      expect(parsed.version).toBe('1.0');
      expect(parsed.appName).toBe('EquiNova');
      expect(parsed.preferences).toEqual(preferences);
    });
  });

  describe('importPreferences', () => {
    it('should import valid preferences', () => {
      const exportData = {
        version: '1.0',
        appName: 'EquiNova',
        preferences: {
          darkMode: true,
          themeVariant: 'classic',
        },
      };

      const result = ThemePreferencesService.importPreferences(JSON.stringify(exportData));

      expect(result).toEqual(exportData.preferences);
    });

    it('should return null for invalid JSON', () => {
      const result = ThemePreferencesService.importPreferences('invalid json');

      expect(result).toBeNull();
    });
  });

  describe('validatePreferences', () => {
    it('should validate correct preferences', () => {
      const preferences = {
        darkMode: true,
        themeVariant: 'default',
        sidenavColor: 'primary',
      };

      const result = ThemePreferencesService.validatePreferences(preferences);

      expect(result).toBe(true);
    });

    it('should reject invalid preferences', () => {
      const preferences = {
        invalidField: true,
      };

      const result = ThemePreferencesService.validatePreferences(preferences);

      expect(result).toBe(false);
    });
  });
});

describe('ColorContrastValidator', () => {
  describe('getContrastRatio', () => {
    it('should calculate correct contrast ratio for black and white', () => {
      const ratio = ColorContrastValidator.getContrastRatio('#000000', '#ffffff');
      expect(ratio).toBeCloseTo(21, 0);
    });

    it('should calculate correct contrast ratio for same colors', () => {
      const ratio = ColorContrastValidator.getContrastRatio('#1976d2', '#1976d2');
      expect(ratio).toBe(1);
    });
  });

  describe('meetsWCAGAA', () => {
    it('should pass for high contrast combinations', () => {
      const result = ColorContrastValidator.meetsWCAGAA('#000000', '#ffffff');
      expect(result).toBe(true);
    });

    it('should fail for low contrast combinations', () => {
      const result = ColorContrastValidator.meetsWCAGAA('#cccccc', '#ffffff');
      expect(result).toBe(false);
    });

    it('should have different requirements for large text', () => {
      const normalText = ColorContrastValidator.meetsWCAGAA('#767676', '#ffffff', false);
      const largeText = ColorContrastValidator.meetsWCAGAA('#767676', '#ffffff', true);
      
      expect(normalText).toBe(false);
      expect(largeText).toBe(true);
    });
  });

  describe('validateThemePalette', () => {
    it('should validate accessible color palette', () => {
      const palette = {
        primary: '#1976d2',
        secondary: '#dc004e',
        error: '#d32f2f',
      };

      const result = ColorContrastValidator.validateThemePalette(palette);

      expect(result.valid).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('should detect accessibility issues', () => {
      const palette = {
        primary: '#ffff00', // Yellow - poor contrast
        error: '#ff9999',   // Light red - poor contrast
      };

      const result = ColorContrastValidator.validateThemePalette(palette);

      expect(result.valid).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.recommendations.length).toBeGreaterThan(0);
    });
  });
});

describe('PerformanceMonitor', () => {
  beforeEach(() => {
    // Mock performance.now()
    vi.spyOn(performance, 'now').mockReturnValue(1000);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('measureFunction', () => {
    it('should measure function execution time', () => {
      const mockFn = vi.fn(() => 'result');
      vi.spyOn(performance, 'now')
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1100);

      const result = PerformanceMonitor.measureFunction('test', mockFn);

      expect(result).toBe('result');
      expect(mockFn).toHaveBeenCalled();
    });
  });

  describe('measureAsyncFunction', () => {
    it('should measure async function execution time', async () => {
      const mockAsyncFn = vi.fn(async () => 'async result');
      vi.spyOn(performance, 'now')
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1200);

      const result = await PerformanceMonitor.measureAsyncFunction('async-test', mockAsyncFn);

      expect(result).toBe('async result');
      expect(mockAsyncFn).toHaveBeenCalled();
    });
  });
});

describe('BundleOptimizer', () => {
  describe('shouldLazyLoad', () => {
    it('should recommend lazy loading for heavy components', () => {
      expect(BundleOptimizer.shouldLazyLoad('BrandCustomizer')).toBe(true);
      expect(BundleOptimizer.shouldLazyLoad('AdvancedThemeConfigurator')).toBe(true);
      expect(BundleOptimizer.shouldLazyLoad('MaterialLegalCasesTable')).toBe(true);
    });

    it('should not recommend lazy loading for light components', () => {
      expect(BundleOptimizer.shouldLazyLoad('Button')).toBe(false);
      expect(BundleOptimizer.shouldLazyLoad('Typography')).toBe(false);
    });
  });

  describe('getOptimizedMUIImport', () => {
    it('should return specific import paths for known components', () => {
      expect(BundleOptimizer.getOptimizedMUIImport('Button')).toBe('@mui/material/Button');
      expect(BundleOptimizer.getOptimizedMUIImport('TextField')).toBe('@mui/material/TextField');
    });

    it('should return generic path for unknown components', () => {
      expect(BundleOptimizer.getOptimizedMUIImport('UnknownComponent')).toBe('@mui/material/UnknownComponent');
    });
  });

  describe('analyzeThemeSystemImpact', () => {
    it('should return bundle analysis', () => {
      const analysis = BundleOptimizer.analyzeThemeSystemImpact();

      expect(analysis).toHaveProperty('estimatedSize');
      expect(analysis).toHaveProperty('components');
      expect(analysis).toHaveProperty('services');
      expect(analysis).toHaveProperty('hooks');
      expect(typeof analysis.components).toBe('number');
    });
  });
});

describe('MemoryOptimizer', () => {
  beforeEach(() => {
    MemoryOptimizer.clearCache();
  });

  describe('memoize', () => {
    it('should cache function results', () => {
      const expensiveFn = vi.fn(() => 'expensive result');

      const result1 = MemoryOptimizer.memoize('test-key', expensiveFn);
      const result2 = MemoryOptimizer.memoize('test-key', expensiveFn);

      expect(result1).toBe('expensive result');
      expect(result2).toBe('expensive result');
      expect(expensiveFn).toHaveBeenCalledTimes(1);
    });

    it('should call function again for different keys', () => {
      const expensiveFn = vi.fn(() => 'expensive result');

      MemoryOptimizer.memoize('key1', expensiveFn);
      MemoryOptimizer.memoize('key2', expensiveFn);

      expect(expensiveFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('getCacheStats', () => {
    it('should return cache statistics', () => {
      MemoryOptimizer.memoize('test1', () => 'result1');
      MemoryOptimizer.memoize('test2', () => 'result2');

      const stats = MemoryOptimizer.getCacheStats();

      expect(stats.size).toBe(2);
      expect(stats.keys).toContain('test1');
      expect(stats.keys).toContain('test2');
    });
  });

  describe('clearCache', () => {
    it('should clear all cached values', () => {
      MemoryOptimizer.memoize('test', () => 'result');
      expect(MemoryOptimizer.getCacheStats().size).toBe(1);

      MemoryOptimizer.clearCache();
      expect(MemoryOptimizer.getCacheStats().size).toBe(0);
    });
  });
});

describe('AccessibilityAuditor', () => {
  describe('auditThemeSystem', () => {
    it('should return audit results', () => {
      const audit = AccessibilityAuditor.auditThemeSystem();

      expect(audit).toHaveProperty('score');
      expect(audit).toHaveProperty('issues');
      expect(audit).toHaveProperty('recommendations');
      expect(typeof audit.score).toBe('number');
      expect(Array.isArray(audit.issues)).toBe(true);
      expect(Array.isArray(audit.recommendations)).toBe(true);
    });

    it('should have score between 0 and 100', () => {
      const audit = AccessibilityAuditor.auditThemeSystem();

      expect(audit.score).toBeGreaterThanOrEqual(0);
      expect(audit.score).toBeLessThanOrEqual(100);
    });
  });

  describe('generateReport', () => {
    it('should generate accessibility report', () => {
      const report = AccessibilityAuditor.generateReport();

      expect(report).toContain('EquiNova Theme System Accessibility Report');
      expect(report).toContain('Overall Score:');
      expect(report).toContain('WCAG 2.1 Compliance');
    });
  });
});

// Integration tests
describe('Theme System Integration', () => {
  it('should handle complete theme switching workflow', () => {
    // Test the complete workflow from preferences to persistence
    const preferences: UserThemePreferences = {
      darkMode: true,
      themeVariant: 'corporate',
      sidenavColor: 'primary',
      miniSidenav: false,
      transparentSidenav: false,
      whiteSidenav: false,
      transparentNavbar: true,
      fixedNavbar: true,
      direction: 'ltr',
      layout: 'dashboard',
    };

    // Save preferences
    ThemePreferencesService.saveToLocalStorage(preferences);

    // Export preferences
    const exported = ThemePreferencesService.exportPreferences(preferences);
    expect(exported).toBeTruthy();

    // Import preferences
    const imported = ThemePreferencesService.importPreferences(exported);
    expect(imported).toEqual(preferences);

    // Validate preferences
    const isValid = ThemePreferencesService.validatePreferences(imported!);
    expect(isValid).toBe(true);
  });

  it('should maintain accessibility standards across theme variants', () => {
    const variants = ['default', 'corporate', 'modern', 'classic'];
    const colors = {
      default: '#1976d2',
      corporate: '#2e7d32',
      modern: '#7b1fa2',
      classic: '#d32f2f',
    };

    variants.forEach(variant => {
      const color = colors[variant as keyof typeof colors];
      const isAccessible = ColorContrastValidator.meetsWCAGAA(color, '#ffffff');
      expect(isAccessible).toBe(true);
    });
  });
});
