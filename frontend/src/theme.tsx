import { createSystem, defaultConfig } from "@chakra-ui/react"
import { buttonRecipe } from "./theme/button.recipe"

export const system = createSystem(defaultConfig, {
  globalCss: {
    html: {
      fontSize: "16px",
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    },
    body: {
      fontSize: "0.875rem",
      margin: 0,
      padding: 0,
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
      lineHeight: "1.6",
      color: "gray.800",
      backgroundColor: "gray.50",
    },
    ".main-link": {
      color: "ui.primary",
      fontWeight: "600",
      textDecoration: "none",
      transition: "all 0.2s ease-in-out",
      _hover: {
        color: "ui.primaryHover",
        textDecoration: "underline",
      },
    },
  },
  theme: {
    tokens: {
      colors: {
        ui: {
          // TunLeg Professional Color Palette
          primary: { value: "#2563eb" },        // Professional blue
          primaryHover: { value: "#1d4ed8" },   // Darker blue on hover
          secondary: { value: "#7c3aed" },      // Purple accent
          success: { value: "#059669" },        // Green for success
          warning: { value: "#d97706" },        // Orange for warnings
          danger: { value: "#dc2626" },         // Red for errors

          // Neutral grays for professional look
          gray: {
            50: { value: "#f9fafb" },
            100: { value: "#f3f4f6" },
            200: { value: "#e5e7eb" },
            300: { value: "#d1d5db" },
            400: { value: "#9ca3af" },
            500: { value: "#6b7280" },
            600: { value: "#4b5563" },
            700: { value: "#374151" },
            800: { value: "#1f2937" },
            900: { value: "#111827" },
          },

          // Background colors
          background: {
            main: { value: "#ffffff" },
            secondary: { value: "#f9fafb" },
            accent: { value: "#f3f4f6" },
          },

          // Border colors
          border: {
            light: { value: "#e5e7eb" },
            medium: { value: "#d1d5db" },
            dark: { value: "#9ca3af" },
          },
        },
      },
      fonts: {
        heading: { value: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif" },
        body: { value: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif" },
      },
      fontSizes: {
        xs: { value: "0.75rem" },
        sm: { value: "0.875rem" },
        md: { value: "1rem" },
        lg: { value: "1.125rem" },
        xl: { value: "1.25rem" },
        "2xl": { value: "1.5rem" },
        "3xl": { value: "1.875rem" },
        "4xl": { value: "2.25rem" },
      },
      shadows: {
        sm: { value: "0 1px 2px 0 rgba(0, 0, 0, 0.05)" },
        md: { value: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)" },
        lg: { value: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" },
        xl: { value: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" },
      },
    },
    recipes: {
      button: buttonRecipe,
    },
  },
})
