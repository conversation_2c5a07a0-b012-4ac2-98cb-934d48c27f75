/**
 * Real-time Notification WebSocket Hook
 * 
 * This hook provides real-time notification updates via WebSocket connection.
 * It automatically reconnects on connection loss and manages notification state.
 */

import { useEffect, useRef, useState, useCallback } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { NotificationPublic } from '@/client/notifications'
import useCustomToast from './useCustomToast'

interface WebSocketMessage {
  type: 'notification' | 'notification_update' | 'ping' | 'error'
  data?: any
  notification?: NotificationPublic
  error?: string
}

interface UseNotificationWebSocketOptions {
  enabled?: boolean
  onNotificationReceived?: (notification: NotificationPublic) => void
  onNotificationUpdated?: (notification: NotificationPublic) => void
  onError?: (error: string) => void
}

export const useNotificationWebSocket = (options: UseNotificationWebSocketOptions = {}) => {
  const {
    enabled = true,
    onNotificationReceived,
    onNotificationUpdated,
    onError,
  } = options

  const toast = useCustomToast()
  const queryClient = useQueryClient()
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionAttempts, setConnectionAttempts] = useState(0)
  const [lastError, setLastError] = useState<string | null>(null)

  const maxReconnectAttempts = 5
  const baseReconnectDelay = 1000 // 1 second
  const maxReconnectDelay = 30000 // 30 seconds

  const getWebSocketUrl = useCallback(() => {
    // In production, this would be your actual WebSocket endpoint
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/api/v1/ws/notifications`
  }, [])

  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      switch (message.type) {
        case 'notification':
          if (message.notification) {
            // Show toast notification for new notifications
            if (message.notification.priority === 'urgent') {
              toast.error(message.notification.title, {
                description: message.notification.message,
                duration: 10000,
              })
            } else if (message.notification.priority === 'high') {
              toast.warning(message.notification.title, {
                description: message.notification.message,
                duration: 7000,
              })
            } else {
              toast.info(message.notification.title, {
                description: message.notification.message,
                duration: 5000,
              })
            }
            
            // Invalidate notifications query to refresh the list
            queryClient.invalidateQueries({ queryKey: ['notifications'] })
            
            // Call custom handler
            onNotificationReceived?.(message.notification)
          }
          break
          
        case 'notification_update':
          if (message.notification) {
            // Invalidate notifications query to refresh the list
            queryClient.invalidateQueries({ queryKey: ['notifications'] })
            
            // Call custom handler
            onNotificationUpdated?.(message.notification)
          }
          break
          
        case 'ping':
          // Respond to ping with pong
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({ type: 'pong' }))
          }
          break
          
        case 'error':
          const errorMessage = message.error || 'WebSocket error occurred'
          setLastError(errorMessage)
          onError?.(errorMessage)
          break
          
        default:
          console.warn('Unknown WebSocket message type:', message.type)
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
      setLastError('Failed to parse WebSocket message')
    }
  }, [toast, queryClient, onNotificationReceived, onNotificationUpdated, onError])

  const connect = useCallback(() => {
    if (!enabled || wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      const wsUrl = getWebSocketUrl()
      const ws = new WebSocket(wsUrl)
      wsRef.current = ws

      ws.onopen = () => {
        console.log('WebSocket connected for notifications')
        setIsConnected(true)
        setConnectionAttempts(0)
        setLastError(null)
        
        // Send authentication token if available
        const token = localStorage.getItem('access_token')
        if (token) {
          ws.send(JSON.stringify({
            type: 'auth',
            token: token
          }))
        }
      }

      ws.onmessage = handleMessage

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setIsConnected(false)
        wsRef.current = null
        
        // Attempt to reconnect if not manually closed
        if (enabled && event.code !== 1000 && connectionAttempts < maxReconnectAttempts) {
          const delay = Math.min(
            baseReconnectDelay * Math.pow(2, connectionAttempts),
            maxReconnectDelay
          )
          
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${connectionAttempts + 1}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            setConnectionAttempts(prev => prev + 1)
            connect()
          }, delay)
        } else if (connectionAttempts >= maxReconnectAttempts) {
          setLastError('Maximum reconnection attempts reached')
          toast.error('Connection lost', {
            description: 'Unable to maintain real-time notifications. Please refresh the page.',
            duration: 10000,
          })
        }
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        setLastError('WebSocket connection error')
      }

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setLastError('Failed to create WebSocket connection')
    }
  }, [enabled, getWebSocketUrl, handleMessage, connectionAttempts, toast])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect')
      wsRef.current = null
    }
    
    setIsConnected(false)
    setConnectionAttempts(0)
    setLastError(null)
  }, [])

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
      return true
    }
    return false
  }, [])

  // Connect on mount and when enabled changes
  useEffect(() => {
    if (enabled) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [enabled, connect, disconnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    isConnected,
    connectionAttempts,
    lastError,
    connect,
    disconnect,
    sendMessage,
  }
}

export default useNotificationWebSocket
