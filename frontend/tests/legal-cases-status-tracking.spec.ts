import { test, expect } from '@playwright/test'

test.describe('React Error #130 - Legal Cases Status Tracking', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/login')

    // Login with lawyer credentials (from seed_test_data.py)
    await page.getByPlaceholder('Email').fill('<EMAIL>')
    await page.getByPlaceholder('Password').fill('LawyerTest123!')
    await page.getByRole('button', { name: 'Log In' }).click()

    // Wait for login to complete - be flexible about the redirect URL
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
  })

  test('REACT_ERROR_130_FIX - Navigate to legal cases page', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Navigate directly to Legal Cases
    console.log('🎯 Navigating to /legal-cases/')
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Wait for the page to load
    await page.waitForTimeout(5000)

    // Log current URL and page title
    const currentUrl = page.url()
    const pageTitle = await page.title()
    console.log('🎯 Current URL:', currentUrl)
    console.log('🎯 Page title:', pageTitle)

    // Check what's actually on the page
    const pageContent = await page.textContent('body')
    console.log('🎯 Page content preview:', pageContent?.substring(0, 500))

    // Check that no React error #130 occurred
    const reactError130 = consoleErrors.find(error =>
      error.includes('Minified React error #130') ||
      error.includes('invariant=130')
    )

    // Log all console errors for debugging
    if (consoleErrors.length > 0) {
      console.log('🎯 Console errors found:', consoleErrors)
    }

    // The main test: no React error #130 should occur
    expect(reactError130).toBeUndefined()

    // If we reach this point, the React error #130 is fixed!
    console.log('🎉 SUCCESS: No React error #130 detected!')
  })

  test('REACT_ERROR_130_FIX - Click legal case and view details', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Navigate directly to Legal Cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Wait for cases to load
    await page.waitForTimeout(2000)

    // Find and click on the first legal case
    const firstCase = page.locator('[data-testid="case-item"]').first()
    if (await firstCase.count() === 0) {
      // If no test data, look for any clickable case element
      const caseLink = page.locator('text=/Corporate|Merger|Contract|Legal/').first()
      if (await caseLink.count() > 0) {
        await caseLink.click()
      } else {
        test.skip('No legal cases found to test')
      }
    } else {
      await firstCase.click()
    }

    // Wait for case details to load
    await page.waitForTimeout(3000)

    // Check that no React error #130 occurred
    const reactError130 = consoleErrors.find(error =>
      error.includes('Minified React error #130') ||
      error.includes('invariant=130')
    )

    expect(reactError130).toBeUndefined()

    // Verify case details page loaded
    await expect(page.locator('text=Case Details')).toBeVisible()
  })

  test('REACT_ERROR_130_FIX - Display status history component', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Navigate directly to Legal Cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Wait for cases to load
    await page.waitForTimeout(2000)

    // Click on a case
    const firstCase = page.locator('[data-testid="case-item"]').first()
    if (await firstCase.count() === 0) {
      const caseLink = page.locator('text=/Corporate|Merger|Contract|Legal/').first()
      if (await caseLink.count() > 0) {
        await caseLink.click()
      } else {
        test.skip('No legal cases found to test')
      }
    } else {
      await firstCase.click()
    }

    // Wait for case details to load
    await page.waitForTimeout(3000)

    // Look for Status History section
    const statusHistorySection = page.locator('text=Status History')
    if (await statusHistorySection.count() > 0) {
      await expect(statusHistorySection).toBeVisible()

      // Wait a bit more for the status history to load
      await page.waitForTimeout(2000)
    }

    // Check that no React error #130 occurred
    const reactError130 = consoleErrors.find(error =>
      error.includes('Minified React error #130') ||
      error.includes('invariant=130')
    )

    expect(reactError130).toBeUndefined()
  })

  test('REACT_ERROR_130_FIX - Change case status functionality', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Navigate directly to Legal Cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Wait for cases to load
    await page.waitForTimeout(2000)

    // Click on a case
    const firstCase = page.locator('[data-testid="case-item"]').first()
    if (await firstCase.count() === 0) {
      const caseLink = page.locator('text=/Corporate|Merger|Contract|Legal/').first()
      if (await caseLink.count() > 0) {
        await caseLink.click()
      } else {
        test.skip('No legal cases found to test')
      }
    } else {
      await firstCase.click()
    }

    // Wait for case details to load
    await page.waitForTimeout(3000)

    // Look for Change Status button
    const changeStatusButton = page.locator('text=Change Status')
    if (await changeStatusButton.count() > 0) {
      await changeStatusButton.click()

      // Wait for modal to open
      await page.waitForTimeout(1000)

      // Close the modal
      const cancelButton = page.locator('text=Cancel')
      if (await cancelButton.count() > 0) {
        await cancelButton.click()
      }
    }

    // Check that no React error #130 occurred
    const reactError130 = consoleErrors.find(error =>
      error.includes('Minified React error #130') ||
      error.includes('invariant=130')
    )

    expect(reactError130).toBeUndefined()
  })
})
