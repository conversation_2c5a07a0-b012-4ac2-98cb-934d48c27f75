import { test as setup } from "@playwright/test"

// Use real test credentials from seed_test_data.py
const testUser = "<EMAIL>"
const testPassword = "LawyerTest123!"

const authFile = "playwright/.auth/user.json"

setup("authenticate", async ({ page }) => {
  await page.goto("/login")
  await page.getByPlaceholder("Email").fill(testUser)
  await page.getByPlaceholder("Password").fill(testPassword)
  await page.getByRole("button", { name: "Log In" }).click()

  // Wait for login to complete - be flexible about redirect
  await page.waitForLoadState('networkidle')
  await page.waitForTimeout(5000)

  // Just save the state regardless of what page we're on
  // The important thing is that we're logged in
  await page.context().storageState({ path: authFile })
})
