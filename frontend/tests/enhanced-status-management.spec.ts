import { test, expect } from '@playwright/test'

test.describe('Enhanced Case Status Management - Issue #72', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/login')

    // Login with lawyer credentials (from seed_test_data.py)
    await page.getByPlaceholder('Email').fill('<EMAIL>')
    await page.getByPlaceholder('Password').fill('LawyerTest123!')
    await page.getByRole('button', { name: 'Log In' }).click()

    // Wait for login to complete
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
  })

  test('Dashboard displays status distribution chart', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Check for status distribution chart
    await expect(page.locator('text=Case Status Distribution')).toBeVisible()
    
    // Check for status badges in the chart
    const statusBadges = page.locator('[data-testid="status-badge"]')
    await expect(statusBadges.first()).toBeVisible()

    // Check for progress bars
    const progressBars = page.locator('[role="progressbar"]')
    await expect(progressBars.first()).toBeVisible()
  })

  test('Status change with progress bar visualization', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Click on first case
    const firstCase = page.locator('[data-testid="case-item"]').first()
    if (await firstCase.count() > 0) {
      await firstCase.click()
    } else {
      // Fallback to any case link
      await page.locator('text=/Corporate|Merger|Contract|Legal/').first().click()
    }

    await page.waitForTimeout(2000)

    // Look for progress bar component
    const progressBar = page.locator('[data-testid="case-progress-bar"]')
    if (await progressBar.count() > 0) {
      await expect(progressBar).toBeVisible()
    }

    // Look for status management section
    const statusSection = page.locator('text=Case Status')
    if (await statusSection.count() > 0) {
      await expect(statusSection).toBeVisible()
    }
  })

  test('Custom status labels for different case types', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Look for custom status badges
    const customStatusBadges = page.locator('[data-testid="custom-status-label"]')
    
    // Check if any custom status labels are present
    if (await customStatusBadges.count() > 0) {
      await expect(customStatusBadges.first()).toBeVisible()
    }

    // Check for case type indicators
    const caseTypeIndicators = page.locator('[data-testid="case-type-badge"]')
    if (await caseTypeIndicators.count() > 0) {
      await expect(caseTypeIndicators.first()).toBeVisible()
    }
  })

  test('Mobile-friendly status indicators', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Check that status indicators are visible on mobile
    const statusIndicators = page.locator('[data-testid="mobile-status-indicator"]')
    if (await statusIndicators.count() > 0) {
      await expect(statusIndicators.first()).toBeVisible()
    }

    // Check for compact status badges
    const compactBadges = page.locator('[data-testid="compact-status-badge"]')
    if (await compactBadges.count() > 0) {
      await expect(compactBadges.first()).toBeVisible()
    }
  })

  test('Status change history and timeline', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Click on first case
    const firstCase = page.locator('[data-testid="case-item"]').first()
    if (await firstCase.count() > 0) {
      await firstCase.click()
    } else {
      await page.locator('text=/Corporate|Merger|Contract|Legal/').first().click()
    }

    await page.waitForTimeout(2000)

    // Look for status history section
    const statusHistory = page.locator('text=Status History')
    if (await statusHistory.count() > 0) {
      await expect(statusHistory).toBeVisible()
    }

    // Look for timeline visualization
    const timeline = page.locator('[data-testid="status-timeline"]')
    if (await timeline.count() > 0) {
      await expect(timeline).toBeVisible()
    }
  })

  test('Bulk status update functionality', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Look for bulk actions
    const bulkActionsButton = page.locator('text=Bulk Actions')
    if (await bulkActionsButton.count() > 0) {
      await bulkActionsButton.click()
      
      // Look for bulk status update option
      const bulkStatusUpdate = page.locator('text=Update Status')
      if (await bulkStatusUpdate.count() > 0) {
        await expect(bulkStatusUpdate).toBeVisible()
      }
    }

    // Alternative: look for checkboxes to select multiple cases
    const caseCheckboxes = page.locator('input[type="checkbox"]')
    if (await caseCheckboxes.count() > 0) {
      await expect(caseCheckboxes.first()).toBeVisible()
    }
  })

  test('Status filtering and sorting', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Look for status filter
    const statusFilter = page.locator('[data-testid="status-filter"]')
    if (await statusFilter.count() > 0) {
      await expect(statusFilter).toBeVisible()
    }

    // Look for filter buttons
    const filterButtons = page.locator('button:has-text("Open"), button:has-text("In Progress"), button:has-text("Closed")')
    if (await filterButtons.count() > 0) {
      await expect(filterButtons.first()).toBeVisible()
    }

    // Look for sort options
    const sortDropdown = page.locator('select, [data-testid="sort-dropdown"]')
    if (await sortDropdown.count() > 0) {
      await expect(sortDropdown.first()).toBeVisible()
    }
  })

  test('Role-based status change permissions', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Click on first case
    const firstCase = page.locator('[data-testid="case-item"]').first()
    if (await firstCase.count() > 0) {
      await firstCase.click()
    } else {
      await page.locator('text=/Corporate|Merger|Contract|Legal/').first().click()
    }

    await page.waitForTimeout(2000)

    // Look for change status button (should be visible for lawyers)
    const changeStatusButton = page.locator('text=Change Status')
    if (await changeStatusButton.count() > 0) {
      await expect(changeStatusButton).toBeVisible()
      
      // Click to open status change modal
      await changeStatusButton.click()
      await page.waitForTimeout(1000)
      
      // Check for status options
      const statusOptions = page.locator('select option, [data-testid="status-option"]')
      if (await statusOptions.count() > 0) {
        await expect(statusOptions.first()).toBeVisible()
      }
      
      // Close modal
      const cancelButton = page.locator('text=Cancel')
      if (await cancelButton.count() > 0) {
        await cancelButton.click()
      }
    }
  })

  test('Status change notifications', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Look for notification system indicators
    const notificationBell = page.locator('[data-testid="notification-bell"]')
    if (await notificationBell.count() > 0) {
      await expect(notificationBell).toBeVisible()
    }

    // Check for any existing notifications
    const notifications = page.locator('[data-testid="notification-item"]')
    if (await notifications.count() > 0) {
      await expect(notifications.first()).toBeVisible()
    }
  })

  test('Visual status indicators with colors and icons', async ({ page }) => {
    // Navigate to legal cases
    await page.goto('/legal-cases/')
    await page.waitForLoadState('networkidle')

    // Check for colored status badges
    const statusBadges = page.locator('[data-testid="status-badge"]')
    if (await statusBadges.count() > 0) {
      await expect(statusBadges.first()).toBeVisible()
      
      // Check that badges have color styling
      const firstBadge = statusBadges.first()
      const backgroundColor = await firstBadge.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      )
      expect(backgroundColor).not.toBe('rgba(0, 0, 0, 0)') // Not transparent
    }

    // Check for status icons
    const statusIcons = page.locator('[data-testid="status-icon"]')
    if (await statusIcons.count() > 0) {
      await expect(statusIcons.first()).toBeVisible()
    }
  })
})
