import { test, expect } from '@playwright/test'

// Override config to avoid dependency issues
process.env.FIRST_SUPERUSER = '<EMAIL>'
process.env.FIRST_SUPERUSER_PASSWORD = 'changethis'

test.describe('React Error #130 Test', () => {
  test('should not have React error #130 when navigating to legal cases', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Navigate to login page
    await page.goto('/login')

    // Login with test credentials
    await page.getByPlaceholder('Email').fill('<EMAIL>')
    await page.getByPlaceholder('Password').fill('changethis')
    await page.getByRole('button', { name: 'Log In' }).click()

    // Wait for login to complete
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(3000)

    // Navigate to Legal Cases
    const legalCasesLink = page.locator('text=Legal Cases').first()
    if (await legalCasesLink.count() > 0) {
      await legalCasesLink.click()
      await page.waitForLoadState('networkidle')
      await page.waitForTimeout(2000)
    }

    // Try to find and click on a legal case
    const caseElements = [
      page.locator('[data-testid="case-item"]').first(),
      page.locator('text=/Corporate|Merger|Contract|Legal/').first(),
      page.locator('tr').nth(1), // Table row
      page.locator('[role="button"]').first()
    ]

    for (const element of caseElements) {
      if (await element.count() > 0) {
        try {
          await element.click()
          await page.waitForLoadState('networkidle')
          await page.waitForTimeout(3000)
          break
        } catch (error) {
          console.log(`Failed to click element: ${error}`)
          continue
        }
      }
    }

    // Check that no React error #130 occurred
    const reactError130 = consoleErrors.find(error =>
      error.includes('Minified React error #130') ||
      error.includes('invariant=130')
    )

    // Log all console errors for debugging
    if (consoleErrors.length > 0) {
      console.log('Console errors found:', consoleErrors)
    }

    expect(reactError130).toBeUndefined()
  })

  test('should load application without React error #130', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Navigate to login page
    await page.goto('/login')
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)

    // Check that no React error #130 occurred during initial load
    const reactError130 = consoleErrors.find(error =>
      error.includes('Minified React error #130') ||
      error.includes('invariant=130')
    )

    // Log all console errors for debugging
    if (consoleErrors.length > 0) {
      console.log('Console errors found:', consoleErrors)
    }

    expect(reactError130).toBeUndefined()
  })
})
