# EquiNova - Environnements de Développement

Ce document explique la distinction claire entre les différents environnements disponibles.

## 🏠 Environnement LOCAL

**Objectif** : Développement quotidien sur votre machine locale

### Configuration
- **Fichiers** : `docker-compose.local.yml` + `.env.local`
- **Script** : `./local-dev.sh`
- **Port<PERSON> directs** : Pas de reverse proxy complexe

### URLs d'accès
- 🌐 **Frontend** : http://localhost:3000
- ⚡ **Backend** : http://localhost:8000
- 📚 **API Docs** : http://localhost:8000/docs
- 🗄️ **Adminer** : http://localhost:8080

### Commandes rapides
```bash
# Démarrer l'environnement local
./local-dev.sh start

# Voir les logs
./local-dev.sh logs

# Arrêter l'environnement
./local-dev.sh stop

# Voir l'aide complète
./local-dev.sh help
```

### Credentials par défaut
- **Email** : admin@localhost
- **Password** : admin123

---

## 🌐 Environnement DEV (Distant)

**Objectif** : Démonstrations, tests d'intégration, partage avec l'équipe

### Configuration
- **Fichiers** : `docker-compose.dev.yml` + `.env.dev`
- **Script** : `./deploy-dev.sh`
- **Reverse proxy** : Traefik avec SSL automatique

### URLs d'accès (après déploiement)
- 🌐 **Frontend** : https://dev.tunleg.com
- ⚡ **API** : https://dev.tunleg.com/api
- 📚 **API Docs** : https://dev.tunleg.com/api/docs
- 🗄️ **Adminer** : https://adminer.tunleg.com
- 📊 **Traefik** : https://traefik.tunleg.com

### Prérequis pour déploiement DEV
1. **Serveur distant** (VPS, cloud, etc.)
2. **DNS configuré** : `*.tunleg.com` → IP du serveur
3. **Domaine** : `tunleg.com` avec sous-domaines

### Commandes de déploiement
```bash
# Sur le serveur distant
./deploy-dev.sh
```

---

## 🔄 Environnement QA (Futur)

**Objectif** : Tests de validation avant production

### Configuration (à venir)
- **Fichiers** : `docker-compose.qa.yml` + `.env.qa`
- **URLs** : https://qa.tunleg.com

---

## 🚀 Environnement PRODUCTION (Futur)

**Objectif** : Application en production pour les utilisateurs finaux

### Configuration (à venir)
- **Fichiers** : `docker-compose.prod.yml` + `.env.prod`
- **URLs** : https://tunleg.com

---

## 📋 Résumé des Environnements

| Environnement | Objectif | Accès | Configuration | Status |
|---------------|----------|-------|---------------|---------|
| **LOCAL** | Développement quotidien | localhost:3000 | Simple, ports directs | ✅ Prêt |
| **DEV** | Démos, tests équipe | dev.tunleg.com | Traefik + SSL | 🔄 À déployer |
| **QA** | Tests de validation | qa.tunleg.com | Production-like | 📋 Planifié |
| **PROD** | Utilisateurs finaux | tunleg.com | Haute disponibilité | 📋 Planifié |

---

## 🛠️ Développement Recommandé

1. **Développement quotidien** → Utilisez l'environnement **LOCAL**
2. **Démonstrations client** → Déployez sur **DEV**
3. **Tests de validation** → Utilisez **QA** (futur)
4. **Mise en production** → Déployez sur **PROD** (futur)

---

## 🔧 Dépannage

### Problème : Ports déjà utilisés
```bash
# Vérifier les ports utilisés
lsof -i :3000
lsof -i :8000

# Arrêter l'ancien environnement
./local-dev.sh stop
```

### Problème : Base de données corrompue
```bash
# Nettoyer complètement
./local-dev.sh clean

# Redémarrer proprement
./local-dev.sh start
```
