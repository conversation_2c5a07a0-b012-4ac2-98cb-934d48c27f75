# Production Docker Compose for EquiNova - tunleg.com
# Issue #107: Deploy Docker + Traefik + PostgreSQL infrastructure

services:
  # Traefik Reverse Proxy with SSL
  traefik:
    image: traefik:3.0
    container_name: equinova-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard (secured)
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/acme.json:/acme.json
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
    networks:
      - traefik-public
    environment:
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=false
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.tunleg.com`)"
      - "traefik.http.routers.traefik.entrypoints=websecure"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik.service=api@internal"
      - "traefik.http.routers.traefik.middlewares=auth"
      - "traefik.http.middlewares.auth.basicauth.users=admin:$$2y$$10$$K8V2VKDpGOKUjVjVY8XqOuH8h8h8h8h8h8h8h8h8h8"

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: equinova-db
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-equinova}
      POSTGRES_USER: ${POSTGRES_USER:-equinova_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-equinova_user} -d ${POSTGRES_DB:-equinova}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - backend
    ports:
      - "127.0.0.1:5432:5432"  # Only accessible locally

  # Backend API (FastAPI)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: equinova-backend
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    environment:
      # Database
      POSTGRES_SERVER: db
      POSTGRES_PORT: 5432
      POSTGRES_DB: ${POSTGRES_DB:-equinova}
      POSTGRES_USER: ${POSTGRES_USER:-equinova_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      
      # Application
      ENVIRONMENT: production
      SECRET_KEY: ${SECRET_KEY}
      FIRST_SUPERUSER: ${FIRST_SUPERUSER}
      FIRST_SUPERUSER_PASSWORD: ${FIRST_SUPERUSER_PASSWORD}
      
      # CORS and Frontend
      FRONTEND_HOST: https://tunleg.com
      BACKEND_CORS_ORIGINS: https://tunleg.com,https://dev.tunleg.com,https://qa.tunleg.com
      
      # Email (optional)
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_USER: ${SMTP_USER:-}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      EMAILS_FROM_EMAIL: ${EMAILS_FROM_EMAIL:-<EMAIL>}
      
      # Monitoring
      SENTRY_DSN: ${SENTRY_DSN:-}
    networks:
      - traefik-public
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      
      # Production API
      - "traefik.http.routers.api-prod.rule=Host(`tunleg.com`) && PathPrefix(`/api`)"
      - "traefik.http.routers.api-prod.entrypoints=websecure"
      - "traefik.http.routers.api-prod.tls.certresolver=letsencrypt"
      - "traefik.http.services.api-prod.loadbalancer.server.port=8000"
      
      # Development API
      - "traefik.http.routers.api-dev.rule=Host(`dev.tunleg.com`) && PathPrefix(`/api`)"
      - "traefik.http.routers.api-dev.entrypoints=websecure"
      - "traefik.http.routers.api-dev.tls.certresolver=letsencrypt"
      
      # QA API
      - "traefik.http.routers.api-qa.rule=Host(`qa.tunleg.com`) && PathPrefix(`/api`)"
      - "traefik.http.routers.api-qa.entrypoints=websecure"
      - "traefik.http.routers.api-qa.tls.certresolver=letsencrypt"

  # Frontend (React/Nginx) - Production
  frontend-prod:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: https://tunleg.com/api
        NODE_ENV: production
    container_name: equinova-frontend-prod
    restart: unless-stopped
    networks:
      - traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.frontend-prod.rule=Host(`tunleg.com`)"
      - "traefik.http.routers.frontend-prod.entrypoints=websecure"
      - "traefik.http.routers.frontend-prod.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend-prod.loadbalancer.server.port=80"

  # Frontend (React/Nginx) - Development
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: https://dev.tunleg.com/api
        NODE_ENV: development
    container_name: equinova-frontend-dev
    restart: unless-stopped
    networks:
      - traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.frontend-dev.rule=Host(`dev.tunleg.com`)"
      - "traefik.http.routers.frontend-dev.entrypoints=websecure"
      - "traefik.http.routers.frontend-dev.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend-dev.loadbalancer.server.port=80"

  # Frontend (React/Nginx) - QA
  frontend-qa:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: https://qa.tunleg.com/api
        NODE_ENV: production
    container_name: equinova-frontend-qa
    restart: unless-stopped
    networks:
      - traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.frontend-qa.rule=Host(`qa.tunleg.com`)"
      - "traefik.http.routers.frontend-qa.entrypoints=websecure"
      - "traefik.http.routers.frontend-qa.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend-qa.loadbalancer.server.port=80"

  # Database Admin (Adminer) - Optional
  adminer:
    image: adminer:4.8.1
    container_name: equinova-adminer
    restart: unless-stopped
    depends_on:
      - db
    environment:
      ADMINER_DEFAULT_SERVER: db
      ADMINER_DESIGN: pepa-linha-dark
    networks:
      - traefik-public
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.adminer.rule=Host(`adminer.tunleg.com`)"
      - "traefik.http.routers.adminer.entrypoints=websecure"
      - "traefik.http.routers.adminer.tls.certresolver=letsencrypt"
      - "traefik.http.services.adminer.loadbalancer.server.port=8080"
      - "traefik.http.routers.adminer.middlewares=auth"

volumes:
  postgres_data:
    driver: local
  traefik_certificates:
    driver: local

networks:
  traefik-public:
    driver: bridge
  backend:
    driver: bridge
    internal: true
