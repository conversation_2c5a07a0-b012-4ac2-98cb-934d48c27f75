# Phase 1: DEV Environment Only - Progressive Deployment
# Issue #115: Deploy EquiNova to production - DEV Phase

services:
  # Traefik Reverse Proxy (shared for all phases)
  traefik:
    image: traefik:3.0
    container_name: equinova-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/acme.json:/acme.json
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
    networks:
      - traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.tunleg.com`)"
      - "traefik.http.routers.traefik.entrypoints=websecure"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik.service=api@internal"
      - "traefik.http.routers.traefik.middlewares=auth"
      - "traefik.http.middlewares.auth.basicauth.users=admin:$$2y$$10$$K8V2VKDpGOKUjVjVY8XqOu"

  # PostgreSQL Database (shared for all phases)
  db:
    image: postgres:15-alpine
    container_name: equinova-db
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-equinova}
      POSTGRES_USER: ${POSTGRES_USER:-equinova_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-equinova_user} -d ${POSTGRES_DB:-equinova}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - backend
    ports:
      - "127.0.0.1:5432:5432"

  # Backend API (DEV only)
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: equinova-backend-dev
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    environment:
      # Database
      POSTGRES_SERVER: db
      POSTGRES_PORT: 5432
      POSTGRES_DB: ${POSTGRES_DB:-equinova}
      POSTGRES_USER: ${POSTGRES_USER:-equinova_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      
      # Application
      ENVIRONMENT: development
      SECRET_KEY: ${SECRET_KEY}
      FIRST_SUPERUSER: ${FIRST_SUPERUSER}
      FIRST_SUPERUSER_PASSWORD: ${FIRST_SUPERUSER_PASSWORD}
      
      # CORS and Frontend
      FRONTEND_HOST: https://dev.tunleg.com
      BACKEND_CORS_ORIGINS: https://dev.tunleg.com
      
      # Email (optional)
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_USER: ${SMTP_USER:-}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      EMAILS_FROM_EMAIL: ${EMAILS_FROM_EMAIL:-<EMAIL>}
    networks:
      - traefik-public
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      
      # Development API
      - "traefik.http.routers.api-dev.rule=Host(`dev.tunleg.com`) && PathPrefix(`/api`)"
      - "traefik.http.routers.api-dev.entrypoints=websecure"
      - "traefik.http.routers.api-dev.tls.certresolver=letsencrypt"
      - "traefik.http.services.api-dev.loadbalancer.server.port=8000"

  # Frontend (DEV only)
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: https://dev.tunleg.com/api
        NODE_ENV: development
    container_name: equinova-frontend-dev
    restart: unless-stopped
    networks:
      - traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.frontend-dev.rule=Host(`dev.tunleg.com`)"
      - "traefik.http.routers.frontend-dev.entrypoints=websecure"
      - "traefik.http.routers.frontend-dev.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend-dev.loadbalancer.server.port=80"

  # Database Admin (optional for DEV)
  adminer:
    image: adminer:4.8.1
    container_name: equinova-adminer
    restart: unless-stopped
    depends_on:
      - db
    environment:
      ADMINER_DEFAULT_SERVER: db
      ADMINER_DESIGN: pepa-linha-dark
    networks:
      - traefik-public
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.adminer.rule=Host(`adminer.tunleg.com`)"
      - "traefik.http.routers.adminer.entrypoints=websecure"
      - "traefik.http.routers.adminer.tls.certresolver=letsencrypt"
      - "traefik.http.services.adminer.loadbalancer.server.port=8080"
      - "traefik.http.routers.adminer.middlewares=auth"

volumes:
  postgres_data:
    driver: local

networks:
  traefik-public:
    driver: bridge
  backend:
    driver: bridge
    internal: true
