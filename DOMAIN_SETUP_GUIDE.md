# 🌐 EquiNova Domain Setup Guide

> **Status**: 🔄 IN PROGRESS - Issue #104  
> **Domain Target**: equinova.com  
> **Registrar**: Namecheap (recommended) or Gandi (EU alternative)

## Domain Purchase Checklist

### Step 1: Domain Availability Check
- [ ] Check `equinova.com` availability on Namecheap
- [ ] Check alternatives: `equinova.legal`, `equinova.tn`
- [ ] Verify no trademark conflicts

### Step 2: Purchase Domain
**Recommended Registrar: Namecheap.com**

#### Required Settings During Purchase:
- [ ] **Auto-renewal**: ENABLED (prevent accidental expiration)
- [ ] **Transfer lock**: ENABLED (prevent unauthorized transfers)
- [ ] **WHOIS privacy**: ENABLED (protect personal information)
- [ ] **DNSSEC**: ENABLED if available (additional security)

#### Purchase Details:
- **Domain**: equinova.com
- **Registration period**: 2 years minimum (better for SEO)
- **Registrant**: Company/Organization details
- **Admin contact**: Technical team email

### Step 3: DNS Configuration

Once domain is purchased, configure these DNS records:

#### A Records (Point to VPS IP)
```
Type: A
Host: @
Value: [VPS_IP_ADDRESS]
TTL: 300 (5 minutes)

Type: A  
Host: www
Value: [VPS_IP_ADDRESS]
TTL: 300
```

#### Subdomain Records
```
Type: A
Host: qa
Value: [VPS_IP_ADDRESS]
TTL: 300

Type: A
Host: dev  
Value: [VPS_IP_ADDRESS]
TTL: 300

Type: A
Host: api
Value: [VPS_IP_ADDRESS]
TTL: 300
```

#### Optional Records
```
Type: MX (if email needed later)
Host: @
Value: [MAIL_SERVER]
Priority: 10
TTL: 3600

Type: TXT (for verification)
Host: @
Value: "v=spf1 -all" (basic SPF record)
TTL: 3600
```

### Step 4: Security Configuration

#### Domain Security Settings:
- [ ] Enable domain lock/transfer protection
- [ ] Set up 2FA on registrar account
- [ ] Configure security notifications
- [ ] Document all credentials securely

#### DNSSEC Setup (if supported):
- [ ] Enable DNSSEC in registrar panel
- [ ] Verify DNSSEC validation
- [ ] Monitor DNSSEC status

### Step 5: Documentation

#### Create Domain Management Documentation:
- [ ] Registrar account details (stored securely)
- [ ] Domain renewal dates
- [ ] DNS configuration backup
- [ ] Contact information used
- [ ] Security settings applied

#### Environment URLs:
- **Production**: https://equinova.com
- **QA/Staging**: https://qa.equinova.com  
- **Development**: https://dev.equinova.com
- **API Production**: https://api.equinova.com
- **API QA**: https://api.qa.equinova.com

### Step 6: Verification

#### Test DNS Propagation:
```bash
# Check A record
dig equinova.com A

# Check subdomains
dig qa.equinova.com A
dig dev.equinova.com A

# Check from different locations
nslookup equinova.com 8.8.8.8
nslookup equinova.com 1.1.1.1
```

#### Online Tools:
- DNS Checker: https://dnschecker.org/
- What's My DNS: https://www.whatsmydns.net/
- DNS Propagation: https://www.dnsmap.io/

## Cost Estimation

### Namecheap Pricing (approximate):
- **equinova.com**: $8.88/year (first year), $12.98/year (renewal)
- **WHOIS privacy**: FREE
- **DNSSEC**: FREE
- **Total first year**: ~$9-10
- **Annual renewal**: ~$13-15

### Alternative Domains:
- **equinova.legal**: ~$40-60/year (premium TLD)
- **equinova.tn**: ~$30-50/year (country TLD)

## Next Steps After Domain Purchase

1. **Immediate**: Configure basic DNS records
2. **Within 24h**: Verify DNS propagation
3. **Before VPS setup**: Document all credentials
4. **After VPS creation**: Update DNS with actual IP
5. **After SSL setup**: Test all subdomains

## Security Best Practices

### Account Security:
- Use strong, unique password for registrar
- Enable 2FA on registrar account
- Use company email for domain contacts
- Keep registrar contact info updated

### Domain Security:
- Enable auto-renewal to prevent expiration
- Set up expiration notifications (60, 30, 7 days)
- Use domain lock/transfer protection
- Monitor domain for unauthorized changes

## Troubleshooting

### Common Issues:
- **DNS not propagating**: Wait 24-48 hours, check TTL values
- **Subdomain not working**: Verify A record configuration
- **SSL issues**: Ensure DNS points to correct IP before SSL setup
- **Email not working**: Configure MX records properly

### Support Contacts:
- **Namecheap Support**: https://www.namecheap.com/support/
- **DNS Issues**: Check with hosting provider
- **SSL Issues**: Verify with Traefik/Let's Encrypt logs

## Links & Resources

### Domain Registrars:
- **Namecheap**: https://www.namecheap.com/domains/
- **Gandi**: https://www.gandi.net/domain

### DNS Tools:
- **DNS Checker**: https://dnschecker.org/
- **What's My DNS**: https://www.whatsmydns.net/
- **DNS Map**: https://www.dnsmap.io/

### Documentation:
- **Namecheap DNS Guide**: https://www.namecheap.com/support/knowledgebase/article.aspx/319/2237/how-can-i-set-up-an-a-address-record-for-my-domain
- **DNSSEC Guide**: https://www.namecheap.com/support/knowledgebase/article.aspx/9722/2237/dnssec-activationdeactivation

---

**Issue**: #104 - Purchase and configure domain name equinova.com  
**Priority**: High  
**Estimated Time**: 2-4 hours  
**Dependencies**: None  
**Next Issue**: #105 - Create and secure Hetzner VPS
